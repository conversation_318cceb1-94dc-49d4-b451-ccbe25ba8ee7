package com.ruoyi.auth.factory;

import com.ruoyi.auth.service.SysLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class LoginServiceFactory {

    private final Map<String, SysLoginService> loginServiceMap = new HashMap<>();

    private final ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, SysLoginService> services = applicationContext.getBeansOfType(SysLoginService.class);
        for (SysLoginService service : services.values()) {
            loginServiceMap.put(service.getLoginType(), service);
        }
    }

    public SysLoginService getLoginService(String loginType) {
        SysLoginService loginService = loginServiceMap.get(loginType);
        if (loginService == null) {
            throw new IllegalArgumentException("无效的登录类型： " + loginType);
        }
        return loginService;
    }
}
