package com.ruoyi.auth;

import com.ruoyi.common.security.annotation.EnableCustomConfig;
import com.ruoyi.common.security.annotation.EnableRyFeignClients;
import com.wnkx.common.lb.annotation.EnableFeignInterceptor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 认证授权中心
 *
 * <AUTHOR>
 */
@EnableRyFeignClients
@EnableFeignInterceptor
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableCustomConfig
@RestController
public class RuoYiAuthApplication {
    public static void main(String[] args) {
        SpringApplication.run(RuoYiAuthApplication.class, args);
    }

    @GetMapping("/hello")
    public String hello() {
        return "hello";
    }
}
