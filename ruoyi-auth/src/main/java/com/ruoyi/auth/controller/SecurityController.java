package com.ruoyi.auth.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.auth.service.AuthSmsService;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.vo.SmsVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * SecurityController
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@RestController
@RequiredArgsConstructor
public class SecurityController {
    private final AuthSmsService authSmsService;
    private final RedisService redisService;

    @PostMapping("/sms")
    public R<String> sms(@RequestBody SmsVo smsVo) {
        authSmsService.sendCode(smsVo);
        if (StrUtil.isNotBlank(smsVo.getTicket())) {
            String ticket = smsVo.getTicket();
            String unionId = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket);
            if (StringUtils.isBlank(unionId)) {
                throw new Biz200Exception("绑定用户信息已经过期");
            }
            redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId, ticket, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
            redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket, unionId, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
        }
        return R.ok("发送验证码成功");
    }

    @PostMapping("/register/sms")
    public R<String> registerSms(@RequestBody SmsVo smsVo) {
        authSmsService.registerSendCode(smsVo);
        if (StrUtil.isNotBlank(smsVo.getTicket())) {
            String ticket = smsVo.getTicket();
            String unionId = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket);
            if (StringUtils.isBlank(unionId)) {
                throw new Biz200Exception("绑定用户信息已经过期");
            }
            redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId, ticket, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
            redisService.setCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + ticket, unionId, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
        }
        return R.ok("发送验证码成功");
    }
}
