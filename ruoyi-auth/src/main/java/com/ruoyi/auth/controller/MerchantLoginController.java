package com.ruoyi.auth.controller;

import com.ruoyi.auth.factory.LoginServiceFactory;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 商家端登录controller
 *
 * <AUTHOR>
 * @date 2024/5/28 11:00
 */
@RestController
@RequestMapping("/business")
@Api(value = "商家端-商家端登录", tags = "商家端-商家端登录")
@RequiredArgsConstructor
public class MerchantLoginController {
    private final TokenService tokenService;
    private final LoginServiceFactory loginServiceFactory;

    @GetMapping("/wechat/qrcode")
    @ApiOperation(value = "生成二维码")
    public R<QrCodeDTO> generateQrcode(@RequestParam(name = "type", defaultValue = "0") Integer type, @RequestParam(name = "code", required = false) String code) {
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BUSINESS);
        return R.ok(sysLoginService.generateQrcode(type, code));
    }

    /**
     * 登录
     *
     * @param form 登录信息
     * @return token
     */
    @PostMapping("login")
    @ApiOperation(value = "登录")
    public R<Map<String, Object>> login(@RequestBody LoginBody form) {
        // 用户登录
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BUSINESS);
        LoginBaseEntity merchantInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(merchantInfo));
    }

    @PostMapping("/phoneLogin")
    @ApiOperation("手机号登录")
    public R<PhoneLoginVO> phoneLogin(@RequestBody @Validated PhoneLoginDTO dto) {
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BUSINESS);
        return R.ok(sysLoginService.phoneLogin(dto));
    }
    @PostMapping("/channel/phoneLogin")
    @ApiOperation("手机号登录(渠道端)")
    @Deprecated(since = "2024-12-02", forRemoval = true)
    public R<ChannelPhoneLoginVO> channelPhoneLogin(@RequestBody @Validated ChannelPhoneLoginDTO dto) {
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BUSINESS);
        return R.ok(sysLoginService.channelPhoneLogin(dto));
    }

    /**
     * 登出
     */
    @DeleteMapping("logout")
    public R<String> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BACK);
            sysLoginService.logout(username);
        }
        return R.ok();
    }
}
