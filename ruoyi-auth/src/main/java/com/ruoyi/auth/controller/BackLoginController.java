package com.ruoyi.auth.controller;

import com.ruoyi.auth.factory.LoginServiceFactory;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.form.RegisterBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 后台登录controller
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class BackLoginController {
    private final TokenService tokenService;

    private final LoginServiceFactory loginServiceFactory;

    @PostMapping("login")
    public R<Map<String, Object>> login(@RequestBody LoginBody form) {
        // 用户登录
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BACK);
        LoginBaseEntity userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<String> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BACK);
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<String> refresh(HttpServletRequest request) {
        LoginBaseEntity loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<String> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.BACK);
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }
}
