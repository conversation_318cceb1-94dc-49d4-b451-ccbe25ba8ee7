package com.ruoyi.auth.controller;

import com.ruoyi.auth.factory.LoginServiceFactory;
import com.ruoyi.auth.form.LoginBody;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.JwtUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.security.auth.AuthUtil;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 商家端登录controller
 *
 * <AUTHOR>
 * @date 2024/5/28 11:00
 */
@RestController
@RequestMapping("/model")
@Api(value = "模特端-模特端登录", tags = "模特端-模特端登录")
@RequiredArgsConstructor
public class ModelLoginController {
    private final TokenService tokenService;
    private final LoginServiceFactory loginServiceFactory;
    /**
     * 登录
     *
     * @param form 登录信息
     * @return token
     */
    @PostMapping("login")
    @ApiOperation(value = "登录")
    public R<Map<String, Object>> login(@RequestBody LoginBody form) {
        // 用户登录
        SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.MODEL);
        LoginBaseEntity modelInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(modelInfo));
    }

    /**
     * 登出
     */
    @DeleteMapping("logout")
    public R<String> logout(HttpServletRequest request) {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            SysLoginService sysLoginService = loginServiceFactory.getLoginService(TokenConstants.MODEL);
            sysLoginService.logout(username);
        }
        return R.ok();
    }
}
