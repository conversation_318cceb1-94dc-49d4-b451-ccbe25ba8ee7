package com.ruoyi.auth.service.impl;

import cn.hutool.core.lang.Assert;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.auth.utils.LoginUtil;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.ip.IpUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/28 12:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BackLoginServiceImpl implements SysLoginService {

    private final LoginUtil loginUtil;
    private final RemoteUserService remoteUserService;

    /**
     * 后台端登录
     *
     * @param username 用户名称
     * @param password 密码
     */
    @Override
    public LoginUser login(String username, String password) {
        //  登录校验
        loginUtil.loginCheck(username, password, UserTypeConstants.MANAGER);

        // 查询用户信息
        LoginUser userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);
        Assert.notNull(userResult, "用户不存在");

        SysUser user = userResult.getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            loginUtil.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除", UserTypeConstants.MANAGER);
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            loginUtil.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员", UserTypeConstants.MANAGER);
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            loginUtil.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码错误", UserTypeConstants.MANAGER);
            throw new ServiceException("密码错误");
        }
        loginUtil.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功", UserTypeConstants.MANAGER);

        user.setLoginIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        user.setLoginDate(new Date());
        Assert.isTrue(remoteUserService.recordLoginInfo(user, SecurityConstants.INNER), "记录用户登录信息失败");
        LoginUser userInfo = userResult;
        userInfo.setUserType(UserTypeConstants.MANAGER);
        userInfo.setUserid(userInfo.getSysUser().getUserId());
        userInfo.setUsername(userInfo.getSysUser().getUserName());
        return userInfo;
    }

    @Override
    public QrCodeDTO generateQrcode(Integer type, String code) {
        throw new ServiceException("不被允许的操作");
    }


    @Override
    public void logout(String loginName) {
        loginUtil.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", UserTypeConstants.MANAGER);
    }

    @Override
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        Assert.isTrue(remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER), "注册用户失败");
        loginUtil.recordLogininfor(username, Constants.REGISTER, "注册成功", UserTypeConstants.MANAGER);
    }

    @Override
    public String getLoginType() {
        return TokenConstants.BACK;
    }

    @Override
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        throw new ServiceException("不允许的操作");
    }

    @Override
    public ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto) {
        throw new ServiceException("不允许的操作");
    }
}
