package com.ruoyi.auth.service;

import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
public interface SysLoginService {

    /**
     * 登录
     *
     * @param username 用户名
     * @param password 密码
     * @return 登录用户信息
     */
    LoginBaseEntity login(String username, String password);


    /**
     * 生成二维码
     * @param type
     * @param code
     * @return
     */
    QrCodeDTO generateQrcode(Integer type, String code);

    /**
     * 登录类型
     *
     * @return 登录类型
     */
    String getLoginType();

    /**
     * 登出
     *
     * @param loginName 用户名
     */
    void logout(String loginName);

    /**
     * 注册
     *
     * @param username 用户名
     * @param password 密码
     */
    void register(String username, String password);

    /**
     * 手机号登录
     * @param dto
     * @return
     */
    PhoneLoginVO phoneLogin(PhoneLoginDTO dto);

    /**
     * 渠道手机号登录
     * @param dto
     * @return
     */
    ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto);
}
