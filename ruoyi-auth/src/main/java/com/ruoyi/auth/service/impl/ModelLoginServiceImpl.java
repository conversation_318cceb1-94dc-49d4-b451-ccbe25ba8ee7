package com.ruoyi.auth.service.impl;

import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.auth.utils.LoginUtil;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.ModelStatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.RemoteModelService;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.model.LoginModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/28 12:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelLoginServiceImpl implements SysLoginService {

    private final LoginUtil loginUtil;
    private final RemoteModelService remoteModelService;
    private String THE_OPERATION_IS_NOT_ALLOWED = "不允许的操作";

    /**
     * 商家端登录
     *
     * @param account      model_account.account
     * @param password     密码
     */
    @Override
    public LoginModel login(String account, String password) {
        // 获取模特数据
        final LoginModel modelByModelAccount = remoteModelService.getModelByModelLoginAccount(account);
        if (StringUtils.isNull(modelByModelAccount)) {
            loginUtil.recordLogininfor(account, Constants.LOGIN_FAIL, "登录账号不存在", UserTypeConstants.MODEL);
            throw new ServiceException("Account does not exist");
        }
        if (ModelStatusEnum.CANCEL.getCode().equals(modelByModelAccount.getModelLoginVO().getStatus())) {
            loginUtil.recordLogininfor(account, Constants.LOGIN_FAIL, "登录账号已取消合作", UserTypeConstants.MODEL);
            throw new ServiceException("Account abnormality prevents login");
        }

        final LoginModel model = modelByModelAccount;
        loginUtil.recordLogininfor(model.getModelLoginVO().getName(), Constants.LOGIN_SUCCESS, "登录成功", UserTypeConstants.MODEL);
        return model;
    }

    @Override
    public QrCodeDTO generateQrcode(Integer type, String code) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public void logout(String loginName) {
        loginUtil.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", UserTypeConstants.MODEL);
    }

    /**
     * 注册
     */
    @Override
    public void register(String username, String password) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public String getLoginType() {
        return TokenConstants.MODEL;
    }

    @Override
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }
}
