package com.ruoyi.auth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.auth.utils.LoginUtil;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.TokenConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.RemoteDistributionChannelService;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO;
import com.ruoyi.system.api.model.LoginChannel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/5/28 12:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChannelLoginServiceImpl implements SysLoginService {

    private final LoginUtil loginUtil;
    private final RemoteDistributionChannelService remoteDistributionChannelService;
    private String THE_OPERATION_IS_NOT_ALLOWED = "不允许的操作";

    /**
     * 商家端登录
     *
     * @param account      model_account.account
     * @param password     密码
     */
    @Override
    public LoginChannel login(String account, String password) {
        Assert.notNull(account, "请输入账户，限制4个字符");
        Assert.isTrue(account.length() <= 4 , "账号限制4个字符");
        Assert.notNull(password, "请输入密码，限制4个字符");
        Assert.isTrue(password.length() <= 4 , "密码限制4个数字");

        // 获取渠道数据
        DistributionChannel distributionChannel = remoteDistributionChannelService.getDistributionChannelEntityBySeedCode(account, SecurityConstants.INNER);
        if (StringUtils.isNull(distributionChannel)) {
            loginUtil.recordLogininfor(account, Constants.LOGIN_FAIL, "登录账号不存在", UserTypeConstants.CHANNEL);
            throw new ServiceException("登录账号：" + account + " 不存在");
        }
        if (StatusEnum.UN_ENABLED.getCode().equals(distributionChannel.getStatus())) {
            loginUtil.recordLogininfor(account, Constants.LOGIN_FAIL, "登录账号已暂停合作", UserTypeConstants.CHANNEL);
            throw new ServiceException("登录账号：" + account + " 已暂停合作");
        }
        if (!password.equals(distributionChannel.getPassword())) {
            loginUtil.recordLogininfor(account, Constants.LOGIN_FAIL, "用户密码错误", UserTypeConstants.MANAGER);
            throw new ServiceException("密码错误");
        }

        final LoginChannel channel = new LoginChannel(BeanUtil.copyProperties(distributionChannel, DistributionChannelVO.class));
        loginUtil.recordLogininfor(distributionChannel.getChannelName(), Constants.LOGIN_SUCCESS, "登录成功", UserTypeConstants.CHANNEL);
        return channel;
    }

    @Override
    public QrCodeDTO generateQrcode(Integer type, String code) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public void logout(String loginName) {
        loginUtil.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", UserTypeConstants.MODEL);
    }

    /**
     * 注册
     */
    @Override
    public void register(String username, String password) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public String getLoginType() {
        return TokenConstants.CHANNEL;
    }

    @Override
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }

    @Override
    public ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto) {
        throw new ServiceException(THE_OPERATION_IS_NOT_ALLOWED);
    }
}
