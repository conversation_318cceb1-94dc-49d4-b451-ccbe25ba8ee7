package com.ruoyi.auth.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.auth.service.SysLoginService;
import com.ruoyi.auth.utils.LoginUtil;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.enums.UserStatus;
import com.ruoyi.common.core.enums.WxChatLoginStatusEnum;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.config.WechatConfig;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.domain.vo.biz.business.ChannelPhoneLoginVO;
import com.ruoyi.system.api.domain.vo.biz.business.PhoneLoginVO;
import com.ruoyi.system.api.model.LoginBusiness;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/28 12:00
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MerchantLoginServiceImpl implements SysLoginService {

    private final LoginUtil loginUtil;
    private final RedisService redisService;
    private final WechatConfig wechatConfig;
    private final RemoteBusinessAccountService remoteBusinessAccountService;
    private final TokenService tokenService;

    /**
     * 商家端登录
     *
     * @param merchantName 商家名称
     * @param password     密码
     */
    @Override
    public LoginBusiness login(String merchantName, String password) {
        throw new ServiceException("不允许的操作");
    }

    @Override
    public QrCodeDTO generateQrcode(Integer type, String code) {
        QrCodeDTO qrcode = remoteBusinessAccountService.generateQrcode(type, code, SecurityConstants.INNER);
        if (ObjectUtil.isNotNull(qrcode)){
            return qrcode;
        }
        throw new Biz200Exception("系统繁忙");
    }

    @Override
    public void logout(String loginName) {
        loginUtil.recordLogininfor(loginName, Constants.LOGOUT, "退出成功", UserTypeConstants.USER);
    }

    /**
     * 注册
     */
    @Override
    public void register(String username, String password) {
        throw new ServiceException("不允许的操作");
    }

    @Override
    public String getLoginType() {
        return TokenConstants.BUSINESS;
    }

    @Override
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        final PhoneLoginVO phoneLoginVO = remoteBusinessAccountService.phoneLogin(dto, SecurityConstants.INNER);
        if (ObjectUtil.isNull(phoneLoginVO)) {
            throw new ServiceException("系统繁忙");
        }
        if (WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR.equals(phoneLoginVO.getLoginStatus())){
            throw new Biz200Exception(phoneLoginVO.getMsg());
        }
        if (StringUtils.isNotBlank(phoneLoginVO.getMsg())){
            loginUtil.recordLogininfor(dto.getPhone(), Constants.LOGIN_FAIL, phoneLoginVO.getMsg(), UserTypeConstants.USER);
            throw new ServiceException(phoneLoginVO.getMsg());
        }
        if (ObjectUtil.isNull(phoneLoginVO.getBusinessAccountVO())){
            return phoneLoginVO;
        }
        final BusinessAccountVO businessAccountVO = phoneLoginVO.getBusinessAccountVO();
        if (UserStatus.DELETED.getCode().equals(String.valueOf(businessAccountVO.getUserStatus()))) {
            loginUtil.recordLogininfor(dto.getPhone(), Constants.LOGIN_FAIL, "对不起，您的账号已删除企微，如需恢复，请重新添加企微", UserTypeConstants.USER);
            throw new ServiceException("对不起，您的账号：" + dto.getPhone() + " 删除企微，如需恢复，请重新添加企微~");
        }
        //现只根据账号来，商家状态已弃用
        Assert.isTrue(businessAccountVO.getUserStatus().compareTo(StatusEnum.UN_ENABLED.getCode()) != 0, "您的账户已被禁用!");
        loginUtil.recordLogininfor(dto.getPhone(), Constants.LOGIN_SUCCESS, "登录成功", UserTypeConstants.USER);
        phoneLoginVO.setToken(loginUser(businessAccountVO));
        phoneLoginVO.setBusinessAccountVO(null);
        return phoneLoginVO;
    }

    @Override
    public ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto) {
        throw new ServiceException("不允许的操作");
    }
    /**
     * 更新全局变量 返回token
     */
    private String loginUser(BusinessAccountVO accountVO) {
        final LoginBusiness login = new LoginBusiness(accountVO);
        final Map<String, Object> tokenMap = tokenService.createToken(login);
        return (String) tokenMap.get(CommonConstant.ACCESS_TOKEN);
    }
}
