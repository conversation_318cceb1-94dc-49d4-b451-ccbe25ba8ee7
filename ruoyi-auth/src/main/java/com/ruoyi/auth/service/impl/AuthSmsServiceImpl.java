package com.ruoyi.auth.service.impl;

import com.ruoyi.auth.service.AuthSmsService;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.domain.vo.SmsVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import java.util.Base64;

/**
 * 短信验证码服务
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AuthSmsServiceImpl implements AuthSmsService {

    private final RemoteBusinessAccountService remoteBusinessAccountService;
    private final Cipher cipher;

    @Override
    public void sendCode(SmsVo smsVo) {
        verifyData(smsVo);
        final String result = remoteBusinessAccountService.sendSms(smsVo, SecurityConstants.INNER);
        if (StringUtils.isNotEmpty(result)) {
            log.error("调用商家服务发送短信验证码失败:{},手机号:{}", result, smsVo.getPhoneNum());
            throw new Biz200Exception(result);
        }
    }

    @Override
    public void registerSendCode(SmsVo smsVo) {
        verifyData(smsVo);
        final String result = remoteBusinessAccountService.registerSendCode(smsVo, SecurityConstants.INNER);
        if (StringUtils.isNotEmpty(result)) {
            log.error("调用商家服务发送短信验证码失败:{}", result);
            throw new Biz200Exception(result);
        }
    }


    @SneakyThrows
    private void verifyData(SmsVo smsVo) {
        String fingerprint = ServletUtils.getRequest().getHeader(CommonConstant.W_N_K_X_FP);
        if (StringUtils.isBlank(fingerprint)) {
            throw Biz200Exception.verifyError();
        }
        String decryptData = String.format("%s.%s", smsVo.getPhoneNum(), fingerprint);
        byte[] encryptedBytes = Base64.getDecoder().decode(smsVo.getVerifyToken());
        byte[] bytes = cipher.doFinal(encryptedBytes);
        if (!new String(bytes).equals(decryptData)) {
            throw Biz200Exception.verifyError();
        }
    }
}
