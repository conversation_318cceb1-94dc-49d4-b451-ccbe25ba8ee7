package com.ruoyi.auth.config;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

import javax.crypto.Cipher;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

@Slf4j
public class RsaConfig {
    @Bean(name = "RsaCipher")
    @SneakyThrows
    private Cipher asymmetricCrypto() {

        // 使用KeyFactory将字节数组转换为PrivateKey对象
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(readKey("/rsaPrivateKey.pem"));
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher;
    }

    @SneakyThrows
    private byte[] readKey(String path) {
        InputStream inputStream = RsaConfig.class.getResourceAsStream(path);
        if (inputStream == null) {
            log.error("读取支付宝私钥失败");
            return new byte[]{};
        }
        StringBuilder contentBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                contentBuilder.append(line).append("\n");
            }
        }
        return Base64.getDecoder().decode(contentBuilder.toString().replace("\n",""));
    }

}
