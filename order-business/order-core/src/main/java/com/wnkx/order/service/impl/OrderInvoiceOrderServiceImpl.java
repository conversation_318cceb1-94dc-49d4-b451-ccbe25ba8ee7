package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoBackVO;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoCompanyVO;
import com.wnkx.order.mapper.OrderInvoiceOrderMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceOrderServiceImpl extends ServiceImpl<OrderInvoiceOrderMapper, OrderInvoiceOrder> implements OrderInvoiceOrderService {

    private final IOrderVideoService orderVideoService;
    private final IOrderService orderService;
    private final RemoteService remoteService;
    private final OrderInvoiceOrderVideoService orderInvoiceOrderVideoService;
    private final IOrderPayLogService orderPayLogService;

    /**
     * 商家端-发票管理-开票相关信息
     */
    @Override
    public List<OrderInvoiceVideoCompanyVO> getInvoiceVideoCompanyList(Long invoiceId, Integer orderType) {
        List<OrderInvoiceOrder> orderInvoiceOrders = baseMapper.selectListByInvoiceId(invoiceId);
        if (CollUtil.isEmpty(orderInvoiceOrders)) {
            return Collections.emptyList();
        }

        List<OrderInvoiceVideoCompanyVO> orderInvoiceVideoCompanyVOS = BeanUtil.copyToList(orderInvoiceOrders, OrderInvoiceVideoCompanyVO.class);
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            List<Long> orderInvoiceOrderIds = orderInvoiceOrders.stream().map(OrderInvoiceOrder::getId).collect(Collectors.toList());
            List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = orderInvoiceOrderVideoService.selectListByOrderInvoiceOrderIds(orderInvoiceOrderIds);
            Map<Long, List<OrderInvoiceOrderVideo>> orderInvoiceOrderVideoMap = orderInvoiceOrderVideos.stream().collect(Collectors.groupingBy(OrderInvoiceOrderVideo::getInvoiceOrderId));
            for (OrderInvoiceVideoCompanyVO orderInvoiceVideoCompanyVO : orderInvoiceVideoCompanyVOS) {
                List<OrderInvoiceOrderVideo> invoiceOrderVideos = orderInvoiceOrderVideoMap.getOrDefault(orderInvoiceVideoCompanyVO.getId(), new ArrayList<>());
                orderInvoiceVideoCompanyVO.setVideoCodes(invoiceOrderVideos.stream().map(OrderInvoiceOrderVideo::getVideoCode).collect(Collectors.toList()));
            }
        }

        return orderInvoiceVideoCompanyVOS;
    }

    /**
     * 运营端-发票管理-发票关联订单
     */
    @Override
    public List<OrderInvoiceVideoBackVO> getInvoiceVideoBackList(Long invoiceId) {
        return baseMapper.getInvoiceVideoBackList(invoiceId);
    }

    /**
     * 根据发票id查询发票关联的视频订单
     */
    @Override
    public List<OrderInvoiceOrder> selectListByInvoiceIds(List<Long> invoiceIds) {
        return baseMapper.selectListByInvoiceIds(invoiceIds);
    }

    @Override
    public List<OrderInvoiceOrder> selectListByOrderNums(List<String> orderNums) {
        return baseMapper.selectListByOrderNums(orderNums);
    }

    /**
     * 批量添加发票关联视频订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchOrderInvoiceVideo(OrderInvoice orderInvoice, Set<String> orderNums, Boolean isRedPushReopen) {
        if (OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(orderInvoice.getOrderType())) {
            List<OrderPayLog> orderPayLogList = orderPayLogService.getOrderPayLogListByOrderNums(new ArrayList<>(orderNums));
            Assert.notEmpty(orderPayLogList, "订单不存在");
            Assert.isTrue(orderPayLogList.stream().allMatch(item -> orderInvoice.getOrderType().equals(item.getOrderType())), "只能提交相同订单类型的发票申请~");
            //  查询视频订单已提现记录
            List<WithdrawDepositRecordVO> businessBalanceDetailLocks = remoteService.withdrawDepositRecord(WithdrawDepositRecordDTO.builder()
                    .status(List.of(BusinessBalanceAuditStatusEnum.APPROVE.getCode(), BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode()))
                    .prepayNums(orderPayLogList.stream().map(OrderPayLog::getOrderNum).collect(Collectors.toList()))
                    .businessId(orderInvoice.getMerchantId())
                    .build());
            Assert.isTrue(CollUtil.isEmpty(businessBalanceDetailLocks.stream().filter(item -> BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(item.getStatus())).collect(Collectors.toList())), "申请的订单中存在正在提现的线上余额支付订单，无法申请开票~");
            Map<String, BigDecimal> orderPayoutAmount = businessBalanceDetailLocks.stream()
                    .collect(Collectors.toMap(
                            WithdrawDepositRecordVO::getPrepayNum,
                            WithdrawDepositRecordVO::getPayOutAmount,
                            BigDecimal::add
                    ));
            List<OrderInvoiceOrder> orderInvoiceOrders = new ArrayList<>();
            for (OrderPayLog item : orderPayLogList) {
                BigDecimal payoutAmount = orderPayoutAmount.get(item.getOrderNum());
                if (ObjectUtil.isNotNull(payoutAmount)
                        && (item.getRealPayAmount().subtract(payoutAmount).compareTo(BigDecimal.ZERO) <= 0)
                        && !Boolean.TRUE.equals(isRedPushReopen)) {
                    continue;
                }
                if (ObjectUtil.isNull(payoutAmount)){
                    payoutAmount = BigDecimal.ZERO;
                }
                OrderInvoiceOrder orderInvoiceOrder = new OrderInvoiceOrder();
                orderInvoiceOrder.setInvoiceId(orderInvoice.getId());
                orderInvoiceOrder.setOrderNum(item.getOrderNum());
                orderInvoiceOrder.setInvoiceAmount(item.getRealPayAmount().subtract(payoutAmount).compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : item.getRealPayAmount().subtract(payoutAmount));
                orderInvoiceOrders.add(orderInvoiceOrder);
            }
            Assert.notEmpty(orderInvoiceOrders, "订单已提现，无法申请重开（如有疑问请联系客服）");
            baseMapper.saveBatch(orderInvoiceOrders);
            return;
        }
        List<OrderInvoiceOrder> orderInvoiceOrders = new ArrayList<>();
        List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = new ArrayList<>();

        List<Order> orders = orderService.selectListByOrderNums(orderNums);
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        Assert.notEmpty(orders, "订单不存在");

        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderInvoice.getOrderType())) {
            List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orderNums);
            Assert.notEmpty(orderVideos, "视频订单不存在");

            //  查询视频订单已提现记录
            List<WithdrawDepositRecordVO> businessBalanceDetailLocks = remoteService.withdrawDepositRecord(WithdrawDepositRecordDTO.builder()
                    .status(List.of(BusinessBalanceAuditStatusEnum.APPROVE.getCode(), BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode()))
                    .videoCodes(orderVideos.stream().map(OrderVideo::getVideoCode).collect(Collectors.toList()))
                    .businessId(orderInvoice.getMerchantId())
                    .build());
            Assert.isTrue(CollUtil.isEmpty(businessBalanceDetailLocks.stream().filter(item -> BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(item.getStatus())).collect(Collectors.toList())), "申请的订单中存在正在提现的视频订单，无法申请开票~");
            Map<String, List<WithdrawDepositRecordVO>> businessBalanceDetailLockMap = businessBalanceDetailLocks.stream().collect(Collectors.groupingBy(WithdrawDepositRecordVO::getVideoCode));

            //  大订单共提现多少钱
            Map<String, BigDecimal> orderPayoutAmount = new HashMap<String, BigDecimal>();
            for (OrderVideo video : orderVideos) {
                List<WithdrawDepositRecordVO> businessBalanceDetailLockList = businessBalanceDetailLockMap.getOrDefault(video.getVideoCode(), new ArrayList<>());
                BigDecimal payoutAmount = businessBalanceDetailLockList.stream().map(WithdrawDepositRecordVO::getPayOutAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal bigDecimal = orderPayoutAmount.get(video.getOrderNum());
                if (ObjectUtil.isNotNull(bigDecimal)) {
                    orderPayoutAmount.put(video.getOrderNum(), bigDecimal.add(payoutAmount));
                } else {
                    orderPayoutAmount.put(video.getOrderNum(), payoutAmount);
                }
            }

            Map<String, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));
            for (Order order : orders) {
                List<OrderVideo> videos = orderVideoMap.get(order.getOrderNum());
                if (CollUtil.isEmpty(videos)) {
                    continue;
                }
                BigDecimal payoutAmount = orderPayoutAmount.get(order.getOrderNum());
                if (ObjectUtil.isNotNull(payoutAmount)
                        && (order.getRealPayAmount().subtract(payoutAmount).compareTo(BigDecimal.ZERO) <= 0)
                        && !Boolean.TRUE.equals(isRedPushReopen)) {
                    continue;
                }
                OrderInvoiceOrder orderInvoiceOrder = new OrderInvoiceOrder();
                orderInvoiceOrder.setInvoiceId(orderInvoice.getId());
                orderInvoiceOrder.setOrderNum(order.getOrderNum());
                orderInvoiceOrder.setInvoiceAmount(order.getRealPayAmount().subtract(payoutAmount).compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : order.getRealPayAmount().subtract(payoutAmount));
                baseMapper.insert(orderInvoiceOrder);
                for (OrderVideo orderVideo : videos) {
                    if (orderVideo.getUseBalance().compareTo(orderVideo.getPayAmount()) >= 0) {
                        continue;
                    }
                    OrderInvoiceOrderVideo orderInvoiceOrderVideo = new OrderInvoiceOrderVideo();
                    orderInvoiceOrderVideo.setInvoiceOrderId(orderInvoiceOrder.getId());
                    orderInvoiceOrderVideo.setVideoId(orderVideo.getId());
                    orderInvoiceOrderVideo.setVideoCode(orderVideo.getVideoCode());
                    orderInvoiceOrderVideos.add(orderInvoiceOrderVideo);
                }
            }

        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(orderInvoice.getOrderType())) {
            for (Order order : orders) {
                OrderInvoiceOrder orderInvoiceOrder = new OrderInvoiceOrder();
                orderInvoiceOrder.setInvoiceId(orderInvoice.getId());
                orderInvoiceOrder.setOrderNum(order.getOrderNum());
                orderInvoiceOrder.setInvoiceAmount(order.getRealPayAmount());
                orderInvoiceOrders.add(orderInvoiceOrder);
            }
        }

        if (OrderTypeEnum.VIP_ORDER.getCode().equals(orderInvoice.getOrderType())) {
            Assert.notEmpty(orderInvoiceOrders, "订单已提现，无法申请重开（如有疑问请联系客服）");
            baseMapper.saveBatch(orderInvoiceOrders);
        }else if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderInvoice.getOrderType())) {
            Assert.notEmpty(orderInvoiceOrderVideos, "订单已提现，无法申请重开（如有疑问请联系客服）");
            orderInvoiceOrderVideoService.saveBatch(orderInvoiceOrderVideos);
        }

    }
}
