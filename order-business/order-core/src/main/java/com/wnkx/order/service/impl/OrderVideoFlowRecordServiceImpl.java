package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFlow;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.wnkx.order.mapper.OrderVideoFlowMapper;
import com.wnkx.order.service.OrderVideoFlowRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 16:57
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowRecordServiceImpl extends ServiceImpl<OrderVideoFlowMapper, OrderVideoFlow> implements OrderVideoFlowRecordService {

    /**
     * @param eventName          事件名称
     * @param orderVideoFlowDTOS dto
     */
    @Override
    public void createOrderVideoFlow(String eventName, List<OrderVideoFlowDTO> orderVideoFlowDTOS) {
        if (CollUtil.isEmpty(orderVideoFlowDTOS)) {
            return;
        }
        List<OrderVideoFlow> orderVideoFlows = BeanUtil.copyToList(orderVideoFlowDTOS, OrderVideoFlow.class);

        LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();

        for (OrderVideoFlow item : orderVideoFlows) {
            item.setEventName(eventName);
            item.setEventExecuteObject(loginUserInfoVo.getUserType());
            item.setEventExecuteUserId(loginUserInfoVo.getUserId());
            item.setEventExecuteUserName(loginUserInfoVo.getName());
            item.setEventExecuteNickName(loginUserInfoVo.getNickName());
            item.setEventExecutePhone(loginUserInfoVo.getPhone());
            item.setEventExecuteTime(DateUtil.date());
        }

        baseMapper.saveBatch(orderVideoFlows);
    }
}
