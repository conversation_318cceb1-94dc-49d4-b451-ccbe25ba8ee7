package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRedOrder;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO;
import com.wnkx.order.mapper.OrderInvoiceRedOrderMapper;
import com.wnkx.order.service.OrderInvoiceRedOrderService;
import com.wnkx.order.service.OrderInvoiceRedOrderVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceRedOrderServiceImpl extends ServiceImpl<OrderInvoiceRedOrderMapper, OrderInvoiceRedOrder> implements OrderInvoiceRedOrderService {

    private final OrderInvoiceRedOrderVideoService orderInvoiceRedOrderVideoService;

    /**
     * 通过发票红冲ID获取发票红冲视频订单
     */
    @Override
    public List<OrderInvoiceRedOrderVideoVO> selectRedOrderVideoListByInvoiceRedIds(List<Long> invoiceRedIds) {
        List<OrderInvoiceRedOrder> orderInvoiceRedOrders = selectListByInvoiceRedIds(invoiceRedIds);
        if (CollUtil.isEmpty(orderInvoiceRedOrders)) {
            return Collections.emptyList();
        }
        List<Long> invoiceRedOrderIds = orderInvoiceRedOrders.stream().map(OrderInvoiceRedOrder::getId).collect(Collectors.toList());
        return orderInvoiceRedOrderVideoService.selectRedOrderVideoVOListByRedOrderIds(invoiceRedOrderIds);
    }

    /**
     * 通过发票红冲ID获取发票红冲订单
     */
    @Override
    public List<OrderInvoiceRedOrder> selectListByInvoiceRedIds(List<Long> invoiceRedIds) {
        if (CollUtil.isEmpty(invoiceRedIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectListByInvoiceRedIds(invoiceRedIds);
    }

}
