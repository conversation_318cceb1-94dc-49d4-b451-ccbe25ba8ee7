package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRedOrder;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:29
 */
public interface OrderInvoiceRedOrderService extends IService<OrderInvoiceRedOrder> {

    /**
     * 通过发票红冲ID获取发票红冲视频订单
     */
    List<OrderInvoiceRedOrderVideoVO> selectRedOrderVideoListByInvoiceRedIds(List<Long> invoiceRedIds);

    /**
     * 通过发票红冲ID获取发票红冲订单
     */
    List<OrderInvoiceRedOrder> selectListByInvoiceRedIds(List<Long> invoiceRedIds);
}
