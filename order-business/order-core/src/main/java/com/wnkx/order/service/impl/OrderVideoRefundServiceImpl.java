package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailFlowDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.model.LoginBusiness;
import com.ruoyi.system.api.model.LoginUser;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderVideoRefundMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderDataScopeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单_视频_退款Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
@RequiredArgsConstructor
public class OrderVideoRefundServiceImpl extends ServiceImpl<OrderVideoRefundMapper, OrderVideoRefund>
        implements IOrderVideoRefundService {
    /**
     * redis
     */
    private final RedisService redisService;
    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderVideoMatchService orderVideoMatchService;
    /**
     * 远程服务
     */
    private final RemoteService remoteService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderDataScopeService orderDataScopeService;
    private final OrderVideoTaskService orderVideoTaskService;
    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;
    private final OrderPromotionDetailService orderPromotionDetailService;

    private final CopyOptions copyOptions = CopyOptions.create()
            .setIgnoreNullValue(true)
            .setIgnoreProperties("id", "refundPicCount", "createTime", "updateTime");


    /**
     * 财务对账-退款记录列表-导出
     */
    @Override
    public void refundSuccessListExport(OrderVideoRefundSuccessListDTO dto, HttpServletResponse response) {
        List<OrderVideoRefundSuccessVO> orderVideoRefundSuccessVOS = refundSuccessList(dto);
        ExcelUtil<OrderVideoRefundSuccessExportVO> util = new ExcelUtil<>(OrderVideoRefundSuccessExportVO.class);
        List<OrderVideoRefundSuccessExportVO> orderPayVideoDetailExportVOS = BeanUtil.copyToList(orderVideoRefundSuccessVOS, OrderVideoRefundSuccessExportVO.class);
        ExcelUtil.setAttachmentResponseHeader(response, "退款记录");
        util.exportExcel(response, orderPayVideoDetailExportVOS, "退款记录");
    }

    /**
     * 财务对账-退款记录列表
     */
    @Override
    public List<OrderVideoRefundSuccessVO> refundSuccessList(OrderVideoRefundSuccessListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovr.operate_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        return baseMapper.refundSuccessList(dto);
    }

    /**
     * 根据视频订单id查询简单的退款列表
     */
    @Override
    public List<OrderVideoRefundSimpleVO> selectOrderVideoRefundSimpleListByVideoId(Collection<Long> videoId) {
        List<OrderVideoRefund> orderVideoRefunds = baseMapper.selectOrderVideoRefundListByVideoId(videoId);
        return BeanUtil.copyToList(orderVideoRefunds, OrderVideoRefundSimpleVO.class);
    }

    /**
     * 查询有退款或者正在退款的视频订单id
     */
    @Override
    public List<Long> selectVideoIdByRefund(List<Long> videoIds) {
        return baseMapper.selectVideoIdByRefund(RefundStatusEnum.getCodeMap(), videoIds);
    }

    /**
     * 根据视频订单id查询退款列表
     */
    @Override
    public List<OrderVideoRefundVO> selectOrderVideoRefundListByVideoId(Collection<Long> videoId) {
        List<OrderVideoRefund> orderVideoRefunds = baseMapper.selectOrderVideoRefundListByVideoId(videoId);
        return BeanUtil.copyToList(orderVideoRefunds, OrderVideoRefundVO.class);
    }

    /**
     * 商家取消退款
     */
    @Override
    public void cancelRefund(List<Long> id) {
        List<OrderVideoRefund> refunds = this.listByIds(id);
        orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder()
                .videoIds(refunds.stream().map(OrderVideoRefund::getVideoId).collect(Collectors.toList()))
                .build());

        Assert.isTrue(SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER), "只有商家才能取消退款！");
        Assert.isFalse(baseMapper
                .checkRefundStatus(id, RefundStatusEnum.AFTER_SALE_UN_CHECK), "当前订单状态不允许取消!");
        Assert.isFalse(baseMapper
                .checkCanCancel(id, SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), "不允许取消平台发起的退款订单!");


        RefundFlowDTO refundFlowDTO = new RefundFlowDTO();
        refundFlowDTO.setRefunds(refunds);
        refundFlowDTO.setRefundStatus(RefundStatusEnum.CANCEL);
        this.refundFlow(refundFlowDTO);
    }

    /**
     * 拒绝退款
     */
    @Override
    public void rejectRefund(RejectRefundDTO rejectRefundDTO) {
        Assert.isFalse(baseMapper
                .checkRefundStatus(rejectRefundDTO.getId(), RefundStatusEnum.AFTER_SALE_UN_CHECK), "当前订单状态非待审核，不允许操作!");

        List<OrderVideoRefund> refunds = this.listByIds(rejectRefundDTO.getId());

        List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
        refunds.forEach(refund -> {
                    refund.setRejectCause(rejectRefundDTO.getRejectCause());
                    orderVideoOperateDTOS.add(OrderVideoOperateDTO.builder().videoId(refund.getVideoId()).eventContent(StrUtil.format(OrderVideoOperateTypeEnum.REFUND_NOT_APPROVED.getEventContent(), refund.getInitiatorName(), RefundTypeEnum.getByCode(refund.getRefundType()).getLabel())).build());
                }
        );

        RefundFlowDTO refundFlowDTO = new RefundFlowDTO();
        refundFlowDTO.setRefunds(refunds);
        refundFlowDTO.setRefundStatus(RefundStatusEnum.REJECT);
        this.refundFlow(refundFlowDTO);

        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REFUND_NOT_APPROVED.getEventName(), null, RefundTypeEnum.CANCEL_ORDER.getCode().equals(refunds.get(0).getRefundType()) ? 0 : OrderVideoOperateTypeEnum.REFUND_NOT_APPROVED.getIsPublic(), null, orderVideoOperateDTOS);
    }

    /**
     * 同意退款
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agreeRefund(AgreeRefundDTO dto) {
        Assert.isFalse(baseMapper.checkCanRefund(dto.getId()), "订单已处理或不支持退款,当前状态非待审核!");

        List<OrderVideoRefund> refunds = this.listByIds(dto.getId());

        List<Long> videoIds = new ArrayList<>();
        for (OrderVideoRefund item : refunds){
            item.setRemark(dto.getRemark());
            videoIds.add(item.getVideoId());
        }

        List<OrderVideo> orderVideos = orderVideoService.listByIds(videoIds);
        orderVideoService.checkVideoStatus(orderVideos,
                OrderStatusEnum.UN_CONFIRM, OrderStatusEnum.UN_MATCH, OrderStatusEnum.NEED_FILLED,
                OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM, OrderStatusEnum.FINISHED);

        RefundFlowDTO refundFlowDTO = new RefundFlowDTO();
        refundFlowDTO.setRefunds(refunds);
        refundFlowDTO.setRefundStatus(RefundStatusEnum.AFTER_SALE_FINISHED);
        List<OrderVideoRefund> orderVideoRefunds = this.refundFlow(refundFlowDTO);

        //只有取消订单 才需要把订单改为交易关闭状态
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, p -> p));
        List<OrderVideo> tradeCloseOrderVideos = new ArrayList<>();
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
        List<Long> taskDetailIds = new ArrayList<>();

        List<OrderVideo> updateOrderVideos = new ArrayList<>();
        for (OrderVideoRefund refund : refunds) {
            if (RefundTypeEnum.CANCEL_ORDER.getCode().equals(refund.getRefundType())) {
                OrderVideo orderVideo = orderVideoMap.get(refund.getVideoId());
                if (orderVideo != null) {
                    tradeCloseOrderVideos.add(orderVideo);
                }
            }
            if (!RefundTypeEnum.REPARATION.getCode().equals(refund.getRefundType())){
                orderVideoOperateDTOS.add(OrderVideoOperateDTO.builder().videoId(refund.getVideoId())
                        .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.REFUND_THROUGH.getEventContent(),
                                refund.getInitiatorName(),
                                RefundTypeEnum.getByCode(refund.getRefundType()).getLabel(),
                                refund.getRefundAmount())
                        ).build());
            }

            if (RefundTypeEnum.REPARATION.getCode().equals(refund.getRefundType())) {
                if (ObjectUtil.isNotNull(refund.getTaskDetailId())){
                    taskDetailIds.addAll(StringUtils.splitToLong(refund.getTaskDetailId(), StrPool.COMMA));
                }
//                补偿订单单独处理提示语
                orderVideoOperateDTOS.add(OrderVideoOperateDTO.builder().videoId(refund.getVideoId())
                        .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.REFUND_THROUGH_REPARATION.getEventContent(),
                                refund.getInitiatorName(),
                                RefundTypeEnum.getByCode(refund.getRefundType()).getLabel(),
                                refund.getRefundAmount())
                        ).build());
                if (StatusTypeEnum.YES.getCode().equals(refund.getIsCancelOrder())){
                    OrderVideo orderVideo = orderVideoMap.get(refund.getVideoId());
                    if (orderVideo != null) {
                        tradeCloseOrderVideos.add(orderVideo);
                    }
                }
            }

            if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(refund.getRefundType())) {
                OrderVideo orderVideo = orderVideoMap.get(refund.getVideoId());
                if (ObjectUtil.isNotNull(orderVideo)) {
                    OrderVideo updateOrderVideo = new OrderVideo();
                    updateOrderVideo.setId(orderVideo.getId());
                    updateOrderVideo.setRefundPicCount(orderVideo.getRefundPicCount() + refund.getRefundPicCount());
                    updateOrderVideos.add(updateOrderVideo);
                }
            }
        }
        if (CollUtil.isNotEmpty(updateOrderVideos)) {
            orderVideoService.updateBatchById(updateOrderVideos);
        }
        if (CollUtil.isNotEmpty(taskDetailIds)) {
            orderVideoTaskDetailService.finishWorkOrders(taskDetailIds, OrderTaskDetailFlowCompletionModeEnum.COMPENSATION, TaskDetailFlowRecordOperateByTypeEnum.ASSIGNEE,Collections.emptyList());
        }

        SpringUtils.getBean(IOrderService.class).createOrderFlow(tradeCloseOrderVideos, OrderStatusEnum.TRADE_CLOSE, "同意退款");
        if (RefundTypeEnum.CANCEL_ORDER.getCode().equals(refunds.get(0).getRefundType())) {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REFUND_THROUGH.getEventName(),
                    OrderVideoOperateTypeEnum.NO_PUBLIC,
                    orderVideoOperateDTOS
            );
        }
        if (!RefundTypeEnum.CANCEL_ORDER.getCode().equals(refunds.get(0).getRefundType())) {
            if (RefundTypeEnum.REPARATION.getCode().equals(refunds.get(0).getRefundType())) {
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REFUND_THROUGH_REPARATION.getEventName(),
                        OrderVideoOperateTypeEnum.REFUND_THROUGH_REPARATION.getIsPublic(),
                        orderVideoOperateDTOS
                );
            }
            if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(refunds.get(0).getRefundType())) {
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REFUND_THROUGH.getEventName(),
                        OrderVideoOperateTypeEnum.REFUND_THROUGH.getIsPublic(),
                        orderVideoOperateDTOS
                );
            }
            orderVideoOperateService.updateOrderVideoOperateToPublic(videoIds);
        }

        Order orderByOrderNum = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideoRefunds.get(0).getOrderNum());
        // 修改余额
        for (OrderVideoRefund refund : orderVideoRefunds) {
            try {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + refund.getBusinessId(), 60L), "其他退款正在操作中，请稍后重试！");
                BusinessBalanceDTO businessBalanceDto = new BusinessBalanceDTO();
                businessBalanceDto.setBusinessId(refund.getBusinessId());
                businessBalanceDto.setUseBalance(refund.getRefundAmount());
                businessBalanceDto.setOrigin(refund.getRefundType());
                businessBalanceDto.setIsBalanceLock(StatusTypeEnum.NO.getCode());
                businessBalanceDto.setAuditStatus(AuditStatusEnum.APPROVE.getCode());

                BusinessBalanceFlowDTO businessBalanceFlowDto = new BusinessBalanceFlowDTO();
                businessBalanceFlowDto.setBusinessId(refund.getBusinessId());
                businessBalanceFlowDto.setOrderNum(refund.getOrderNum());
                businessBalanceFlowDto.setRefundNum(refund.getRefundNum());
                businessBalanceFlowDto.setOrderTime(refund.getOperateTime());
                businessBalanceFlowDto.setVideoCode(refund.getVideoCode());
                businessBalanceFlowDto.setVideoId(refund.getVideoId());


                OrderVideo orderVideo = orderVideoMap.get(refund.getVideoId());

                businessBalanceFlowDto.setCreateOrderUserName(orderVideo.getCreateOrderUserName());
                businessBalanceFlowDto.setCreateOrderUserNickName(orderVideo.getCreateOrderUserNickName());
                businessBalanceFlowDto.setPayType(orderByOrderNum.getPayType());
                businessBalanceFlowDto.setOrderTableOrderTime(orderByOrderNum.getOrderTime());

                BusinessBalanceDetailFlowDTO businessBalanceDetailFlowdto = new BusinessBalanceDetailFlowDTO();
                businessBalanceDetailFlowdto.setNumber(refund.getRefundNum());
                businessBalanceDetailFlowdto.setVideoId(refund.getVideoId());
                businessBalanceDetailFlowdto.setVideoCode(refund.getVideoCode());
                businessBalanceDetailFlowdto.setAmount(orderVideo.getAmount());
                businessBalanceDetailFlowdto.setPayAmount(orderVideo.getPayAmount());
                businessBalanceDetailFlowdto.setDifferenceAmount(orderVideo.getDifferenceAmount());
                businessBalanceDetailFlowdto.setUseBalance(orderVideo.getUseBalance());
                businessBalanceDetailFlowdto.setType(BalanceType.INCOME.getCode());


                businessBalanceFlowDto.setBusinessBalanceDetailFlowList(Arrays.asList(businessBalanceDetailFlowdto));
                businessBalanceDto.setBusinessBalanceFlowDTOS(List.of(businessBalanceFlowDto));
                SpringUtils.getBean(IOrderService.class).updateBusinessBalance(businessBalanceDto);
            } finally {
                redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + refund.getBusinessId());
            }
        }
    }

    /**
     * 导出退款订单
     */
    @Override
    public List<OrderVideoRefundExportDTO> exportExcel(OrderVideoRefundListDTO orderVideoRefundListDTO) {
        List<OrderVideoRefundVO> orderVideoRefundVOS = selectOrderVideoRefundListByCondition(orderVideoRefundListDTO);

        return assembleExcelData(orderVideoRefundVOS);
    }

    /**
     * 申请退款
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyRefund(ApplyRefundDTO applyRefundDTO) {
        LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        final Integer initiatorSource;
        final String initiatorName;
        if (loginUser instanceof LoginBusiness) {
            this.checkMerchantCanRefund(applyRefundDTO);
            initiatorSource = InitiatorSourceEnum.MERCHANT.getCode();
            initiatorName = StrUtil.isNotBlank(loginUser.getUsername()) ? loginUser.getUsername() : ((LoginBusiness) loginUser).getBusinessAccountVO().getNickName();
        } else if (loginUser instanceof LoginUser) {
            Assert.isTrue(StrUtil.isNotBlank(applyRefundDTO.getRefundCause()), "[退款原因]不能为空");
            List<OrderVideo> orderVideos = orderVideoService.listByIds(Collections.singletonList(applyRefundDTO.getVideoId()));
            orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_CONFIRM, OrderStatusEnum.UN_MATCH,
                    OrderStatusEnum.NEED_FILLED, OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM, OrderStatusEnum.FINISHED);
            initiatorSource = InitiatorSourceEnum.OPERATION.getCode();
            initiatorName = loginUser.getUsername();
        } else {
            throw new ServiceException("非法操作！");
        }

        /**
         * 1.取消订单校验：非已取消（上文已校验）、不存在审核中数据（后续校验是否存在审核中数据）
         * 2.商家端退款：只能存在一个退款申请（专门处理）
         * 3.补偿订单：非已取消（上文已校验）、不存在审核中数据（后续校验是否存在审核中数据）
         * 4.照片选配：非已取消（上文已校验）、不存在审核中数据（后续校验是否存在审核中数据）、退款照片数量总和要在视频照片数量内、照片数量与退款金额需要一致
         */
        List<OrderVideoRefundVO> orderVideoRefunds = selectOrderVideoRefundListByVideoId(Arrays.asList(applyRefundDTO.getVideoId()));
        //照片选配退款金额、退款照片数量
        BigDecimal refundPicAmount = BigDecimal.ZERO;
        Integer refundPicCount = 0;
        BigDecimal totalRefundedAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(orderVideoRefunds)) {
            for (OrderVideoRefundVO item : orderVideoRefunds) {
                if (RefundStatusEnum.AFTER_SALE_FINISHED.getCode().equals(item.getRefundStatus())){
                    totalRefundedAmount = totalRefundedAmount.add(item.getRefundAmount());
                }
                if (RefundStatusEnum.AFTER_SALE_FINISHED.getCode().equals(item.getRefundStatus()) && RefundTypeEnum.CANCEL_OPENTION.getCode().equals(item.getRefundType())) {
                    //退款成功 && 照片选配
                    refundPicAmount = refundPicAmount.add(item.getRefundAmount());
                    refundPicCount += item.getRefundPicCount();
                }
            }
            OrderVideoRefundVO orderVideoRefundVO = orderVideoRefunds.get(0);
            //校验只能存在一个审核中的退款数据
            Assert.isFalse(RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode().equals(orderVideoRefundVO.getRefundStatus()), "{}审核中，无法重复发起！", RefundTypeEnum.getByCode(orderVideoRefundVO.getRefundType()).getLabel());

            //商家端退款 只能存在一个退款申请
            if (InitiatorSourceEnum.MERCHANT.getCode().equals(initiatorSource)) {
                Assert.isFalse(baseMapper.checkCanApply(applyRefundDTO.getVideoId()), "已存在退款申请审批，无法重复发起");
            }
        }
        RefundInfoVO refundInfo = getRefundInfo(BeanUtil.copyProperties(applyRefundDTO, RefundInfoDTO.class));
        if (RefundTypeEnum.CANCEL_ORDER.getCode().equals(applyRefundDTO.getRefundType())) {
            //只有取消订单金额必须一致
            Assert.isTrue(refundInfo.getRefundAmount().compareTo(applyRefundDTO.getRefundAmount()) == 0, "退款金额错误！");
        }
        if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(applyRefundDTO.getRefundType())) {
            //如果是取消选配 已退金额需要小于订单金额
            Assert.isTrue(refundInfo.getRefundTotal().compareTo(refundInfo.getAmount()) < 0, "可退款金额为0，无法发起退款");
        }


        OrderVideoRefund orderVideoRefund = new OrderVideoRefund();
        BeanUtil.copyProperties(applyRefundDTO, orderVideoRefund, copyOptions);

        OrderVideo orderVideo = orderVideoService.getById(applyRefundDTO.getVideoId());
        BeanUtil.copyProperties(orderVideo, orderVideoRefund, copyOptions);
        orderVideoRefund.setRealAmount(orderVideo.getPayAmount().subtract(orderVideo.getDifferenceAmount()));
        if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(applyRefundDTO.getRefundType())) {
            //剩余待退款照片数量
            Integer surplusPicCount = refundInfo.getSurplusPicCount();
            Assert.isTrue(surplusPicCount.compareTo(applyRefundDTO.getRefundPicCount()) >= 0, "退款照片数量错误！");
            //剩余待退款照片数量 >= 本次退款照片数量
            if (surplusPicCount.compareTo(applyRefundDTO.getRefundPicCount()) == 0) {
                //全部退款
                Assert.isTrue(refundInfo.getRefundAmount().compareTo(applyRefundDTO.getRefundAmount()) == 0, "退款金额错误！");
                orderVideoRefund.setIsFullRefundPic(StatusTypeEnum.YES.getCode());
            } else {
                orderVideoRefund.setIsFullRefundPic(StatusTypeEnum.NO.getCode());
                //退款金额需要和照片费用对应
                BigDecimal refundAmount = refundInfo.getRefundAmount();
                BigDecimal computeRefundAmount = refundAmount.divide(BigDecimal.valueOf(surplusPicCount)).setScale(2, RoundingMode.DOWN).multiply(BigDecimal.valueOf(applyRefundDTO.getRefundPicCount()));
                Assert.isTrue(computeRefundAmount.compareTo(applyRefundDTO.getRefundAmount()) == 0, "退款金额错误！");
            }
        }

        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideo.getOrderNum());
        BeanUtil.copyProperties(order, orderVideoRefund, copyOptions);

        orderVideoRefund.setBusinessId(order.getMerchantId());
        orderVideoRefund.setInitiatorSource(initiatorSource);
        orderVideoRefund.setInitiatorName(initiatorName);
        orderVideoRefund.setApplyTime(DateUtil.date());
        orderVideoRefund.setRefundNum(createRefundNum(orderVideo.getVideoCode()));
        orderVideoRefund.setRefundStatus(RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode());
        orderVideoRefund.setRefundAmountTotal(totalRefundedAmount);
        if (refundInfo.getRefundAmount().compareTo(applyRefundDTO.getRefundAmount()) == 0 && !RefundTypeEnum.CANCEL_OPENTION.getCode().equals(applyRefundDTO.getRefundType())) {
            orderVideoRefund.setIsFullRefund(StatusEnum.ENABLED.getCode());
        }
        if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(applyRefundDTO.getRefundType())) {
            orderVideoRefund.setRefundPicCount(applyRefundDTO.getRefundPicCount());
        }
        if (CollUtil.isNotEmpty(applyRefundDTO.getTaskDetailIds())) {
            orderVideoTaskService.checkRefundPendingTaskThenRecord(applyRefundDTO.getVideoId(), applyRefundDTO.getTaskDetailIds(), orderVideoRefund);
        }
        baseMapper.insert(orderVideoRefund);

        if (RefundTypeEnum.CANCEL_ORDER.getCode().equals(applyRefundDTO.getRefundType())) {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.PAYMENT_CANCEL_ORDER.getEventName(),
                    OrderVideoOperateTypeEnum.PAYMENT_CANCEL_ORDER.getIsPublic(),
                    OrderVideoOperateDTO.builder().videoId(applyRefundDTO.getVideoId())
                            .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.PAYMENT_CANCEL_ORDER.getEventContent(),
                                    UserTypeConstants.MANAGER_TYPE == SecurityUtils.getLoginUserType() ? "客服" : "",
                                    applyRefundDTO.getRefundAmount())
                            ).build());
        }
        if (RefundTypeEnum.REPARATION.getCode().equals(applyRefundDTO.getRefundType())) {
            if (StatusTypeEnum.YES.getCode().equals(applyRefundDTO.getIsCancelOrder())){
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION_AND_CANCEL.getEventName(),
                        OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION_AND_CANCEL.getIsPublic(),
                        OrderVideoOperateDTO.builder().videoId(applyRefundDTO.getVideoId())
                                .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION_AND_CANCEL.getEventContent(),
                                        RefundTypeEnum.getByCode(applyRefundDTO.getRefundType()).getLabel(), applyRefundDTO.getRefundAmount(), applyRefundDTO.getRefundCause())
                                ).build());
            }else {
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION.getEventName(),
                        OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION.getIsPublic(),
                        OrderVideoOperateDTO.builder().videoId(applyRefundDTO.getVideoId())
                                .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION.getEventContent(),
                                        RefundTypeEnum.getByCode(applyRefundDTO.getRefundType()).getLabel(), applyRefundDTO.getRefundAmount(), applyRefundDTO.getRefundCause())
                                ).build());
            }
        }
        if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(applyRefundDTO.getRefundType())) {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_REFUND_CANCEL_OPTION.getEventName(),
                    OrderVideoOperateTypeEnum.ORDER_REFUND_CANCEL_OPTION.getIsPublic(),
                    OrderVideoOperateDTO.builder().videoId(applyRefundDTO.getVideoId())
                            .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.ORDER_REFUND_CANCEL_OPTION.getEventContent(),
                                    RefundTypeEnum.getByCode(applyRefundDTO.getRefundType()).getLabel(), applyRefundDTO.getRefundPicCount(), applyRefundDTO.getRefundAmount(), applyRefundDTO.getRefundCause())
                            ).build());
        }
    }

    /**
     * 检查商家能否发起退款
     */
    private void checkMerchantCanRefund(ApplyRefundDTO applyRefundDTO) {
        Assert.isTrue(RefundTypeEnum.CANCEL_ORDER.getCode().equals(applyRefundDTO.getRefundType()), "商家只能发起取消订单！");

        OrderVideo orderVideo = orderVideoService.getById(applyRefundDTO.getVideoId());
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.UN_CONFIRM, OrderStatusEnum.UN_MATCH);

        Assert.isFalse(DateUtil.isIn(DateUtil.date(), orderVideo.getUnConfirmTime(), DateUtil.offset(orderVideo.getUnConfirmTime(), DateField.HOUR_OF_DAY, orderVideoProperties.getMerchantApplyRefundOverTime())), "当前还在限制时间范围内，不允许申请退款！");
    }

    /**
     * 申请退款-获取退款信息
     */
    @Override
    public RefundInfoVO getRefundInfo(RefundInfoDTO getRefundInfoDTO) {
        RefundInfoVO refundInfoVO = new RefundInfoVO();
        List<OrderVideoRefundStatisticsVO> orderVideoRefundStatisticsVOS = baseMapper.refundStatistics(Arrays.asList(getRefundInfoDTO.getVideoId()));
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cancelOpentionAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(orderVideoRefundStatisticsVOS)) {
            OrderVideoRefundStatisticsVO orderVideoRefundStatisticsVO = orderVideoRefundStatisticsVOS.get(0);
            totalAmount = orderVideoRefundStatisticsVO.getReparationAmount().add(orderVideoRefundStatisticsVO.getCancelOrderAmount()).add(orderVideoRefundStatisticsVO.getCancelOpentionAmount());
            cancelOpentionAmount = orderVideoRefundStatisticsVO.getCancelOpentionAmount();
        }
        refundInfoVO.setRefundTotal(totalAmount);

        OrderVideo orderVideo = orderVideoService.getById(getRefundInfoDTO.getVideoId());
        BigDecimal realAmount = orderVideo.getPayAmount().subtract(orderVideo.getDifferenceAmount());
        refundInfoVO.setAmount(realAmount);
        refundInfoVO.setOrderDiscountDetailVOS(orderPromotionDetailService.selectOrderDiscountDetailsByVideoId(orderVideo.getId()));
        refundInfoVO.setOriginAmount(orderVideo.getAmount());//订单原价
        if (RefundTypeEnum.CANCEL_ORDER.getCode().equals(getRefundInfoDTO.getRefundType())) {
            //如果是取消订单
            refundInfoVO.setRefundAmount(realAmount.compareTo(totalAmount) > 0 ? realAmount.subtract(totalAmount) : BigDecimal.ZERO);
            return refundInfoVO;
        }

        if (RefundTypeEnum.REPARATION.getCode().equals(getRefundInfoDTO.getRefundType())) {
            //如果是补偿订单
            refundInfoVO.setRefundAmount(realAmount);
            return refundInfoVO;
        }
        //取消选配退款金额 = 订单选配金额 - 订单选配已退金额
        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideo.getOrderNum());
        Assert.notNull(order.getCurrentExchangeRate(), "订单汇率为空，数据异常，请联系管理员处理！");

        BigDecimal refundAmount = getOpentionAmount(orderVideo.getPicPrice(), order.getCurrentExchangeRate());
        refundInfoVO.setPicCount(orderVideo.getPicCount());
        refundInfoVO.setPicPrice(orderVideo.getPicPrice());
        refundInfoVO.setSurplusPicCount(PicCountEnum.getValue(orderVideo.getPicCount()) - orderVideo.getRefundPicCount());
        refundInfoVO.setRefundAmount(refundAmount.subtract(cancelOpentionAmount));
        return refundInfoVO;
    }

    @Override
    public List<OrderVideoRefund> selectValidOrderVideoRefundListByVideoId(Collection<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectValidOrderVideoRefundListByVideoId(videoIds);
    }

    /**
     * 获取退款信息(特殊处理接口 内部不调用)
     *
     * @param getRefundInfoDTO
     * @return
     */
    @Override
    public RefundInfoVO getRefundInfoVo(RefundInfoDTO getRefundInfoDTO) {
        RefundInfoVO refundInfo = getRefundInfo(getRefundInfoDTO);
        refundInfo.setCanRefund(StatusTypeEnum.YES.getCode());
        List<OrderVideoRefundVO> orderVideoRefunds = selectOrderVideoRefundListByVideoId(Arrays.asList(getRefundInfoDTO.getVideoId()));
        if (CollUtil.isNotEmpty(orderVideoRefunds)) {
            OrderVideoRefundVO orderVideoRefundVO = orderVideoRefunds.get(0);
            //校验只能存在一个审核中的退款数据
            if (RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode().equals(orderVideoRefundVO.getRefundStatus())) {
                refundInfo.setCanRefund(StatusTypeEnum.NO.getCode());
                refundInfo.setRefundType(orderVideoRefundVO.getRefundType());

                //特殊处理 如果存在照片选配的审核中订单 可退金额、剩余可退照片需要减去审核中的数据
                if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(getRefundInfoDTO.getRefundType())) {
                    if (refundInfo.getRefundTotal().compareTo(refundInfo.getAmount()) >= 0) {
                        refundInfo.setRefundAmount(BigDecimal.ZERO);
                        refundInfo.setSurplusPicCount(0);
                        return refundInfo;
                    }

                    if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(orderVideoRefundVO.getRefundType())) {
                        refundInfo.setSurplusPicCount(refundInfo.getSurplusPicCount() - orderVideoRefundVO.getRefundPicCount());
                        refundInfo.setRefundAmount(refundInfo.getRefundAmount().compareTo(orderVideoRefundVO.getRefundAmount()) >= 0
                                ? refundInfo.getRefundAmount().subtract(orderVideoRefundVO.getRefundAmount()) : BigDecimal.ZERO);
                    }
                    if (refundInfo.getRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
                        refundInfo.setSurplusPicCount(0);
                    }
                }

                return refundInfo;
            }
            //照片选配 只能存在一个退款申请
            if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(getRefundInfoDTO.getRefundType())) {
                //退款金额为0
                if (refundInfo.getRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
                    refundInfo.setCanRefund(StatusTypeEnum.NO.getCode());
                }
                //退款总额 >= 视频订单金额
                if (refundInfo.getRefundTotal().compareTo(refundInfo.getAmount()) >= 0) {
                    refundInfo.setCanRefund(StatusTypeEnum.NO.getCode());
                    refundInfo.setRefundAmount(BigDecimal.ZERO);
                }
            }

            if (UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()) && Boolean.TRUE.equals(baseMapper.checkCanApply(getRefundInfoDTO.getVideoId()))) {
                refundInfo.setCanRefund(StatusTypeEnum.NO.getCode());
            }
        }
//        当通过补偿订单退完全额且没有任何 取消订单/取消选配 的操作时，需将取消选配金额置为0
        if (getRefundInfoDTO.getRefundType().equals(RefundTypeEnum.CANCEL_OPENTION.getCode())) {
            if (refundInfo.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                refundInfo.setRefundAmount(BigDecimal.ZERO);
            }
        }
        return refundInfo;
    }

    /**
     * 退款订单列表
     *
     * @param orderVideoRefundListDTO 列表条件入参
     * @return 退款订单列表
     */
    @Override
    public List<OrderVideoRefundVO> selectOrderVideoRefundListByCondition(OrderVideoRefundListDTO orderVideoRefundListDTO) {
        if (Boolean.FALSE.equals(assembleCondition(orderVideoRefundListDTO))) {
            return Collections.emptyList();
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("apply_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderVideoRefundVO> orderVideoRefundVOS = baseMapper.selectOrderVideoRefundListByCondition(orderVideoRefundListDTO);

        assembleRefundList(orderVideoRefundVOS);

        return orderVideoRefundVOS;
    }

    /**
     * 计算视频订单选配费用
     *
     * @param picPrice            图片选配费用（单位：$）
     * @param currentExchangeRate 当时的百度汇率
     */
    private BigDecimal getOpentionAmount(BigDecimal picPrice, BigDecimal currentExchangeRate) {
        //  选配金额：展示当前照片选配金额=（&照片费用*1.044*当时的百度汇率)
        return (
                //  照片费用 + 照片手续费 = 照片费用*1.044
                picPrice.add(
                                //  照片手续费
                                picPrice.multiply(
                                        BigDecimal.valueOf(CommonConstant.PAYPAL_EXCHANGE_RATE))
                        )
                        .add(
                                //  佣金代缴服务费
                                picPrice.multiply(
                                        BigDecimal.valueOf(CommonConstant.COMMISSION_PAYS_TAXES_RATE)
                                )
                        )
        )
                .multiply(currentExchangeRate)
                .setScale(2, RoundingMode.DOWN);
    }


    /**
     * 退款状态流转
     */
    @Override
    public List<OrderVideoRefund> refundFlow(RefundFlowDTO refundFlowDTO) {
        List<OrderVideoRefund> refunds = refundFlowDTO.getRefunds();
        //  加锁
        List<String> lockRefundNums = new ArrayList<>();

        try {
            for (OrderVideoRefund refund : refunds) {
                Assert.isTrue(redisService.getLock(CacheConstants.REFUND_FLOW_KEY + refund.getRefundNum(), 60L),
                        "退款正在进行中，请稍后重试！");
                lockRefundNums.add(refund.getRefundNum());
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (String lockRefundNum : lockRefundNums) {
                redisService.releaseLock(CacheConstants.REFUND_FLOW_KEY + lockRefundNum);
            }
            throw new ServiceException("订单正在处理中，请稍后重试！");
        }

        try {
            RefundStatusEnum refundStatus = refundFlowDTO.getRefundStatus();
            for (OrderVideoRefund refund : refunds) {
                refund.setOperateBy(SecurityUtils.getUsername());
                refund.setOperateById(SecurityUtils.getUserId());
                refund.setOperateTime(DateUtil.date());
                switch (refundStatus) {
                    case AFTER_SALE_FINISHED:
                        refund.setRefundStatus(RefundStatusEnum.AFTER_SALE_FINISHED.getCode());
                        break;
                    case REJECT:
                        refund.setRefundStatus(RefundStatusEnum.REJECT.getCode());
                        break;
                    case CANCEL:
                        refund.setRefundStatus(RefundStatusEnum.CANCEL.getCode());
                        break;
                    default:
                        throw new ServiceException("退款状态有误！");
                }
            }
            baseMapper.updateBatchById(refunds);

            return refunds;
        } finally {
            for (OrderVideoRefund refund : refunds) {
                redisService.releaseLock(CacheConstants.REFUND_FLOW_KEY + refund.getRefundNum());
            }
        }
    }

    @Override
    public List<OrderVideoRefund> getOrderVideoRefundListByBusinessId(List<String> numbers) {
        return baseMapper.getOrderVideoRefundList(numbers);
    }

    /**
     * 回显数据
     *
     * @param orderVideoRefundVOS 退款返回对象VO
     */
    private void assembleRefundList(List<OrderVideoRefundVO> orderVideoRefundVOS) {
        if (CollUtil.isEmpty(orderVideoRefundVOS)) {
            return;
        }

        List<Long> shootModelId = orderVideoRefundVOS.stream()
                .map(OrderVideoRefundVO::getShootModelId).filter(Objects::nonNull).collect(Collectors.toList());

        List<Long> videoIds = orderVideoRefundVOS.stream()
                .map(OrderVideoRefundVO::getVideoId).filter(Objects::nonNull).collect(Collectors.toList());
        List<OrderVideo> orderVideoList = orderVideoService.listByIds(videoIds);
        Map<Long, OrderVideo> orderVideoMap = new HashMap<>();
        Set<Long> contactIds = orderVideoRefundVOS.stream().map(OrderVideoRefundVO::getContactId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        Set<Long> issueId = orderVideoRefundVOS.stream().map(OrderVideoRefundVO::getIssueId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());

        Map<Long, ModelOrderSimpleVO> modelMap = remoteService.getModelSimpleMap(shootModelId);

        contactIds.addAll(issueId);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        if (CollUtil.isNotEmpty(orderVideoList)) {
            Map<Long, OrderVideo> collect = orderVideoList.stream().collect(Collectors.toMap(OrderVideo::getId, p -> p));
            orderVideoMap.putAll(collect);
        }

        orderVideoRefundVOS.forEach(vo -> {
            if (UserTypeConstants.USER_TYPE == SecurityUtils.getLoginUserType()) {
                vo.setRefundCause(null);
                vo.setRejectCause(null);
            }
            vo.setShootModel(modelMap.get(vo.getShootModelId()));
            vo.setContactUser(userMap.get(vo.getContactId()));
            vo.setIssueUser(userMap.get(vo.getIssueId()));
            vo.setCreateOrderUserAccount(ObjectUtil.isNull(orderVideoMap.get(vo.getVideoId())) ? "" : orderVideoMap.get(vo.getVideoId()).getCreateOrderUserAccount());
            vo.setVideoStatus(ObjectUtil.isNull(orderVideoMap.get(vo.getVideoId())) ? 0 : orderVideoMap.get(vo.getVideoId()).getStatus());

        });
    }

    /**
     * 生成退款订单号
     *
     * @return 退款订单号
     */
    private String createRefundNum(String videoCode) {
        long uniqueId = IdUtil.getSnowflakeNextId();
        String random3Digits = String.format("%03d", uniqueId % 1000);

        return OrderConstant.REFUND_NUM_PREFIX_WN + videoCode + random3Digits;
    }

    /**
     * 组装列表查询条件
     *
     * @param orderVideoRefundListDTO 列表条件入参
     */
    private Boolean assembleCondition(OrderVideoRefundListDTO orderVideoRefundListDTO) {
        if (StrUtil.isNotBlank(orderVideoRefundListDTO.getShootModelName()) || StrUtil.isNotBlank(orderVideoRefundListDTO.getModelAccount())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setName(orderVideoRefundListDTO.getShootModelName());
            modelListDTO.setAccount(orderVideoRefundListDTO.getModelAccount());
            List<ModelInfoVO> models = remoteService.innerList(modelListDTO);

            if (CollUtil.isNotEmpty(models)) {
                orderVideoRefundListDTO.setModelIds(models.stream().map(ModelInfoVO::getId).collect(Collectors.toSet()));
            } else {
                return false;
            }
        }

        if (UserTypeConstants.USER_TYPE == SecurityUtils.getLoginUserType()) {
            orderVideoRefundListDTO.setLoginBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        }

        if (StrUtil.isNotBlank(orderVideoRefundListDTO.getKeyword())) {
            List<SysUser> userList = remoteService.getUserList(SysUserListDTO.builder().userName(orderVideoRefundListDTO.getKeyword()).build());
            orderVideoRefundListDTO.setBackUserIds(userList.stream().map(SysUser::getUserId).collect(Collectors.toSet()));
        }

        return true;
    }

    /**
     * 导出excel数据处理
     */
    private List<OrderVideoRefundExportDTO> assembleExcelData(List<OrderVideoRefundVO> orderVideoRefundVOS) {
        return orderVideoRefundVOS.stream().map(item -> {
            OrderVideoRefundExportDTO orderVideoRefundExportDTO = new OrderVideoRefundExportDTO();
            BeanUtil.copyProperties(item, orderVideoRefundExportDTO);

            orderVideoRefundExportDTO.setShootModelName(Optional.ofNullable(item.getShootModel()).orElse(new ModelOrderSimpleVO()).getName());
            orderVideoRefundExportDTO.setContactUserName(Optional.ofNullable(item.getContactUser()).orElse(new UserVO()).getName());
            orderVideoRefundExportDTO.setIssueUserName(Optional.ofNullable(item.getIssueUser()).orElse(new UserVO()).getName());
            orderVideoRefundExportDTO.setInitiatorName(item.getInitiatorName());
            return orderVideoRefundExportDTO;
        }).collect(Collectors.toList());
    }
}
