package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.CheckedException;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.bo.biz.datastatistics.ModelOrderCommissionAnalysisBO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.ModelOrderDTO;
import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.*;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelOrderCommissionAnalysisVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO;
import com.wnkx.order.mapper.OrderVideoMatchMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/15 15:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderVideoMatchServiceImpl extends ServiceImpl<OrderVideoMatchMapper, OrderVideoMatch> implements OrderVideoMatchService {

    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;
    private final RedisService redisService;
    private final RemoteService remoteService;
    private final IOrderVideoModelService orderVideoModelService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoModelChangeService orderVideoModelChangeService;
    private final OrderResourceService orderResourceService;
    private final IOrderVideoContentService videoContentService;
    private final IOrderVideoCarryService orderVideoCarryService;
    private final OrderVideoCommentService orderVideoCommentService;
    private final OrderVideoModelShippingAddressService orderVideoModelShippingAddressService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final IOrderVideoTagService orderVideoTagService;

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-添加凑单推荐
     */
    @Override
    public void addMatchRecommend(List<AddMatchRecommendDTO> dtoList) {
        Set<Long> addRecommendVideoIds = new HashSet<>();
        for (AddMatchRecommendDTO dto : dtoList) {
            List<MyPreselectFailListRecommendListVO> list = getMyPreselectFailListRecommendListVOS(null, dto.getModelId(), false, dto.getAddRecommendVideoIds());
            Assert.isFalse(CollUtil.isEmpty(list) || list.size() != dto.getAddRecommendVideoIds().size(), "订单信息有变化，请刷新列表后重试~");
            addRecommendVideoIds.addAll(dto.getAddRecommendVideoIds());
        }

        List<OrderVideoMatch> orderVideoMatches = baseMapper.selectLastOrderVideoMatchByVideoIds(new ArrayList<>(addRecommendVideoIds));
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoMatches) && orderVideoMatches.size() == addRecommendVideoIds.size(), "数据有误，请联系管理员");

        for (AddMatchRecommendDTO dto : dtoList) {
            dto.setOrderVideoMatches(orderVideoMatches.stream().filter(item -> dto.getAddRecommendVideoIds().contains(item.getVideoId())).collect(Collectors.toList()));
        }

        List<Long> lockMatchIds = new ArrayList<>();
        try {
            for (OrderVideoMatch match : orderVideoMatches) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + match.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
                lockMatchIds.add(match.getId());
            }

            orderVideoMatchPreselectModelService.addMatchRecommend(dtoList);
        } finally {
            for (Long matchId : lockMatchIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchId);
            }
        }
    }

    /**
     * 获取可凑单的模特
     */
    @Override
    public List<ModelInfoVO> getCanMatchModel(List<Long> modelIds) {
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelIds);
        Assert.isTrue(CollUtil.isNotEmpty(modelMap) && modelMap.size() == modelIds.size(), "模特不存在");

        List<Long> resultModelIds = new ArrayList<>();

        List<ModelOrderVO> modelOrderCount = SpringUtils.getBean(IOrderVideoService.class).getModelOrderCountCopy(modelIds);
        Map<Long, ModelOrderVO> modelOrderCountMap = modelOrderCount.stream().collect(Collectors.toMap(ModelOrderVO::getModelId, Function.identity()));

        List<Long> doesNotMeetTheMinimumOrderRequirementModelIds = baseMapper.getDoesNotMeetTheMinimumOrderRequirementModelIds(modelIds);

        modelMap.forEach((key, value) -> {
            if (!doesNotMeetTheMinimumOrderRequirementModelIds.contains(key)) {
                Long waits = modelOrderCountMap.getOrDefault(key, new ModelOrderVO()).getWaits();
                if ((ModelCooperationEnum.ORDINARY.getCode().equals(value.getCooperation()) && waits < 3) || (ModelCooperationEnum.QUALITY.getCode().equals(value.getCooperation()) && waits < 5)) {
                    resultModelIds.add(key);
                }
            }
        });
        return modelMap.values().stream().filter(model -> resultModelIds.contains(model.getId())).collect(Collectors.toList());
    }

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-同链接订单
     */
    @Override
    public List<OrderTheSameProductListVO> selectOrderTheSameProductList(String productLink) {
        return baseMapper.selectOrderTheSameProductList(productLink);
    }

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-添加排单推荐
     */
    @Override
    public void addRecommend(AddRecommendDTO dto) {
        List<MyPreselectFailListRecommendListVO> list = getMyPreselectFailListRecommendListVOS(dto.getVideoId(), dto.getModelId(), true, dto.getAddRecommendVideoIds());
        Assert.isFalse(CollUtil.isEmpty(list) || list.size() != dto.getAddRecommendVideoIds().size(), "订单信息有变化，请刷新列表后重试~");

        List<OrderVideoMatch> orderVideoMatches = baseMapper.selectLastOrderVideoMatchByVideoIds(dto.getAddRecommendVideoIds());
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoMatches) && orderVideoMatches.size() == dto.getAddRecommendVideoIds().size(), "数据有误，请联系管理员");

        List<Long> lockMatchIds = new ArrayList<>();
        try {
            for (OrderVideoMatch match : orderVideoMatches) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + match.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
                lockMatchIds.add(match.getId());
            }

            orderVideoMatchPreselectModelService.addRecommend(dto.getModelId(), orderVideoMatches.stream().map(OrderVideoMatch::getId).collect(Collectors.toList()));
        } finally {
            for (Long matchId : lockMatchIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchId);
            }
        }
    }

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-排单推荐列表
     */
    @Override
    public List<MyPreselectFailListRecommendListVO> selectMyPreselectFailListRecommendList(Long videoId, Long modelId, Boolean isOrderPlacementRecommendation) {
        List<MyPreselectFailListRecommendListVO> list = getMyPreselectFailListRecommendListVOS(videoId, modelId, isOrderPlacementRecommendation, null);

        if (CollUtil.isEmpty(list)) {
            return list;
        }

        Set<Long> intentionModelIds = list.stream().map(MyPreselectFailListRecommendListVO::getIntentionModelId).collect(Collectors.toSet());
        Map<Long, ModelInfoVO> intentionModelMap = remoteService.getModelMap(intentionModelIds);

        List<Long> videoIds = list.stream().map(MyPreselectFailListRecommendListVO::getVideoId).collect(Collectors.toList());
        //  查询拍摄要求、注意事项
        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIds(videoIds);
        Map<Long, List<OrderVideoContent>> orderVideoContentMap = orderVideoContents.stream().collect(Collectors.groupingBy(OrderVideoContent::getVideoId));

        List<String> referencePicIds = list.stream().map(MyPreselectFailListRecommendListVO::getReferencePicId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
        List<String> cautionsPicIds = list.stream().map(MyPreselectFailListRecommendListVO::getCautionsPicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cautionsPicIds)) {
            resourceIds.addAll(StringUtils.splitToLong(cautionsPicIds, StrUtil.COMMA));
        }
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        for (MyPreselectFailListRecommendListVO myPreselectFailListRecommendListVO : list) {
            myPreselectFailListRecommendListVO.setIntentionModelName(intentionModelMap.getOrDefault(myPreselectFailListRecommendListVO.getIntentionModelId(), new ModelInfoVO()).getName());

            if (ObjectUtil.isNotNull(myPreselectFailListRecommendListVO.getPicCount())) {
                myPreselectFailListRecommendListVO.setSurplusPicCount(PicCountEnum.getValue(myPreselectFailListRecommendListVO.getPicCount()) - Optional.ofNullable(myPreselectFailListRecommendListVO.getRefundPicCount()).orElse(0));
            } else {
                myPreselectFailListRecommendListVO.setSurplusPicCount(0);
            }

            List<OrderVideoContent> videoContents = orderVideoContentMap.get(myPreselectFailListRecommendListVO.getVideoId());
            if (CollUtil.isNotEmpty(videoContents)) {
                List<OrderVideoContent> shootRequired = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> cautions = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> sellingPointProducts = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                myPreselectFailListRecommendListVO.setShootRequired(shootRequired);
                myPreselectFailListRecommendListVO.setSellingPointProduct(CollUtil.isNotEmpty(sellingPointProducts) ? sellingPointProducts.get(0).getContent() : null);

                OrderVideoCautionsVO orderVideoCautionsVO = new OrderVideoCautionsVO();
                if (CharSequenceUtil.isNotBlank(myPreselectFailListRecommendListVO.getCautionsPicId())) {
                    for (Long cautionsPicId : StringUtils.splitToLong(myPreselectFailListRecommendListVO.getCautionsPicId(), StrUtil.COMMA)) {
                        orderVideoCautionsVO.getCautionsPics().add(resourceMap.getOrDefault(cautionsPicId, new OrderResource()).getObjectKey());
                    }
                }
                orderVideoCautionsVO.setCautions(cautions);
                myPreselectFailListRecommendListVO.setOrderVideoCautionsVO(orderVideoCautionsVO);
            }

            List<Long> referencePicLongIds = StringUtils.splitToLong(myPreselectFailListRecommendListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicLongIds) {
                myPreselectFailListRecommendListVO.getReferencePic().add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
            }
        }
        return list;
    }

    /**
     * @param isOrderPlacementRecommendation 是否是排单推荐
     */
    private List<MyPreselectFailListRecommendListVO> getMyPreselectFailListRecommendListVOS(Long videoId, Long modelId, Boolean isOrderPlacementRecommendation, List<Long> videoIds) {
        String productLink = null;
        Long createOrderBusinessId = null;
        if (Boolean.TRUE.equals(isOrderPlacementRecommendation)) {
            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(videoId);
            Assert.notNull(orderVideo, "视频订单不存在");

            productLink = orderVideo.getProductLink();
            createOrderBusinessId = orderVideo.getCreateOrderBusinessId();
        }
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(List.of(modelId));
        Assert.notEmpty(modelMap, "模特不存在");
        ModelInfoVO modelInfoVO = modelMap.get(modelId);
        List<UserModelBlacklist> userModelBlacklists = remoteService.userBlackModelListByModelId(modelId);

        //  获取模特排的订单的产品品类
        List<Long> orderTagIds = orderVideoTagService.selectModelTagIdsByModelId(modelId);
        List<Long> modelTagIds = modelInfoVO.getSpecialtyCategory().stream().map(ModelTagVO::getId).collect(Collectors.toList());

        return baseMapper.selectMyPreselectFailListRecommendList(
                modelId,
                modelInfoVO.getPlatform(),
                modelInfoVO.getNation(),
                modelInfoVO.getType(),
                userModelBlacklists.stream().map(UserModelBlacklist::getBizUserId).collect(Collectors.toList()),
                productLink,
                createOrderBusinessId,
                orderTagIds,
                modelTagIds,
                videoIds
        );
    }

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表
     */
    @Override
    public List<MyPreselectDockingListVO> selectMyPreselectFailListByCondition(MyPreselectFailListDTO dto) {
        List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
        if (CollUtil.isEmpty(modelPeople) && !SecurityUtils.currentUserIsAdmin()) {
            return Collections.emptyList();
        }

        dto.setBackUserRelevanceModelIds(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList()));
        dto.setCurrentUserIsAdmin(SecurityUtils.currentUserIsAdmin());

        Map<String, String> oustTimeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(dto.getOustTimes())) {
            dto.getOustTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    oustTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    oustTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    oustTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    oustTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
        }
        dto.setOustTimeMap(oustTimeMap);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovm.start_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<MyPreselectDockingListVO> list = baseMapper.selectMyPreselectFailListByCondition(dto);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        assembleOrderPoolList(list);

        //  获取 相同产品链接未匹配订单数量 以及 相同产品链接订单数量
        List<String> productLinks = list.stream().map(MyPreselectDockingListVO::getProductLink).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(productLinks)) {
            List<MyPreselectDockingListVO> productLinkOrderList = baseMapper.getOrderTheSameProducts(productLinks);
            Map<String, List<MyPreselectDockingListVO>> productLinkOrderMap = productLinkOrderList.stream().collect(Collectors.groupingBy(MyPreselectDockingListVO::getProductLink));
            for (MyPreselectDockingListVO myPreselectDockingListVO : list) {
                List<MyPreselectDockingListVO> productLinkOrders = productLinkOrderMap.get(myPreselectDockingListVO.getProductLink());
                if (CollUtil.isNotEmpty(productLinkOrders)) {
                    myPreselectDockingListVO.setUnMatchSameProductLinkOrderCount(productLinkOrders.stream().mapToInt(MyPreselectDockingListVO::getUnMatchSameProductLinkOrderCount).sum());
                    myPreselectDockingListVO.setSameProductLinkOrderCount(productLinkOrders.stream().mapToInt(MyPreselectDockingListVO::getSameProductLinkOrderCount).sum());
                }
            }
        }


        Set<Long> matchIds = list.stream().map(MyPreselectDockingListVO::getId).collect(Collectors.toSet());
        dto.setMatchIds(matchIds);
        List<OrderVideoMatchPreselectModelVO> preselectModelList = orderVideoMatchPreselectModelService.selectMyPreselectFailListByCondition(dto);
        if (CollUtil.isEmpty(preselectModelList)) {
            return Collections.emptyList();
        }

        Map<Long, List<OrderVideoMatchPreselectModelVO>> preselectModelMap = preselectModelList.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModelVO::getMatchId));

        Set<Long> videoIds = list.stream().map(MyPreselectDockingListVO::getVideoId).collect(Collectors.toSet());
        List<OrderVideoMatch> lastOrderVideoMatches = baseMapper.selectLastOrderVideoMatchByVideoIds(videoIds);
        Map<Long, OrderVideoMatch> lastOrderVideoMatcheMap = lastOrderVideoMatches.stream().collect(Collectors.toMap(OrderVideoMatch::getVideoId, Function.identity()));

        for (MyPreselectDockingListVO listVO : list) {
            if (ObjectUtil.isNotNull(listVO.getPicCount())) {
                listVO.setSurplusPicCount(PicCountEnum.getValue(listVO.getPicCount()) - Optional.ofNullable(listVO.getRefundPicCount()).orElse(0));
            } else {
                listVO.setSurplusPicCount(0);
            }
            List<OrderVideoMatchPreselectModelVO> matchPreselectModelVOS = preselectModelMap.get(listVO.getId());
            if (CollUtil.isEmpty(matchPreselectModelVOS)) {
                continue;
            }
            // 通过Comparator进行排序
            matchPreselectModelVOS.sort(Comparator
                    .comparing((OrderVideoMatchPreselectModelVO vo) -> !dto.getBackUserRelevanceModelIds().contains(vo.getModelId())) // 按是否包含在backUserRelevanceModelIds中排序
                    .thenComparing(OrderVideoMatchPreselectModelVO::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())) // 按addTime降序，null值排在最后
            );
            listVO.setPreselectModelList(matchPreselectModelVOS);

            OrderVideoMatch orDefault = lastOrderVideoMatcheMap.getOrDefault(listVO.getVideoId(), new OrderVideoMatch());
            listVO.setLatestMatchId(orDefault.getId());
            listVO.setLatestMatchStatus(orDefault.getStatus());
        }

        return list;
    }

    /**
     * 查询模特数据表列表
     */
    @Override
    public List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO modelDataTableListDTO) {
        List<ModelDataTableListVO> modelDataTableListVOList = baseMapper.selectModelDataTableListByCondition(modelDataTableListDTO);
        List<ModelDataTableListVO> modelAfterSaleRates = baseMapper.selectModelDataTableAfterSaleRate(modelDataTableListDTO);

        Set<Long> collect1 = modelDataTableListVOList.stream().map(ModelDataTableListVO::getId).collect(Collectors.toSet());
        Set<Long> collect2 = modelAfterSaleRates.stream().map(ModelDataTableListVO::getId).collect(Collectors.toSet());
        collect1.addAll(collect2);

        Map<Long, ModelDataTableListVO> map1 = modelDataTableListVOList.stream().collect(Collectors.toMap(ModelDataTableListVO::getId, Function.identity()));
        Map<Long, ModelDataTableListVO> map2 = modelAfterSaleRates.stream().collect(Collectors.toMap(ModelDataTableListVO::getId, Function.identity()));

        List<ModelDataTableListVO> modelDataTableListVOS = new ArrayList<>();
        for (Long modelId : collect1) {
            ModelDataTableListVO modelDataTableListVO = new ModelDataTableListVO();
            ModelDataTableListVO orDefault = map1.getOrDefault(modelId, new ModelDataTableListVO());
            BeanUtil.copyProperties(orDefault, modelDataTableListVO);

            ModelDataTableListVO orDefault1 = map2.getOrDefault(modelId, modelDataTableListVO);
            modelDataTableListVO.setAfterSaleCount(orDefault1.getAfterSaleCount());
            modelDataTableListVO.setFeedbackMaterialsCount(orDefault1.getFeedbackMaterialsCount());
            modelDataTableListVO.setAfterSaleRate(orDefault1.getAfterSaleRate());
            modelDataTableListVOS.add(modelDataTableListVO);
        }
        return modelDataTableListVOS;
    }

    /**
     * 查询视频订单历史选择数据
     */
    @Override
    public List<HistoryPreselectModelCountVO> getHistoryPreselectModelCountList(List<Long> videoIds) {
        return baseMapper.getHistoryPreselectModelCountList(videoIds);
    }

    @Override
    public void refreshModelAllSort() {
        baseMapper.refreshModelAllSort();
    }

    /**
     * 预选管理-添加分发
     */
    @Override
    public AddDistributionErrorVO addDistribution(AddDistributionDTO dto) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(dto.getMatchId());
        Assert.notNull(orderVideoMatch, "未找到匹配单");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后再试~");
            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getVideoId());
            Assert.notNull(orderVideo, "未找到视频订单");
            Assert.isTrue(OrderStatusEnum.UN_MATCH.getCode().equals(orderVideo.getStatus()), "订单匹配已结束，请刷新页面");
            Assert.isTrue(OrderVideoMatchStatusEnum.NORMAL.getCode().equals(orderVideoMatch.getStatus()), "订单已暂停匹配，无法添加，请刷新页面重试。");
            Assert.isNull(orderVideoMatch.getEndTime(), "订单匹配已结束，请刷新页面");

            return orderVideoMatchPreselectModelService.addDistribution(dto, SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getVideoId()));
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 获取平均匹配时长（天）
     *
     * @return 平均匹配时长（天）
     */
    @Override
    public BigDecimal getAverageMatchingDuration(String date) {
        return baseMapper.getAverageMatchingDuration(date);
    }

    /**
     * 通过视频订单ID获取预选模特拍摄注意事项
     */
    @Override
    public String getPreselectModelShootAttentionByVideoId(Long videoId) {
        OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(videoId);
        if (ObjectUtil.isNull(orderVideoMatch) || ObjectUtil.isNull(orderVideoMatch.getShootModelId())) {
            return CharSequenceUtil.EMPTY;
        }
        OrderVideoMatchPreselectModel selectedModelByMatchId = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(orderVideoMatch.getId());
        return selectedModelByMatchId.getShootAttention();
    }

    /**
     * 通过提交日期查询提交过的匹配单
     */
    @Override
    public List<OrderVideoMatch> selectListBySubmitTime(String beginDate, String endDate) {
        return baseMapper.selectListBySubmitTime(beginDate, endDate);
    }

    /**
     * 通过提交日期查询最新的匹配单
     */
    @Override
    public List<OrderVideoMatch> selectLastListBySubmitTime(String beginDate, String endDate) {
        return baseMapper.selectLastListBySubmitTime(beginDate, endDate);
    }

    /**
     * 模特数据-成功匹配次数
     */
    @Override
    public ModelSuccessMatchCountVO getModelSuccessMatchCount() {
        ModelSuccessMatchCountVO modelSuccessMatchCountVO = baseMapper.getModelSuccessMatchCount();
        modelSuccessMatchCountVO.setPieChartVOS(List.of(
                PieChartVO.builder()
                        .label(PreselectModelAddTypeEnum.INTENTION_MODEL.getLabel())
                        .count(modelSuccessMatchCountVO.getMerchantIntentionCount())
                        .ratio(modelSuccessMatchCountVO.getSuccessMatchCount() > 0 ? BigDecimal.valueOf(modelSuccessMatchCountVO.getMerchantIntentionCount()).divide(BigDecimal.valueOf(modelSuccessMatchCountVO.getSuccessMatchCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                        .build(),
                PieChartVO.builder()
                        .label(PreselectModelAddTypeEnum.OPERATION.getLabel())
                        .count(modelSuccessMatchCountVO.getServiceCustomerCount())
                        .ratio(modelSuccessMatchCountVO.getSuccessMatchCount() > 0 ? BigDecimal.valueOf(modelSuccessMatchCountVO.getServiceCustomerCount()).divide(BigDecimal.valueOf(modelSuccessMatchCountVO.getSuccessMatchCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                        .build(),
                PieChartVO.builder()
                        .label(PreselectModelAddTypeEnum.MODEL_OPTIONAL.getLabel())
                        .count(modelSuccessMatchCountVO.getModelCustomerCount())
                        .ratio(modelSuccessMatchCountVO.getSuccessMatchCount() > 0 ? BigDecimal.valueOf(modelSuccessMatchCountVO.getModelCustomerCount()).divide(BigDecimal.valueOf(modelSuccessMatchCountVO.getSuccessMatchCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                        .build(),
                PieChartVO.builder()
                        .label(PreselectModelAddTypeEnum.DISTRIBUTION.getLabel())
                        .count(modelSuccessMatchCountVO.getServiceDistributionCount())
                        .ratio(modelSuccessMatchCountVO.getSuccessMatchCount() > 0 ? BigDecimal.valueOf(modelSuccessMatchCountVO.getServiceDistributionCount()).divide(BigDecimal.valueOf(modelSuccessMatchCountVO.getSuccessMatchCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                        .build()
        ));
        modelSuccessMatchCountVO.setDateTime(DateUtil.date());
        return modelSuccessMatchCountVO.echo();
    }

    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    @Override
    public ModelOrderCommissionAnalysisVO getModelOrderCommissionAnalysis(ModelOrderCommissionAnalysisDTO dto) {
        List<ModelOrderCommissionAnalysisBO> modelOrderCommissionAnalysisBOS = baseMapper.getModelOrderCommissionAnalysis(dto);

        List<Long> amazonInfluencerOrderCountArray = new ArrayList<>(Collections.nCopies(OrderCommissionSectionEnum.getLabels().size(), 0L));
        List<Long> averagePeopleOrderCountArray = new ArrayList<>(Collections.nCopies(OrderCommissionSectionEnum.getLabels().size(), 0L));

        for (ModelOrderCommissionAnalysisBO modelOrderCommissionAnalysisBO : modelOrderCommissionAnalysisBOS) {
            //  佣金单位转换
            //  加币*0.73
            //  英镑*1.35
            //  欧元*1.15
            modelOrderCommissionAnalysisBO.setCommission(modelOrderCommissionAnalysisBO.getCommission().multiply(CommissionUnitEnum.getByUnit(modelOrderCommissionAnalysisBO.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));

            OrderCommissionSectionEnum bySection = OrderCommissionSectionEnum.getBySection(modelOrderCommissionAnalysisBO.getCommission());
            if (ModelTypeEnum.INFLUENT.getCode().equals(modelOrderCommissionAnalysisBO.getShootModelType())) {
                amazonInfluencerOrderCountArray.set(bySection.getCode() - 1, amazonInfluencerOrderCountArray.get(bySection.getCode() - 1) + 1);
            } else if (ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(modelOrderCommissionAnalysisBO.getShootModelType())) {
                averagePeopleOrderCountArray.set(bySection.getCode() - 1, averagePeopleOrderCountArray.get(bySection.getCode() - 1) + 1);
            }
        }

        ModelOrderCommissionAnalysisVO modelOrderCommissionAnalysisVO = new ModelOrderCommissionAnalysisVO();
        if (CollUtil.isNotEmpty(modelOrderCommissionAnalysisBOS)) {
            BigDecimal commissionTotal = modelOrderCommissionAnalysisBOS.stream().map(ModelOrderCommissionAnalysisBO::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add);
            modelOrderCommissionAnalysisVO.setAverageOrderCommission(commissionTotal.divide(BigDecimal.valueOf(modelOrderCommissionAnalysisBOS.size()), 2, RoundingMode.DOWN));
        }

        modelOrderCommissionAnalysisVO.setDateTime(DateUtil.date());
        modelOrderCommissionAnalysisVO.setOrderCount(Convert.toLong(modelOrderCommissionAnalysisBOS.size()));
        modelOrderCommissionAnalysisVO.setAmazonInfluencerOrderCountArray(amazonInfluencerOrderCountArray);
        modelOrderCommissionAnalysisVO.setAveragePeopleOrderCountArray(averagePeopleOrderCountArray);
        modelOrderCommissionAnalysisVO.setOrderCommissionSectionArray(OrderCommissionSectionEnum.getLabels());

        return modelOrderCommissionAnalysisVO.echo();
    }

    /**
     * 模特数据统计-获取模特基础数据
     */
    @Override
    public ModelBasicDataVO getModelBasicData() {
        ModelBasicDataVO modelBasicDataVO = new ModelBasicDataVO();
        //  获取首次匹配成功率
        BigDecimal successRateOfTheFirstMatch = baseMapper.getSuccessRateOfTheFirstMatch();
        //  获取模特售后率
        BigDecimal modelAfterSalesRate = baseMapper.getModelAfterSalesRate();
        //  获取模特超时率
        BigDecimal modelOvertimeRate = baseMapper.getModelOvertimeRate();
        //  获取意向匹配成功率
        BigDecimal successRateOfIntentionMatching = baseMapper.getSuccessRateOfIntentionMatching();
        //  获取平均匹配时长（天）
        BigDecimal averageMatchingDuration = baseMapper.getAverageMatchingDuration(null);
        //  获取平均反馈时长（天）
        BigDecimal averageFeedbackDuration = baseMapper.getAverageFeedbackDuration();

        modelBasicDataVO.setSuccessRateOfTheFirstMatch(successRateOfTheFirstMatch);
        modelBasicDataVO.setModelAfterSalesRate(modelAfterSalesRate);
        modelBasicDataVO.setModelOvertimeRate(modelOvertimeRate);
        modelBasicDataVO.setSuccessRateOfIntentionMatching(successRateOfIntentionMatching);
        modelBasicDataVO.setAverageMatchingDuration(averageMatchingDuration);
        modelBasicDataVO.setAverageFeedbackDuration(averageFeedbackDuration);
        return modelBasicDataVO;
    }

    /**
     * 我的预选-完成匹配-拍摄模特下拉框
     */
    @Override
    public List<ModelSelectVO> myPreselectShootModelSelect(String keyword) {
        Set<Long> modelIds = baseMapper.myPreselectShootModelSelect();
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelIds);
        modelListDTO.setName(keyword);

        return remoteService.modelSelect(modelListDTO);
    }

    /**
     * 校验当前模特是否有其他的相同产品链接的订单
     */
    @Override
    public boolean checkModelAnOrderWithTheSameProductLinkExists(Long modelId, String productLink) {
        return baseMapper.checkModelAnOrderWithTheSameProductLinkExists(modelId, productLink);
    }

    /**
     * 通过视频订单ID获取被商家驳回的模特ID
     */
    @Override
    public AddPreselectRemoteVO selectRejectModelIdByVideoId(Long videoId) {
        Set<Long> rejectModelIds = baseMapper.selectRejectModelIdByVideoId(videoId);
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(videoId);
        Assert.notNull(orderVideo, "订单不存在");

        AddPreselectRemoteVO addPreselectRemoteVO = baseMapper.selectSameProductModelIdsByProductLink(orderVideo.getProductLink());
        addPreselectRemoteVO.setRejectModelIds(rejectModelIds);
        if (CharSequenceUtil.isNotBlank(addPreselectRemoteVO.getSameProductModelIdStr())) {
            addPreselectRemoteVO.setSameProductModelIds(CharSequenceUtil.split(addPreselectRemoteVO.getSameProductModelIdStr(), ',', -1, true, Long::valueOf));
        }
        if (CharSequenceUtil.isNotBlank(addPreselectRemoteVO.getSameProductOutModelIdStr())) {
            addPreselectRemoteVO.setSameProductOutModelIds(CharSequenceUtil.split(addPreselectRemoteVO.getSameProductOutModelIdStr(), ',', -1, true, Long::valueOf));
        }
        if (CharSequenceUtil.isNotBlank(addPreselectRemoteVO.getSameProductPreselectModelIdStr())) {
            addPreselectRemoteVO.setSameProductPreselectModelIds(CharSequenceUtil.split(addPreselectRemoteVO.getSameProductPreselectModelIdStr(), ',', -1, true, Long::valueOf));
        }
        return addPreselectRemoteVO;
    }

    /**
     * 获取不同于视频订单carry_ignore的匹配单
     */
    @Override
    public List<OrderVideoMatch> getNotCarryIgnoreMatch() {
        return baseMapper.getNotCarryIgnoreMatch();
    }

    /**
     * 获取模特剩余需携带订单数
     */
    @Override
    public List<ModelCarryVO> getModelLeftCarryCountByModelIds(Collection<Long> modelIds) {
        return baseMapper.getModelLeftCarryCountByModelIds(modelIds);
    }

    /**
     * 主携带订单 若底下有携带的订单 这些携带的订单调整为排单类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackOrderUpdateCarryOrder(Long videoId) {
        List<OrderVideoMatch> carryMatches = baseMapper.selectCarryMatchListByMainCarryVideoId(videoId);
        if (CollUtil.isEmpty(carryMatches)) {
            return;
        }
        Set<Long> videoIds = carryMatches.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toSet());
        List<OrderVideo> orderVideos = SpringUtils.getBean(IOrderVideoService.class).listByIds(videoIds);
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));

        List<Long> matchIds = carryMatches.stream().map(OrderVideoMatch::getId).collect(Collectors.toList());
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = orderVideoMatchPreselectModelService.selectedListByMatchIds(matchIds);
        Assert.isTrue(carryMatches.size() == orderVideoMatchPreselectModels.size(), "匹配单没有选定模特 无法操作");
        Map<Long, OrderVideoMatchPreselectModel> orderVideoMatchPreselectModelMap = orderVideoMatchPreselectModels.stream().collect(Collectors.toMap(OrderVideoMatchPreselectModel::getMatchId, Function.identity()));

        Set<Long> shootModelIds = orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelIds);

        List<OrderVideoOperateDTO> orderVideoFlowDTOS = new ArrayList<>();

        List<Integer> orderStatusCodes = List.of(OrderStatusEnum.UN_MATCH.getCode(), OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode(), OrderStatusEnum.NEED_CONFIRM.getCode());
        for (OrderVideoMatch item : carryMatches) {
            OrderVideo orderVideo = orderVideoMap.get(item.getVideoId());
            if (ObjectUtil.isNull(orderVideo)) {
                continue;
            }

            if (OrderStatusEnum.FINISHED.getCode().equals(orderVideo.getStatus())) {
                item.setCarryIgnore(FlagEnum.FLAG.getCode());
            } else if (orderStatusCodes.contains(orderVideo.getStatus())) {
                OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = orderVideoMatchPreselectModelMap.get(item.getId());
                ModelInfoVO modelInfoVO = modelMap.get(orderVideoMatchPreselectModel.getModelId());
                item.setScheduleType(ScheduleTypeEnum.ORDER.getCode());
                item.setCommissionUnit(modelInfoVO.getCommissionUnit());
                item.setCommission(BigDecimal.ZERO);
                item.setOverstatement(null);
                item.setCarryType(null);
                item.setMainCarryCount(null);
                item.setMainCarryVideoId(null);

                orderVideoFlowDTOS.add(OrderVideoOperateDTO.builder()
                        .videoId(orderVideo.getId())
                        .eventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.UNCARRY.getEventContent(), CommissionUnitEnum.getLabelByUnit(item.getCommissionUnit())))
                        .build());
            }
        }
        updateOrderVideoMatchBatchFieldNullToNull(carryMatches);
        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.UNCARRY.getEventName(), OrderVideoOperateTypeEnum.UNCARRY.getIsPublic(), orderVideoFlowDTOS);
    }

    /**
     * 视频订单回退淘汰预选模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackOrderOustPreselectModel(Long videoId, String cause) {
        OrderVideoMatch videoMatch = baseMapper.getActiveByVideoId(videoId);
        Assert.notNull(videoMatch, "匹配单不存在");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + videoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后再试~");

            orderVideoMatchPreselectModelService.rollbackOrderOustPreselectModel(videoMatch.getId(), cause);
            // 订单回退后，调字以当前为准（调标不显示）/同时更新日志表
            videoMatch.setCautionsChange(Boolean.FALSE);
            videoMatch.setShootRequiredChange(Boolean.FALSE);
            videoMatch.setParticularEmphasisChange(Boolean.FALSE);
            videoMatch.setOrderSpecificationRequireChange(Boolean.FALSE);
            videoMatch.setSellingPointProductChange(Boolean.FALSE);
            baseMapper.updateById(videoMatch);
            videoContentService.rollbackOrderVideoContent(videoId);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + videoMatch.getId());
        }
    }

    /**
     * 翻译匹配单拍摄要求并存储翻译结果
     */
    @Override
    public List<String> translateShootRequired(Long matchId, TranslateBatchDTO translateBatchDTO) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(matchId);
        Assert.notNull(orderVideoMatch, "匹配单不存在");

        // List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIds(List.of(orderVideoMatch.getVideoId()));
        // List<OrderVideoContent> shootRequired = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        if (CompareUtil.compare(translateBatchDTO.getWordList(), JSON.parseArray(orderVideoMatch.getShootRequiredOriginal(), String.class), false) != 0) {
            List<String> shootRequiredTranslation = remoteService.translateBatch(translateBatchDTO);
            orderVideoMatch.setShootRequiredOriginal(JSON.toJSONString(translateBatchDTO.getWordList()));
            orderVideoMatch.setShootRequiredTranslation(JSON.toJSONString(shootRequiredTranslation));
            baseMapper.updateById(orderVideoMatch);
        }
        return JSON.parseArray(orderVideoMatch.getShootRequiredTranslation(), String.class);
    }

    /**
     * 通过视频订单ID获取匹配单
     */
    @Override
    public OrderVideoMatch getActiveByVideoId(Long videoId) {
        return baseMapper.getActiveByVideoId(videoId);
    }

    /**
     * 设置模特选择记录的状态为卖方取消
     */
    @Override
    public void updateModelSelectStatusToCancelByVideoId(List<Long> videoIds) {
        List<VideoMatchOrderVO> videoMatchOrderVOS = baseMapper.selectActiveListByVideoIds(videoIds);
        if (CollUtil.isEmpty(videoMatchOrderVOS)) {
            return;
        }

        orderVideoMatchPreselectModelService.updateModelSelectStatusToCancelByVideoId(videoMatchOrderVOS.stream().map(VideoMatchOrderVO::getId).collect(Collectors.toList()));

    }

    /**
     * 修改订单信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editOrderVideo(OrderOperationVideoDTO orderOperationVideoDTO) {
        IOrderVideoService orderVideoService = SpringUtils.getBean(IOrderVideoService.class);
        OrderVideo orderVideo = orderVideoService.getById(orderOperationVideoDTO.getId());
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.UN_MATCH, OrderStatusEnum.NEED_FILLED, OrderStatusEnum.UN_FINISHED);
        Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus())
                        && ObjectUtil.notEqual(orderOperationVideoDTO.getVideoStyle(), orderVideo.getVideoStyle()),
                OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更视频风格");
        Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus())
                        && ObjectUtil.notEqual(orderOperationVideoDTO.getVideoDuration(), orderVideo.getVideoDuration()),
                OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更视频时长");
        Long oldIntentionModelId = orderVideo.getIntentionModelId();
        OrderVideoCurrentInfoVO orderVideoCurrentInfo = orderVideoService.getOrderVideoCurrentInfo(orderOperationVideoDTO.getId());
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_FLOW_KEY + orderOperationVideoDTO.getId(), 60L), "订单流转中，请稍后重试！");
            if (OrderStatusEnum.UN_MATCH.getCode().equals(orderVideo.getStatus())) {
                orderVideoService.backUserEditOrderVideo(orderOperationVideoDTO, orderVideo, ChangeLogTypeEnum.EDIT_VIDEO_LOG);
            } else if (OrderStatusEnum.NEED_FILLED.getCode().equals(orderVideo.getStatus())) {
                orderVideoService.backUserEditOrderVideo(orderOperationVideoDTO, orderVideo, ChangeLogTypeEnum.NEED_FILLED_EDIT_VIDEO_LOG);
            } else if (OrderStatusEnum.UN_FINISHED.getCode().equals(orderVideo.getStatus())) {
                orderVideoService.backUserEditOrderVideo(orderOperationVideoDTO, orderVideo, ChangeLogTypeEnum.UN_FINISHED_EDIT_VIDEO_LOG);
            }


            OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(orderOperationVideoDTO.getId());
            //  匹配单不存在或暂停匹配 不做操作

            try {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

                if (ObjectUtil.notEqual(orderOperationVideoDTO.getIntentionModelId(), oldIntentionModelId)) {
                    orderVideoMatch.setIntentionModelChange(true);
                    Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus()),
                            OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更意向模特");
                } else {
                    orderVideoMatch.setIntentionModelChange(false);
                }

                if (ObjectUtil.notEqual(orderOperationVideoDTO.getPicCount(), orderVideo.getPicCount())) {
                    orderVideoMatch.setPicCountChange(true);
                    Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus()),
                            OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更照片数量");
                } else {
                    orderVideoMatch.setPicCountChange(false);
                }
                List<String> referencePic = orderVideoCurrentInfo.getReferencePic();
                if (!orderVideoMatch.getPicCountChange() &&
                        (referencePic != null || orderOperationVideoDTO.getReferencePic() != null) &&
                        !CollUtil.disjunction(referencePic, orderOperationVideoDTO.getReferencePic()).isEmpty()) {
                    orderVideoMatch.setPicCountChange(true);

                    Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus()),
                            OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更参考图片");
                }

                if (!CharSequenceUtil.equals(orderVideoCurrentInfo.getProductPic(), orderOperationVideoDTO.getProductPic())) {
                    Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus()),
                            OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更产品图");
                    orderVideoMatch.setProductPicChange(true);
                } else {
                    orderVideoMatch.setProductPicChange(false);
                }
                if (ObjectUtil.notEqual(orderVideoCurrentInfo.getProductChinese(), orderOperationVideoDTO.getProductChinese())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getProductEnglish(), orderOperationVideoDTO.getProductEnglish())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getProductLink(), orderOperationVideoDTO.getProductLink())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getPlatform(), orderOperationVideoDTO.getPlatform())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getShootingCountry(), orderOperationVideoDTO.getShootingCountry())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getModelType(), orderOperationVideoDTO.getModelType())
                        || ObjectUtil.notEqual(orderVideoCurrentInfo.getVideoFormat(), orderOperationVideoDTO.getVideoFormat())
                ) {
                    Assert.isFalse(Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus()),
                            OrderStatusEnum.getLabel(orderVideo.getStatus()) + "订单无法变更产品基础信息");
                    orderVideoMatch.setGoodsInfoChange(true);
                } else {
                    orderVideoMatch.setGoodsInfoChange(false);
                }
                if (!orderVideoMatch.getGoodsInfoChange() && ObjectUtil.notEqual(orderVideoCurrentInfo.getReferenceVideoLink(), orderOperationVideoDTO.getReferenceVideoLink())){
                    orderVideoMatch.setGoodsInfoChange(true);
                }
                Map<Integer, OrderVideoContent> integerOrderVideoContentMap = videoContentService.selectMapByVideoIdsAndTypesAsc(Arrays.asList(orderOperationVideoDTO.getId()), null);
                if (CollUtil.isNotEmpty(integerOrderVideoContentMap)) {
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.CAUTIONS.getCode()))) {
                        orderVideoMatch.setCautionsChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.CAUTIONS.getCode()).getFirstEdit()));
                    }
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.REQUIRE.getCode()))) {
                        orderVideoMatch.setShootRequiredChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.REQUIRE.getCode()).getFirstEdit()));
                    }
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode()))) {
                        orderVideoMatch.setSellingPointProductChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode()).getFirstEdit()));
                    }
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode()))) {
                        orderVideoMatch.setOrderSpecificationRequireChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode()).getFirstEdit()));
                    }
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode()))) {
                        orderVideoMatch.setParticularEmphasisChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode()).getFirstEdit()));
                    }
                    if (ObjectUtil.isNotNull(integerOrderVideoContentMap.get(VideoContentTypeEnum.PARTICULAR_EMPHASIS_PIC.getCode()))) {
                        orderVideoMatch.setParticularEmphasisChange(StatusTypeEnum.YES.getCode().equals(integerOrderVideoContentMap.get(VideoContentTypeEnum.PARTICULAR_EMPHASIS_PIC.getCode()).getFirstEdit()));
                    }
                }
//                else {
//                    orderVideoMatch.setCautionsChange(false);
//                    orderVideoMatch.setShootRequiredChange(false);
//                    orderVideoMatch.setProductPicChange(false);
//                    orderVideoMatch.setParticularEmphasisChange(false);
//                    orderVideoMatch.setOrderSpecificationRequireChange(false);
//                    orderVideoMatch.setSellingPointProductChange(false);
//                }

                orderVideoMatch.setUpdateBy(SecurityUtils.getUsername());
                orderVideoMatch.setUpdateById(SecurityUtils.getUserId());
                baseMapper.updateById(orderVideoMatch);
                //待匹配才需要进行后续处理
                if (OrderStatusEnum.UN_MATCH.getCode().equals(orderVideo.getStatus())){
                    clearFlag(orderVideoMatch.getId());
                    orderVideoMatchPreselectModelService.editOrderVideoPreselectModelFromJointedToUnJointed(orderVideoMatch.getId());
                    //  新意向模特与旧意向模特不同 将旧意向模特淘汰掉
                    if (ObjectUtil.notEqual(orderOperationVideoDTO.getIntentionModelId(), oldIntentionModelId)) {
                        orderVideoMatchPreselectModelService.eliminateModelByMatchId(orderVideoMatch.getId());
                        if (ObjectUtil.isNotNull(orderOperationVideoDTO.getIntentionModelId())) {
                            orderVideoMatchPreselectModelService.addPreselectModel(null, orderVideoMatch.getId(), List.of(orderOperationVideoDTO.getIntentionModelId()), PreselectModelAddTypeEnum.INTENTION_MODEL, PreselectStatusEnum.UN_JOINTED, null);
                        }
                    }
                }
            } finally {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
            }
        } catch (Exception e) {
            log.error("修改订单信息异常", e);
            throw new ServiceException(e.getMessage());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_FLOW_KEY + orderOperationVideoDTO.getId());
        }
    }

    /**
     * 继续模特匹配
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void continueMatch(Long videoId) {
        OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(videoId);
        Assert.notNull(orderVideoMatch, "匹配单不存在");
        Assert.isTrue(OrderVideoMatchStatusEnum.PAUSE.getCode().equals(orderVideoMatch.getStatus()), "匹配单未完结，无法开始新的匹配");
        Assert.notNull(orderVideoMatch.getEndTime(), "匹配单未完结，无法开始新的匹配");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(videoId);
            Assert.notNull(orderVideo, "视频订单不存在");

            List<OrderVideoMatch> orderVideoMatches = baseMapper.selectHistoryMatchListByVideoId(orderVideo.getId());

            DateTime dateTime = DateUtil.date();
            OrderVideoMatch saveOrderVideoMatch = new OrderVideoMatch();
            saveOrderVideoMatch.setVideoId(orderVideo.getId());
            saveOrderVideoMatch.setRollbackId(orderVideo.getRollbackId());
            saveOrderVideoMatch.setCount(CollUtil.isEmpty(orderVideoMatches) ? 1 : orderVideoMatches.size() + 1);
            saveOrderVideoMatch.setStartTime(dateTime);
            saveOrderVideoMatch.setCreateBy(SecurityUtils.getUsername());
            saveOrderVideoMatch.setCreateById(SecurityUtils.getUserId());
            baseMapper.insert(saveOrderVideoMatch);
            baseMapper.updateShuffledSortKey(saveOrderVideoMatch.getId());

            orderVideoMatchPreselectModelService.continueMatch(orderVideoMatch.getId(), saveOrderVideoMatch.getId(), orderVideo.getId(), orderVideo.getIntentionModelId(), dateTime);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 暂停匹配单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pauseMatch(VideoPauseMatchDTO videoPauseMatchDTO) {
        OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(videoPauseMatchDTO.getVideoId());
        checkOrderVideoMatch(orderVideoMatch);
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            orderVideoMatch.setStatus(OrderVideoMatchStatusEnum.PAUSE.getCode());
            orderVideoMatch.setEndTime(DateUtil.date());
            orderVideoMatch.setUpdateBy(SecurityUtils.getUsername());
            orderVideoMatch.setUpdateById(SecurityUtils.getUserId());
            orderVideoMatch.setPauseReason(videoPauseMatchDTO.getPauseReason());
            baseMapper.updateById(orderVideoMatch);

            orderVideoMatchPreselectModelService.pauseMatch(orderVideoMatch.getId(), videoPauseMatchDTO.getPauseReason());
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.PAUSE_MATCH.getEventName(),
                    OrderVideoOperateTypeEnum.PAUSE_MATCH.getIsPublic(),
                    OrderVideoOperateDTO.builder()
                            .videoId(orderVideoMatch.getVideoId())
                            .eventContent(StrUtil.format(
                                    OrderVideoOperateTypeEnum.PAUSE_MATCH.getEventContent(),
                                    videoPauseMatchDTO.getPauseReason())
                            ).build());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 通过添加预选时间筛选符合条件的匹配单的videoId（给订单列表筛选用）
     */
    @Override
    public Set<Long> selectVideoIdByAddPreselectTimeOfOrderVideo(String addPreselectTimeBegin, String addPreselectTimeEnd) {
        return baseMapper.selectVideoIdByAddPreselectTimeOfOrderVideo(addPreselectTimeBegin, addPreselectTimeEnd);
    }

    /**
     * 结束预选-预选记录
     */
    @Override
    public HistoryPreselectModelVO preselectionRecord(Long matchId) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(matchId);
        Assert.notNull(orderVideoMatch, "匹配单不存在");
        HistoryPreselectModelVO historyPreselectModelVO = BeanUtil.copyProperties(orderVideoMatch, HistoryPreselectModelVO.class);

        List<OrderVideoMatchPreselectModelVO> matchPreselectModels = orderVideoMatchPreselectModelService.selectListByMatchId(matchId);
        matchPreselectModels = matchPreselectModels.stream()
                .filter(item -> !PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(item.getAddType()) || DistributionResultEnum.WANT.getCode().equals(item.getDistributionResult()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(matchPreselectModels)) {
            return historyPreselectModelVO;
        }

        for (OrderVideoMatchPreselectModelVO matchPreselectModel : matchPreselectModels) {
            if (!PreselectStatusEnum.SELECTED.getCode().equals(matchPreselectModel.getStatus()) && ObjectUtil.isNull(historyPreselectModelVO.getShootModel())) {
                historyPreselectModelVO.getPreselectModelList().add(matchPreselectModel);
            }
        }

        if (ObjectUtil.isNotNull(orderVideoMatch.getShootModelId())) {
            List<AddPreselectModelListVO> modelListVOS = remoteService.selectModelInfoOfPreselection(ModelListDTO.builder().id(List.of(orderVideoMatch.getShootModelId())).build());
            if (CollUtil.isNotEmpty(modelListVOS)) {
                historyPreselectModelVO.setShootModel(modelListVOS.get(0));
            }
        }

        if (CharSequenceUtil.isNotBlank(historyPreselectModelVO.getShippingPic())) {
            List<Long> resourceIds = StringUtils.splitToLong(historyPreselectModelVO.getShippingPic(), StrUtil.COMMA);
            Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

            for (Long resourceId : resourceIds) {
                historyPreselectModelVO.getShippingPics().add(resourceMap.getOrDefault(resourceId, new OrderResource()).getObjectKey());
            }
        }

        return historyPreselectModelVO;
    }

    /**
     * 修改选定模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editSelectedModel(MarkOrderDTO markOrderDTO) {
        OrderVideoMatchPreselectModel matchPreselectModel = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(markOrderDTO.getId());
        Assert.notNull(matchPreselectModel, "该匹配单不存在选定模特");
        Assert.isTrue(matchPreselectModel.getId().equals(markOrderDTO.getMatchPreselectModelId()), "该模特非选定模特");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchPreselectModel.getMatchId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            clearFlag(markOrderDTO.getId());
            OrderVideoMatch orderVideoMatch = baseMapper.selectById(markOrderDTO.getId());
            editMatchMarkInfo(markOrderDTO, orderVideoMatch);

            if (CharSequenceUtil.isNotBlank(markOrderDTO.getShootAttention())
                    && ObjectUtil.notEqual(matchPreselectModel.getShootAttention(), markOrderDTO.getShootAttention())
                    && ObjectUtil.isNull(matchPreselectModel.getNeedRemindShootAttention())
            ) {
                matchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.YES.getCode());
            } else if (CharSequenceUtil.isBlank(matchPreselectModel.getShootAttention())) {
                matchPreselectModel.setNeedRemindShootAttention(StatusTypeEnum.NO.getCode());
            }
            matchPreselectModel.setShootAttention(markOrderDTO.getShootAttention() == null ? CharSequenceUtil.EMPTY : markOrderDTO.getShootAttention());
            if (CollUtil.isNotEmpty(markOrderDTO.getShootAttentionObjectKey())) {
                matchPreselectModel.setShootAttentionObjectKey(String.join(",", markOrderDTO.getShootAttentionObjectKey()));
            } else {
                matchPreselectModel.setShootAttentionObjectKey(null);
            }
            orderVideoMatchPreselectModelService.updateById(matchPreselectModel);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchPreselectModel.getMatchId());
        }
    }

    private void editMatchMarkInfo(MarkOrderDTO markOrderDTO, OrderVideoMatch orderVideoMatch) {
        if (CarryTypeEnum.CARRY.getCode().equals(markOrderDTO.getCarryType())) {
            OrderVideoMatch mainCarryVideoMatch = baseMapper.getMainCarryMatchByMainCarryVideoId(markOrderDTO.getMainCarryVideoId());
            Assert.notNull(mainCarryVideoMatch, "目标订单不是主携带订单,无法关联!");
            Long carriedCount = baseMapper.getCarriedCountByMainCarryVideoId(markOrderDTO.getMainCarryVideoId(), null);
            Assert.isTrue(carriedCount < mainCarryVideoMatch.getMainCarryCount(), "主携带订单可携带数量上限,无法关联!");
        }

        BeanUtil.copyProperties(markOrderDTO, orderVideoMatch);
        if (CollUtil.isNotEmpty(markOrderDTO.getShippingPic())) {
            List<Long> shippingPicIds = orderResourceService.saveBatchOrderResource(markOrderDTO.getShippingPic());
            orderVideoMatch.setShippingPic(StrUtil.join(StrUtil.COMMA, shippingPicIds));
        }
        orderVideoMatch.setUpdateBy(SecurityUtils.getUsername());
        orderVideoMatch.setUpdateById(SecurityUtils.getUserId());
        baseMapper.updateById(orderVideoMatch);
    }

    /**
     * 选定模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = CheckedException.class)
    public void selectedModel(MarkOrderDTO markOrderDTO) {
        OrderVideoMatchPreselectModel matchPreselectModel = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(markOrderDTO.getId());
        Assert.isNull(matchPreselectModel, "该订单已有选定模特");

        OrderVideoMatch orderVideoMatch = baseMapper.selectById(markOrderDTO.getId());
        checkOrderVideoMatch(orderVideoMatch);
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            editMatchMarkInfo(markOrderDTO, orderVideoMatch);
            orderVideoMatchPreselectModelService.selectedModel(markOrderDTO);

            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_MARK.getEventName(),
                    null,
                    OrderVideoOperateTypeEnum.ORDER_MARK.getIsPublic(),
                    null,
                    OrderVideoOperateDTO.builder().videoId(orderVideoMatch.getVideoId()).eventContent(OrderVideoOperateTypeEnum.ORDER_MARK.getEventContent()).build()
            );
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 选定模特信息-主携带订单下拉框
     */
    @Override
    public List<ModelCarryVO> selectedModelMainCarryListByMatchId(Long matchId, Long modelId) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(matchId);
        checkOrderVideoMatch(orderVideoMatch);
        return baseMapper.markOrderMainCarryListByModelId(orderVideoMatch.getVideoId(), modelId);
    }

    /**
     * 选定模特信息
     */
    @Override
    public MarkOrderVO selectedModelInfo(Long matchId, Long modelId) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(matchId);
        checkOrderVideoMatch(orderVideoMatch);
        MarkOrderVO markOrderVO = BeanUtil.copyProperties(orderVideoMatch, MarkOrderVO.class);

        List<AddPreselectModelListVO> modelListVOS = remoteService.selectModelInfoOfPreselection(ModelListDTO.builder().id(List.of(modelId)).build());
        Assert.notEmpty(modelListVOS, "没有该选定模特的信息");

        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = orderVideoMatchPreselectModelService.getActiveByMatchIdAndModelId(matchId, modelId);
        Assert.notNull(orderVideoMatchPreselectModel, "该订单没有添加该模特");
        markOrderVO.setSelectedTime(orderVideoMatchPreselectModel.getSelectedTime());
        markOrderVO.setAddType(orderVideoMatchPreselectModel.getAddType());
        markOrderVO.setShootAttention(orderVideoMatchPreselectModel.getShootAttention());
        markOrderVO.setShootAttentionObjectKey(orderVideoMatchPreselectModel.getShootAttentionObjectKey());

        AddPreselectModelListVO addPreselectModelListVO = modelListVOS.get(0);
        markOrderVO.setPreselectModelVO(addPreselectModelListVO);
        markOrderVO.setOriginCommissionUnit(addPreselectModelListVO.getCommissionUnit());
        markOrderVO.setOriginCommission(addPreselectModelListVO.getCommission());

        if (StrUtil.isNotBlank(markOrderVO.getShippingPic())) {
            List<Long> resourceIds = StringUtils.splitToLong(markOrderVO.getShippingPic(), StrUtil.COMMA);
            Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

            for (Long resourceId : resourceIds) {
                markOrderVO.getShippingPics().add(resourceMap.getOrDefault(resourceId, new OrderResource()).getObjectKey());
            }
        }

        if (ObjectUtil.isNotNull(orderVideoMatch.getMainCarryVideoId())) {
            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getMainCarryVideoId());
            markOrderVO.setMainCarryVideoCode(orderVideo.getVideoCode());
        }

        List<ModelCarryVO> modelCarryVOS = baseMapper.getModelLeftCarryCountByModelIds(List.of(modelId));
        if (CollUtil.isNotEmpty(modelCarryVOS)) {
            markOrderVO.setLeftCarryCount(modelCarryVOS.get(0).getLeftCarryCount());
        }

        return markOrderVO;
    }

    /**
     * 清空匹配单的标记
     */
    @Override
    public void clearFlag(List<Long> matchIds) {
        if (CollUtil.isEmpty(matchIds)) {
            return;
        }
        baseMapper.clearFlag(matchIds);
    }

    /**
     * 清空匹配单的标记
     */
    @Override
    public void clearFlag(Long matchId) {
        clearFlag(List.of(matchId));
    }

    /**
     * 预选管理-模特匹配-我的预选-结束预选
     */
    @Override
    public List<MyPreselectEndMatchListVO> selectMyPreselectEndMatchList(MyPreselectEndMatchListDTO dto) {
        assembleCondition(dto);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovm.end_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<MyPreselectEndMatchListVO> list = baseMapper.selectMyPreselectEndMatchList(dto);
        assembleOrderPoolList(list);
        return list;
    }

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    @Override
    public List<MyPreselectDockingListVO> selectMyPreselectDockingList(MyPreselectDockingListDTO dto) {
        assembleCondition(dto);
        if (CollUtil.isEmpty(dto.getBackUserRelevanceModelIds()) && !dto.getCurrentUserIsAdmin() && !dto.getIsPendingSubmit() && !dto.getCanViewAllPreselection()) {
            return Collections.emptyList();
        }
        Map<String, String> addPreselectTimeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(dto.getAddPreselectTimes())) {
            dto.getAddPreselectTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    addPreselectTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
        }
        dto.setAddPreselectTimeMap(addPreselectTimeMap);
        if (!dto.getCurrentUserIsAdmin() && ObjectUtil.isNotNull(dto.getStatus()) && !dto.getIsPendingSubmit()) {
            dto.setCurrentUserId(SecurityUtils.getUserId());
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovm.start_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<MyPreselectDockingListVO> list = getMyPreselectDockingListVOS(dto);

        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        assembleOrderPoolList(list);

        Set<Long> matchIds = list.stream().map(MyPreselectDockingListVO::getId).collect(Collectors.toSet());
        dto.setMatchIds(matchIds);
        List<OrderVideoMatchPreselectModelVO> preselectModelList = orderVideoMatchPreselectModelService.selectMyPreselectDockingList(dto);
        if (CollUtil.isEmpty(preselectModelList)) {
            return Collections.emptyList();
        }

        //  获取 相同产品链接未匹配订单数量 以及 相同产品链接订单数量
        List<String> productLinks = list.stream().map(MyPreselectDockingListVO::getProductLink).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        Map<String, List<MyPreselectDockingListVO>> productLinkOrderMap = new HashMap<>();
        if (CollUtil.isNotEmpty(productLinks)) {
            List<MyPreselectDockingListVO> productLinkOrderList = baseMapper.getOrderTheSameProducts(productLinks);
            productLinkOrderMap = productLinkOrderList.stream().collect(Collectors.groupingBy(MyPreselectDockingListVO::getProductLink));
        }

        Map<Long, List<OrderVideoMatchPreselectModelVO>> preselectModelMap = preselectModelList.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModelVO::getMatchId));
        for (MyPreselectDockingListVO listVO : list) {
            if (ObjectUtil.isNotNull(listVO.getPicCount())) {
                listVO.setSurplusPicCount(PicCountEnum.getValue(listVO.getPicCount()) - Optional.ofNullable(listVO.getRefundPicCount()).orElse(0));
            } else {
                listVO.setSurplusPicCount(0);
            }
            List<OrderVideoMatchPreselectModelVO> matchPreselectModelVOS = preselectModelMap.get(listVO.getId());
            if (CollUtil.isEmpty(matchPreselectModelVOS)) {
                continue;
            }
            // 通过Comparator进行排序
            if (!dto.getIsPendingSubmit()) {
                matchPreselectModelVOS.sort(Comparator
                        .comparing((OrderVideoMatchPreselectModelVO vo) -> !dto.getBackUserRelevanceModelIds().contains(vo.getModelId())) // 按是否包含在backUserRelevanceModelIds中排序
                        .thenComparing(OrderVideoMatchPreselectModelVO::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())) // 按addTime降序，null值排在最后
                );
            }
            listVO.setPreselectModelList(matchPreselectModelVOS);

            List<MyPreselectDockingListVO> productLinkOrders = productLinkOrderMap.get(listVO.getProductLink());
            if (CollUtil.isNotEmpty(productLinkOrders)) {
                listVO.setUnMatchSameProductLinkOrderCount(productLinkOrders.stream().mapToInt(MyPreselectDockingListVO::getUnMatchSameProductLinkOrderCount).sum());
                listVO.setSameProductLinkOrderCount(productLinkOrders.stream().mapToInt(MyPreselectDockingListVO::getSameProductLinkOrderCount).sum());
            }
        }

        return list;
    }

    private List<MyPreselectDockingListVO> getMyPreselectDockingListVOS(MyPreselectDockingListDTO dto) {
        List<MyPreselectDockingListVO> list = baseMapper.selectMyPreselectDockingList(dto);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        List<Long> videoIds = list.stream().map(MyPreselectDockingListVO::getVideoId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollUtil.isEmpty(videoIds)) {
            list.forEach(item -> item.setHistoryPreselectModelCount(0));
            return list;
        }
        List<HistoryPreselectModelCountVO> historyPreselectModelCountList = baseMapper.getHistoryPreselectModelCountList(videoIds);
        if (CollUtil.isEmpty(historyPreselectModelCountList)) {
            list.forEach(item -> item.setHistoryPreselectModelCount(0));
            return list;
        }
        Map<Long, Integer> historyPreselectModelCountMap = historyPreselectModelCountList.stream().collect(Collectors.toMap(HistoryPreselectModelCountVO::getVideoId, HistoryPreselectModelCountVO::getHistoryPreselectModelCount));
        for (MyPreselectDockingListVO item : list) {
            item.setHistoryPreselectModelCount(historyPreselectModelCountMap.getOrDefault(item.getVideoId(), 0));
        }
        return list;
    }

    /**
     * 查询当前匹配单活跃的预选模特
     */
    @Override
    public List<OrderVideoMatchPreselectModelVO> selectActivePreselectModelListByMatchId(Long matchId) {
        // OrderVideoMatch orderVideoMatch = baseMapper.selectById(matchId);
        // checkOrderVideoMatch(orderVideoMatch);
        return orderVideoMatchPreselectModelService.selectActivePreselectModelListByMatchId(matchId);
    }

    /**
     * 预选管理-模特匹配-订单池
     */
    @Override
    public List<OrderPoolListVO> selectOrderPoolListByCondition(OrderPoolListDTO orderPoolListDTO) {
        assembleCondition(orderPoolListDTO);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovm.start_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<OrderPoolListVO> list = baseMapper.selectOrderPoolListByCondition(orderPoolListDTO);
        assembleOrderPoolList(list);
        return list;
    }

    /**
     * 通过视频订单id 更改模特记录状态 为 已确认拍摄
     */
    @Override
    public void updateModelSelectStatusToConfirmByVideoId(List<Long> videoIds) {
        List<VideoMatchOrderVO> videoMatchOrderVOS = baseMapper.selectActiveListByVideoIds(videoIds);
        List<Long> matchIds = videoMatchOrderVOS.stream().map(VideoMatchOrderVO::getId).collect(Collectors.toList());


        List<Long> lockMatchIds = new ArrayList<>();
        try {
            for (Long matchId : matchIds) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchId, CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
                lockMatchIds.add(matchId);
            }
        } catch (Exception e) {
            for (Long lockMatchId : lockMatchIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + lockMatchId);
            }
        }

        try {
            List<OrderVideoMatchPreselectModel> matchPreselectModels = orderVideoMatchPreselectModelService.selectedListByMatchIds(matchIds);
            Assert.notEmpty(matchPreselectModels, "订单未选定模特，无法继续！");

            for (OrderVideoMatchPreselectModel matchPreselectModel : matchPreselectModels) {
                if (OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(matchPreselectModel.getSelectStatus())) {
                    matchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CONFIRM.getCode());
                }
            }
            orderVideoMatchPreselectModelService.updateBatchById(matchPreselectModels);
        } finally {
            for (Long matchId : matchIds) {
                redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + matchId);
            }
        }
    }

    /**
     * 通过视频订单id和模特id查询未对接或已对接的模特
     *
     * @param videoId 订单id
     * @param modelId 模特id
     * @return 未对接的模特
     */
    @Override
    public OrderVideoMatchPreselectModel getUnJointedOrJointedByVideoIdAndModelId(Long videoId, Long modelId) {
        OrderVideoMatch videoMatch = baseMapper.getActiveByVideoId(videoId);
        checkOrderVideoMatch(videoMatch);
        return orderVideoMatchPreselectModelService.getUnJointedOrJointedByVideoIdAndModelId(videoMatch.getId(), modelId);
    }

    /**
     * 更新预选模特列表为已淘汰
     */
    @Override
    public void outPreselectModel(List<OutPreselectModelDTO> dtoList) {
        orderVideoMatchPreselectModelService.outPreselectModel(dtoList);
    }

    /**
     * 更改预选模特状态
     */
    @Override
    public void editPreselectModel(EditPreselectModelDTO editPreselectModelDTO) {
        orderVideoMatchPreselectModelService.editPreselectModel(editPreselectModelDTO);
    }

    /**
     * 添加预选模特
     */
    @Override
    public AddDistributionErrorVO addPreselectModel(AddPreselectModelDTO dto) {
        if (!SecurityUtils.currentUserIsAdmin()) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            Assert.isTrue(CollUtil.isNotEmpty(modelPeople) && new HashSet<>(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList())).containsAll(dto.getModelIds()), "仅可操作自己关联的模特");
        }

        OrderVideoMatch orderVideoMatch = baseMapper.selectById(dto.getMatchId());
        checkOrderVideoMatch(orderVideoMatch);
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            OrderVideoMatchPreselectModel selectedModelByMatchId = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(dto.getMatchId());
            Assert.isNull(selectedModelByMatchId, "当前订单已选定模特，无法继续添加！");

            OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(orderVideoMatch.getVideoId());
            Assert.notNull(orderVideo, "订单有误，请联系管理员");

            //  校验添加的模特能否接单
            Collection<Long> cannotModelIds = SpringUtils.getBean(IOrderService.class).checkModelCanAccept(dto.getModelIds(), orderVideo.getCreateOrderBizUserId());
            Assert.isTrue(CollUtil.isEmpty(cannotModelIds), "模特无法接单！");

            SpringUtils.getBean(IOrderVideoService.class).checkVideoStatus(orderVideo, OrderStatusEnum.UN_MATCH);

            return orderVideoMatchPreselectModelService.addPreselectModel(
                    orderVideo.getIsGund(),
                    dto.getMatchId(),
                    dto.getModelIds(),
                    PreselectModelAddTypeEnum.OPERATION,
                    PreselectStatusEnum.UN_JOINTED,
                    null);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 通过视频订单id查询活跃的关联的预选模特（运营端订单列表）
     */
    @Override
    public List<VideoMatchOrderVO> selectActiveListByVideoIds(List<Long> videoIds) {
        List<VideoMatchOrderVO> videoMatchOrderVOS = baseMapper.selectActiveListByVideoIds(videoIds);
        if (CollUtil.isEmpty(videoMatchOrderVOS)) {
            return Collections.emptyList();
        }
        List<Long> matchIds = videoMatchOrderVOS.stream().map(VideoMatchOrderVO::getId).collect(Collectors.toList());
        List<OrderVideoMatchPreselectModel> matchPreselectModels = orderVideoMatchPreselectModelService.selectActiveListByMatchIdsOfOrderVideoList(matchIds);
        Map<Long, List<OrderVideoMatchPreselectModel>> matchPreselectModelMap = matchPreselectModels.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModel::getMatchId));

        for (VideoMatchOrderVO matchOrderVO : videoMatchOrderVOS) {
            List<OrderVideoMatchPreselectModel> preselectModels = matchPreselectModelMap.get(matchOrderVO.getId());
            if (CollUtil.isEmpty(preselectModels)) {
                continue;
            }
            // 按 status=2 优先，然后按 addTime 降序排序// 只保留前3条
            preselectModels = preselectModels.stream()
                                             .sorted(Comparator.comparing((OrderVideoMatchPreselectModel model) -> model.getStatus() != 2)
                                                               .thenComparing(OrderVideoMatchPreselectModel::getAddTime, Comparator.reverseOrder()))
                                             .limit(3)
                                             .collect(Collectors.toList());

            matchOrderVO.setNormalVideoMatchPreselectModelOrderVOS(BeanUtil.copyToList(preselectModels, VideoMatchPreselectModelOrderVO.class));
        }
        return videoMatchOrderVOS;
    }

    /**
     * 通过视频订单id查询历史关联的预选模特
     */
    @Override
    public List<OrderVideoMatchVO> selectHistoryMatchListByVideoId(Long videoId, boolean isHistory) {
        List<OrderVideoMatch> orderVideoMatches = baseMapper.selectHistoryMatchListByVideoId(videoId);
        if (CollUtil.isEmpty(orderVideoMatches)) {
            return Collections.emptyList();
        }
        List<Long> matchIds = orderVideoMatches.stream().map(OrderVideoMatch::getId).collect(Collectors.toList());
        Set<Long> activeModelIds = orderVideoMatchPreselectModelService.selectActivePreselectModelListByMatchIdSimple(orderVideoMatches.get(0).getId())
                .stream()
                .filter(item -> ObjectUtil.isNull(item.getDistributionResult())
                                || (
                                !DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(item.getDistributionResult())
                                        && !DistributionResultEnum.WANT_NOT.getCode().equals(item.getDistributionResult())
                        )
                )
                .map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toSet());
        List<OrderVideoMatchPreselectModelVO> orderVideoMatchPreselectModels = orderVideoMatchPreselectModelService.selectListByMatchIds(matchIds);
        orderVideoMatchPreselectModels = orderVideoMatchPreselectModels.stream()
                .filter(m -> !PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(m.getAddType()) || ModelIntentionEnum.WANT.getCode().equals(m.getModelIntention()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(orderVideoMatchPreselectModels)) {
            return Collections.emptyList();
        }

        List<OrderVideoMatchVO> orderVideoMatchVOS = BeanUtil.copyToList(orderVideoMatches, OrderVideoMatchVO.class);
        Map<Long, List<OrderVideoMatchPreselectModelVO>> orderVideoMatchPreselectModelMap = orderVideoMatchPreselectModels.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModelVO::getMatchId));

        for (OrderVideoMatchVO matchVO : orderVideoMatchVOS) {
            List<OrderVideoMatchPreselectModelVO> matchPreselectModels = orderVideoMatchPreselectModelMap.get(matchVO.getId());
            if (CollUtil.isEmpty(matchPreselectModels)) {
                continue;
            }
            for (OrderVideoMatchPreselectModelVO matchPreselectModel : matchPreselectModels) {
                if (activeModelIds.contains(matchPreselectModel.getModelId())) {
                    matchPreselectModel.setActive(true);
                }
            }
            if (isHistory) {
                List<OrderVideoMatchPreselectModelVO> normalOrderVideoMatchPreselectModelVOS = matchPreselectModels.stream().filter(item -> !PreselectStatusEnum.OUT.getCode().equals(item.getStatus())).sorted(Comparator.comparing(OrderVideoMatchPreselectModelVO::getAddTime).reversed()).collect(Collectors.toList());
                matchVO.setNormalOrderVideoMatchPreselectModelVOS(normalOrderVideoMatchPreselectModelVOS);
            }
            List<OrderVideoMatchPreselectModelVO> outOrderVideoMatchPreselectModelVOS = matchPreselectModels.stream().filter(item -> PreselectStatusEnum.OUT.getCode().equals(item.getStatus())).sorted(Comparator.comparing(OrderVideoMatchPreselectModelVO::getAddTime).reversed()).collect(Collectors.toList());
            matchVO.setOutOrderVideoMatchPreselectModelVOS(outOrderVideoMatchPreselectModelVOS);
        }
        return orderVideoMatchVOS;
    }

    /**
     * 查询视频订单已淘汰模特数量
     */
    @Override
    public Long countVideoOutModel(Long videoId) {
        OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(videoId);
        checkOrderVideoMatch(orderVideoMatch);

        return orderVideoMatchPreselectModelService.countMatchOutModel(orderVideoMatch.getId());
    }

    /**
     * 查询视频订单已淘汰模特数量
     */
    @Override
    public List<CountVideoOutModelVO> countVideoOutModel(Collection<Long> videoIds) {
        return baseMapper.countVideoOutModelByVideoIds(videoIds);
    }

    /**
     * 确认提交预选模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = CheckedException.class)
    public void submitPreselectModel(SubmitPreselectModelDTO dto) {
        OrderVideoMatch orderVideoMatch = baseMapper.selectById(dto.getMatchId());
        checkOrderVideoMatch(orderVideoMatch);
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");

            IOrderVideoService orderVideoService = SpringUtils.getBean(IOrderVideoService.class);
            OrderVideo orderVideo = orderVideoService.getById(orderVideoMatch.getVideoId());
            orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.UN_MATCH);

            if (CarryTypeEnum.CARRY.getCode().equals(orderVideoMatch.getCarryType())) {
                OrderVideoMatch mainCarryVideoMatch = baseMapper.getMainCarryMatchByMainCarryVideoId(orderVideoMatch.getMainCarryVideoId());
                Assert.notNull(mainCarryVideoMatch, "目标订单不是主携带订单,无法关联!");
                Long carriedCount = baseMapper.getCarriedCountByMainCarryVideoId(orderVideoMatch.getMainCarryVideoId(), orderVideoMatch.getVideoId());
                Assert.isTrue(carriedCount < mainCarryVideoMatch.getMainCarryCount(), "主携带订单可携带数量上限,无法关联!");
            }

            //  1、校验视频有没有没选定模特的
            OrderVideoMatchPreselectModel preselectModel = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(dto.getMatchId());
            Assert.notNull(preselectModel, "模特选定状态发生变化，请刷新页面重试~");

            //  2、校验模特档期
            boolean isSchedule = orderVideoMatchPreselectModelService.checkPreselectModelSchedule(List.of(preselectModel));
            if (isSchedule) {
                if (ObjectUtil.isNotNull(dto.getIsClear()) && StatusTypeEnum.YES.getCode().equals(dto.getIsClear())) {
                    orderVideoMatchPreselectModelService.modelCannotAcceptUpdateInfo(List.of(preselectModel));
                }
                throw new CheckedException(2, preselectModel.getRemark());
            }

            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(List.of(preselectModel.getModelId()));
            ModelInfoVO modelInfoVO = modelMap.getOrDefault(preselectModel.getModelId(), new ModelInfoVO());

            orderVideoMatchPreselectModelService.checkModelConformOrderVideoInfo(modelMap, orderVideo);

            DateTime date = DateUtil.date();

            ModelOrderDTO modelOrderDTO = new ModelOrderDTO();
            modelOrderDTO.setModelId(preselectModel.getModelId());
            modelOrderDTO.setVideoId(orderVideo.getId());
            //  设置拍摄模特
            orderVideo.setShootModelId(preselectModel.getModelId());
            orderVideo.setLastModelSubmitTime(date);

            //  被携带订单 新增视频订单携带记录
            if (ObjectUtil.isNotNull(orderVideoMatch.getMainCarryVideoId())) {
                OrderVideoCarry orderVideoCarry = new OrderVideoCarry();
                orderVideoCarry.setModelId(orderVideo.getShootModelId());
                orderVideoCarry.setMainCarryVideoId(orderVideoMatch.getMainCarryVideoId());
                orderVideoCarry.setCarryVideoId(orderVideoMatch.getVideoId());
                orderVideoCarryService.insertOrderVideoCarry(orderVideoCarry);
            }

            //  意向模特 = 拍摄模特 且 不需要发货 进入待完成
            if (orderVideo.getShootModelId().equals(orderVideo.getIntentionModelId()) && IsObjectEnum.NO_OBJECT.getCode().equals(orderVideo.getIsObject())) {
                SpringUtils.getBean(IOrderService.class).createOrderFlow(List.of(orderVideo), OrderStatusEnum.UN_FINISHED, "确认提交预选模特");
                orderVideoFlowNodeDiagramService.setNodeCompleteTime(List.of(orderVideo.getId()), OrderVideoFlowNodeEnum.MATCHING_MODEL, OrderVideoFlowNodeEnum.MERCHANT_DELIVERY);
                modelOrderDTO.setAcceptTime(date);
            } else {
                if (orderVideo.getIsObject().equals(IsObjectEnum.OBJECT.getCode())) {
                    OrderVideoModelShippingAddress orderVideoModelShippingAddress = new OrderVideoModelShippingAddress();
                    orderVideoModelShippingAddress.setVideoId(orderVideo.getId());
                    orderVideoModelShippingAddress.setRollbackId(orderVideo.getRollbackId());
                    orderVideoModelShippingAddress.setShootModelId(orderVideo.getShootModelId());

                    orderVideoModelShippingAddress.setNation(modelInfoVO.getNation());
                    orderVideoModelShippingAddress.setRecipient(modelInfoVO.getRecipient());
                    orderVideoModelShippingAddress.setCity(modelInfoVO.getCity());
                    orderVideoModelShippingAddress.setState(modelInfoVO.getState());
                    orderVideoModelShippingAddress.setZipcode(modelInfoVO.getZipcode());
                    orderVideoModelShippingAddress.setDetailAddress(modelInfoVO.getDetailAddress());
                    orderVideoModelShippingAddress.setPhone(modelInfoVO.getPhone());

                    orderVideoModelShippingAddress.setShippingRemark(orderVideoMatch.getShippingRemark());
                    orderVideoModelShippingAddress.setShippingPic(orderVideoMatch.getShippingPic());
                    //  获取商家信息
                    List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(List.of(orderVideo.getCreateOrderBusinessId()), null);
                    Assert.isTrue(CollUtil.isNotEmpty(businessAccountDetailVOS), "商家信息不存在，请联系管理员处理~");
                    orderVideoModelShippingAddress.setPhoneVisible(businessAccountDetailVOS.get(0).getPhoneVisible());

                    orderVideoModelShippingAddressService.saveOrderVideoModelShippingAddress(orderVideoModelShippingAddress);
                    OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                    orderVideoLogisticFlowDTO.setBusinessId(businessAccountDetailVOS.get(0).getBusinessId());
                    orderVideoLogisticFlowDTO.setMemberCode(businessAccountDetailVOS.get(0).getMemberCode());
                    orderVideoLogisticFlowDTO.setVideoCode(orderVideo.getVideoCode());
                    orderVideoLogisticFlowDTO.setVideoId(orderVideo.getId());
                    orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.UN_NOTIFIED);
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);

                }else {
                    List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(List.of(orderVideo.getCreateOrderBusinessId()), null);
                    Assert.isTrue(CollUtil.isNotEmpty(businessAccountDetailVOS), "商家信息不存在，请联系管理员处理~");
                    OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
                    orderVideoLogisticFlowDTO.setBusinessId(businessAccountDetailVOS.get(0).getBusinessId());
                    orderVideoLogisticFlowDTO.setMemberCode(businessAccountDetailVOS.get(0).getMemberCode());
                    orderVideoLogisticFlowDTO.setVideoCode(orderVideo.getVideoCode());
                    orderVideoLogisticFlowDTO.setVideoId(orderVideo.getId());
                    orderVideoLogisticFlowDTO.setHandleStatus(HandleStatusEnum.UN_NOTIFIED_CONFIRM_MODEL);
                    orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.NEED_HANDLE);
                }
                SpringUtils.getBean(IOrderService.class).createOrderFlow(List.of(orderVideo), OrderStatusEnum.NEED_FILLED, "确认提交预选模特");
                orderVideoFlowNodeDiagramService.setNodeCompleteTime(List.of(orderVideo.getId()), OrderVideoFlowNodeEnum.MATCHING_MODEL);
            }


            OrderVideoModelChangeDTO orderVideoModelChangeDTO = BeanUtil.copyProperties(orderVideo, OrderVideoModelChangeDTO.class);
            orderVideoModelChangeDTO.setSelectedTime(date);
            orderVideoModelChangeDTO.setVideoId(orderVideo.getId());
            orderVideoModelChangeDTO.setRollbackId(orderVideo.getRollbackId());
            orderVideoModelChangeDTO.setModelId(orderVideo.getShootModelId());
            orderVideoModelChangeDTO.setSource(OrderVideoModelChangeSourceEnum.SHOOT_MODEL.getCode());
            orderVideoModelChangeDTO.setScheduleType(orderVideoMatch.getScheduleType());
            orderVideoModelChangeDTO.setCarryType(orderVideoMatch.getCarryType());
            orderVideoModelChangeDTO.setCommission(orderVideoMatch.getCommission());
            orderVideoModelChangeDTO.setCommissionUnit(orderVideoMatch.getCommissionUnit());
            orderVideoModelChangeDTO.setOverstatement(orderVideoMatch.getOverstatement());

            //  减少模特单量
            orderVideoModelService.addOrderVideoModel(List.of(modelOrderDTO));

            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.SUBMIT_MODEL.getEventName(), null, OrderVideoOperateTypeEnum.SUBMIT_MODEL.getIsPublic(), null, List.of(OrderVideoOperateDTO.builder().videoId(orderVideo.getId()).eventContent(StrUtil.format(OrderVideoOperateTypeEnum.SUBMIT_MODEL.getEventContent(), modelInfoVO.getName())).build()));
            orderVideoModelChangeService.saveOrderVideoModelChange(orderVideoModelChangeDTO);

            orderVideoMatch.setEndTime(date);
            orderVideoMatch.setSubmitTime(date);
            orderVideoMatch.setUpdateBy(SecurityUtils.getUsername());
            orderVideoMatch.setUpdateById(SecurityUtils.getUserId());
            orderVideoMatch.setShootModelId(preselectModel.getModelId());
            orderVideoMatch.setShootModelAddType(preselectModel.getAddType());
            orderVideoMatch.setShootModelAccount(modelInfoVO.getAccount());
            orderVideoMatch.setShootModelName(modelInfoVO.getName());
            orderVideoMatch.setShootModelType(modelInfoVO.getType());
            orderVideoMatch.setShootModelPlatform(modelInfoVO.getPlatform());
            orderVideoMatch.setShootModelCooperation(modelInfoVO.getCooperation());
            orderVideoMatch.setShootModelPersonId(Optional.ofNullable(modelInfoVO.getPersons()).orElse(new ArrayList<>()).get(0).getId());
            orderVideoMatch.setShootModelPersonName(Optional.ofNullable(modelInfoVO.getPersons()).orElse(new ArrayList<>()).get(0).getName());
            baseMapper.updateById(orderVideoMatch);

            orderVideoMatchPreselectModelService.submitPreselectModelUpdateToOut(dto.getMatchId());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 商家更换模特 对旧的选定的模特进行淘汰
     *
     * @param videoId 视频订单id
     */
    @Override
    public void changeModel(Long videoId, String reason) {
        OrderVideoMatch orderVideoMatch = baseMapper.getActiveByVideoId(videoId);
        Assert.notNull(orderVideoMatch, "匹配单不存在");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId(), CacheConstants.ORDER_MATCH_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
            orderVideoMatchPreselectModelService.changeModel(orderVideoMatch.getId(), reason);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 批量新增匹配单
     */
    @Override
    public void saveBatchOrderVideoMatch(List<SaveBatchOrderVideoMatchDTO> dtoList) {
        List<OrderVideoMatch> list = baseMapper.selectListByVideoIds(dtoList.stream().map(SaveBatchOrderVideoMatchDTO::getVideoId).collect(Collectors.toList()));
        Map<Long, List<OrderVideoMatch>> orderVideoMatchMap = list.stream().collect(Collectors.groupingBy(OrderVideoMatch::getVideoId));

        List<SaveBatchOrderVideoMatchPreselectModelDTO> saveBatchOrderVideoMatchPreselectModelDTOS = new ArrayList<>();

        DateTime startTime = DateUtil.date();
        for (SaveBatchOrderVideoMatchDTO dto : dtoList) {
            OrderVideoMatch orderVideoMatch = new OrderVideoMatch();
            orderVideoMatch.setVideoId(dto.getVideoId());
            orderVideoMatch.setRollbackId(dto.getRollbackId());
            orderVideoMatch.setCount(orderVideoMatchMap.get(dto.getVideoId()) == null ? 1 : orderVideoMatchMap.get(dto.getVideoId()).size() + 1);
            orderVideoMatch.setStartTime(startTime);
            orderVideoMatch.setCreateBy(SecurityUtils.getUsername());
            orderVideoMatch.setCreateById(SecurityUtils.getUserId());
            if (Boolean.TRUE.equals(dto.getIsRollback())){
//                订单回退处理，暂停模特匹配
                orderVideoMatch.setStatus(OrderVideoMatchStatusEnum.PAUSE.getCode());
                orderVideoMatch.setEndTime(startTime);
                orderVideoMatch.setUpdateBy(SecurityUtils.getUsername());
                orderVideoMatch.setUpdateById(SecurityUtils.getUserId());
                orderVideoMatch.setPauseReason(OrderConstant.ROLLBACK_ORDER);
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.PAUSE_MATCH.getEventName(),
                        OrderVideoOperateTypeEnum.PAUSE_MATCH.getIsPublic(),
                        OrderVideoOperateDTO.builder()
                                .videoId(orderVideoMatch.getVideoId())
                                .eventContent(StrUtil.format(
                                        OrderVideoOperateTypeEnum.PAUSE_MATCH.getEventContent(),
                                        OrderVideoOperateTypeEnum.ROLLBACK_ORDER.getEventName())
                                ).build());
            }
            baseMapper.insert(orderVideoMatch);
            baseMapper.updateShuffledSortKey(orderVideoMatch.getId());

            if (ObjectUtil.isNotNull(dto.getModelId()) && !Boolean.TRUE.equals(dto.getIsRollback())) {
                SaveBatchOrderVideoMatchPreselectModelDTO saveBatchOrderVideoMatchPreselectModelDTO = new SaveBatchOrderVideoMatchPreselectModelDTO();
                saveBatchOrderVideoMatchPreselectModelDTO.setMatchId(orderVideoMatch.getId());
                saveBatchOrderVideoMatchPreselectModelDTO.setModelId(dto.getModelId());
                saveBatchOrderVideoMatchPreselectModelDTO.setAddType(PreselectModelAddTypeEnum.INTENTION_MODEL);
                saveBatchOrderVideoMatchPreselectModelDTO.setAddTime(startTime);
                saveBatchOrderVideoMatchPreselectModelDTOS.add(saveBatchOrderVideoMatchPreselectModelDTO);
            }
        }
        orderVideoMatchPreselectModelService.saveBatchOrderVideoMatchPreselectModel(saveBatchOrderVideoMatchPreselectModelDTOS);
    }

    /**
     * 组装订单池列表数据
     */
    private <T extends OrderPoolListVO> void assembleOrderPoolList(List<T> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> videoIds = list.stream().map(OrderPoolListVO::getVideoId).filter(Objects::nonNull).collect(Collectors.toList());

        //  查询参考图资源
        List<String> referencePicIds = list.stream().map(OrderPoolListVO::getReferencePicId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));

        //  查询视频订单注意事项的图片
        List<OrderVideo> orderVideos = SpringUtils.getBean(IOrderVideoService.class).listByIds(videoIds);
        List<String> cautionsPicIds = orderVideos.stream().map(OrderVideo::getCautionsPicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<String> particularEmphasisPicIds = orderVideos.stream().map(OrderVideo::getParticularEmphasisPicIds).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cautionsPicIds)) {
            resourceIds.addAll(StringUtils.splitToLong(cautionsPicIds, StrUtil.COMMA));
        }
        if (CollUtil.isNotEmpty(particularEmphasisPicIds)) {
            resourceIds.addAll(StringUtils.splitToLong(particularEmphasisPicIds, StrUtil.COMMA));
        }
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);
        Map<Long, OrderVideo> orderVideoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, Function.identity()));

        //  查询意向模特 和 拍摄模特 和 预选模特
        List<Long> intentionModelIds = list.stream().map(OrderPoolListVO::getIntentionModelId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> shootModelIds = list.stream().map(OrderPoolListVO::getShootModelId).filter(Objects::nonNull).collect(Collectors.toList());

        List<Long> matchIds = list.stream().map(OrderPoolListVO::getId).collect(Collectors.toList());
        List<OrderVideoMatchPreselectModel> orderVideoMatchPreselectModels = orderVideoMatchPreselectModelService.selectActiveListByMatchIdsOfOrderVideoList(matchIds);
        Map<Long, List<OrderVideoMatchPreselectModel>> orderVideoMatchPreselectModelMap = orderVideoMatchPreselectModels.stream().collect(Collectors.groupingBy(OrderVideoMatchPreselectModel::getMatchId));
        List<Long> preselectModelIds = orderVideoMatchPreselectModels.stream().map(OrderVideoMatchPreselectModel::getModelId).collect(Collectors.toList());

        intentionModelIds.addAll(shootModelIds);
        intentionModelIds.addAll(preselectModelIds);
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(intentionModelIds);

        //  查询拍摄要求、注意事项
        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIds(videoIds);
        Map<Long, List<OrderVideoContent>> orderVideoContentMap = orderVideoContents.stream().collect(Collectors.groupingBy(OrderVideoContent::getVideoId));

        //  查询对接人（中文部客服）、出单人（英文部客服）
        List<Long> contactIds = list.stream().map(OrderPoolListVO::getContactId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> issueIds = list.stream().map(OrderPoolListVO::getIssueId).filter(Objects::nonNull).collect(Collectors.toList());
        contactIds.addAll(issueIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        //  查询有备注的视频订单ID
        List<Long> commentVideoIds = orderVideoCommentService.getVideoIds();

        //  查询视频订单退款信息
        OrderVideoRefundSimpleListVO orderVideoRefundSimpleListVO = SpringUtils.getBean(IOrderService.class).getVideoRefundMap(videoIds);

        for (OrderPoolListVO orderPoolListVO : list) {
            List<Long> referencePicLongIds = StringUtils.splitToLong(orderPoolListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicLongIds) {
                orderPoolListVO.getReferencePic().add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
            }
            orderPoolListVO.setIntentionModel(modelSimpleMap.get(orderPoolListVO.getIntentionModelId()));
            orderPoolListVO.setShootModel(modelSimpleMap.get(orderPoolListVO.getShootModelId()));
            List<OrderVideoMatchPreselectModel> preselectModels = orderVideoMatchPreselectModelMap.get(orderPoolListVO.getId());
            if (CollUtil.isNotEmpty(preselectModels)) {
                preselectModels.sort(Comparator.comparing(OrderVideoMatchPreselectModel::getAddTime).reversed());
                for (OrderVideoMatchPreselectModel preselectModel : preselectModels) {
                    orderPoolListVO.getPreselectModels().add(modelSimpleMap.get(preselectModel.getModelId()));
                }
                orderPoolListVO.setPreselectModelList(BeanUtil.copyToList(preselectModels, OrderVideoMatchPreselectModelVO.class));
            }
            if (ObjectUtil.isNotNull(orderPoolListVO.getPicCount())) {
                orderPoolListVO.setSurplusPicCount(PicCountEnum.getValue(orderPoolListVO.getPicCount()) - Optional.ofNullable(orderPoolListVO.getRefundPicCount()).orElse(0));
            } else {
                orderPoolListVO.setSurplusPicCount(0);
            }

            List<OrderVideoContent> videoContents = orderVideoContentMap.get(orderPoolListVO.getVideoId());
            if (CollUtil.isNotEmpty(videoContents)) {
                List<OrderVideoContent> shootRequired = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> cautions = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> orderSpecificationRequire = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> particularEmphasis = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                List<OrderVideoContent> sellingPointProducts = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                orderPoolListVO.setShootRequired(shootRequired);

                OrderVideoCautionsVO orderVideoCautionsVO = new OrderVideoCautionsVO();
                OrderVideo orderVideo = orderVideoMap.get(orderPoolListVO.getVideoId());
                if (CharSequenceUtil.isNotBlank(orderVideo.getCautionsPicId())) {
                    for (Long cautionsPicId : StringUtils.splitToLong(orderVideo.getCautionsPicId(), StrUtil.COMMA)) {
                        orderVideoCautionsVO.getCautionsPics().add(resourceMap.getOrDefault(cautionsPicId, new OrderResource()).getObjectKey());
                    }
                }
                List<String> particularEmphasisPic = new ArrayList<>();
                if (CharSequenceUtil.isNotBlank(orderVideo.getParticularEmphasisPicIds())) {
                    for (Long particularEmphasisPicId : StringUtils.splitToLong(orderVideo.getParticularEmphasisPicIds(), StrUtil.COMMA)) {
                        particularEmphasisPic.add(resourceMap.getOrDefault(particularEmphasisPicId, new OrderResource()).getObjectKey());
                    }
                }
                orderPoolListVO.setParticularEmphasisPic(particularEmphasisPic);
                orderVideoCautionsVO.setCautions(cautions);
                orderPoolListVO.setOrderVideoCautionsVO(orderVideoCautionsVO);
                orderPoolListVO.setOrderSpecificationRequire(CollUtil.isNotEmpty(orderSpecificationRequire) ? orderSpecificationRequire.get(0).getContent() : null);
                orderPoolListVO.setParticularEmphasis(CollUtil.isNotEmpty(particularEmphasis) ? particularEmphasis.get(0).getContent() : null);
                orderPoolListVO.setSellingPointProduct(CollUtil.isNotEmpty(sellingPointProducts) ? sellingPointProducts.get(0).getContent() : null);
            }

            orderPoolListVO.setContact(userMap.get(orderPoolListVO.getContactId()));
            orderPoolListVO.setIssue(userMap.get(orderPoolListVO.getIssueId()));
            orderPoolListVO.setHasComment(commentVideoIds.contains(orderPoolListVO.getVideoId()));

            orderPoolListVO.setOrderVideoRefund(orderVideoRefundSimpleListVO.getMapVo().get(orderPoolListVO.getVideoId()));

            // 通品/非通品优质模特时间限制判断逻辑
            boolean isLimitedToQualityModel = false;
            OrderVideo orderVideo = orderVideoMap.get(orderPoolListVO.getVideoId());
            if (ObjectUtil.isNotNull(orderVideo) && ObjectUtil.isNotNull(orderVideo.getIsGund())) {
                Date orderFirstMatchTime = orderVideo.getFirstMatchTime();
                if (orderFirstMatchTime != null) {
                    long daysElapsed = DateUtil.betweenDay(orderFirstMatchTime, new Date(), true);
                    if (daysElapsed <= BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                        if (StatusTypeEnum.YES.getCode().equals(orderVideo.getIsGund())) {
                            // 通品：3天内（第0、1、2天）仅可选择优质模特
                            if (daysElapsed < BusinessConstants.COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                                isLimitedToQualityModel = true;
                            }
                        } else {
                            // 非通品：1天内（当天，第0天）仅可选择优质模特
                            if (daysElapsed < BusinessConstants.NON_COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD) {
                                isLimitedToQualityModel = true;
                            }
                        }
                    }
                }
            }
            orderPoolListVO.setIsLimitedToQualityModel(isLimitedToQualityModel);

        }
    }

    /**
     * 组装订单池查询条件
     */
    private void assembleCondition(OrderPoolListDTO orderPoolListDTO) {
        if (!orderPoolListDTO.getIsPendingSubmit()) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            orderPoolListDTO.setBackUserRelevanceModelIds(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toList()));
            if (CollUtil.isNotEmpty(orderPoolListDTO.getIssueIds())){
                orderPoolListDTO.setEnglishCustomerServiceModelIds(remoteService.selectBackUserRelevanceModelByUserIds(orderPoolListDTO.getIssueIds()).stream().map(ModelPerson::getModelId).collect(Collectors.toList()));
            }
        }
        orderPoolListDTO.setCurrentUserIsAdmin(SecurityUtils.currentUserIsAdmin());

        // 设置新的预选管理权限控制
        orderPoolListDTO.setCanViewAllPreselection(hasSelectionManagementPermission());

        if (CharSequenceUtil.isNotBlank(orderPoolListDTO.getKeyword()) || CollUtil.isNotEmpty(orderPoolListDTO.getModelCooperations())) {
            List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().name(orderPoolListDTO.getKeyword()).cooperation(orderPoolListDTO.getModelCooperations()).build());
            orderPoolListDTO.setIntentionModelIds(modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toSet()));
            orderPoolListDTO.setPreselectModelIds(modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toSet()));
        }

        if (ObjectUtil.isNotNull(orderPoolListDTO.getPreselection())) {
            OrderPoolPreselectionEnum orderPoolPreselectionEnum = OrderPoolPreselectionEnum.getByCode(orderPoolListDTO.getPreselection());
            orderPoolListDTO.setPreselectionBegin(orderPoolPreselectionEnum.getBegin());
            orderPoolListDTO.setPreselectionEnd(orderPoolPreselectionEnum.getEnd());
        }

        if (CollUtil.isNotEmpty(orderPoolListDTO.getCounts())) {
            List<String> countSqlList = new ArrayList<>();
            for (Integer count : orderPoolListDTO.getCounts()) {
                OrderPoolMatchCountEnum orderPoolMatchCountEnum = OrderPoolMatchCountEnum.getByCode(count);
                countSqlList.add(StrUtil.builder().append(orderPoolMatchCountEnum.getLogic()).append(CharSequenceUtil.SPACE).append(orderPoolMatchCountEnum.getCount()).toString());
            }
            orderPoolListDTO.setCountSqlList(countSqlList);
        }

        Map<String, String> matchStartTimeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderPoolListDTO.getMatchStartTimes())) {
            orderPoolListDTO.getMatchStartTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    matchStartTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    matchStartTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    matchStartTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    matchStartTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
        }
        orderPoolListDTO.setMatchStartTimeMap(matchStartTimeMap);

        // 处理日期范围查询，确保包含结束日期当天的所有数据
        // 处理匹配开始时间范围查询
        if (orderPoolListDTO.getBeforeMatchStartTimeStart() != null && orderPoolListDTO.getBeforeMatchStartTimeEnd() != null) {

            // 结束时间需要调整到指定日期的23:59:59，确保包含当天所有数据
            Date endTime = DateUtil.endOfDay(orderPoolListDTO.getBeforeMatchStartTimeEnd());
            orderPoolListDTO.setBeforeMatchStartTimeEnd(endTime);
        }

        // 处理匹配结束时间范围查询（如果存在的话）
        if (orderPoolListDTO.getBeforeMatchEndTimeStart() != null && orderPoolListDTO.getBeforeMatchEndTimeEnd() != null) {
            // 结束时间调整到指定日期的23:59:59
            Date endTime = DateUtil.endOfDay(orderPoolListDTO.getBeforeMatchEndTimeEnd());
            orderPoolListDTO.setBeforeMatchEndTimeEnd(endTime);
        }
    }

    /**
     * 校验匹配单
     */
    private void checkOrderVideoMatch(OrderVideoMatch orderVideoMatch) {
        Assert.notNull(orderVideoMatch, "匹配单不存在");
        Assert.isTrue(OrderVideoMatchStatusEnum.NORMAL.getCode().equals(orderVideoMatch.getStatus()), "匹配单已结束");
        Assert.isNull(orderVideoMatch.getEndTime(), "匹配单已结束");
    }

    /**
     * 更新匹配单 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideoMatchBatchFieldNullToNull(List<OrderVideoMatch> carryMatches) {
        for (OrderVideoMatch carryMatch : carryMatches) {
            baseMapper.updateOrderVideoMatchFieldNullToNull(carryMatch);
        }
    }

    /**
     * 检查当前用户是否有预选管理-沟通中的查看全部权限
     * 注意：直接从数据库查询最新权限，确保数据实时性
     * @return true-可以查看全部，false-只能查看自己的
     */
    public boolean hasSelectionManagementPermission() {
        try {
            Long userId = SecurityUtils.getUserId();
            // 直接从数据库查询最新的用户权限信息，确保数据实时性
            SysUser latestUserInfo = remoteService.selectUserById(userId);
            return latestUserInfo != null && SelectionManagementEnum.VIEW_ALL.getCode().equals(latestUserInfo.getSelectionManagement());
        } catch (Exception e) {
            // 如果获取用户信息失败，默认返回false（只能查看自己的）
            return false;
        }
    }
}
