package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableOrderScheduledRecordDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledRecordVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledTagCountVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:03
 */
public interface ModelDataTableService {

    /**
     * 排单记录
     */
    List<ModelDataTableOrderScheduledRecordVO> selectModelDataTableOrderScheduledRecordList(ModelDataTableOrderScheduledRecordDTO dto);

    /**
     * 排单记录-标签统计
     */
    ModelDataTableOrderScheduledTagCountVO getModelDataTableOrderScheduledTagCount(Long modelId);
}
