package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay;
import com.wnkx.order.mapper.OrderVideoDataStatisticsDayMapper;
import com.wnkx.order.service.OrderVideoDataStatisticsDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 
 * @Date 2025-06-04 10:38:12 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoDataStatisticsDayServiceImpl extends ServiceImpl<OrderVideoDataStatisticsDayMapper, OrderVideoDataStatisticsDay> implements OrderVideoDataStatisticsDayService {

    /**
     * 通过日期查询客服数据
     */
    @Override
    public OrderVideoDataStatisticsDay getByWriteTime(String date) {
        return baseMapper.getByWriteTime(date);
    }
}
