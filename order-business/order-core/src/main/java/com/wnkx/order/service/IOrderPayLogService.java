package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.OrderPayLogDTO;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_pay_log(订单支付记录表)】的数据库操作Service
* @createDate 2024-12-18 09:10:26
*/
@Validated
public interface IOrderPayLogService extends IService<OrderPayLog> {

    /**
     * 保存订单支付记录
     * @param dto
     * @return
     */

    OrderPayLog saveOrderPayLog(@Valid OrderPayLogDTO dto);

    /**
     * 初始化商家后 填充商家ID数据
     * @param orderNum      订单号
     * @param businessId    商家ID
     */
    void setBusinessId(String orderNum, Long businessId);

    /**
     * 根据订单号列表获取支付流水数据
     * @param orderNums
     * @return
     */
    List<OrderPayLog> getOrderPayLogListByOrderNums(List<String> orderNums);
}
