package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.wnkx.order.mapper.OrderResourceMapper;
import com.wnkx.order.service.OrderResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/26 10:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderResourceServiceImpl extends ServiceImpl<OrderResourceMapper, OrderResource> implements OrderResourceService {


    /**
     * 通过id查询数据(map)
     */
    @Override
    public Map<Long, OrderResource> getResourceMapByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<OrderResource> orderResources = baseMapper.selectBatchIds(ids);
        return orderResources.stream().collect(Collectors.toMap(OrderResource::getId, Function.identity()));
    }

    /**
     * 通过id查询数据
     */
    @Override
    public List<OrderResource> selectListByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(ids);
    }

    /**
     * 新增数据
     */
    @Override
    public Long saveOrderResource(String objectKey) {
        if (StrUtil.isBlank(objectKey)) {
            return null;
        }
        OrderResource build = OrderResource.builder().objectKey(objectKey).build();
        baseMapper.insert(build);
        return build.getId();
    }

    /**
     * 批量新增数据
     */
    @Override
    public List<Long> saveBatchOrderResource(Collection<String> objectKeys) {
        if (CollUtil.isEmpty(objectKeys)) {
            return Collections.emptyList();
        }
        List<OrderResource> orderResources = objectKeys.stream().map(objectKey ->
                OrderResource.builder().objectKey(objectKey).build()
        ).collect(Collectors.toList());
        baseMapper.saveBatch(orderResources);
        return orderResources.stream().map(OrderResource::getId).collect(Collectors.toList());
    }
}
