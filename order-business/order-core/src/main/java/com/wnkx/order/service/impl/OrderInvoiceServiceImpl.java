package com.wnkx.order.service.impl;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.config.OrderInvoiceProperties;
import com.wnkx.order.mapper.OrderInvoiceMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单_发票Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-17
 */
@Service
@RequiredArgsConstructor
public class OrderInvoiceServiceImpl extends ServiceImpl<OrderInvoiceMapper, OrderInvoice>
        implements IOrderInvoiceService {

    private final RemoteService remoteService;
    private final IOrderVideoService orderVideoService;
    private final OrderInvoiceOrderService orderInvoiceOrderService;
    private final OrderInvoiceOrderVideoService orderInvoiceOrderVideoService;
    private final OrderInvoiceOperateService orderInvoiceOperateService;
    private final OrderInvoiceRedService orderInvoiceRedService;
    private final OrderInvoiceRecordService orderInvoiceRecordService;
    private final IOrderMemberService orderMemberService;
    private final OrderInvoiceProperties orderInvoiceProperties;
    private final OrderInvoiceReopenService orderInvoiceReopenService;
    private final IOrderPayLogService orderPayLogService;

    /**
     * 通过订单号获取开票信息
     */
    @Override
    public OrderInvoice getLastInvoiceByOrderNum(String orderNum) {
        return baseMapper.getLastInvoiceByOrderNum(orderNum);
    }

    /**
     * 提现申请通过后 对发票的处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawalSuccess(List<WithdrawalSuccessDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }
        List<WithdrawalSuccessDTO> videoList = new ArrayList<>();
        List<WithdrawalSuccessDTO> rechargeList = new ArrayList<>();
        for (WithdrawalSuccessDTO item : dtoList){
            if (StrUtil.isNotBlank(item.getVideoCode())){
                videoList.add(item);
            }
            if (StrUtil.isNotBlank(item.getPrepayNum())){
                rechargeList.add(item);
            }
        }
        saveRecharge(rechargeList);

        if (CollUtil.isEmpty(videoList)) {
            return;
        }
        List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = orderInvoiceOrderVideoService.selectListByVideoCodes(videoList.stream().map(WithdrawalSuccessDTO::getVideoCode).collect(Collectors.toList()));
        if (CollUtil.isEmpty(orderInvoiceOrderVideos)) {
            return;
        }

        List<Long> invoiceOrderIds = orderInvoiceOrderVideos.stream().map(OrderInvoiceOrderVideo::getInvoiceOrderId).collect(Collectors.toList());
        List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.listByIds(invoiceOrderIds);
        if (CollUtil.isEmpty(orderInvoiceOrders)) {
            return;
        }

        List<Long> invoiceIds = orderInvoiceOrders.stream().map(OrderInvoiceOrder::getInvoiceId).collect(Collectors.toList());
        List<OrderInvoice> orderInvoices = baseMapper.selectOrderInvoiceListByIdsAndStatus(invoiceIds, List.of(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode(), OrderInvoiceStatusEnum.UN_INVOICE.getCode(), OrderInvoiceStatusEnum.UN_CONFIRM.getCode(), OrderInvoiceStatusEnum.DELIVER.getCode()));
        if (CollUtil.isEmpty(orderInvoices)) {
            return;
        }
        List<OrderInvoiceOperate> orderInvoiceOperates = new ArrayList<>();

        Map<Long, List<OrderInvoiceOrder>> orderInvoiceVideoMap = orderInvoiceOrders.stream().collect(Collectors.groupingBy(OrderInvoiceOrder::getInvoiceId));
        Map<Long, List<OrderInvoiceOrderVideo>> orderInvoiceOrderVideoMap = orderInvoiceOrderVideos.stream().collect(Collectors.groupingBy(OrderInvoiceOrderVideo::getInvoiceOrderId));

        //  需要红冲的发票
        Map<Long, List<WithdrawalSuccessDTO>> needRedPushInvoice = new HashMap<>();

        Map<String, List<WithdrawalSuccessDTO>> dtoMap = videoList.stream().collect(Collectors.groupingBy(WithdrawalSuccessDTO::getVideoCode));

        for (OrderInvoice orderInvoice : orderInvoices) {
            //  旧数据过滤
            if (ObjectUtil.isNotNull(orderInvoice.getInvoicingTime())
                    && ObjectUtil.compare(orderInvoice.getInvoicingTime(), DateUtil.parse(orderInvoiceProperties.getOldDataEndTime())) < 0) {
                continue;
            }
            if (OrderInvoiceTypeEnum.PROFORMA_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
                continue;
            }
            List<OrderInvoiceOrder> orDefault = orderInvoiceVideoMap.get(orderInvoice.getId());
            if (CollUtil.isEmpty(orDefault)) {
                continue;
            }
            List<WithdrawalSuccessDTO> withdrawalSuccessDTOS = new ArrayList<>();
            for (OrderInvoiceOrder orderInvoiceOrder : orDefault) {
                List<OrderInvoiceOrderVideo> invoiceOrderVideos = orderInvoiceOrderVideoMap.get(orderInvoiceOrder.getId());
                if (CollUtil.isEmpty(invoiceOrderVideos)) {
                    continue;
                }
                for (OrderInvoiceOrderVideo invoiceOrderVideo : invoiceOrderVideos) {
                    List<WithdrawalSuccessDTO> withdrawalSuccessDTO = dtoMap.get(invoiceOrderVideo.getVideoCode());
                    if (CollUtil.isNotEmpty(withdrawalSuccessDTO)) {
                        for (WithdrawalSuccessDTO successDTO : withdrawalSuccessDTO) {
                            successDTO.setOrderNum(orderInvoiceOrder.getOrderNum());
                            successDTO.setVideoId(invoiceOrderVideo.getVideoId());
                            withdrawalSuccessDTOS.add(successDTO);
                        }
                    }
                }
            }

            if (OrderInvoiceStatusEnum.DELIVER.getCode().equals(orderInvoice.getStatus())) {
                needRedPushInvoice.put(orderInvoice.getId(), withdrawalSuccessDTOS);
            }

            if (CollUtil.isNotEmpty(withdrawalSuccessDTOS)) {
                OrderInvoiceOperate orderInvoiceOperate = new OrderInvoiceOperate();
                orderInvoiceOperate.setInvoiceId(orderInvoice.getId());
                orderInvoiceOperate.setType(OrderInvoiceOperateTypeEnum.RED_ALERT.getCode());
                orderInvoiceOperate.setContent(CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.RED_ALERT.getEventContent(), withdrawalSuccessDTOS.stream().map(WithdrawalSuccessDTO::getWithdrawDepositAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                orderInvoiceOperates.add(orderInvoiceOperate);
            }
        }

        orderInvoiceRedService.withdrawalSuccess(needRedPushInvoice);
        orderInvoiceOperateService.saveBatchOrderInvoiceOperate(orderInvoiceOperates);
    }

    private void saveRecharge(List<WithdrawalSuccessDTO> rechargeList) {
        if (CollUtil.isEmpty(rechargeList)){
            return;
        }
        List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByOrderNums(rechargeList.stream().map(WithdrawalSuccessDTO::getPrepayNum).collect(Collectors.toList()));
        if (CollUtil.isEmpty(orderInvoiceOrders)){
            return;
        }
        List<Long> invoiceIds = orderInvoiceOrders.stream().map(OrderInvoiceOrder::getInvoiceId).collect(Collectors.toList());
        List<OrderInvoice> orderInvoices = baseMapper.selectOrderInvoiceListByIdsAndStatus(invoiceIds, List.of(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode(), OrderInvoiceStatusEnum.UN_INVOICE.getCode(), OrderInvoiceStatusEnum.UN_CONFIRM.getCode(), OrderInvoiceStatusEnum.DELIVER.getCode()));
        if (CollUtil.isEmpty(orderInvoices)) {
            return;
        }
        List<OrderInvoiceOperate> orderInvoiceOperates = new ArrayList<>();

        Map<Long, List<OrderInvoiceOrder>> orderInvoiceVideoMap = orderInvoiceOrders.stream().collect(Collectors.groupingBy(OrderInvoiceOrder::getInvoiceId));
        //  需要红冲的发票
        Map<Long, List<WithdrawalSuccessDTO>> needRedPushInvoice = new HashMap<>();

        Map<String, List<WithdrawalSuccessDTO>> dtoMap = rechargeList.stream().collect(Collectors.groupingBy(WithdrawalSuccessDTO::getPrepayNum));

        for (OrderInvoice orderInvoice : orderInvoices) {
            //  旧数据过滤
            if (ObjectUtil.isNotNull(orderInvoice.getInvoicingTime())
                    && ObjectUtil.compare(orderInvoice.getInvoicingTime(), DateUtil.parse(orderInvoiceProperties.getOldDataEndTime())) < 0) {
                continue;
            }
            if (OrderInvoiceTypeEnum.PROFORMA_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
                continue;
            }
            List<OrderInvoiceOrder> orDefault = orderInvoiceVideoMap.get(orderInvoice.getId());
            if (CollUtil.isEmpty(orDefault)) {
                continue;
            }
            List<WithdrawalSuccessDTO> withdrawalSuccessDTOS = new ArrayList<>();
            for (OrderInvoiceOrder orderInvoiceOrder : orDefault) {
                List<WithdrawalSuccessDTO> withdrawalSuccessDTO = dtoMap.get(orderInvoiceOrder.getOrderNum());
                if (CollUtil.isNotEmpty(withdrawalSuccessDTO)) {
                    for (WithdrawalSuccessDTO successDTO : withdrawalSuccessDTO) {
                        successDTO.setOrderNum(orderInvoiceOrder.getOrderNum());
                        withdrawalSuccessDTOS.add(successDTO);
                    }
                }
            }

            if (OrderInvoiceStatusEnum.DELIVER.getCode().equals(orderInvoice.getStatus())) {
                needRedPushInvoice.put(orderInvoice.getId(), withdrawalSuccessDTOS);
            }

            if (CollUtil.isNotEmpty(withdrawalSuccessDTOS)) {
                OrderInvoiceOperate orderInvoiceOperate = new OrderInvoiceOperate();
                orderInvoiceOperate.setInvoiceId(orderInvoice.getId());
                orderInvoiceOperate.setType(OrderInvoiceOperateTypeEnum.RED_ALERT.getCode());
                orderInvoiceOperate.setContent(CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.RED_ALERT.getEventContent(), withdrawalSuccessDTOS.stream().map(WithdrawalSuccessDTO::getWithdrawDepositAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                orderInvoiceOperates.add(orderInvoiceOperate);
            }
        }

        orderInvoiceRedService.withdrawalSuccess(needRedPushInvoice);
        orderInvoiceOperateService.saveBatchOrderInvoiceOperate(orderInvoiceOperates);
    }

    /**
     * 运营端-发票管理-开票金额统计
     */
    @Override
    public InvoiceAmountStatisticsVO invoiceAmountStatistics() {
        InvoiceAmountStatisticsVO statisticsVO = Optional.ofNullable(baseMapper.invoiceAmountStatistics()).orElse(new InvoiceAmountStatisticsVO());
        statisticsVO.setAmountToBeFlushed(orderInvoiceRedService.getToBeRedInvoiceAmount());

        return statisticsVO;
    }

    /**
     * 运营端-发票管理-数量统计
     */
    @Override
    public BackInvoiceStatisticsVO backInvoiceStatistics() {
        BackInvoiceStatisticsVO statisticsVO = Optional.ofNullable(baseMapper.backInvoiceStatistics()).orElse(new BackInvoiceStatisticsVO());
        statisticsVO.setQuantityToBeFlushed(Convert.toInt(orderInvoiceRedService.getToBeRedInvoiceCount()));

        return statisticsVO;
    }

    /**
     * 商家端-发票管理-数量统计
     */
    @Override
    public CompanyInvoiceStatisticsVO companyInvoiceStatistics() {
        CompanyInvoiceStatisticsVO statisticsVO = Optional.ofNullable(baseMapper.companyInvoiceStatistics(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())).orElse(new CompanyInvoiceStatisticsVO());
        statisticsVO.setUnbilledQuantity(selectCompanyNotInvoicedListByCondition(new CompanyNotInvoicedListDTO()).size());
        return statisticsVO;
    }

    /**
     * 商家端-发票管理-申请重开
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyForReopening(ApplyForReopeningDTO dto) {
        OrderInvoice orderInvoice = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.DELIVER.getCode().equals(orderInvoice.getStatus()) && StatusTypeEnum.YES.getCode().equals(orderInvoice.getIsApplyReopen()), "请刷新页面后重试~");
        Assert.isTrue(ObjectUtil.compare(orderInvoice.getInvoicingTime(), DateUtil.parse(orderInvoiceProperties.getOldDataEndTime())) >= 0, "申请失败。请联系蜗牛客服处理~");
        Assert.isTrue(dto.getInvoiceType().equals(orderInvoice.getInvoiceType()), "只允许重开相同类型的发票");

        removeInvoiceNewFlag(dto.getId());

        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
            //  增值税普票需红冲
            dto.setOrderType(orderInvoice.getOrderType());
            dto.setInvoiceAmount(orderInvoice.getInvoiceAmount());
            orderInvoiceRedService.applyForReopening(dto);
        } else {
            OrderInvoice saveOrderInvoice = new OrderInvoice();
            Long invoiceCount = baseMapper.getInvoiceCount();
            saveOrderInvoice.setTicketCode(OrderConstant.ORDER_INVOICE_CODE_PREFIX + (invoiceCount + 1));
            saveOrderInvoice.setSource(OrderInvoiceSourceEnum.MERCHANT_APPLICATION.getCode());
            saveOrderInvoice.setOrderType(orderInvoice.getOrderType());
            saveOrderInvoice.setMerchantId(orderInvoice.getMerchantId());
            saveOrderInvoice.setMerchantCode(orderInvoice.getMerchantCode());
            saveOrderInvoice.setInvoiceAmount(orderInvoice.getInvoiceAmount());
            saveOrderInvoice.setStatus(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode());
            saveOrderInvoice.setInvoiceType(orderInvoice.getInvoiceType());
            saveOrderInvoice.setCompanyName(dto.getCompanyName());
            saveOrderInvoice.setCompanyAddress(dto.getCompanyAddress());
            saveOrderInvoice.setCompanyPhone(dto.getCompanyPhone());
            saveOrderInvoice.setCompanyContact(dto.getCompanyContact());
            saveOrderInvoice.setAttachmentObjectKey(dto.getAttachmentObjectKey());
            saveOrderInvoice.setContent(dto.getContent());
            saveOrderInvoice.setInvoiceRemark(dto.getInvoiceRemark());
            saveOrderInvoice.setSubmitBy(SecurityUtils.getUsername());
            saveOrderInvoice.setSubmitById(SecurityUtils.getUserId());
            saveOrderInvoice.setSubmitTime(DateUtil.date());
            saveOrderInvoice.setIsNew(StatusTypeEnum.YES.getCode());
            saveOrderInvoice.setIsApplyReopen(StatusTypeEnum.NO.getCode());
            baseMapper.insert(saveOrderInvoice);

            //  批量添加发票关联视频订单
            List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByInvoiceIds(List.of(orderInvoice.getId()));
            orderInvoiceOrderService.saveBatchOrderInvoiceVideo(saveOrderInvoice, orderInvoiceOrders.stream().map(OrderInvoiceOrder::getOrderNum).collect(Collectors.toSet()), false);
            //  保存开票操作记录
            orderInvoiceOperateService.saveOrderInvoiceOperate(saveOrderInvoice.getId(), OrderInvoiceOperateTypeEnum.APPLY_FOR_BILLING, null);

            OrderInvoiceReopen orderInvoiceReopen = new OrderInvoiceReopen();
            orderInvoiceReopen.setOldInvoiceId(orderInvoice.getId());
            orderInvoiceReopen.setNewInvoiceId(saveOrderInvoice.getId());
            orderInvoiceReopenService.saveOrderInvoiceReopen(orderInvoiceReopen);

            orderInvoice.setStatus(OrderInvoiceStatusEnum.REOPENED.getCode());
        }
        orderInvoice.setIsApplyReopen(StatusTypeEnum.NO.getCode());
        baseMapper.updateById(orderInvoice);

        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.RE_BILLING, null);
    }

    /**
     * 商家端-发票管理-去除发票新标记
     */
    @Override
    public void removeInvoiceNewFlag(Long invoiceId) {
        baseMapper.removeInvoiceNewFlag(invoiceId);
    }

    /**
     * 运营端-发票管理-已完成列表-导出
     */
    @Override
    public void exportInvoiceFinishList(InvoiceFinishListDTO dto, HttpServletResponse response) {
        final List<InvoiceFinishListVO> invoiceFinishListVOS = selectInvoiceFinishListByCondition(dto);

        String lf = StrPool.LF;
        String defaultValue = StrPool.DASHED;

        List<InvoiceFinishListExportVO> invoiceFinishListExportVOS = invoiceFinishListVOS.stream().map(item -> {
            InvoiceFinishListExportVO invoiceFinishListExportVO = BeanUtil.copyProperties(item, InvoiceFinishListExportVO.class);
            if (CollUtil.isNotEmpty(item.getOrderNums())) {
                StringBuilder orderNumStr = StrUtil.builder();
                for (String orderNum : item.getOrderNums()) {
                    orderNumStr.append(orderNum).append(lf);
                }
                invoiceFinishListExportVO.setOrderNumStr(orderNumStr.toString());
            }

            String invoiceInfo;
            if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(item.getInvoiceType())) {
                StringBuilder builder = StrUtil.builder()
                        .append("发票抬头：").append(CharSequenceUtil.isNotBlank(item.getTitle()) ? item.getTitle() : defaultValue)
                        .append(lf)
                        .append("税号：").append(CharSequenceUtil.isNotBlank(item.getDutyParagraph()) ? item.getDutyParagraph() : defaultValue);
                if (CharSequenceUtil.isNotBlank(item.getCautions())) {
                    builder.append(lf)
                            .append("开票注意事项：").append(CharSequenceUtil.isNotBlank(item.getCautions()) ? item.getCautions() : CharSequenceUtil.EMPTY);
                }
                invoiceInfo = builder.toString();
            } else {
                invoiceInfo = StrUtil.builder()
                        .append("公司名称：").append(CharSequenceUtil.isNotBlank(item.getCompanyName()) ? item.getCompanyName() : defaultValue)
                        .append(lf)
                        .append("公司地址：").append(CharSequenceUtil.isNotBlank(item.getCompanyAddress()) ? item.getCompanyAddress() : defaultValue)
                        .append(lf)
                        .append("联系电话：").append(CharSequenceUtil.isNotBlank(item.getCompanyPhone()) ? item.getCompanyPhone() : defaultValue)
                        .append(lf)
                        .append("联系人：").append(CharSequenceUtil.isNotBlank(item.getCompanyContact()) ? item.getCompanyContact() : defaultValue)
                        .toString();
            }
            invoiceFinishListExportVO.setInvoiceInfo(invoiceInfo);

            if (ObjectUtil.equal(item.getOperatorByType(), UserTypeConstants.MANAGER_TYPE)) {
                invoiceFinishListExportVO.setOperatorUserName(Optional.ofNullable(item.getOperatorBack()).orElse(new UserVO()).getName());
            } else if (ObjectUtil.equal(item.getOperatorByType(), UserTypeConstants.USER_TYPE)) {
                invoiceFinishListExportVO.setOperatorUserName(Optional.ofNullable(item.getOperatorCompany()).orElse(new BusinessAccountDetailVO()).getName());
            }

            return invoiceFinishListExportVO;
        }).collect(Collectors.toList());

        ExcelUtil<InvoiceFinishListExportVO> util = new ExcelUtil<>(InvoiceFinishListExportVO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "已完成列表");
        util.exportExcel(response, invoiceFinishListExportVOS, "已完成列表");
    }

    /**
     * 商家/运营端-发票管理-已完成列表
     */
    @Override
    public List<InvoiceFinishListVO> selectInvoiceFinishListByCondition(InvoiceFinishListDTO dto) {
        if (CollUtil.isNotEmpty(dto.getStatus()) && dto.getStatus().contains(OrderInvoiceStatusEnum.DELIVER.getCode())) {
            dto.getStatus().add(OrderInvoiceStatusEnum.REOPENED.getCode());
        }
        dto.setCurrentUserType(SecurityUtils.getLoginUserType());
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE)) {
            dto.setCurrentBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
            if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
                List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantBySearchName(dto.getKeyword());
                dto.setKeywordCompanyUserIds(businessAccountDetailVOS.stream().map(BusinessAccountDetailVO::getId).collect(Collectors.toList()));
            }
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("oi.operator_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("oi.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<InvoiceFinishListVO> invoiceFinishListVOS = baseMapper.selectInvoiceFinishListByCondition(dto);
        if (CollUtil.isEmpty(invoiceFinishListVOS)) {
            return invoiceFinishListVOS;
        }

        List<Long> invoiceIds = invoiceFinishListVOS.stream().map(InvoiceFinishListVO::getId).collect(Collectors.toList());
        List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByInvoiceIds(invoiceIds);
        Map<Long, List<OrderInvoiceOrder>> orderInvoiceVideoMap = orderInvoiceOrders.stream().collect(Collectors.groupingBy(OrderInvoiceOrder::getInvoiceId));

        List<Long> backUserIds = invoiceFinishListVOS.stream().filter(item -> ObjectUtil.equal(item.getOperatorByType(), UserTypeConstants.MANAGER_TYPE)).map(InvoiceFinishListVO::getOperatorById).collect(Collectors.toList());
        List<Long> companyUserIds = invoiceFinishListVOS.stream().filter(item -> ObjectUtil.equal(item.getOperatorByType(), UserTypeConstants.USER_TYPE)).map(InvoiceFinishListVO::getOperatorById).collect(Collectors.toList());
        List<Long> submitByIds = invoiceFinishListVOS.stream().map(InvoiceFinishListVO::getSubmitById).collect(Collectors.toList());
        companyUserIds.addAll(submitByIds);

        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(backUserIds).build());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(BusinessAccountDetailDTO.builder().ids(companyUserIds).build());
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (InvoiceFinishListVO invoiceFinishListVO : invoiceFinishListVOS) {
            List<OrderInvoiceOrder> invoiceVideos = orderInvoiceVideoMap.getOrDefault(invoiceFinishListVO.getId(), new ArrayList<>());
            invoiceFinishListVO.setOrderNums(invoiceVideos.stream().map(OrderInvoiceOrder::getOrderNum).collect(Collectors.toSet()));

            if (ObjectUtil.equal(invoiceFinishListVO.getOperatorByType(), UserTypeConstants.MANAGER_TYPE)) {
                invoiceFinishListVO.setOperatorBack(userMap.get(invoiceFinishListVO.getOperatorById()));
            } else if (ObjectUtil.equal(invoiceFinishListVO.getOperatorByType(), UserTypeConstants.USER_TYPE)) {
                invoiceFinishListVO.setOperatorCompany(accountMap.get(invoiceFinishListVO.getOperatorById()));
            }
            invoiceFinishListVO.setSubmitter(accountMap.get(invoiceFinishListVO.getSubmitById()));
            if (OrderInvoiceStatusEnum.DELIVER.getCode().equals(invoiceFinishListVO.getStatus())
                    && ObjectUtil.compare(invoiceFinishListVO.getInvoicingTime(), DateUtil.parse(orderInvoiceProperties.getOldDataEndTime())) >= 0
                    && StatusTypeEnum.YES.getCode().equals(invoiceFinishListVO.getIsApplyReopen())) {
                invoiceFinishListVO.setCanApplyReopening(true);
            }
        }

        return invoiceFinishListVOS;
    }

    /**
     * 运营端-发票管理-红冲详情
     */
    @Override
    public OrderInvoiceRedDetailVO getRedInvoiceDetail(Long invoiceRedId) {
        return orderInvoiceRedService.getRedInvoiceDetail(invoiceRedId);
    }

    /**
     * 运营端-发票管理-标记红冲
     */
    @Override
    public void markRedInvoice(MarkRedInvoiceDTO dto) {
        orderInvoiceRedService.markRedInvoice(dto);
    }

    /**
     * 运营端-发票管理-开票记录
     */
    @Override
    public List<OrderInvoiceRecordVO> getInvoiceRecord(Long invoiceId) {
        return orderInvoiceRecordService.getInvoiceRecord(invoiceId);
    }

    /**
     * 运营端-发票管理-待红冲列表-导出
     */
    @Override
    public void exportToBeRedInvoiceList(ToBeRedInvoiceListDTO dto, HttpServletResponse response) {
        orderInvoiceRedService.exportToBeRedInvoiceList(dto, response);
    }

    /**
     * 运营端-发票管理-待红冲列表
     */
    @Override
    public List<ToBeRedInvoiceListVO> selectToBeRedInvoiceListByCondition(ToBeRedInvoiceListDTO dto) {
        return orderInvoiceRedService.selectToBeRedInvoiceListByCondition(dto);
    }

    /**
     * 运营端-发票管理-确认发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmInvoice(Long invoiceId) {
        OrderInvoice orderInvoice = baseMapper.selectById(invoiceId);
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.UN_CONFIRM.getCode().equals(orderInvoice.getStatus()), "发票状态不是待确认");

        orderInvoice.setStatus(OrderInvoiceStatusEnum.DELIVER.getCode());
        orderInvoice.setOperatorTime(DateUtil.date());
        orderInvoice.setOperatorById(SecurityUtils.getUserId());
        orderInvoice.setOperatorByType(SecurityUtils.getLoginUserType());
        baseMapper.updateById(orderInvoice);

        OrderInvoiceRecord orderInvoiceRecord = new OrderInvoiceRecord();
        orderInvoiceRecord.setInvoiceId(invoiceId);
        orderInvoiceRecord.setType(OrderInvoiceRecordTypeEnum.INVOICE.getCode());
        orderInvoiceRecord.setInvoiceAmount(orderInvoice.getInvoiceAmount());
        orderInvoiceRecord.setNumber(orderInvoice.getNumber());
        orderInvoiceRecord.setInvoicingTime(orderInvoice.getInvoicingTime());
        orderInvoiceRecord.setObjectKey(orderInvoice.getObjectKey());
        orderInvoiceRecord.setRemark(orderInvoice.getInvoiceRemark());
        orderInvoiceRecordService.saveInvoiceRecord(orderInvoiceRecord);
        orderInvoiceOperateService.saveOrderInvoiceOperate(invoiceId, OrderInvoiceOperateTypeEnum.AUDIT_INVOICE, null);
    }

    /**
     * 运营端-发票管理-重新上传发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reUploadInvoice(UploadInvoiceDTO dto) {
        OrderInvoice orderInvoice = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.UN_CONFIRM.getCode().equals(orderInvoice.getStatus()), "发票状态不是待确认");

        BeanUtil.copyProperties(dto, orderInvoice);
        orderInvoice.setOperatorByType(SecurityUtils.getLoginUserType());
        orderInvoice.setOperatorById(SecurityUtils.getUserId());
        orderInvoice.setOperatorTime(DateUtil.date());
        orderInvoice.setObjectKey(StrUtil.join(StrUtil.COMMA, dto.getObjectKeys()));
        baseMapper.updateById(orderInvoice);
        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.RE_UPLOAD_INVOICE, dto.getObjectKeys());
    }

    /**
     * 运营端-发票管理-上传发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadInvoice(UploadInvoiceDTO dto) {
        OrderInvoice orderInvoice = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.UN_INVOICE.getCode().equals(orderInvoice.getStatus()), "发票状态不是待开票");

        BeanUtil.copyProperties(dto, orderInvoice);
        orderInvoice.setStatus(OrderInvoiceStatusEnum.UN_CONFIRM.getCode());
        orderInvoice.setOperatorByType(SecurityUtils.getLoginUserType());
        orderInvoice.setOperatorById(SecurityUtils.getUserId());
        orderInvoice.setOperatorTime(DateUtil.date());
        orderInvoice.setObjectKey(StrUtil.join(StrUtil.COMMA, dto.getObjectKeys()));
        baseMapper.updateById(orderInvoice);

        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.UPLOAD_INVOICE, dto.getObjectKeys());
    }

    /**
     * 运营端-发票管理-修改发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoice(AuditInvoiceDTO dto) {
        OrderInvoice orderInvoice = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.UN_INVOICE.getCode().equals(orderInvoice.getStatus()),"发票状态不是待开票");
        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getTitle()) && CharSequenceUtil.isNotBlank(dto.getDutyParagraph()), "[发票抬头]和[发票税号]不能为空");
        } else {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getCompanyName())
                            && CharSequenceUtil.isNotBlank(dto.getCompanyAddress())
                    , "[公司名称]和[公司地址]不能为空");
        }

        StringBuilder eventContent = StrUtil.builder();
        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
            if (ObjectUtil.notEqual(orderInvoice.getInvoiceAmount(), dto.getInvoiceAmount())) {
                eventContent.append("\n原金额：").append(orderInvoice.getInvoiceAmount()).append("；新金额：").append(dto.getInvoiceAmount());
            }
            if (ObjectUtil.notEqual(orderInvoice.getTitle(), dto.getTitle())) {
                eventContent.append("\n原抬头：").append(orderInvoice.getTitle()).append("；新抬头：").append(dto.getTitle());
            }
            if (ObjectUtil.notEqual(orderInvoice.getDutyParagraph(), dto.getDutyParagraph())) {
                eventContent.append("\n原税号：").append(orderInvoice.getDutyParagraph()).append("；新税号：").append(dto.getDutyParagraph());
            }
        } else {
            if (ObjectUtil.notEqual(orderInvoice.getInvoiceAmount(), dto.getInvoiceAmount())) {
                eventContent.append("\n原金额：").append(orderInvoice.getInvoiceAmount()).append("；新金额：").append(dto.getInvoiceAmount());
            }
            if (ObjectUtil.notEqual(orderInvoice.getCompanyName(), dto.getCompanyName())) {
                eventContent.append("\n原公司名称：").append(orderInvoice.getCompanyName()).append("；新公司名称：").append(dto.getCompanyName());
            }
            if (ObjectUtil.notEqual(orderInvoice.getCompanyAddress(), dto.getCompanyAddress())) {
                eventContent.append("\n原公司地址：").append(orderInvoice.getCompanyAddress()).append("；新公司地址：").append(dto.getCompanyAddress());
            }
            if (ObjectUtil.notEqual(orderInvoice.getCompanyPhone(), dto.getCompanyPhone())) {
                eventContent.append("\n原联系电话：").append(CharSequenceUtil.isNotBlank(orderInvoice.getCompanyPhone()) ? orderInvoice.getCompanyPhone() : CharSequenceUtil.EMPTY).append("；新联系电话：").append(dto.getCompanyPhone());
            }
            if (ObjectUtil.notEqual(orderInvoice.getCompanyContact(), dto.getCompanyContact())) {
                eventContent.append("\n原联系人：").append(CharSequenceUtil.isNotBlank(orderInvoice.getCompanyContact()) ? orderInvoice.getCompanyContact() : CharSequenceUtil.EMPTY).append("；新联系人：").append(dto.getCompanyContact());
            }
        }
        if (CharSequenceUtil.isBlank(eventContent.toString())) {
            return;
        }
        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.MODIFY_BILLING_INFORMATION.getCode(), OrderInvoiceOperateTypeEnum.MODIFY_BILLING_INFORMATION.getEventContent() + eventContent);

        BeanUtil.copyProperties(dto, orderInvoice);
        baseMapper.updateById(orderInvoice);
    }

    /**
     * 运营端-发票管理-审核发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditInvoice(AuditInvoiceDTO dto) {
        OrderInvoice orderInvoice = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode().equals(orderInvoice.getStatus()),"发票状态不是待审核");
        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getTitle()) && CharSequenceUtil.isNotBlank(dto.getDutyParagraph()), "[发票抬头]和[发票税号]不能为空");
        } else {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getCompanyName())
                            && CharSequenceUtil.isNotBlank(dto.getCompanyAddress())
                    , "[公司名称]和[公司地址]不能为空");
        }

        StringBuilder eventContent = StrUtil.builder();
        if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(orderInvoice.getInvoiceType())) {
            if (ObjectUtil.notEqual(orderInvoice.getInvoiceAmount(), dto.getInvoiceAmount())
                    || ObjectUtil.notEqual(orderInvoice.getTitle(), dto.getTitle())
                    || ObjectUtil.notEqual(orderInvoice.getDutyParagraph(), dto.getDutyParagraph())
            ) {
                eventContent.append("，修改开票信息");
            }
        } else {
            if (ObjectUtil.notEqual(orderInvoice.getInvoiceAmount(), dto.getInvoiceAmount())
                    || ObjectUtil.notEqual(orderInvoice.getCompanyName(), dto.getCompanyName())
                    || ObjectUtil.notEqual(orderInvoice.getCompanyAddress(), dto.getCompanyAddress())
                    || ObjectUtil.notEqual(orderInvoice.getCompanyPhone(), dto.getCompanyPhone())
                    || ObjectUtil.notEqual(orderInvoice.getCompanyContact(), dto.getCompanyContact())
            ) {
                eventContent.append("，修改开票信息");
            }
        }
        if (CharSequenceUtil.isNotBlank(dto.getCautions())) {
            eventContent.append("\n开票注意事项：").append(dto.getCautions());
        }
        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.CONFIRMATION_BILLING.getCode(), OrderInvoiceOperateTypeEnum.CONFIRMATION_BILLING.getEventContent() + eventContent);

        BeanUtil.copyProperties(dto, orderInvoice);
        orderInvoice.setStatus(OrderInvoiceStatusEnum.UN_INVOICE.getCode());
        baseMapper.updateById(orderInvoice);
    }

    /**
     * 商家/运营端-发票管理-取消开票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelInvoice(Long invoiceId) {
        OrderInvoice orderInvoice = baseMapper.selectById(invoiceId);
        Assert.notNull(orderInvoice, "发票不存在");
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE)) {
            Assert.isTrue(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode().equals(orderInvoice.getStatus())
                            || OrderInvoiceStatusEnum.UN_INVOICE.getCode().equals(orderInvoice.getStatus())
                            || OrderInvoiceStatusEnum.UN_CONFIRM.getCode().equals(orderInvoice.getStatus())
                    , "当前状态不支持取消开票");
        } else {
            Assert.isTrue(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode().equals(orderInvoice.getStatus())
                    , "页面停留时间太久，请刷新~");
        }
        orderInvoice.setStatus(OrderInvoiceStatusEnum.CANCELED.getCode());
        orderInvoice.setOperatorByType(SecurityUtils.getLoginUserType());
        orderInvoice.setOperatorById(SecurityUtils.getUserId());
        orderInvoice.setOperatorTime(DateUtil.date());
        baseMapper.updateById(orderInvoice);
        orderInvoiceOperateService.saveOrderInvoiceOperate(invoiceId, OrderInvoiceOperateTypeEnum.COMPANY_CANCEL_BILLING, null);

        //  如果是重开而产生新的发票 将旧的发票的isApplyReopen改为1
        Long oldInvoiceId = orderInvoiceReopenService.getOldInvoiceReopenByNewInvoiceId(invoiceId);
        if (ObjectUtil.isNotNull(oldInvoiceId)) {
            OrderInvoice oldOrderInvoice = baseMapper.selectById(oldInvoiceId);
            Assert.notNull(oldOrderInvoice, "旧发票不存在");

            if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE)) {
                oldOrderInvoice.setIsApplyReopen(StatusTypeEnum.YES.getCode());
            }
            oldOrderInvoice.setStatus(OrderInvoiceStatusEnum.DELIVER.getCode());
            baseMapper.updateById(oldOrderInvoice);
        }
    }

    /**
     * 运营端-发票管理-流转记录
     */
    @Override
    public List<OrderInvoiceOperate> getInvoiceOperateRecord(Long invoiceId) {
        return orderInvoiceOperateService.getInvoiceOperateRecord(invoiceId);
    }

    /**
     * 运营端-发票管理-发票详情
     */
    @Override
    public OrderInvoiceDetailVO getBackInvoiceVideoList(Long invoiceId) {
        OrderInvoice orderInvoice = baseMapper.selectById(invoiceId);
        Assert.notNull(orderInvoice, "发票不存在");

        OrderInvoiceDetailVO detailVO = BeanUtil.copyProperties(orderInvoice, OrderInvoiceDetailVO.class);
        List<OrderInvoiceVideoBackVO> invoiceVideoList = orderInvoiceOrderService.getInvoiceVideoBackList(invoiceId);
        if (CollUtil.isNotEmpty(invoiceVideoList)) {
            List<String> orderNums = invoiceVideoList.stream().map(OrderInvoiceVideoBackVO::getOrderNum).collect(Collectors.toList());
            List<Order> orders = SpringUtils.getBean(IOrderService.class).selectListByOrderNums(orderNums);
            BigDecimal payAmount = orders.stream().map(Order::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            detailVO.setPayAmount(payAmount);
        }

        detailVO.setMerchant(remoteService.getBusinessVo(BusinessDTO.builder().id(orderInvoice.getMerchantId()).build()));
        detailVO.setOrderInvoiceVideoBackVOS(invoiceVideoList);

        if (OrderInvoiceStatusEnum.CANCELLATION.getCode().equals(detailVO.getStatus())) {
            //  如果是已作废状态 查询红冲关联订单
            detailVO.setOrderInvoiceRedOrderVideoVOS(orderInvoiceRedService.selectRedInvoiceVideoListByInvoiceId(detailVO.getId()));
        }

        return detailVO;
    }

    /**
     * 商家端-发票管理-发票详情
     */
    @Override
    public OrderInvoiceDetailVO getCompanyInvoiceDetail(Long invoiceId) {
        OrderInvoice orderInvoice = baseMapper.selectById(invoiceId);
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(ObjectUtil.equal(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId(), orderInvoice.getMerchantId()), "发票不存在");

        OrderInvoiceDetailVO detailVO = BeanUtil.copyProperties(orderInvoice, OrderInvoiceDetailVO.class);

        List<OrderInvoiceVideoCompanyVO> orderInvoiceVideoCompanyVOS = orderInvoiceOrderService.getInvoiceVideoCompanyList(invoiceId, orderInvoice.getOrderType());

        if (ObjectUtil.isNotNull(orderInvoice.getSubmitById())) {
            detailVO.setSubmitter(remoteService.getBusinessAccountByAccountId(orderInvoice.getSubmitById()));
        }
        detailVO.setOrderInvoiceVideoCompanyVOS(orderInvoiceVideoCompanyVOS);

        return detailVO;
    }

    /**
     * 运营端-发票管理-待开票列表-导出
     */
    @Override
    public void exportToBeInvoicedList(ToBeInvoicedListDTO dto, HttpServletResponse response) {
        final List<ToBeInvoicedListVO> toBeInvoicedListVOS = selectToBeInvoicedListByCondition(dto);

        String lf = StrPool.LF;
        String defaultValue = StrPool.DASHED;

        List<ToBeInvoicedListExportVO> toBeInvoicedListExportVOS = toBeInvoicedListVOS.stream().map(item -> {
            ToBeInvoicedListExportVO toBeInvoicedListExportVO = BeanUtil.copyProperties(item, ToBeInvoicedListExportVO.class);
            if (CollUtil.isNotEmpty(item.getOrderNums())) {
                StringBuilder orderNumStr = StrUtil.builder();
                for (String orderNum : item.getOrderNums()) {
                    orderNumStr.append(orderNum).append(lf);
                }
                toBeInvoicedListExportVO.setOrderNumStr(orderNumStr.toString());
            }

            String invoiceInfo;
            if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(item.getInvoiceType())) {
                StringBuilder builder = StrUtil.builder()
                        .append("发票抬头：").append(CharSequenceUtil.isNotBlank(item.getTitle()) ? item.getTitle() : defaultValue)
                        .append(lf)
                        .append("税号：").append(CharSequenceUtil.isNotBlank(item.getDutyParagraph()) ? item.getDutyParagraph() : defaultValue);
                if (CharSequenceUtil.isNotBlank(item.getCautions())) {
                    builder.append(lf)
                            .append("开票注意事项：").append(CharSequenceUtil.isNotBlank(item.getCautions()) ? item.getCautions() : CharSequenceUtil.EMPTY);
                }
                invoiceInfo = builder.toString();
            } else {
                invoiceInfo = StrUtil.builder()
                        .append("公司名称：").append(CharSequenceUtil.isNotBlank(item.getCompanyName()) ? item.getCompanyName() : defaultValue)
                        .append(lf)
                        .append("公司地址：").append(CharSequenceUtil.isNotBlank(item.getCompanyAddress()) ? item.getCompanyAddress() : defaultValue)
                        .append(lf)
                        .append("联系电话：").append(CharSequenceUtil.isNotBlank(item.getCompanyPhone()) ? item.getCompanyPhone() : defaultValue)
                        .append(lf)
                        .append("联系人：").append(CharSequenceUtil.isNotBlank(item.getCompanyContact()) ? item.getCompanyContact() : defaultValue)
                        .toString();
            }
            toBeInvoicedListExportVO.setInvoiceInfo(invoiceInfo);

            return toBeInvoicedListExportVO;
        }).collect(Collectors.toList());

        ExcelUtil<ToBeInvoicedListExportVO> util = new ExcelUtil<>(ToBeInvoicedListExportVO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "待开票列表");
        util.exportExcel(response, toBeInvoicedListExportVOS, "待开票列表");
    }

    /**
     * 商家/运营端-发票管理-待开票列表
     */
    @Override
    public List<ToBeInvoicedListVO> selectToBeInvoicedListByCondition(ToBeInvoicedListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("oi.submit_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("oi.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        dto.setCurrentUserType(SecurityUtils.getLoginUserType());
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE)) {
            dto.setCurrentBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
            if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
                List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantBySearchName(dto.getKeyword());
                dto.setKeywordCompanyUserIds(businessAccountDetailVOS.stream().map(BusinessAccountDetailVO::getId).collect(Collectors.toList()));
            }
        }
        List<ToBeInvoicedListVO> list = baseMapper.selectToBeInvoicedListByCondition(dto);

        if (CollUtil.isEmpty(list)) {
            return list;
        }

        List<Long> invoiceId = list.stream().map(ToBeInvoicedListVO::getId).collect(Collectors.toList());
        List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByInvoiceIds(invoiceId);
        Map<Long, List<OrderInvoiceOrder>> orderInvoiceVideoMap = orderInvoiceOrders.stream().collect(Collectors.groupingBy(OrderInvoiceOrder::getInvoiceId));

        List<Long> invoiceOrderIds = orderInvoiceOrders.stream().map(OrderInvoiceOrder::getId).collect(Collectors.toList());
        List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = orderInvoiceOrderVideoService.selectListByOrderInvoiceOrderIds(invoiceOrderIds);
        Map<Long, List<OrderInvoiceOrderVideo>> orderInvoiceOrderVideoMap = orderInvoiceOrderVideos.stream().collect(Collectors.groupingBy(OrderInvoiceOrderVideo::getInvoiceOrderId));

        List<String> applyPayoutVideoCodes = new ArrayList<>();
        List<WithdrawDepositRecordVO> businessBalanceDetailLocks = new ArrayList<>();
        List<String> videoCodePrepayNums = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderInvoiceOrderVideos)) {
            videoCodePrepayNums.addAll(orderInvoiceOrderVideos.stream().map(OrderInvoiceOrderVideo::getVideoCode).collect(Collectors.toList()));
        }
        videoCodePrepayNums.addAll(orderInvoiceOrders.stream().map(OrderInvoiceOrder::getOrderNum).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(videoCodePrepayNums)) {
            Set<Long> businessIds = list.stream().map(ToBeInvoicedListVO::getMerchantId).collect(Collectors.toSet());
            businessBalanceDetailLocks = remoteService.withdrawDepositRecord(WithdrawDepositRecordDTO.builder()
                    .videoCodePrepayNums(videoCodePrepayNums)
                    .status(List.of(BusinessBalanceAuditStatusEnum.APPROVE.getCode(), BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode()))
                    .businessIds(businessIds)
                    .build());
            applyPayoutVideoCodes = businessBalanceDetailLocks.stream().map(WithdrawDepositRecordVO::getVideoCode).collect(Collectors.toList());
        }

        Map<String, List<WithdrawDepositRecordVO>> prePayMap = new HashMap<>();
        if (CollUtil.isNotEmpty(businessBalanceDetailLocks)) {
            Map<String, List<WithdrawDepositRecordVO>> collect = businessBalanceDetailLocks.stream().filter(item -> StrUtil.isNotBlank(item.getPrepayNum())).collect(Collectors.groupingBy(WithdrawDepositRecordVO::getPrepayNum));
            if (CollUtil.isNotEmpty(collect)){
                prePayMap.putAll(collect);
            }
        }
        Set<Long> submitByIds = list.stream().map(ToBeInvoicedListVO::getSubmitById).collect(Collectors.toSet());
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(remoteService.queryMerchantByBusinessIdsAndAccountIds(null, submitByIds));

        for (ToBeInvoicedListVO toBeInvoicedListVO : list) {
            List<OrderInvoiceOrder> invoiceVideos = orderInvoiceVideoMap.getOrDefault(toBeInvoicedListVO.getId(), new ArrayList<>());
            toBeInvoicedListVO.setOrderNums(invoiceVideos.stream().map(OrderInvoiceOrder::getOrderNum).collect(Collectors.toSet()));

            for (OrderInvoiceOrder invoiceVideo : invoiceVideos) {
                if (invoiceVideo.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)){
                    List<WithdrawDepositRecordVO> withdrawDepositRecordVOS = prePayMap.getOrDefault(invoiceVideo.getOrderNum(), new ArrayList<>());
                    Date lastPayoutTime = null;
                    Date lastCreateTime = null;
                    if (CollUtil.isNotEmpty(withdrawDepositRecordVOS)) {
                        withdrawDepositRecordVOS.sort(Comparator
                                .comparing(WithdrawDepositRecordVO::getAuditTime, Comparator.nullsLast(Comparator.naturalOrder()))
                                .reversed());
                        lastPayoutTime = withdrawDepositRecordVOS.get(0).getAuditTime();
                        lastCreateTime = withdrawDepositRecordVOS.get(0).getCreateTime();
                    }
                    if (CollUtil.isNotEmpty(withdrawDepositRecordVOS)
                            && (ObjectUtil.compare(lastPayoutTime, toBeInvoicedListVO.getSubmitTime()) >= 0 || ObjectUtil.compare(lastCreateTime, toBeInvoicedListVO.getSubmitTime()) >= 0)) {
                        toBeInvoicedListVO.setIsApplyWithdrawal(true);
                        break;
                    }
                }else {
                    List<OrderInvoiceOrderVideo> invoiceOrderVideos = orderInvoiceOrderVideoMap.getOrDefault(invoiceVideo.getId(), new ArrayList<>());
                    List<String> videoCodes = invoiceOrderVideos.stream().map(OrderInvoiceOrderVideo::getVideoCode).collect(Collectors.toList());

                    //  最近提现时间
                    List<WithdrawDepositRecordVO> lastPayoutList = businessBalanceDetailLocks
                            .stream()
                            .filter(item -> videoCodes.contains(item.getVideoCode()))
                            .collect(Collectors.toList());
                    Date lastPayoutTime = null;
                    Date lastCreateTime = null;
                    if (CollUtil.isNotEmpty(lastPayoutList)) {
                        lastPayoutList.sort(Comparator
                                .comparing(WithdrawDepositRecordVO::getAuditTime, Comparator.nullsLast(Comparator.naturalOrder()))
                                .reversed());
                        lastPayoutTime = lastPayoutList.get(0).getAuditTime();
                        lastCreateTime = lastPayoutList.get(0).getCreateTime();
                    }

                    if (CollUtil.isNotEmpty(videoCodes)
                            && CollUtil.containsAny(applyPayoutVideoCodes, videoCodes)
                            && (ObjectUtil.compare(lastPayoutTime, toBeInvoicedListVO.getSubmitTime()) >= 0 || ObjectUtil.compare(lastCreateTime, toBeInvoicedListVO.getSubmitTime()) >= 0)) {
                        toBeInvoicedListVO.setIsApplyWithdrawal(true);
                        break;
                    }
                }

            }

            toBeInvoicedListVO.setSubmitter(accountMap.get(toBeInvoicedListVO.getSubmitById()));
        }

        return list;
    }

    /**
     * 商家端-发票管理-申请开票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyForBilling(ApplyForBillingDTO dto) {
        if (OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(dto.getOrderType())){
            List<OrderPayLog> orderPayLogs = orderPayLogService.listByIds(dto.getOrderId());
            Assert.notEmpty(orderPayLogs, "订单不存在，请刷新页面重试~");
            Assert.isTrue(dto.getOrderId().size() == orderPayLogs.size(), "订单不存在，请刷新页面重试~");
            Assert.isTrue(orderPayLogs.stream().allMatch(item -> dto.getOrderType().equals(item.getOrderType())), "只能提交相同订单类型的发票申请~");

            List<String> orderNums = orderPayLogs.stream().map(OrderPayLog::getOrderNum).collect(Collectors.toList());

            CompanyNotInvoicedListDTO companyNotInvoicedListDTO = new CompanyNotInvoicedListDTO();
            companyNotInvoicedListDTO.setOrderNums(orderNums);
            List<CompanyNotInvoicedListVO> companyNotInvoicedListVOS = selectCompanyNotInvoicedListByCondition(companyNotInvoicedListDTO);
            Assert.isTrue(dto.getOrderId().size() == companyNotInvoicedListVOS.size(), "页面停留时间太久，请刷新~");
            Assert.isTrue(companyNotInvoicedListVOS.stream().noneMatch(CompanyNotInvoicedListVO::getIsApplyWithdrawal), "选择的订单存在正在申请提现，无法申请开票~");

            initOrderInvoice(dto, companyNotInvoicedListVOS,  new HashSet<>(orderNums));
            return;
        }
        List<Order> orders = SpringUtils.getBean(IOrderService.class).listByIds(dto.getOrderId());
        Assert.notEmpty(orders, "订单不存在，请刷新页面重试~");
        Assert.isTrue(dto.getOrderId().size() == orders.size(), "订单不存在，请刷新页面重试~");
        Assert.isTrue(orders.stream().allMatch(item -> SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId().equals(item.getMerchantId())), "订单不存在，请刷新页面重试~");
        Assert.isTrue(orders.stream().allMatch(item -> dto.getOrderType().equals(item.getOrderType())), "只能提交相同订单类型的发票申请~");

        CompanyNotInvoicedListDTO companyNotInvoicedListDTO = new CompanyNotInvoicedListDTO();
        companyNotInvoicedListDTO.setOrderId(dto.getOrderId());
        List<CompanyNotInvoicedListVO> companyNotInvoicedListVOS = selectCompanyNotInvoicedListByCondition(companyNotInvoicedListDTO);
        Assert.isTrue(dto.getOrderId().size() == companyNotInvoicedListVOS.size(), "页面停留时间太久，请刷新~");
        Assert.isTrue(companyNotInvoicedListVOS.stream().noneMatch(CompanyNotInvoicedListVO::getIsApplyWithdrawal), "选择的订单存在正在申请提现，无法申请开票~");
        initOrderInvoice(dto, companyNotInvoicedListVOS, orders.stream().map(Order::getOrderNum).collect(Collectors.toSet()));
    }

    private void initOrderInvoice(ApplyForBillingDTO dto, List<CompanyNotInvoicedListVO> companyNotInvoicedListVOS, Set<String> orderNums) {
        OrderInvoice orderInvoice = BeanUtil.copyProperties(dto, OrderInvoice.class);
        Long invoiceCount = baseMapper.getInvoiceCount();
        orderInvoice.setTicketCode(OrderConstant.ORDER_INVOICE_CODE_PREFIX + (invoiceCount + 1));
        orderInvoice.setSource(OrderInvoiceSourceEnum.MERCHANT_APPLICATION.getCode());
        orderInvoice.setOrderType(dto.getOrderType());
        orderInvoice.setMerchantId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        orderInvoice.setMerchantCode(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessVO().getMemberCode());
        orderInvoice.setInvoiceAmount(companyNotInvoicedListVOS.stream().map(CompanyNotInvoicedListVO::getInvoiceAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        orderInvoice.setStatus(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode());
        orderInvoice.setSubmitBy(SecurityUtils.getUsername());
        orderInvoice.setSubmitById(SecurityUtils.getUserId());
        orderInvoice.setSubmitTime(DateUtil.date());
        baseMapper.insert(orderInvoice);

        //  批量添加发票关联视频订单
        orderInvoiceOrderService.saveBatchOrderInvoiceVideo(orderInvoice, orderNums, false);
        //  保存开票操作记录
        orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.APPLY_FOR_BILLING, null);
    }

    /**
     * 商家端-发票管理-订单详情
     */
    @Override
    public CompanyNotInvoicedOrderDetailVO getCompanyNotInvoicedOrderDetail(Long orderId, Integer orderType) {
        Assert.notNull(orderId, "订单id不能为空~");
        Assert.notNull(orderType, "订单类型不能为空~");
        CompanyNotInvoicedListDTO companyNotInvoicedListDTO = new CompanyNotInvoicedListDTO();
        if (OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(orderType)){
            OrderPayLog orderPayLog = orderPayLogService.getById(orderId);
            Assert.isTrue(orderPayLog.getOrderType().equals(orderType), "订单不存在，请刷新页面重试~");
            companyNotInvoicedListDTO.setOrderNums(List.of(orderPayLog.getOrderNum()));
        }else {
            companyNotInvoicedListDTO.setOrderId(List.of(orderId));
        }

        List<CompanyNotInvoicedListVO> companyNotInvoicedListVOS = selectCompanyNotInvoicedListByCondition(companyNotInvoicedListDTO);
        Assert.notEmpty(companyNotInvoicedListVOS, "页面停留时间太久，请刷新~");

        CompanyNotInvoicedOrderDetailVO detailVO = BeanUtil.copyProperties(companyNotInvoicedListVOS.get(0), CompanyNotInvoicedOrderDetailVO.class);

        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(detailVO.getOrderType())) {
            List<OrderVideo> orderVideos = orderVideoService.selectByOrderNum(detailVO.getOrderNum());

            List<OrderVideoSimpleVO> orderVideoSimpleVOS = new ArrayList<>();
            for (OrderVideo orderVideo : orderVideos) {
                if (orderVideo.getUseBalance().compareTo(orderVideo.getPayAmount()) >= 0) {
                    continue;
                }
                OrderVideoSimpleVO orderVideoSimpleVO = BeanUtil.copyProperties(orderVideo, OrderVideoSimpleVO.class);
                orderVideoSimpleVOS.add(orderVideoSimpleVO);
            }
            detailVO.setOrderVideoSimpleVOS(orderVideoSimpleVOS);
        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(detailVO.getOrderType())) {
            OrderMember orderMember = orderMemberService.getByOrderNum(detailVO.getOrderNum());
            detailVO.setPackageType(orderMember.getPackageType());
        }

        return detailVO;
    }

    /**
     * 商家端-发票管理-未开票列表
     * 使用orderId 无法查询到线上先报充值订单
     */
    @Override
    public List<CompanyNotInvoicedListVO> selectCompanyNotInvoicedListByCondition(CompanyNotInvoicedListDTO dto) {
        dto.setBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        List<WithdrawDepositRecordVO> businessBalanceDetailLocks = remoteService.withdrawDepositRecord(WithdrawDepositRecordDTO.builder()
                .status(List.of(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode(), BusinessBalanceAuditStatusEnum.APPROVE.getCode()))
                .businessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
                .build());
        //  有提现申请的订单
        Set<String> applyOrderNums = new HashSet<>();
        if (CollUtil.isNotEmpty(businessBalanceDetailLocks)) {
            List<String> videoCodes = businessBalanceDetailLocks.stream().map(WithdrawDepositRecordVO::getVideoCode).collect(Collectors.toList());
            List<OrderVideo> videos = orderVideoService.selectListByVideoCodes(videoCodes);

            List<String> applyVideoCodes = businessBalanceDetailLocks.stream().filter(item -> BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(item.getStatus())).map(WithdrawDepositRecordVO::getVideoCode).collect(Collectors.toList());
            for (WithdrawDepositRecordVO item : businessBalanceDetailLocks){
                if (BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(item.getStatus())){
                    applyVideoCodes.add(item.getVideoCode());
                    applyOrderNums.add(item.getPrepayNum());
                }
            }

            List<WithdrawDepositRecordVO> approveList = businessBalanceDetailLocks.stream().filter(item -> BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(item.getStatus())).collect(Collectors.toList());
            //  大订单提现了多少钱
            Map<String, BigDecimal> orderPayoutAmountMap = new HashMap<>();

            //  每笔视频订单对应的提现了多少钱
            Map<String, List<WithdrawDepositRecordVO>> payoutAmountMap = new HashMap<>();
            for (WithdrawDepositRecordVO item : approveList) {
                if (BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode().equals(item.getOrigin())) {
                    BigDecimal payoutAmount = orderPayoutAmountMap.get(item.getPrepayNum());
                    if (ObjectUtil.isNotNull(payoutAmount)) {
                        orderPayoutAmountMap.put(item.getPrepayNum(), payoutAmount.add(item.getPayOutAmount()));
                    } else {
                        orderPayoutAmountMap.put(item.getPrepayNum(), Optional.ofNullable(item.getPayOutAmount()).orElse(BigDecimal.ZERO));
                    }
                }else {
                    if (StrUtil.isNotBlank(item.getVideoCode())){
                        List<WithdrawDepositRecordVO> withdrawDepositRecordVOS = payoutAmountMap.get(item.getVideoCode());
                        if (CollUtil.isNotEmpty(withdrawDepositRecordVOS)) {
                            withdrawDepositRecordVOS.add(item);
                        }else {
                            withdrawDepositRecordVOS = new ArrayList<>();
                            withdrawDepositRecordVOS.add(item);
                            payoutAmountMap.put(item.getPrepayNum(), withdrawDepositRecordVOS);
                        }
                    }
                }
            }

            for (OrderVideo video : videos) {
                if (applyVideoCodes.contains(video.getVideoCode())) {
                    applyOrderNums.add(video.getOrderNum());
                }
                List<WithdrawDepositRecordVO> payoutAmountList = payoutAmountMap.getOrDefault(video.getVideoCode(), new ArrayList<>());
                BigDecimal payoutAmount = payoutAmountList.stream().map(WithdrawDepositRecordVO::getPayOutAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (ObjectUtil.isNotNull(payoutAmount) && BigDecimal.ZERO.compareTo(payoutAmount) < 0) {
                    BigDecimal orderPayoutAmount = orderPayoutAmountMap.get(video.getOrderNum());
                    if (ObjectUtil.isNotNull(orderPayoutAmount)) {
                        orderPayoutAmountMap.put(video.getOrderNum(), orderPayoutAmount.add(payoutAmount));
                    } else {
                        orderPayoutAmountMap.put(video.getOrderNum(), payoutAmount);
                    }
                }
            }
            dto.setOrderPayoutAmountMap(orderPayoutAmountMap);
        }

        //  补充支付类型
        PayTypeEnum.assemblePayType(dto.getPayType());

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ot.pay_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<CompanyNotInvoicedListVO> list = SpringUtils.getBean(IOrderService.class).selectCompanyNotInvoicedListByCondition(dto);
        for (CompanyNotInvoicedListVO companyNotInvoicedListVO : list) {
            companyNotInvoicedListVO.setIsApplyWithdrawal(applyOrderNums.contains(companyNotInvoicedListVO.getOrderNum()));
        }
        return list;
    }

    @Override
    public Map<String, OrderInvoice> getInvoiceMap(List<String> orderNums) {
        List<OrderVideoInvoiceVO> orderInvoices = baseMapper.selectDeliverOrRedPushListByOrderNums(orderNums, null);
        if (StringUtils.isNotEmpty(orderInvoices)){
            return orderInvoices.stream().collect(Collectors.toMap(OrderInvoice::getOrderNum, p -> p, (v1,v2) -> v1));
        }
        return new HashMap<>();
    }

    @Override
    public Map<Long, OrderVideoInvoiceVO> getOrderVideoInvoiceVOMap(List<Long> videoIds) {
        List<OrderVideoInvoiceVO> orderInvoices = baseMapper.selectDeliverOrRedPushListByOrderNums(null, videoIds);
        if (StringUtils.isNotEmpty(orderInvoices)){
            return orderInvoices.stream().collect(Collectors.toMap(OrderVideoInvoiceVO::getVideoId, p -> p));
        }
        return new HashMap<>();
    }

    @Override
    public void setMerchant(List<String> orderNums, Long merchantId) {
        if (ObjectUtil.isNull(merchantId) || merchantId.compareTo(0L) == 0){
            throw new ServiceException("商家ID不能为无效数据");
        }
        if (CollUtil.isEmpty(orderNums)){
            throw new ServiceException("订单号不能为空");
        }
        baseMapper.setMerchant(orderNums, merchantId);
    }

    /**
     * 获取发票数量
     */
    @Override
    public Long getInvoiceCount() {
        return baseMapper.getInvoiceCount();
    }
}
