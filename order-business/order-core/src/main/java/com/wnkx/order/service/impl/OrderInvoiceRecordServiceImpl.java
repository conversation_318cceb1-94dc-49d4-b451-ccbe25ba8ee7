package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRecord;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRecordVO;
import com.wnkx.order.mapper.OrderInvoiceRecordMapper;
import com.wnkx.order.service.OrderInvoiceRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceRecordServiceImpl extends ServiceImpl<OrderInvoiceRecordMapper, OrderInvoiceRecord> implements OrderInvoiceRecordService {


    /**
     * 新增发票开票记录
     */
    @Override
    public void saveInvoiceRecord(OrderInvoiceRecord orderInvoiceRecord) {
        baseMapper.insert(orderInvoiceRecord);
    }

    /**
     * 运营端-发票管理-开票记录
     */
    @Override
    public List<OrderInvoiceRecordVO> getInvoiceRecord(Long invoiceId) {
        List<OrderInvoiceRecord> invoiceRecord = baseMapper.getInvoiceRecord(invoiceId);
        return BeanUtil.copyToList(invoiceRecord, OrderInvoiceRecordVO.class);
    }
}
