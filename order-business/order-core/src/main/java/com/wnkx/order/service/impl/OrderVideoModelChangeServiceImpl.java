package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderVideoModelChangeDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange;
import com.ruoyi.system.api.domain.vo.biz.model.ModelChangeVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelTagVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelChangeVO;
import com.wnkx.order.mapper.OrderVideoModelChangeMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoModelChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15 11:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderVideoModelChangeServiceImpl extends ServiceImpl<OrderVideoModelChangeMapper, OrderVideoModelChange> implements OrderVideoModelChangeService {

    private final RemoteService remoteService;
    private static final String[] ignoreProperties = new String[]{"id", "createBy", "createTime", "updateBy", "updateTime", "specialtyCategory"};

    /**
     * 更新模特变更记录 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideoModelChangeBatchFieldNullToNull(List<OrderVideoModelChange> orderVideoModelChanges) {
        for (OrderVideoModelChange orderVideoModelChange : orderVideoModelChanges) {
            baseMapper.updateOrderVideoModelChangeFieldNullToNull(orderVideoModelChange);
        }
    }

    /**
     * 查询模特变更记录
     */
    @Override
    public List<OrderVideoModelChangeVO> selectOrderVideoModelChangeListByVideoId(Long videoId) {
        List<OrderVideoModelChange> orderVideoModelChanges = baseMapper.selectOrderVideoModelChangeListByVideoId(videoId);
        return BeanUtil.copyToList(orderVideoModelChanges, OrderVideoModelChangeVO.class);
    }

    @Override
    public List<OrderVideoModelChangeVO> selectOrderVideoModelChangeListByVideoIdAndModelId(Long videoId, Long modelId) {
        List<OrderVideoModelChange> orderVideoModelChanges = baseMapper.selectOrderVideoModelChangeListByVideoIdAndModelId(videoId, modelId);
        return BeanUtil.copyToList(orderVideoModelChanges, OrderVideoModelChangeVO.class);
    }

    @Override
    public void saveOrderVideoModelChange(OrderVideoModelChangeDTO dto) {
        saveOrderVideoModelChange(List.of(dto));
    }

    /**
     * 新增模特变更记录
     */
    @Override
    public void saveOrderVideoModelChange(List<OrderVideoModelChangeDTO> dtoList) {
        List<ModelChangeVO> modelChangeVOS = remoteService.selectModelChangeList(dtoList.stream().map(OrderVideoModelChangeDTO::getModelId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(modelChangeVOS)) {
            return;
        }
        Map<Long, ModelChangeVO> modelChangeVOMap = modelChangeVOS.stream().collect(Collectors.toMap(ModelChangeVO::getId, p -> p));

        List<OrderVideoModelChange> orderVideoModelChanges = BeanUtil.copyToList(dtoList, OrderVideoModelChange.class);
        for (OrderVideoModelChange orderVideoModelChange : orderVideoModelChanges) {
            ModelChangeVO modelChangeVO = modelChangeVOMap.get(orderVideoModelChange.getModelId());
            BeanUtil.copyProperties(modelChangeVO, orderVideoModelChange, ignoreProperties);

            orderVideoModelChange.setModelAccount(modelChangeVO.getAccount());
            orderVideoModelChange.setIssueUserName(modelChangeVO.getPersons().get(0).getName());
            if (CollUtil.isNotEmpty(modelChangeVO.getSpecialtyCategory())) {
                List<String> collect = modelChangeVO.getSpecialtyCategory().stream().map(ModelTagVO::getName).collect(Collectors.toList());
                orderVideoModelChange.setSpecialtyCategory(StrUtil.join("、", collect));
            }
            if (CollUtil.isNotEmpty(modelChangeVO.getTags())) {
                List<String> collect = modelChangeVO.getTags().stream().map(ModelTagVO::getName).collect(Collectors.toList());
                orderVideoModelChange.setModelTag(StrUtil.join("、", collect));
            }
        }
        baseMapper.saveBatch(orderVideoModelChanges);
    }
}
