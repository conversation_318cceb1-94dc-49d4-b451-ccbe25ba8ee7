package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigChangelog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_payee_account_config_changelog(收款人账号关联表)】的数据库操作Service
* @createDate 2024-12-17 16:04:06
*/
public interface OrderPayeeAccountConfigChangelogService extends IService<OrderPayeeAccountConfigChangelog> {

    void saveNewChangeLog(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO);

    List<OrderPayeeAccountConfigChangelogDTO> historyLog(Long type);

    void saveUpdateChangeLog(String oldCompanyName, String companyName,Integer type);

    void saveModifyChangeLog(String name, Integer type);
}
