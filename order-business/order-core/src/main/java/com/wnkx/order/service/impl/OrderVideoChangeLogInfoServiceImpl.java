package com.wnkx.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLogInfo;
import com.wnkx.order.mapper.OrderVideoChangeLogInfoMapper;
import com.wnkx.order.service.OrderVideoChangeLogInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoChangeLogInfoServiceImpl extends ServiceImpl<OrderVideoChangeLogInfoMapper, OrderVideoChangeLogInfo> implements OrderVideoChangeLogInfoService {

    /**
     * 通过变更记录id查询变更记录详情
     */
    @Override
    public List<OrderVideoChangeLogInfo> selectChangeLogInfoListByChangeLogIds(List<Long> changeLogIds) {
        List<OrderVideoChangeLogInfo> logInfoList = baseMapper.selectChangeLogInfoListByChangeLogIds(changeLogIds);
        for (OrderVideoChangeLogInfo item : logInfoList) {
            if (ObjectUtil.isNull(item.getValue())) {
                continue;
            }
            try {
                Class<?> aClass = Class.forName(item.getFieldType());
                item.setValue(JSON.parseObject(item.getValue().toString(), aClass));
            } catch (ClassNotFoundException e) {
                log.error("类型转换失败", e);
                throw new ServiceException("类型转换失败");
            }
        }
        return logInfoList;
    }

}
