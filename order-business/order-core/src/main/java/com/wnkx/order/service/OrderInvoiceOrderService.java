package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOrder;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoBackVO;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoCompanyVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:11
 */
public interface OrderInvoiceOrderService extends IService<OrderInvoiceOrder> {

    /**
     * 批量添加发票关联视频订单
     */
    void saveBatchOrderInvoiceVideo(OrderInvoice orderInvoice, Set<String> orderNums, Boolean isRedPushReopen);

    /**
     * 根据发票id查询发票关联的视频订单
     */
    List<OrderInvoiceOrder> selectListByInvoiceIds(List<Long> invoiceIds);

    /**
     * 根据订单号列表获取发票关联的订单
     * @param orderNums
     * @return
     */
    List<OrderInvoiceOrder> selectListByOrderNums(List<String> orderNums);

    /**
     * 运营端-发票管理-发票关联订单
     */
    List<OrderInvoiceVideoBackVO> getInvoiceVideoBackList(Long invoiceId);

    /**
     * 商家端-发票管理-开票相关信息
     */
    List<OrderInvoiceVideoCompanyVO> getInvoiceVideoCompanyList(Long invoiceId, Integer orderType);
}
