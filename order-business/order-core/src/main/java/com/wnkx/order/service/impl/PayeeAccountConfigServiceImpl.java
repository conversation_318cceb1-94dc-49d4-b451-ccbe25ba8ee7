package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.PayeeAccountConfig;
import com.wnkx.order.mapper.PayeeAccountConfigMapper;
import com.wnkx.order.service.IPayeeAccountConfigService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【payee_account_config(收款人账号配置表)】的数据库操作Service实现
* @createDate 2024-09-13 11:08:38
*/
@Service
public class PayeeAccountConfigServiceImpl extends ServiceImpl<PayeeAccountConfigMapper, PayeeAccountConfig>
    implements IPayeeAccountConfigService {

    @Override
    public PayeeAccountConfig getValidConfigByAccountType(Integer type) {
        return baseMapper.getValidConfigByAccountType(type);
    }
}




