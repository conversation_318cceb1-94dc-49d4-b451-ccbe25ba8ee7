package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.vo.order.OrderTaskFlowRecordVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO;
import com.wnkx.order.mapper.OrderVideoTaskDetailMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoTaskFlowCore;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单_工单任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
@RequiredArgsConstructor
public class OrderVideoTaskDetailServiceImpl extends ServiceImpl<OrderVideoTaskDetailMapper, OrderVideoTaskDetail> implements IOrderVideoTaskDetailService {

    private final IOrderVideoTaskDetailFlowRecordService orderVideoTaskDetailFlowRecordService;
    private final RemoteService remoteService;
    private final OrderResourceService orderResourceService;
    private final OrderVideoTaskDetailProcessRecordService orderVideoTaskDetailProcessRecordService;
    private final OrderVideoTaskWorkAssigneeHistoryService orderVideoTaskWorkAssigneeHistoryService;

    /**
     * 通过日期 获取中文部客服新增/完成任务单数量
     */
    @Override
    public List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceTaskCountByDate(String date) {
        return baseMapper.getChineseCustomerServiceTaskCountByDate(date);
    }

    /**
     * 通过视频订单ID查询任务单（部分字段）
     */
    @Override
    public List<OrderVideoTaskDetail> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(videoIds);
    }

    /**
     * 通过视频订单ID关闭任务单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeTaskByVideoIds(List<Long> taskIds, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum) {
        if (CollUtil.isEmpty(taskIds)) {
            return;
        }
        List<OrderVideoTaskDetail> uncompletedOrderVideoTaskDetails = baseMapper.selectUncompletedTaskDetailListByTaskIds(taskIds);
        if (CollUtil.isEmpty(uncompletedOrderVideoTaskDetails)) {
            return;
        }
        DateTime date = DateUtil.date();

        String remark = CharSequenceUtil.EMPTY;

        List<OrderVideoTaskDetailProcessRecordDTO> processRecordDTOS = new ArrayList<>();
        for (OrderVideoTaskDetail item : uncompletedOrderVideoTaskDetails) {
            item.setStatus(OrderTaskStatusEnum.CLOSE.getCode());
            item.setEndTime(date);
            if (OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER.equals(operateTypeEnum)) {
                item.setRemark(OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER.getLabel());
            } else if (OrderTaskDetailFlowOperateTypeEnum.CLOSE_AFTER_SALE.equals(operateTypeEnum) || OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.equals(operateTypeEnum)) {
                if (ObjectUtil.isNotNull(item.getAfterSaleClass())) {
                    remark = "订单退款";
                    item.setRemark(remark);
                    operateTypeEnum = OrderTaskDetailFlowOperateTypeEnum.CLOSE_AFTER_SALE;
                } else if (ObjectUtil.isNotNull(item.getWorkOrderType())) {
                    remark = "订单退款";
                    item.setRemark(remark);
                    operateTypeEnum = OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER;
                }
            }


            OrderVideoTaskDetailProcessRecordDTO processRecordDTO = OrderVideoTaskDetailProcessRecordDTO.builder()
                    .taskNum(item.getTaskNum())
                    .operateType(operateTypeEnum.getCode())
                    .content(remark)
                    .operateByType(TaskDetailFlowRecordOperateByTypeEnum.SYSTEM.getCode())
                    .time(date)
                    .build();
            processRecordDTOS.add(processRecordDTO);
        }
        baseMapper.updateBatchById(uncompletedOrderVideoTaskDetails);

        //  关闭任务单添加流转记录
        orderVideoTaskDetailFlowRecordService.rollbackOrderCloseTaskSaveFlowRecord(uncompletedOrderVideoTaskDetails.stream().map(OrderVideoTaskDetail::getTaskNum).collect(Collectors.toList()), operateTypeEnum);

        orderVideoTaskDetailProcessRecordService.saveBatchWorkOrderProcessRecord(processRecordDTOS);
    }

    /**
     * 工单-新增处理记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.getTaskDetailByTaskNum(dto.getTaskNum());
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()), "当前状态不允许操作");
        Assert.isTrue(checkWorkOrderSubmitOrAssigneePermission(orderVideoTaskDetail.getId()), "对不起，您无权操作新增处理记录");

        dto.setCompletionMode(null);
        dto.setOperateType(null);
        orderVideoTaskDetail.setLastReplyTime(DateUtil.date());
        baseMapper.updateById(orderVideoTaskDetail);
        orderVideoTaskDetailProcessRecordService.addWorkOrderProcessRecord(dto);
    }

    /**
     * 工单-重新打开
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reopenWorkOrder(TaskDetailOperateDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(dto.getId());
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(checkWorkOrderSubmitPermission(dto.getId()), "对不起，您无权操作重新打开");
        taskDetailOperate(dto, orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum.WORK_ORDER_REOPEN);

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(OrderVideoTaskFlowDTO.builder()
                .orderTaskStatus(OrderTaskStatusEnum.UN_HANDLE)
                .orderVideoTaskDetail(orderVideoTaskDetail)
                .build());
    }

    /**
     * 工单-关闭工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void closeWorkOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(checkWorkOrderSubmitPermission(taskDetailId), "对不起，您无权操作关闭工单");

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(OrderVideoTaskFlowDTO.builder()
                .orderTaskStatus(OrderTaskStatusEnum.CLOSE)
                .orderVideoTaskDetail(orderVideoTaskDetail)
                .build());

        OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
        orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.getCode());
        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);

        OrderVideoTaskDetailProcessRecordDTO orderVideoTaskDetailProcessRecordDTO = new OrderVideoTaskDetailProcessRecordDTO();
        orderVideoTaskDetailProcessRecordDTO.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailProcessRecordDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.getCode());
        orderVideoTaskDetailProcessRecordService.addWorkOrderProcessRecord(orderVideoTaskDetailProcessRecordDTO);
    }

    /**
     * 工单-拒绝工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectWorkOrder(TaskDetailOperateDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(dto.getId());
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(checkWorkOrderAssigneePermission(dto.getId()), "对不起，您无权操作拒绝工单");
        taskDetailOperate(dto, orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum.REJECT_WORK_ORDER);

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(OrderVideoTaskFlowDTO.builder()
                .orderTaskStatus(OrderTaskStatusEnum.REJECT)
                .orderVideoTaskDetail(orderVideoTaskDetail)
                .build());

        OrderVideoTaskDetailProcessRecordDTO orderVideoTaskDetailProcessRecordDTO = new OrderVideoTaskDetailProcessRecordDTO();
        orderVideoTaskDetailProcessRecordDTO.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailProcessRecordDTO.setContent(dto.getRemark());
        orderVideoTaskDetailProcessRecordDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.REJECT_WORK_ORDER.getCode());
        orderVideoTaskDetailProcessRecordService.addWorkOrderProcessRecord(orderVideoTaskDetailProcessRecordDTO);
    }

    /**
     * 工单-指派处理人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignHandler(AssignHandlerDTO dto) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(dto.getId());
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()), "当前状态不允许操作");
        Assert.isTrue(OrderTaskWorkOrderTypeEnum.MODEL_DIDNT_GET_IT.getCode().equals(orderVideoTaskDetail.getWorkOrderType()) || OrderTaskWorkOrderTypeEnum.ACCELERATOR_MATERIAL.getCode().equals(orderVideoTaskDetail.getWorkOrderType()), "只有[模特没收到]和[催素材]才允许指派");
        Assert.isTrue(checkWorkOrderAssigneePermission(dto.getId()), "对不起，您无权操作指派");

        OrderVideoTaskWorkAssigneeHistory orderVideoTaskWorkAssigneeHistory = new OrderVideoTaskWorkAssigneeHistory();
        orderVideoTaskWorkAssigneeHistory.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskWorkAssigneeHistory.setOriginalAssigneeId(orderVideoTaskDetail.getAssigneeId());
        orderVideoTaskWorkAssigneeHistory.setCurrentAssigneeId(dto.getAssigneeId());
        orderVideoTaskWorkAssigneeHistoryService.save(orderVideoTaskWorkAssigneeHistory);

        orderVideoTaskDetail.setAssigneeId(dto.getAssigneeId());
        orderVideoTaskDetail.setRemark(dto.getRemark());

        OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
        orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.TRANSFER_TO_A_HANDLER.getCode());
        orderVideoTaskDetailFlowRecord.setRemark(dto.getRemark());
        orderVideoTaskDetailFlowRecord.setAppointeeId(dto.getAssigneeId());

        if (CollUtil.isNotEmpty(dto.getIssuePic())) {
            List<Long> resource = orderResourceService.saveBatchOrderResource(dto.getIssuePic());
            orderVideoTaskDetail.setIssuePicId(CharSequenceUtil.join(StrUtil.COMMA, resource));
            orderVideoTaskDetailFlowRecord.setIssuePicId(CharSequenceUtil.join(StrUtil.COMMA, resource));
        }

        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);

        baseMapper.updateById(orderVideoTaskDetail);

    }

    /**
     * 批量完结工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishWorkOrders(List<Long> taskDetailIds, OrderTaskDetailFlowCompletionModeEnum completionMode, TaskDetailFlowRecordOperateByTypeEnum operateByTypeEnum, List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails) {
        if (CollUtil.isEmpty(taskDetailIds)) {
            return;
        }
        List<OrderVideoTaskDetail> orderVideoTaskDetails = baseMapper.selectBatchIds(taskDetailIds);
        Assert.isTrue(CollUtil.isNotEmpty(orderVideoTaskDetails) && orderVideoTaskDetails.size() == taskDetailIds.size(), "售后单/工单 不存在");

        DateTime date = DateUtil.date();
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();

        Map<Long, OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetailMap = materialInfoTaskDetails.stream().collect(Collectors.toMap(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId, Function.identity()));

        List<OrderVideoTaskDetailFlowRecord> orderVideoTaskDetailFlowRecords = new ArrayList<>();
        List<OrderVideoTaskDetailProcessRecordDTO> orderVideoTaskDetailProcessRecordDTOS = new ArrayList<>();
        for (OrderVideoTaskDetail orderVideoTaskDetail : orderVideoTaskDetails) {
            if (!OrderTaskStatusEnum.UN_HANDLE.getCode().equals(orderVideoTaskDetail.getStatus()) && !OrderTaskStatusEnum.HANDLE_ING.getCode().equals(orderVideoTaskDetail.getStatus())) {
                continue;
            }
            orderVideoTaskDetail.setEndTime(date);
            orderVideoTaskDetail.setStatus(OrderTaskStatusEnum.HANDLE.getCode());

            OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
            orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
            if (ObjectUtil.isNotNull(orderVideoTaskDetail.getAfterSaleClass())) {
                orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_FINISHED.getCode());
            } else if (ObjectUtil.isNotNull(orderVideoTaskDetail.getWorkOrderType())) {
                orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.FINISHED_WORK_ORDER.getCode());
            }

            orderVideoTaskDetailFlowRecord.setCompletionMode(completionMode.getCode());
            orderVideoTaskDetailFlowRecord.setTime(date);
            if (TaskDetailFlowRecordOperateByTypeEnum.EDITOR.equals(operateByTypeEnum) && ObjectUtil.isNotNull(materialInfoTaskDetailMap.get(orderVideoTaskDetail.getId()))) {
                OrderVideoFeedBackMaterialInfoTaskDetail orderVideoFeedBackMaterialInfoTaskDetail = materialInfoTaskDetailMap.getOrDefault(orderVideoTaskDetail.getId(), new OrderVideoFeedBackMaterialInfoTaskDetail());
                orderVideoTaskDetailFlowRecord.setOperateBy(orderVideoFeedBackMaterialInfoTaskDetail.getSubmitBy());
                orderVideoTaskDetailFlowRecord.setOperateById(orderVideoFeedBackMaterialInfoTaskDetail.getSubmitById());
            } else {
                orderVideoTaskDetailFlowRecord.setOperateBy(username);
                orderVideoTaskDetailFlowRecord.setOperateById(userId);
            }
            orderVideoTaskDetailFlowRecord.setOperateByType(operateByTypeEnum.getCode());
            orderVideoTaskDetailFlowRecords.add(orderVideoTaskDetailFlowRecord);

            OrderVideoTaskDetailProcessRecordDTO orderVideoTaskDetailProcessRecordDTO = new OrderVideoTaskDetailProcessRecordDTO();
            orderVideoTaskDetailProcessRecordDTO.setTaskNum(orderVideoTaskDetail.getTaskNum());
            orderVideoTaskDetailProcessRecordDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.FINISHED_WORK_ORDER.getCode());
            orderVideoTaskDetailProcessRecordDTO.setCompletionMode(completionMode.getCode());
            orderVideoTaskDetailProcessRecordDTO.setTime(date);
            if (TaskDetailFlowRecordOperateByTypeEnum.EDITOR.equals(operateByTypeEnum) && ObjectUtil.isNotNull(materialInfoTaskDetailMap.get(orderVideoTaskDetail.getId()))) {
                OrderVideoFeedBackMaterialInfoTaskDetail orderVideoFeedBackMaterialInfoTaskDetail = materialInfoTaskDetailMap.getOrDefault(orderVideoTaskDetail.getId(), new OrderVideoFeedBackMaterialInfoTaskDetail());
                orderVideoTaskDetailProcessRecordDTO.setOperateBy(orderVideoFeedBackMaterialInfoTaskDetail.getSubmitBy());
                orderVideoTaskDetailProcessRecordDTO.setOperateById(orderVideoFeedBackMaterialInfoTaskDetail.getSubmitById());
            } else {
                orderVideoTaskDetailProcessRecordDTO.setOperateBy(username);
                orderVideoTaskDetailProcessRecordDTO.setOperateById(userId);
            }
            orderVideoTaskDetailProcessRecordDTO.setOperateByType(operateByTypeEnum.getCode());
            orderVideoTaskDetailProcessRecordDTOS.add(orderVideoTaskDetailProcessRecordDTO);
        }
        baseMapper.updateBatchById(orderVideoTaskDetails);
        orderVideoTaskDetailProcessRecordService.saveBatchWorkOrderProcessRecord(orderVideoTaskDetailProcessRecordDTOS);
        orderVideoTaskDetailFlowRecordService.saveBatch(orderVideoTaskDetailFlowRecords);

    }

    /**
     * 完结工单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishWorkOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(!OrderTaskWorkOrderTypeEnum.MODEL_DIDNT_GET_IT.getCode().equals(orderVideoTaskDetail.getWorkOrderType()) && !OrderTaskWorkOrderTypeEnum.ACCELERATOR_MATERIAL.getCode().equals(orderVideoTaskDetail.getWorkOrderType()), "[模特没收到]和[催素材]不允许手动完成");
        Assert.isTrue(checkWorkOrderAssigneePermission(taskDetailId), "对不起，您无权操作完结工单");

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(OrderVideoTaskFlowDTO.builder()
                .orderTaskStatus(OrderTaskStatusEnum.HANDLE)
                .orderVideoTaskDetail(orderVideoTaskDetail)
                .build());

        OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
        orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.FINISHED_WORK_ORDER.getCode());
        orderVideoTaskDetailFlowRecord.setCompletionMode(OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION.getCode());
        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);

        OrderVideoTaskDetailProcessRecordDTO orderVideoTaskDetailProcessRecordDTO = new OrderVideoTaskDetailProcessRecordDTO();
        orderVideoTaskDetailProcessRecordDTO.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailProcessRecordDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.FINISHED_WORK_ORDER.getCode());
        orderVideoTaskDetailProcessRecordDTO.setCompletionMode(OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION.getCode());
        orderVideoTaskDetailProcessRecordService.addWorkOrderProcessRecord(orderVideoTaskDetailProcessRecordDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishAfterSaleOrder(Long taskDetailId) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.selectById(taskDetailId);
        Assert.notNull(orderVideoTaskDetail, "工单不存在");
        Assert.isTrue(checkWorkOrderAssigneePermission(taskDetailId), "对不起，您无权操作指派");

        SpringUtils.getBean(OrderVideoTaskFlowCore.class).flowVideoTask(OrderVideoTaskFlowDTO.builder()
                .orderTaskStatus(OrderTaskStatusEnum.HANDLE)
                .orderVideoTaskDetail(orderVideoTaskDetail)
                .build());

        OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
        orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailFlowRecord.setOperateType(OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_FINISHED.getCode());
        orderVideoTaskDetailFlowRecord.setCompletionMode(OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION.getCode());
        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);

        OrderVideoTaskDetailProcessRecordDTO orderVideoTaskDetailProcessRecordDTO = new OrderVideoTaskDetailProcessRecordDTO();
        orderVideoTaskDetailProcessRecordDTO.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailProcessRecordDTO.setOperateType(OrderTaskDetailFlowOperateTypeEnum.AFTER_SALE_FINISHED.getCode());
        orderVideoTaskDetailProcessRecordDTO.setCompletionMode(OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION.getCode());
        orderVideoTaskDetailProcessRecordService.addWorkOrderProcessRecord(orderVideoTaskDetailProcessRecordDTO);
    }

    /**
     * 校验工单处理人操作权限
     */
    @Override
    public Boolean checkWorkOrderAssigneePermission(List<Long> taskDetailIds) {
        if (SecurityUtils.currentUserIsAdmin()) {
            return true;
        }
        return baseMapper.checkWorkOrderAssigneePermission(taskDetailIds);
    }

    /**
     * 校验工单处理人操作权限
     */
    @Override
    public Boolean checkWorkOrderAssigneePermission(Long taskDetailId) {
        if (SecurityUtils.currentUserIsAdmin()) {
            return true;
        }
        return baseMapper.checkWorkOrderAssigneePermission(taskDetailId);
    }

    /**
     * 校验工单提交人操作权限
     */
    @Override
    public Boolean checkWorkOrderSubmitPermission(Long taskDetailId) {
        if (SecurityUtils.currentUserIsAdmin()) {
            return true;
        }
        return baseMapper.checkWorkOrderSubmitPermission(taskDetailId);
    }

    /**
     * 校验工单 提交人 或 处理人 操作权限
     */
    private Boolean checkWorkOrderSubmitOrAssigneePermission(Long taskDetailId) {
        if (SecurityUtils.currentUserIsAdmin()) {
            return true;
        }
        return baseMapper.checkWorkOrderSubmitOrAssigneePermission(taskDetailId);
    }

    /**
     * 通过任务单详情ID及任务单详情流转记录新增流转记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTaskDetailFlow(List<Long> taskDetailIds, OrderVideoTaskDetailFlowRecordDTO dto) {
        List<OrderVideoTaskDetail> orderVideoTaskDetails = baseMapper.selectBatchIds(taskDetailIds);
        Assert.notEmpty(orderVideoTaskDetails, "售后单或工单不存在");

        List<OrderVideoTaskDetailFlowRecord> orderVideoTaskDetailFlowRecords = orderVideoTaskDetails.stream().map(item -> {
            OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
            orderVideoTaskDetailFlowRecord.setTaskNum(item.getTaskNum());
            orderVideoTaskDetailFlowRecord.setTime(DateUtil.date());
            orderVideoTaskDetailFlowRecord.setOperateBy(SecurityUtils.getUsername());
            orderVideoTaskDetailFlowRecord.setOperateById(SecurityUtils.getUserId());
            orderVideoTaskDetailFlowRecord.setOperateType(dto.getOperateType());
            orderVideoTaskDetailFlowRecord.setCompletionMode(dto.getCompletionMode());
            orderVideoTaskDetailFlowRecord.setIssuePicId(dto.getIssuePicId());
            orderVideoTaskDetailFlowRecord.setRemark(dto.getRemark());
            return orderVideoTaskDetailFlowRecord;
        }).collect(Collectors.toList());
        orderVideoTaskDetailFlowRecordService.saveBatch(orderVideoTaskDetailFlowRecords);
    }

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getFeedbackMaterialPendingTask(List<Long> taskIds, List<Long> taskDetailId, List<Integer> afterSaleClass, List<Long> existingTaskDetailIds, Boolean isToBeEdited) {
        return baseMapper.getFeedbackMaterialPendingTask(taskIds, taskDetailId, afterSaleClass, existingTaskDetailIds, isToBeEdited);
    }

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getBackHelpModelUploadMaterialPendingTask(Long taskId, List<Long> taskDetailIds) {
        return baseMapper.getBackHelpModelUploadMaterialPendingTask(taskId, taskDetailIds, SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId());
    }

    /**
     * 申请补偿退款_获取视频订单相关联的任务单信息
     */
    @Override
    public List<OrderVideoTaskDetailVO> getRefundPendingTask(Long taskId, List<Long> taskDetailId) {
        return baseMapper.getRefundPendingTask(taskId, taskDetailId, SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId());
    }

    /**
     * 校验任务单是否已存在
     */
    @Override
    public Boolean checkTaskExist(OrderTaskDTO dto) {
        return baseMapper.checkTaskExist(dto);
    }

    /**
     * 获取所有模特的售后单（重拍视频、补拍视频）（去重、多个视频订单算一个）
     */
    @Override
    public List<OrderVideo> getModelAfterSaleDistinctTask() {
        return baseMapper.getModelAfterSaleDistinctTask();
    }

    /**
     * 获取所有模特的工单（去重、多个视频订单算一个）
     */
    @Override
    public List<OrderVideoTaskDetail> getModelAllDistinctTask() {
        return baseMapper.getModelAllDistinctTask();
    }

    /**
     * 查看工单详情
     */
    @Override
    public WorkOrderTaskInfoVO getWorkOrderInfo(String taskNum) {
        OrderVideoTaskDetail orderVideoTaskDetail = baseMapper.getTaskDetailByTaskNum(taskNum);
        if (ObjectUtil.isNull(orderVideoTaskDetail)) {
            return new WorkOrderTaskInfoVO();
        }

        WorkOrderTaskInfoVO orderTaskInfoVO = BeanUtil.copyProperties(orderVideoTaskDetail, WorkOrderTaskInfoVO.class);
        assembleWorkOrderTaskDetailList(Collections.singletonList(orderTaskInfoVO));

        List<OrderTaskFlowRecordVO> records = orderVideoTaskDetailFlowRecordService.selectOrderTaskFlowRecordListByTaskNum(taskNum);
        orderTaskInfoVO.setRecords(records);
        return orderTaskInfoVO;
    }

    @Override
    public OrderVideoTaskDetail getByTaskNum(String taskNum) {
        return baseMapper.getTaskDetailByTaskNum(taskNum);
    }

    @Override
    public AfterSaleTaskDetailInfoVO getAfterSaleTaskInfo(String taskNum) {
        AfterSaleTaskDetailInfoVO afterSaleTaskDetailInfoVO = baseMapper.getAfterSaleTaskDetailInfoVO(taskNum);
        if (ObjectUtil.isNull(afterSaleTaskDetailInfoVO)){
            return new AfterSaleTaskDetailInfoVO();

        }
        //加载三方数据
        if (ObjectUtil.isNotNull(afterSaleTaskDetailInfoVO.getAssigneeId()) || ObjectUtil.isNotNull(afterSaleTaskDetailInfoVO.getSubmitById())) {
            List<Long> userIds = new ArrayList<>();
            if (ObjectUtil.isNotNull(afterSaleTaskDetailInfoVO.getAssigneeId())){
                userIds.add(afterSaleTaskDetailInfoVO.getAssigneeId());
            }
            if (ObjectUtil.isNotNull(afterSaleTaskDetailInfoVO.getSubmitById())){
                userIds.add(afterSaleTaskDetailInfoVO.getSubmitById());
            }
            SysUserListDTO dto = new SysUserListDTO();
            dto.setUserId(userIds);
            final Map<Long, UserVO> userMap = remoteService.getUserMap(dto);
            if (CollUtil.isNotEmpty(userMap)) {
                afterSaleTaskDetailInfoVO.setAssignee(userMap.get(afterSaleTaskDetailInfoVO.getAssigneeId()));
                afterSaleTaskDetailInfoVO.setSubmit(userMap.get(afterSaleTaskDetailInfoVO.getSubmitById()));
            }
        }


        if (StrUtil.isNotBlank(afterSaleTaskDetailInfoVO.getIssuePicId())) {
            List<OrderResource> orderResources = orderResourceService.selectListByIds(StringUtils.splitToLong(Arrays.asList(afterSaleTaskDetailInfoVO.getIssuePicId()), StrUtil.COMMA));
            if (CollUtil.isNotEmpty(orderResources)) {
                afterSaleTaskDetailInfoVO.setIssuePic(orderResources.stream().map(OrderResource::getObjectKey).collect(Collectors.toList()));
            }
        }

        List<OrderTaskFlowRecordVO> records = orderVideoTaskDetailFlowRecordService.selectOrderTaskFlowRecordListByTaskNum(taskNum);
        afterSaleTaskDetailInfoVO.setRecords(records);
        return afterSaleTaskDetailInfoVO;
    }

    /**
     * 创建任务单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTaskDetail(OrderVideoTaskDetail orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum flowOperateTypeEnum) {
        if (OrderTaskWorkOrderTypeEnum.MODEL_DIDNT_GET_IT.getCode().equals(orderVideoTaskDetail.getWorkOrderType())) {
            Assert.isFalse(baseMapper.checkTaskExist(BeanUtil.copyProperties(orderVideoTaskDetail, OrderTaskDTO.class)), "已存在一笔同为[模特没收到]类型的工单进行中");
        } else if (OrderTaskAfterSaleVideoTypeEnum.RESHOOT_VIDEO.getCode().equals(orderVideoTaskDetail.getAfterSaleVideoType())) {
            Assert.isFalse(baseMapper.checkTaskExist(BeanUtil.copyProperties(orderVideoTaskDetail, OrderTaskDTO.class)), "已存在一笔同为[重拍视频]类型的售后单进行中");
        } else if (OrderTaskAfterSalePicTypeEnum.RESHOOT_PIC.getCode().equals(orderVideoTaskDetail.getAfterSalePicType())) {
            Assert.isFalse(baseMapper.checkTaskExist(BeanUtil.copyProperties(orderVideoTaskDetail, OrderTaskDTO.class)), "已存在一笔同为[重拍照片]类型的售后单进行中");
        }
        String taskNum = createTaskNum();
        orderVideoTaskDetail.setTaskNum(taskNum);
        orderVideoTaskDetail.setSubmitBy(SecurityUtils.getUsername());
        orderVideoTaskDetail.setSubmitById(SecurityUtils.getUserId());
        orderVideoTaskDetail.setSubmitTime(DateUtil.date());
        baseMapper.insert(orderVideoTaskDetail);

        //  创建流转记录
        createTaskFlowRecord(OrderVideoTaskDetailFlowRecord.builder().taskNum(taskNum).operateBy(orderVideoTaskDetail.getSubmitBy()).operateById(orderVideoTaskDetail.getSubmitById()).operateType(flowOperateTypeEnum.getCode()).issuePicId(orderVideoTaskDetail.getIssuePicId()).build());
    }

    /**
     * 工单列表
     */
    @Override
    public List<WorkOrderTaskDetailListVO> selectWorkOrderTaskDetailListByCondition(WorkOrderTaskListDTO dto) {
        List<WorkOrderTaskDetailListVO> workOrderTaskDetailListVOS = baseMapper.selectWorkOrderTaskDetailListByCondition(dto);

        assembleWorkOrderTaskDetailList(workOrderTaskDetailListVOS);

        return workOrderTaskDetailListVOS;
    }

    @Override
    public List<AfterSaleTaskDetailListVO> selectAfterSaleOrderTaskDetailListByCondition(AfterSaleOrderTaskListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovtd.status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("ovtd.priority", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("ovtd.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.orderBy(orderByDto);
        List<AfterSaleTaskDetailListVO> afterSaleTaskDetailListVOS = baseMapper.selectAfterSaleOrderTaskDetailListByCondition(dto);
        if (CollUtil.isEmpty(afterSaleTaskDetailListVOS)){
            return Collections.emptyList();
        }
        assembleAfterSaleOrderTaskDetailList(afterSaleTaskDetailListVOS);
        return afterSaleTaskDetailListVOS;
    }

    @Override
    public List<WorkbenchTaskDetailVO> selectWorkbenchTaskDetailList() {
        List<WorkbenchTaskDetailVO> workbenchTaskDetailVOS = baseMapper.selectWorkbenchTaskDetailList(SecurityUtils.currentUserIsAdmin() ? null: SecurityUtils.getUserId());
        if (CollUtil.isEmpty(workbenchTaskDetailVOS)){
            return Collections.emptyList();
        }
        List<String> issuePicIds = workbenchTaskDetailVOS.stream().map(WorkbenchTaskDetailVO::getIssuePicId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(StringUtils.splitToLong(issuePicIds, StrUtil.COMMA));

        workbenchTaskDetailVOS.forEach(orderTaskListVO -> {
            for (Long issuePicId : StringUtils.splitToLong(orderTaskListVO.getIssuePicId(), StrUtil.COMMA)) {
                orderTaskListVO.getIssuePic().add(resourceMap.getOrDefault(issuePicId, new OrderResource()).getObjectKey());
            }
        });
        return workbenchTaskDetailVOS;
    }

    @Override
    public List<WorkbenchTaskDetailVO> selectWorkbenchRefuseTaskDetailList() {
        List<WorkbenchTaskDetailVO> workbenchTaskDetailVOS = baseMapper.selectWorkbenchRefuseTaskDetailList(SecurityUtils.currentUserIsAdmin() ? null: SecurityUtils.getUserId());
        if (CollUtil.isEmpty(workbenchTaskDetailVOS)){
            return Collections.emptyList();
        }
        return workbenchTaskDetailVOS;
    }

    /**
     * 生成工单编号
     *
     * @return 工单编号
     */
    private String createTaskNum() {
        // 使用Snowflake算法生成唯一ID
        long uniqueId = IdUtil.getSnowflakeNextId();

        // 截取唯一ID的最后4位，确保是数字
        String random4Digits = String.format("%04d", uniqueId % 10_000);

        return DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN) + random4Digits;
    }

    /**
     * 组装工单列表数据
     *
     * @param workOrderTaskDetailListVOS 工单列表数据
     */
    private void assembleWorkOrderTaskDetailList(List<WorkOrderTaskDetailListVO> workOrderTaskDetailListVOS) {
        List<Long> submitByIds = workOrderTaskDetailListVOS.stream().map(WorkOrderTaskDetailListVO::getSubmitById).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<Long> assigneeIds = workOrderTaskDetailListVOS.stream().map(WorkOrderTaskDetailListVO::getAssigneeId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        submitByIds.addAll(assigneeIds);

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(submitByIds);
        final Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        List<String> issuePicIds = workOrderTaskDetailListVOS.stream().map(WorkOrderTaskDetailListVO::getIssuePicId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(StringUtils.splitToLong(issuePicIds, StrUtil.COMMA));

        workOrderTaskDetailListVOS.forEach(orderTaskListVO -> {
            orderTaskListVO.setSubmit(userMap.get(orderTaskListVO.getSubmitById()));
            orderTaskListVO.setAssignee(userMap.get(orderTaskListVO.getAssigneeId()));
            for (Long issuePicId : StringUtils.splitToLong(orderTaskListVO.getIssuePicId(), StrUtil.COMMA)) {
                orderTaskListVO.getIssuePic().add(resourceMap.getOrDefault(issuePicId, new OrderResource()).getObjectKey());
            }
        });
    }
    private void assembleAfterSaleOrderTaskDetailList(List<AfterSaleTaskDetailListVO> saleTaskDetailListVOS) {
        List<Long> assigneeIds = saleTaskDetailListVOS.stream().map(AfterSaleTaskDetailListVO::getAssigneeId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<Long> submitByIds = saleTaskDetailListVOS.stream().map(AfterSaleTaskDetailListVO::getSubmitById).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        assigneeIds.addAll(submitByIds);
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(assigneeIds);
        final Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        List<String> issuePicIds = saleTaskDetailListVOS.stream().map(AfterSaleTaskDetailListVO::getIssuePicId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(StringUtils.splitToLong(issuePicIds, StrUtil.COMMA));

        saleTaskDetailListVOS.forEach(orderTaskListVO -> {
            orderTaskListVO.setAssignee(userMap.get(orderTaskListVO.getAssigneeId()));
            orderTaskListVO.setSubmit(userMap.get(orderTaskListVO.getSubmitById()));
            for (Long issuePicId : StringUtils.splitToLong(orderTaskListVO.getIssuePicId(), StrUtil.COMMA)) {
                orderTaskListVO.getIssuePic().add(resourceMap.getOrDefault(issuePicId, new OrderResource()).getObjectKey());
            }
        });
    }

    /**
     * 创建流转记录
     */
    private void createTaskFlowRecord(OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord) {
        orderVideoTaskDetailFlowRecord.setTime(DateUtil.date());
        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);
    }

    @Override
    public void taskDetailOperate(TaskDetailOperateDTO dto, OrderVideoTaskDetail orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum operateType) {
        orderVideoTaskDetail.setRemark(dto.getRemark());

        OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord = new OrderVideoTaskDetailFlowRecord();
        orderVideoTaskDetailFlowRecord.setTaskNum(orderVideoTaskDetail.getTaskNum());
        orderVideoTaskDetailFlowRecord.setOperateType(operateType.getCode());
        orderVideoTaskDetailFlowRecord.setRemark(dto.getRemark());

        if (CollUtil.isNotEmpty(dto.getIssuePic())) {
            List<Long> resource = orderResourceService.saveBatchOrderResource(dto.getIssuePic());
            orderVideoTaskDetail.setIssuePicId(CharSequenceUtil.join(StrUtil.COMMA, resource));
            orderVideoTaskDetailFlowRecord.setIssuePicId(CharSequenceUtil.join(StrUtil.COMMA, resource));
        }

        orderVideoTaskDetailFlowRecordService.insertOrderTaskFlowRecord(orderVideoTaskDetailFlowRecord);
    }
}
