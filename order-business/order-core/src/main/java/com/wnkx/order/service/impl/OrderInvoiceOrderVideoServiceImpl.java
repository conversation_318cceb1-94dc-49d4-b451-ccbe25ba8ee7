package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOrderVideo;
import com.wnkx.order.mapper.OrderInvoiceOrderVideoMapper;
import com.wnkx.order.service.OrderInvoiceOrderVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceOrderVideoServiceImpl extends ServiceImpl<OrderInvoiceOrderVideoMapper, OrderInvoiceOrderVideo> implements OrderInvoiceOrderVideoService {

    /**
     * 根据视频编码查询发票关联的视频订单
     */
    @Override
    public List<OrderInvoiceOrderVideo> selectListByVideoCodes(List<String> videoCodes) {
        return baseMapper.selectListByVideoCodes(videoCodes);
    }

    /**
     * 通过发票订单ID获取关联视频订单
     */
    @Override
    public List<OrderInvoiceOrderVideo> selectListByOrderInvoiceOrderIds(List<Long> orderInvoiceOrderIds) {
        return baseMapper.selectListByOrderInvoiceOrderIds(orderInvoiceOrderIds);
    }

}
