package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account_config(收款人账号关联表)】的数据库操作Service
 * @createDate 2024-12-17 16:04:06
 */
public interface OrderPayeeAccountConfigService extends IService<OrderPayeeAccountConfig> {
    List<OrderPayeeAccountConfigDTO> datalist();

    List<OrderPayeeAccountConfigDTO> typeList(Integer type);

    OrderPayeeAccountConfigInfoDTO typeInfo(Integer type);

    OrderPayeeAccountConfigInfoDTO getPayeeInfoById(Long id);

    /**
     * 根据order_payee_account_config_info.id获取收款人账号配置
     *
     * @param id
     * @return
     */
    OrderPayeeAccountConfigInfoDTO getPayeeInfoByInfoId(Long id);

    void newInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO);

    void updateInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO);

    List<OrderPayeeAccountConfigChangelogDTO> historyLog(Long type);

    void changeActive(Long id);

    OrderPayeeAccountConfigInfoDTO getPayeeInfoByBankAccount(String appId);
}
