package com.wnkx.order.service;

import com.ruoyi.common.core.enums.PayTranStatusEnum;
import com.ruoyi.system.api.domain.dto.order.pay.WeChatPayDTO;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * 微信支付相关接口
 *
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-24 15:53
 **/
public interface WeChatService {

    /**
     * 获取二维码(预下单)
     *
     * @param dto
     * @return
     */
    PrepayResponse prepay(WeChatPayDTO dto);

    /**
     * 商户订单号查询订单
     *
     * @param outTradeNo
     * @param wechatPayAppId
     * @return
     */
    Transaction queryOrderByOutTradeNo(String outTradeNo, String wechatPayAppId);

    /**
     * *关闭订单
     *
     * @param outTradeNo
     */
    void closeOrder(String outTradeNo, String appId);

    /**
     * 回调通知
     *
     * @return
     */
    Transaction callback();
    //------------------------------------以上实际调用微信接口----------------------------------------------


    /**
     * 微信支付下单
     *
     * @param createPayVo
     * @return
     */
    CreatePayVo weChatPay(CreatePayVo createPayVo);


    /**
     * 获取微信二维码
     */
    CreatePayVo generateWeChatQrcode(Boolean isMerge, String payNum, List<String> orderNums, String appId, BigDecimal finalPayAmount, Long payUserId);

    /**
     * 微信支付回调
     */
    void weChatPayCallBack();

    /**
     * 查询订单状态
     *
     * @param orderNum
     * @param wechatPayAppId
     * @return
     */
    PayTranStatusEnum checkOrderStatus(String orderNum, String wechatPayAppId);

    /**
     * 查询及哦啊要状态
     *
     * @param orderNum
     * @param wechatPayAppId
     * @return
     */
    PayTranStatusEnum queryWeChatPayTradeState(String orderNum, String wechatPayAppId);

    /**
     * 关闭所有订单
     *
     * @param orderNum
     */
    void closeAllQrcode(String orderNum);
}
