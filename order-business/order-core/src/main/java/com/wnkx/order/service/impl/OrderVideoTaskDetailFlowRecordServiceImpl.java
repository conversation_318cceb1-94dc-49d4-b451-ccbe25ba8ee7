package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.core.enums.TaskDetailFlowRecordOperateByTypeEnum;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailFlowRecord;
import com.ruoyi.system.api.domain.vo.order.OrderTaskFlowRecordVO;
import com.wnkx.order.mapper.OrderVideoTaskDetailFlowRecordMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoTaskDetailFlowRecordService;
import com.wnkx.order.service.OrderResourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单_工单任务_流转记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
@RequiredArgsConstructor
public class OrderVideoTaskDetailFlowRecordServiceImpl extends ServiceImpl<OrderVideoTaskDetailFlowRecordMapper, OrderVideoTaskDetailFlowRecord> implements IOrderVideoTaskDetailFlowRecordService {
    private final OrderVideoTaskDetailFlowRecordMapper orderVideoTaskFlowRecordMapper;
    private final RemoteService remoteService;
    private final OrderResourceService orderResourceService;

    /**
     * 视频订单回退关闭任务单添加流转记录
     */
    @Override
    public void rollbackOrderCloseTaskSaveFlowRecord(List<String> taskNum, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum) {
        if (CollUtil.isEmpty(taskNum)) {
            return;
        }

        DateTime date = DateUtil.date();

        List<OrderVideoTaskDetailFlowRecord> records = taskNum.stream().map(item -> {
            OrderVideoTaskDetailFlowRecord record = new OrderVideoTaskDetailFlowRecord();
            record.setTaskNum(item);
            record.setTime(date);
            record.setOperateByType(TaskDetailFlowRecordOperateByTypeEnum.SYSTEM.getCode());
            record.setOperateType(operateTypeEnum.getCode());
            if (OrderTaskDetailFlowOperateTypeEnum.CLOSE_AFTER_SALE.equals(operateTypeEnum) || OrderTaskDetailFlowOperateTypeEnum.CLOSE_WORK_ORDER.equals(operateTypeEnum)) {
                record.setRemark("订单退款");
            } else {
                record.setRemark(operateTypeEnum.getLabel());
            }
            return record;
        }).collect(Collectors.toList());

        baseMapper.saveBatch(records);
    }

    /**
     * 通过工单编号查询流转记录
     */
    @Override
    public List<OrderTaskFlowRecordVO> selectOrderTaskFlowRecordListByTaskNum(String taskNum) {
        List<OrderVideoTaskDetailFlowRecord> records = baseMapper.selectOrderTaskFlowRecordListByTaskNum(taskNum);

        List<OrderTaskFlowRecordVO> orderTaskFlowRecordVOS = BeanUtil.copyToList(records, OrderTaskFlowRecordVO.class);
        assembleRecordList(orderTaskFlowRecordVOS);

        return orderTaskFlowRecordVOS;
    }

    /**
     * 组装流转记录数据
     */
    private void assembleRecordList(List<OrderTaskFlowRecordVO> records) {
        List<Long> operateByIds = records.stream().map(OrderTaskFlowRecordVO::getOperateById).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<Long> appointeeIds = records.stream().map(OrderTaskFlowRecordVO::getAppointeeId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());

        operateByIds.addAll(appointeeIds);
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(operateByIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        List<String> issuePicIds = records.stream().map(OrderTaskFlowRecordVO::getIssuePicId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(StringUtils.splitToLong(issuePicIds, StrUtil.COMMA));

        records.forEach(record -> {
            record.setOperate(userMap.get(record.getOperateById()));
            record.setAppointee(userMap.get(record.getAppointeeId()));
            for (Long issuePicId : StringUtils.splitToLong(record.getIssuePicId(), StrUtil.COMMA)) {
                record.getIssuePic().add(resourceMap.getOrDefault(issuePicId, new OrderResource()).getObjectKey());
            }
        });
    }

    /**
     * 新增订单_工单任务_流转记录
     * 
     * @param orderVideoTaskDetailFlowRecord 订单_工单任务_流转记录
     * @return 结果
     */
    @Override
    public int insertOrderTaskFlowRecord(OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord)
    {
        orderVideoTaskDetailFlowRecord.setTime(DateUtil.date());
        orderVideoTaskDetailFlowRecord.setOperateBy(SecurityUtils.getUsername());
        orderVideoTaskDetailFlowRecord.setOperateById(SecurityUtils.getUserId());
        return orderVideoTaskFlowRecordMapper.insert(orderVideoTaskDetailFlowRecord);
    }

}
