package com.wnkx.order.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.ModelDataTableOrderScheduledRecordListColumnEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableOrderScheduledRecordDTO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledRecordVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableOrderScheduledTagCountVO;
import com.wnkx.order.mapper.ModelDataTableMapper;
import com.wnkx.order.service.ModelDataTableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 15:03
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelDataTableServiceImpl implements ModelDataTableService {

    private final ModelDataTableMapper baseMapper;

    /**
     * 排单记录-标签统计
     */
    @Override
    public ModelDataTableOrderScheduledTagCountVO getModelDataTableOrderScheduledTagCount(Long modelId) {
        return baseMapper.getModelDataTableOrderScheduledTagCount(modelId);
    }

    /**
     * 排单记录
     */
    @Override
    public List<ModelDataTableOrderScheduledRecordVO> selectModelDataTableOrderScheduledRecordList(ModelDataTableOrderScheduledRecordDTO dto) {
        PageUtils.startPage();
        if (ObjectUtil.isNotNull(dto.getSortColumn())) {
            dto.setSortColumnStr(ModelDataTableOrderScheduledRecordListColumnEnum.getColumnSortSqlNameByCode(dto.getSortColumn()));
            if (CharSequenceUtil.isNotBlank(dto.getSortWay())) {
                dto.setSortWay(ModelDataTableOrderScheduledRecordListColumnEnum.getColumnSortWayByCode(dto.getSortColumn(), dto.getSortWay()));
            }
        }
        return baseMapper.selectModelDataTableOrderScheduledRecordList(dto.getModelId(), dto);
    }

}
