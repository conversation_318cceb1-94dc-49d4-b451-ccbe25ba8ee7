package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.balance.OnlineRechargeSubmitCredentialDTO;
import com.ruoyi.system.api.domain.dto.order.pay.CreateAnotherPayLinkDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.order.OrderAnotherPay;

/**
 * <AUTHOR>
 * @date 2024/12/6 13:35
 */
public interface OrderAnotherPayService extends IService<OrderAnotherPay> {

    /**
     * 创建或获取代付链接
     */
    String createLink(CreateAnotherPayLinkDTO dto);

    /**
     * 通过 订单号或合并单ID 获取有效代付链接
     */
    OrderAnotherPay getValidLinkByOrderNumOrMergeId(String orderNum, Long mergeId);

    /**
     * 通过 订单号或者合并单ID 获取代付链接
     */
    OrderAnotherPay getOrderAnotherPayByOrderNumOrMergeId(String orderNum, Long mergeId);

    /**
     * 取消代付
     */
    void cancel(String uuid);

    /**
     * 通过code获取有效代付链接
     */
    OrderAnotherPay getValidLinkByUUID(String code);

    /**
     * 通过code获取代付链接
     */
    OrderAnotherPay getOrderAnotherPayByUUID(String code);

    /**
     * 提交线上钱包充值凭证
     * @param dto
     */
    void onlineSubmitCredential(OnlineRechargeSubmitCredentialDTO dto);

    /**
     * 获取钱包充值详情
     * @param orderNum
     * @return
     */
    BusinessBalancePrepay getOnlineDetailByOrderNum(String orderNum);

    /**
     * 支付后关闭代付链接
     */
    void payCancel(String uuid);
}
