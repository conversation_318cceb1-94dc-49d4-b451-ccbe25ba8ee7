package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderNote;

import java.util.List;

/**
 * 订单_商家开票信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
public interface IOrderNoteService extends IService<OrderNote> {
    /**
     * 查询订单_商家开票信息
     *
     * @param id 订单_商家开票信息主键
     * @return 订单_商家开票信息
     */
    public OrderNote selectOrderNoteById(Long id);

    /**
     * 查询订单_商家开票信息列表
     *
     * @param orderNote 订单_商家开票信息
     * @return 订单_商家开票信息集合
     */
    public List<OrderNote> selectOrderNoteList(OrderNote orderNote);

    /**
     * 新增订单_商家开票信息
     *
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    public int insertOrderNote(OrderNote orderNote);

    /**
     * 修改订单_商家开票信息
     *
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    public int updateOrderNote(OrderNote orderNote);

    /**
     * 批量删除订单_商家开票信息
     *
     * @param ids 需要删除的订单_商家开票信息主键集合
     * @return 结果
     */
    public int deleteOrderNoteByIds(Long[] ids);

    /**
     * 删除订单_商家开票信息信息
     *
     * @param id 订单_商家开票信息主键
     * @return 结果
     */
    public int deleteOrderNoteById(Long id);

    /**
     * 新增订单开票信息
     */
    void saveOrderNote(OrderNote orderNote);
}
