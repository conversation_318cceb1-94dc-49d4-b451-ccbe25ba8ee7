package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.VideoCart;
import com.ruoyi.system.api.domain.entity.order.VideoCartContent;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSelectVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.CreateOrderVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoVO;
import com.ruoyi.system.api.domain.vo.order.VideoCartVO;
import com.wnkx.order.mapper.VideoCartMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:45
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VideoCartServiceImpl extends ServiceImpl<VideoCartMapper, VideoCart> implements VideoCartService {
    private final PayService payService;
    private final VideoCartContentService videoCartContentService;
    private final RemoteService remoteService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final RedisService redisService;
    private final AsyncTaskService asyncTaskService;
    private final OrderResourceService orderResourceService;
    private final IOrderVideoService orderVideoService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final CopyOptions copyOptions = CopyOptions.create()
            .setIgnoreNullValue(true)
            .setIgnoreProperties(ignoreProperties);

    private static final String[] ignoreProperties = new String[]{"id", "createBy", "createTime", "updateBy", "updateTime"};


    /**
     * 更改购物车意向模特
     */
    @Override
    public CreateOrderVO updateCartIntentionModel(UpdateCartIntentionModelDTO dto) {
        Assert.isTrue(checkVideoCartScope(dto.getId()), "找不到订单，请刷新后重试");

        if (ObjectUtil.isNotNull(dto.getModelId())) {
            VideoCart videoCart = baseMapper.selectById(dto.getId());
            if (ObjectUtil.isNull(videoCart)) {
                throw new ServiceException("购物车订单不存在！");
            }
            OrderVideoDTO orderVideoDTO = BeanUtil.copyProperties(videoCart, OrderVideoDTO.class);
            //  校验意向模特是否符合订单信息
            List<Long> unfulfilledOrderModelIds = SpringUtils.getBean(IOrderService.class).checkModelMeetOrder(List.of(dto.getModelId()), orderVideoDTO);
            if (null != unfulfilledOrderModelIds) {
                return CreateOrderVO.builder().cannotModel(unfulfilledOrderModelIds).build();
            }

            //  校验模特可否接单
            //  意向模特
            Collection<Long> difference = SpringUtils.getBean(IOrderService.class).checkModelCanAccept(List.of(dto.getModelId()), SecurityUtils.getBizUserId());
            if (CollUtil.isNotEmpty(difference)) {
                return CreateOrderVO.builder().cannotModel(difference).build();
            }
        }
        baseMapper.updateCartIntentionModel(dto);
        return CreateOrderVO.builder().build();
    }

    /**
     * 重新加入购物车
     */
    @Override
    public void rejoinCart(Long videoId) {
        OrderVideoVO orderVideoInfo = orderVideoService.getOrderVideoInfo(videoId);
        Assert.isTrue(OrderStatusEnum.TRADE_CLOSE.getCode().equals(orderVideoInfo.getStatus())
                || OrderStatusEnum.UN_FINISHED.getCode().equals(orderVideoInfo.getStatus())
                || OrderStatusEnum.FINISHED.getCode().equals(orderVideoInfo.getStatus()), "订单不是交易关闭，无需重新加入购物车");

        OrderVideoDTO orderVideoDTO = BeanUtil.copyProperties(orderVideoInfo, OrderVideoDTO.class);
        orderVideoDTO.setShootCount(1);
        orderVideoDTO.setId(null);
        for (VideoContentDTO videoContentDTO : orderVideoDTO.getShootRequired()) {
            videoContentDTO.setId(null);
        }
        SpringUtils.getBean(VideoCartService.class).addCart(orderVideoDTO);
    }

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    @Override
    public void updateBatchOrderCartProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto) {
        List<VideoCart> videoCarts = BeanUtil.copyToList(dto, VideoCart.class);
        Map<String, String> map = UpdateBatchOrderVideoProductPicDTO.getMap(dto);

        Set<String> productLinks = dto.stream().map(UpdateBatchOrderVideoProductPicDTO::getProductLink).collect(Collectors.toSet());
        List<VideoCart> productPicIsNullList = baseMapper.selectListByProductPicIsNullAndProductLink(productLinks);
        if (CollUtil.isNotEmpty(productPicIsNullList)) {
            for (VideoCart videoCart : productPicIsNullList) {
                videoCart.setProductPic(map.get(videoCart.getProductLink()));
            }
            videoCarts.addAll(productPicIsNullList);
        }
        updateBatchById(videoCarts);
    }

    /**
     * 购物车列表-下单运营下拉框
     */
    @Override
    public List<BusinessAccountSelectVO> cartCreateUserSelect(String keyword) {
        Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
        Set<Long> createUserId = baseMapper.getCreateUserId(businessId);

        if (CollUtil.isEmpty(createUserId)) {
            return Collections.emptyList();
        }

        BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
        businessAccountDetailDTO.setIds(createUserId);
        businessAccountDetailDTO.setNickName(keyword);
        return remoteService.businessAccountSelect(businessAccountDetailDTO);
    }

    /**
     * 复制购物车
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public VideoCartVO copyCart(Long cartId) {
        Assert.isTrue(checkVideoCartScope(List.of(cartId)), "找不到订单，请刷新后重试");
        BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        //  同一商家下的用户添加了多少购物车
        Long businessCartCount = baseMapper.getBusinessCartCount(businessAccountVO.getBusinessId());

        Assert.isTrue(OrderConstant.ORDER_CART_MAX >= businessCartCount + 1, StrUtil.format("购物车最多添加{}个视频，还能加购{}单", OrderConstant.ORDER_CART_MAX, OrderConstant.ORDER_CART_MAX - businessCartCount));

        VideoCart videoCart = baseMapper.selectById(cartId);
        Map<Long, List<VideoCartContent>> videoCartContentMap = getVideoCartContentMap(List.of(cartId));
        List<VideoCartContent> saveVideoCartContents = new ArrayList<>();
        List<VideoCartContent> videoCartContents = videoCartContentMap.get(videoCart.getId());

        videoCart.setId(null);
        videoCart.setCreateOrderBizUserId(businessAccountVO.getBizUserId());
        videoCart.setCreateOrderUserId(businessAccountVO.getId());
        videoCart.setCreateOrderUserAccount(businessAccountVO.getAccount());
        videoCart.setCreateOrderUserName(businessAccountVO.getName());
        videoCart.setCreateOrderUserNickName(businessAccountVO.getNickName());
        videoCart.setCreateTime(null);
        videoCart.setUpdateTime(null);
        baseMapper.insert(videoCart);

        if (CollUtil.isNotEmpty(videoCartContents)) {
            videoCartContents.forEach(videoCartContent -> {
                videoCartContent.setId(null);
                videoCartContent.setVideoCartId(videoCart.getId());
                videoCartContent.setCreateTime(null);
                videoCartContent.setUpdateTime(null);
            });
            saveVideoCartContents.addAll(videoCartContents);
        }

        videoCartContentService.saveBatch(saveVideoCartContents);

        VideoCartVO videoCartVO = BeanUtil.copyProperties(videoCart, VideoCartVO.class);
        if (ObjectUtil.isNotNull(videoCartVO.getIntentionModelId())) {
            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(List.of(videoCartVO.getIntentionModelId()));
            videoCartVO.setIntentionModel(modelMap.get(videoCartVO.getIntentionModelId()));
        }
        return videoCartVO;
    }

    /**
     * 当前商户购物车数量统计
     */
    @Override
    public Long getCartCount() {
        return baseMapper.getBusinessCartCount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
    }

    /**
     * 购物车结算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MemberAuth
    public CreateOrderVO cartSettleAccounts(List<CartSettleAccountsDTO> cartSettleAccountsDTOS) {
        List<Long> cartIds = cartSettleAccountsDTOS.stream().map(CartSettleAccountsDTO::getId).collect(Collectors.toList());
        Assert.isTrue(checkVideoCartScope(cartIds), "找不到订单，请刷新后重试！");

        getCartLock(cartIds);
        CreateOrderVO createOrderVO;
        try {
            List<VideoCart> videoCarts = baseMapper.selectBatchIds(cartIds);
            //  如果重新下单 意向模特设置为null
            //  这里再校验一次模特状态，如果是不能接单的话再清，否则不清
            List<Long> repetitionIds = cartSettleAccountsDTOS.stream()
                    .filter(cart -> cart.getRepetition().equals(StatusEnum.ENABLED.getCode()))
                    .map(CartSettleAccountsDTO::getId)
                    .collect(Collectors.toList());

            List<OrderVideoDTO> orderVideoDTOS = new ArrayList<>();

            Map<Long, List<VideoCartContent>> videoCartContentMap = getVideoCartContentMap(cartIds);
            videoCarts.forEach(videoCart -> {
                if (repetitionIds.contains(videoCart.getId())) {
                    videoCart.setIntentionModelId(null);
                }
                OrderVideoDTO orderVideoDTO = new OrderVideoDTO();
                BeanUtil.copyProperties(videoCart, orderVideoDTO, copyOptions);
                orderVideoDTO.setVideoCartId(videoCart.getId());
                orderVideoDTO.setShootCount(1);
                List<VideoCartContent> videoCartContents = videoCartContentMap.get(videoCart.getId());
                if (CollUtil.isNotEmpty(videoCartContents)) {
                    List<VideoContentDTO> shootRequired = BeanUtil.copyToList(videoCartContents, VideoContentDTO.class, copyOptions);
                    shootRequired.sort(Comparator.comparingInt(VideoContentDTO::getSort));
                    orderVideoDTO.setShootRequired(shootRequired);
                }
                orderVideoDTOS.add(orderVideoDTO);
            });

            createOrderVO = checkModelCreateOrder(orderVideoDTOS);
            if (null != createOrderVO) {
                return createOrderVO;
            }
            //  创建订单
            createOrderVO = SpringUtils.getBean(IOrderService.class).getCreateOrderVO(orderVideoDTOS);
            if (CollUtil.isEmpty(createOrderVO.getCannotModel()) && StrUtil.isNotBlank(createOrderVO.getOrderNum())) {
                //  清除选择购物车商品
                baseMapper.deleteCart(cartIds);
                //  清除购物车关联内容
                videoCartContentService.deleteByCartIds(cartIds);
            }
        } catch (Exception e) {
            log.error("创建订单失败:", e);
            throw new ServiceException(e.getMessage());
        } finally {
            releaseCartLock(cartIds);
        }
        return createOrderVO;
    }

    /**
     * 编辑购物车
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCart(OrderVideoDTO orderVideoDTO) {
        Assert.isTrue(checkVideoCartScope(orderVideoDTO.getId()), "找不到订单，请刷新后重试");
        orderVideoDTO.formatVideoLink();
        try {
            List<OrderVideoDTO> orderVideoDTOS = Collections.singletonList(orderVideoDTO);
            payService.calculateOrderAmount(orderVideoDTOS);

            BigDecimal amount = orderVideoDTO.getVideoPrice().add(orderVideoDTO.getPicPrice()).add(orderVideoDTO.getCommissionPaysTaxes()).add(orderVideoDTO.getExchangePrice())
                    //  注：购物车不判断商家账号身份，在做价格统计时，无论是代理还是非代理，统一加上服务费（$3）展示
                    .add(BigDecimal.valueOf(CommonConstant.PAYPAL_PROXY_SERVICE_PRICE));

            VideoCart videoCart = BeanUtil.copyProperties(orderVideoDTO, VideoCart.class);
            videoCart.setAmount(amount);
            if (CollUtil.isNotEmpty(orderVideoDTO.getReferencePic())) {
                List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderVideoDTO.getReferencePic());
                videoCart.setReferencePicId(StrUtil.join(StrUtil.COMMA, resourceIds));
            } else {
                videoCart.setReferencePicId(null);
            }
            //如果意向模特不满足清空意向模特
            if (CollUtil.isNotEmpty(orderVideoDTO.getCannotModelIds())) {
                videoCart.setIntentionModelId(null);
            }
            baseMapper.updateCart(videoCart);
            if (StrUtil.isNotBlank(orderVideoDTO.getProductLink()) && StrUtil.isBlank(orderVideoDTO.getProductPic())) {
                AsyncCrawlProductPicDTO asyncCrawlProductPicDTO = new AsyncCrawlProductPicDTO();
                asyncCrawlProductPicDTO.setId(videoCart.getId());
                asyncCrawlProductPicDTO.setProductLink(videoCart.getProductLink());

                AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
                asyncCrawlTask.setType(OrderTypeEnum.VIDEO_CART.getCode());
                asyncCrawlTask.setAsyncCrawlProductPic(Collections.singletonList(asyncCrawlProductPicDTO));

                SpringUtils.getBean(IOrderService.class).crawlTask(asyncCrawlTask);
            }

            List<VideoCartContent> videoCartContents = new ArrayList<>();
            if (CollUtil.isNotEmpty(orderVideoDTO.getShootRequired())) {
                for (VideoContentDTO videoContentDTO : orderVideoDTO.getShootRequired()) {
                    if (StrUtil.isBlank(videoContentDTO.getContent())) {
                        continue;
                    }
                    VideoCartContent videoCartContent = BeanUtil.copyProperties(videoContentDTO, VideoCartContent.class);
                    videoCartContent.setVideoCartId(videoCart.getId());
                    videoCartContents.add(videoCartContent);
                }
            }
            videoCartContentService.saveBatchContents(videoCart.getId(), videoCartContents);
        } catch (Exception e) {
            log.error("编辑购物车失败：", e);
            throw new ServiceException("编辑购物车失败：" + e.getMessage());
        }
    }

    /**
     * 查看购物车订单
     */
    @Override
    public VideoCartVO getCartInfo(Long cartId) {
        Assert.isTrue(checkVideoCartScope(cartId), "找不到订单，请刷新后重试");
        VideoCart videoCart = baseMapper.selectById(cartId);

        VideoCartVO videoCartVO = BeanUtil.copyProperties(videoCart, VideoCartVO.class);
        try {
            assembleCartList(Collections.singletonList(videoCartVO));
        } catch (Exception e) {
            log.error("组装信息失败！", e);
            throw new ServiceException("查看购物车订单失败！");
        }
        return videoCartVO;
    }

    /**
     * 删除购物车订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCart(List<Long> cartIds) {
        Assert.isTrue(checkVideoCartScope(cartIds), "找不到订单，请刷新后重试！");
        getCartLock(cartIds);
        try {
            baseMapper.deleteCart(cartIds);
            videoCartContentService.deleteByCartIds(cartIds);
        } catch (Exception e) {
            log.error("删除购物车失败！", e);
            throw new ServiceException("删除购物车失败！");
        } finally {
            releaseCartLock(cartIds);
        }

    }

    /**
     * 购物车列表
     */
    @Override
    public List<VideoCartVO> selectCartList(CartListDTO cartListDTO) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        if (StrUtil.isNotBlank(cartListDTO.getKeyword())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setName(cartListDTO.getKeyword());
            modelListDTO.setAccount(cartListDTO.getKeyword());
            List<ModelInfoVO> modelInfoVOList = remoteService.queryLikeModelList(modelListDTO);
            Set<Long> modelIds = modelInfoVOList.stream().map(ModelInfoVO::getId).collect(Collectors.toSet());
            cartListDTO.setModelIds(modelIds);
        }

        List<VideoCartVO> list = baseMapper.selectCartList(cartListDTO, SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());

        try {
            assembleCartList(list);
        } catch (Exception e) {
            log.error("组装信息失败！", e);
            throw new ServiceException("获取购物车列表失败！");
        }

        return list;
    }

    /**
     * 添加购物车
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCart(OrderVideoDTO orderVideoDTO) {
        BusinessAccountVO accountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        //  同一商家下的用户添加了多少购物车
        Long businessCartCount = baseMapper.getBusinessCartCount(accountVO.getBusinessId());

        orderVideoDTO.formatVideoLink();
        Integer shootCount = orderVideoDTO.getShootCount();
        List<Long> intentionModelIds = orderVideoDTO.getIntentionModelIds();
        Assert.isTrue(OrderConstant.ORDER_CART_MAX >= businessCartCount + shootCount, StrUtil.format("购物车最多添加{}个视频，还能加购{}单", OrderConstant.ORDER_CART_MAX, OrderConstant.ORDER_CART_MAX - businessCartCount));
        try {
            List<OrderVideoDTO> orderVideoDTOS = Collections.singletonList(orderVideoDTO);
            payService.calculateOrderAmount(orderVideoDTOS);

            BigDecimal amount = orderVideoDTO.getVideoPrice().add(orderVideoDTO.getPicPrice()).add(orderVideoDTO.getCommissionPaysTaxes().add(orderVideoDTO.getExchangePrice())
                    //  注：购物车不判断商家账号身份，在做价格统计时，无论是代理还是非代理，统一加上服务费（$3）展示
                    .add(BigDecimal.valueOf(CommonConstant.PAYPAL_PROXY_SERVICE_PRICE)));

            List<VideoCartContent> saveVideoCartContents = new ArrayList<>();
            List<AsyncCrawlProductPicDTO> asyncCrawlProductPic = new ArrayList<>();

            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderVideoDTO.getReferencePic());

            List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
            for (int i = 0; i < shootCount; i++) {
                VideoCart videoCart = BeanUtil.copyProperties(orderVideoDTO, VideoCart.class);
                if (CollUtil.isNotEmpty(intentionModelIds) && i < intentionModelIds.size()) {
                    videoCart.setIntentionModelId(intentionModelIds.get(i));
                }
                videoCart.setCreateOrderBusinessId(accountVO.getBusinessId());
                videoCart.setCreateOrderBizUserId(accountVO.getBizUserId());
                videoCart.setCreateOrderUserId(accountVO.getId());
                videoCart.setCreateOrderUserAccount(accountVO.getAccount());
                videoCart.setCreateOrderUserName(accountVO.getName());
                videoCart.setCreateOrderUserNickName(accountVO.getNickName());
                videoCart.setAmount(amount);
                videoCart.setServicePrice(BigDecimal.valueOf(CommonConstant.PAYPAL_PROXY_SERVICE_PRICE));
                String referencePic = StrUtil.join(StrUtil.COMMA, resourceIds);
                videoCart.setReferencePicId(StrUtil.isBlank(referencePic) ? null : referencePic);
                baseMapper.insert(videoCart);

                orderVideoOperateDTOS.add(OrderVideoOperateDTO.builder().videoId(videoCart.getId()).eventContent(OrderVideoOperateTypeEnum.ADD_CART.getEventContent()).build());
                if (StrUtil.isNotBlank(orderVideoDTO.getProductLink()) && StrUtil.isBlank(orderVideoDTO.getProductPic())) {
                    AsyncCrawlProductPicDTO asyncCrawlProductPicDTO = new AsyncCrawlProductPicDTO();
                    asyncCrawlProductPicDTO.setId(videoCart.getId());
                    asyncCrawlProductPicDTO.setProductLink(videoCart.getProductLink());
                    asyncCrawlProductPic.add(asyncCrawlProductPicDTO);
                }

                if (CollUtil.isNotEmpty(orderVideoDTO.getShootRequired())) {
                    List<VideoCartContent> videoCartContents = new ArrayList<>();
                    for (VideoContentDTO videoContentDTO : orderVideoDTO.getShootRequired()) {
                        if (StrUtil.isBlank(videoContentDTO.getContent())) {
                            continue;
                        }
                        VideoCartContent videoCartContent = BeanUtil.copyProperties(videoContentDTO, VideoCartContent.class);
                        videoCartContent.setVideoCartId(videoCart.getId());
                        videoCartContents.add(videoCartContent);
                    }
                    saveVideoCartContents.addAll(videoCartContents);
                }
            }
            if (CollUtil.isNotEmpty(saveVideoCartContents)) {
                videoCartContentService.saveBatch(saveVideoCartContents);
            }
            if (CollUtil.isNotEmpty(asyncCrawlProductPic)) {
                AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
                asyncCrawlTask.setType(OrderTypeEnum.VIDEO_CART.getCode());
                asyncCrawlTask.setAsyncCrawlProductPic(asyncCrawlProductPic);
                SpringUtils.getBean(IOrderService.class).crawlTask(asyncCrawlTask);
            }
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ADD_CART.getEventName(),
                    1,
                    OrderVideoOperateTypeEnum.ADD_CART.getIsPublic(),
                    null,
                    orderVideoOperateDTOS);
        } catch (Exception e) {
            log.error("加入购物车失败：", e);
            throw new ServiceException("加入购物车失败：" + e.getMessage());
        }
    }

    @Override
    public void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto) {
        baseMapper.clearVideoCartIntentionModelId(dto);
    }

    /**
     * 组装购物车列表
     */
    private void assembleCartList(List<VideoCartVO> list) throws ExecutionException, InterruptedException {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        CompletableFuture<Map<Long, ModelInfoVO>> modelMapFuture = CompletableFuture.supplyAsync(() -> {
            Set<Long> intentionModelIds = list.stream().map(VideoCartVO::getIntentionModelId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
            return remoteService.getModelMap(intentionModelIds);
        }, asyncPoolTaskExecutor);

        CompletableFuture<Map<Long, List<VideoCartContent>>> videoCartContentMapFuture = CompletableFuture.supplyAsync(() -> {
            Set<Long> cartIds = list.stream().map(VideoCartVO::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
            return getVideoCartContentMap(cartIds);
        }, asyncPoolTaskExecutor);

        CompletableFuture<Map<Long, OrderResource>> resourceMapFuture = CompletableFuture.supplyAsync(() -> {
            Set<String> referencePicIds = list.stream().map(VideoCartVO::getReferencePicId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
            List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));

            return orderResourceService.getResourceMapByIds(resourceIds);
        }, asyncPoolTaskExecutor);

        //  等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(modelMapFuture, videoCartContentMapFuture, resourceMapFuture);
        allFutures.join();

        Map<Long, ModelInfoVO> modelMap = modelMapFuture.get();
        Map<Long, List<VideoCartContent>> videoCartContentMap = videoCartContentMapFuture.get();
        Map<Long, OrderResource> resourceMap = resourceMapFuture.get();

        for (VideoCartVO cartVO : list) {
            cartVO.setIntentionModel(modelMap.get(cartVO.getIntentionModelId()));
            List<VideoCartContent> videoCartContents = videoCartContentMap.get(cartVO.getId());
            if (CollUtil.isNotEmpty(videoCartContents)) {
                List<VideoCartContent> shootRequired = videoCartContents.stream().sorted(Comparator.comparingInt(VideoCartContent::getSort).thenComparing(VideoCartContent::getCreateTime)).collect(Collectors.toList());
                cartVO.setShootRequired(BeanUtil.copyToList(shootRequired, VideoContentDTO.class));
            }

            List<Long> referencePicIds = StringUtils.splitToLong(cartVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicIds) {
                cartVO.getReferencePic().add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
            }
        }
    }

    /**
     * 获取购物车关联内容map
     */
    private Map<Long, List<VideoCartContent>> getVideoCartContentMap(Collection<Long> cartIds) {
        List<VideoCartContent> videoCartContents = videoCartContentService.selectListByCartId(cartIds);

        if (CollUtil.isEmpty(videoCartContents)) {
            return new HashMap<>();
        }

        return videoCartContents.stream()
                .collect(Collectors.groupingBy(VideoCartContent::getVideoCartId))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().stream()
                                .sorted((a, b) -> b.getSort().compareTo(a.getSort()))
                                .collect(Collectors.toList())
                ));
    }

    /**
     * 获取购物车锁
     */
    private void getCartLock(List<Long> cartIds) {
        //  加锁
        List<Long> lockCartIds = new ArrayList<>();
        try {
            for (Long cartId : cartIds) {
                Assert.isTrue(redisService.getLock(CacheConstants.VIDEO_CART_LOCK_KEY + cartId, 60L), "购物车正在操作中，请稍后重试！");
                lockCartIds.add(cartId);
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (Long lockCartId : lockCartIds) {
                redisService.releaseLock(CacheConstants.VIDEO_CART_LOCK_KEY + lockCartId);
            }
            throw new ServiceException("订单正在处理中，请稍后重试！");
        }
    }


    /**
     * 释放购物车锁
     */
    private void releaseCartLock(List<Long> cartIds) {
        for (Long cartId : cartIds) {
            redisService.releaseLock(CacheConstants.VIDEO_CART_LOCK_KEY + cartId);
        }
    }

    /**
     * 校验购物车订单数据权限
     */
    private Boolean checkVideoCartScope(List<Long> cartIds) {
        Long cartCount = baseMapper.selectCountByIds(cartIds);
        return cartCount == cartIds.size();
    }

    /**
     * 校验购物车订单数据权限
     */
    private Boolean checkVideoCartScope(Long cartId) {
        return checkVideoCartScope(List.of(cartId));
    }

    private CreateOrderVO checkModelCreateOrder(List<OrderVideoDTO> orderVideoDTOS) {
        List<OrderVideoDTO> collect = orderVideoDTOS.stream().filter(item -> ObjectUtil.isNotNull(item.getIntentionModelId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return null;
        }

        Set<Long> intentionModelIds = collect.stream().map(OrderVideoDTO::getIntentionModelId).collect(Collectors.toSet());
        List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().id(intentionModelIds).build());
        Map<Long, ModelOrderSimpleVO> modelOrderSimpleVOMap = modelOrderSimpleVOS.stream().collect(Collectors.toMap(ModelOrderSimpleVO::getId, Function.identity()));

        Set<Long> unfulfilledOrderCartIds = new HashSet<>();
        Set<Long> cannotModelIds = new HashSet<>();
        Set<String> cannotModelNames = new HashSet<>();

        //  校验模特是否符合订单信息
        for (OrderVideoDTO orderVideoDTO : collect) {
            ModelOrderSimpleVO modelOrderSimpleVO = modelOrderSimpleVOMap.get(orderVideoDTO.getIntentionModelId());
            if (ObjectUtil.isNull(modelOrderSimpleVO)) {
                continue;
            }

            //  校验模特国家
            if (!modelOrderSimpleVO.getNation().equals(orderVideoDTO.getShootingCountry())) {
                unfulfilledOrderCartIds.add(orderVideoDTO.getVideoCartId());
                cannotModelIds.add(modelOrderSimpleVO.getId());
                continue;
            }

            //  校验模特平台
            int[] ints = StrUtil.splitToInt(modelOrderSimpleVO.getPlatform(), StrPool.COMMA);
            List<Integer> list = Arrays.stream(ints).boxed().collect(Collectors.toList());
            if (!list.contains(orderVideoDTO.getPlatform())) {
                unfulfilledOrderCartIds.add(orderVideoDTO.getVideoCartId());
                cannotModelIds.add(modelOrderSimpleVO.getId());
                continue;
            }

            //  校验模特类型
            if (!ModelTypeEnum.ALL.getCode().equals(orderVideoDTO.getModelType()) && !modelOrderSimpleVO.getType().equals(orderVideoDTO.getModelType())) {
                unfulfilledOrderCartIds.add(orderVideoDTO.getVideoCartId());
                cannotModelIds.add(modelOrderSimpleVO.getId());
            }
        }

        //  校验模特可否接单
        Collection<Long> checkModelCanAcceptIds = SpringUtils.getBean(IOrderService.class).checkModelCanAccept(intentionModelIds, SecurityUtils.getBizUserId());
        if (CollUtil.isNotEmpty(checkModelCanAcceptIds)) {
            cannotModelIds.addAll(checkModelCanAcceptIds);
            unfulfilledOrderCartIds.addAll(collect.stream()
                    .filter(item -> checkModelCanAcceptIds.contains(item.getIntentionModelId()))
                    .map(OrderVideoDTO::getVideoCartId)
                    .collect(Collectors.toSet()));
        }

        for (ModelOrderSimpleVO modelOrderSimpleVO : modelOrderSimpleVOS) {
            if (cannotModelIds.contains(modelOrderSimpleVO.getId())) {
                cannotModelNames.add(modelOrderSimpleVO.getName());
            }
        }

        if (CollUtil.isNotEmpty(unfulfilledOrderCartIds)) {
            return CreateOrderVO.builder()
                    .cannotModel(cannotModelIds)
                    .cannotModelNames(cannotModelNames)
                    .cannotShoppingCartId(unfulfilledOrderCartIds)
                    .build();
        }
        return null;
    }
}
