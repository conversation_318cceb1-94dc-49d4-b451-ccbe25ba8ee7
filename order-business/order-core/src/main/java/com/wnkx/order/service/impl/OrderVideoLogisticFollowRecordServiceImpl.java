package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowRecordVO;
import com.wnkx.order.mapper.OrderVideoLogisticFollowRecordMapper;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoLogisticFollowRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【order_video_logistic_follow_record(跟进记录表)】的数据库操作Service实现
 * @createDate 2025-04-22 16:26:39
 */
@Service
@RequiredArgsConstructor
public class OrderVideoLogisticFollowRecordServiceImpl extends ServiceImpl<OrderVideoLogisticFollowRecordMapper, OrderVideoLogisticFollowRecord>
        implements OrderVideoLogisticFollowRecordService {
    private final OrderResourceService orderResourceService;

    @Override
    public List<OrderVideoLogisticFollowRecordVO> getListByFollowId(Long followId) {
        List<OrderVideoLogisticFollowRecord> orderVideoLogisticFollowRecords = baseMapper.getListByFollowId(followId);
        if (CollUtil.isEmpty(orderVideoLogisticFollowRecords)) {
            return Collections.emptyList();
        }
        List<OrderVideoLogisticFollowRecordVO> orderVideoLogisticFollowRecordVOS = BeanUtil.copyToList(orderVideoLogisticFollowRecords, OrderVideoLogisticFollowRecordVO.class);
        List<Long> resourceIds = new ArrayList<>();
        for (OrderVideoLogisticFollowRecordVO item : orderVideoLogisticFollowRecordVOS) {
            if (StrUtil.isBlank(item.getResourceId())) {
                continue;
            }
            List<Long> ids = StringUtils.splitToLong(item.getResourceId(), StrUtil.COMMA);
            resourceIds.addAll(ids);
        }
        if (CollUtil.isNotEmpty(resourceIds)){
            Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);
            for (OrderVideoLogisticFollowRecordVO item : orderVideoLogisticFollowRecordVOS) {
                List<String> urls = new ArrayList<>();
                if (StrUtil.isBlank(item.getResourceId())) {
                    item.setResources(urls);
                    continue;
                }
                List<Long> ids = StringUtils.splitToLong(item.getResourceId(), StrUtil.COMMA);
                for (Long id : ids) {
                    OrderResource orderResource = resourceMap.get(id);
                    if (ObjectUtil.isNull(orderResource)){
                        item.setResources(urls);
                        continue;
                    }
                    urls.add(orderResource.getObjectKey());
                }
                item.setResources(urls);
            }
        }


        return orderVideoLogisticFollowRecordVOS;
    }

    @Override
    public void deleteByFollowIds(List<Long> followIds) {
        baseMapper.deleteByFollowIds(followIds);
    }
}




