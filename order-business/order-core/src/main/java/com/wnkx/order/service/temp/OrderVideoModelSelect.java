package com.wnkx.order.service.temp;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_模特选择记录对象 order_video_model_select
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@ApiModel(value = "订单_视频_模特选择记录对象 order_video_model_select")
@TableName("order_video_model_select")
@Data
public class OrderVideoModelSelect implements Serializable {

    private static final long serialVersionUID = -2820817575975753367L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /**
     * 视频订单id
     */
    @NotNull(message = "[视频订单id]不能为空")
    @ApiModelProperty(value = "视频订单id", required = true)
    @Excel(name = "视频订单id")
    private Long videoId;

    /**
     * 预选id FK:order_video_preselect_model.id
     */
    @NotNull(message = "[预选id]不能为空")
    @ApiModelProperty(value = "预选id", required = true)
    @Excel(name = "预选id")
    private Long preselectId;

    /**
     * 模特id
     */
    @NotNull(message = "[模特id]不能为空")
    @ApiModelProperty(value = "模特id", required = true)
    @Excel(name = "模特id")
    private Long modelId;

    /**
     * 当前进度状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄）
     */
    @ApiModelProperty(value = "当前进度状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄）", notes = "0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄", required = true)
    @NotNull(message = "[当前进度状态]不能为空")
    @Excel(name = "当前进度状态", readConverterExp = "0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄")
    private Integer status;

    /**
     * 选择时间
     */
    @ApiModelProperty(value = "选择时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "选择时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date selectTime;

    /**
     * 到期时间
     */
    @ApiModelProperty(value = "到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "到期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date overTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
