package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoBackModifyAmountRecords;
import com.wnkx.order.mapper.OrderVideoBackModifyAmountRecordsMapper;
import com.wnkx.order.service.OrderVideoBackModifyAmountRecordsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/8 11:37
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoBackModifyAmountRecordsServiceImpl extends ServiceImpl<OrderVideoBackModifyAmountRecordsMapper, OrderVideoBackModifyAmountRecords> implements OrderVideoBackModifyAmountRecordsService {

    /**
     * 新增运营编辑视频价格记录
     */
    @Override
    public void saveOrderVideoBackModifyAmountRecords(OrderVideoBackModifyAmountRecords orderVideoBackModifyAmountRecords) {
        baseMapper.insert(orderVideoBackModifyAmountRecords);
    }
}
