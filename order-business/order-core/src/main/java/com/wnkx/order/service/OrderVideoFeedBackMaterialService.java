package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.EditCloseReasonEnum;
import com.ruoyi.system.api.domain.dto.order.MaterialRejectDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackMaterialDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialInfoSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back_material(订单反馈表(模特))】的数据库操作Service
 * @createDate 2024-06-18 17:43:06
 */
public interface OrderVideoFeedBackMaterialService extends IService<OrderVideoFeedBackMaterial> {

    List<OrderFeedBackMaterialVO> modelFeedBackList(Long videoId);

    List<OrderFeedBackMaterialVO> modelFeedBackListByVideoIds(List<Long> videoIds);

    List<OrderFeedBackMaterialInfoSimpleVO> selectOrderFeedBackMaterialInfoSimpleVOListByVideoId(Long videoId);

    void rejectModelMaterial(MaterialRejectDTO materialRejectDTO);


    List<OrderVideoFeedBackMaterial> selectListByVideoId(Long videoId);

    /**
     * 获取单个反馈素材
     */
    OrderFeedBackMaterialVO getModelFeedBack(Long id);

    /**
     * 设置模特反馈素材状态为已完成
     */
    void finishModelMaterial(List<Long> videoIds);

    /**
     * 查询模特有反馈素材的视频订单id
     */
    List<Long> selectHasFeedBackMaterialByVideoIds(List<Long> videoIds);

    /**
     * 查询模特视频订单id
     * @param dto
     * @return
     */
    List<Long> selectHasFeedBackMaterialByDto(OrderVideoFeedBackMaterialDTO dto);

    /**
     * 上传链接
     */
    void uploadLink(UploadLinkDTO dto);

    /**
     * 标记下载状态
     * @param id
     */
    void markDownload(Long id);

    /**
     * 通过视频订单ID和回退ID关闭模特反馈素材详情
     */
    void closeMaterialInfoByVideoIdAndRollbackId(Long videoId, Long rollbackId, EditCloseReasonEnum editCloseReasonEnum);

    /**
     * 通过视频订单ID关闭模特反馈素材详情
     */
    void closeMaterialInfoByVideoIds(List<Long> videoIds, EditCloseReasonEnum editCloseReasonEnum);

    /**
     * 通过视频订单ID和回退ID查询是否有模特反馈素材
     */
    Boolean checkExistByVideoIdAndRollbackId(Long videoId, Long rollbackId);
}
