package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderMergeDetail;
import com.wnkx.order.mapper.OrderMergeDetailMapper;
import com.wnkx.order.service.OrderMergeDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 
 * @Date 2025-03-04 10:01:07 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderMergeDetailServiceImpl extends ServiceImpl<OrderMergeDetailMapper, OrderMergeDetail> implements OrderMergeDetailService {

    /**
     * 创建合并单明细
     */
    @Override
    public void createOrderMergeDetail(Long mergeId, List<String> orderNums) {
        List<OrderMergeDetail> orderMergeDetails = orderNums.stream().map(orderNum -> {
            OrderMergeDetail orderMergeDetail = new OrderMergeDetail();
            orderMergeDetail.setMergeId(mergeId);
            orderMergeDetail.setOrderNum(orderNum);
            return orderMergeDetail;
        }).collect(Collectors.toList());

        baseMapper.saveBatch(orderMergeDetails);
    }
}
