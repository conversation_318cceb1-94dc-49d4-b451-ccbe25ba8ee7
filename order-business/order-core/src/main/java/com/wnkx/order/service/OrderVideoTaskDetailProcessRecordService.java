package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskDetailProcessRecordDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailProcessRecord;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailProcessRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 18:02
 */
public interface OrderVideoTaskDetailProcessRecordService extends IService<OrderVideoTaskDetailProcessRecord> {

    /**
     * 工单-新增处理记录
     */
    void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto);

    /**
     * 批量新增处理记录
     */
    void saveBatchWorkOrderProcessRecord(List<OrderVideoTaskDetailProcessRecordDTO> dtoList);

    /**
     * 工单-查询处理记录
     */
    List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordList(String taskNum);
    List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordListByTaskNums(List<String> taskNum);
}
