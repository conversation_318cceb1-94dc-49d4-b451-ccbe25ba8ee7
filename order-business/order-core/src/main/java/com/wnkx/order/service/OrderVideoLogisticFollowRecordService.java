package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollowRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.logistic.OrderVideoLogisticFollowRecordVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_video_logistic_follow_record(跟进记录表)】的数据库操作Service
* @createDate 2025-04-22 16:26:39
*/
public interface OrderVideoLogisticFollowRecordService extends IService<OrderVideoLogisticFollowRecord> {

    /**
     * 根据物流跟进数据Id获取跟进记录列表
     * @param followId
     * @return
     */
    List<OrderVideoLogisticFollowRecordVO> getListByFollowId(Long followId);

    /**
     * 根据跟进表Id列表删除更新记录
     * @param followIds
     */
    void deleteByFollowIds(List<Long> followIds);
}
