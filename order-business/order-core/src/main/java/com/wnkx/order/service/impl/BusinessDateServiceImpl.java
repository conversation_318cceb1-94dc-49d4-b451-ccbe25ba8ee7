package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.BusinessAnalysisVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberDiscountVO;
import com.wnkx.order.mapper.OrderPromotionDetailMapper;
import com.wnkx.order.service.BusinessDateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2025-06-19 11:22
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessDateServiceImpl implements BusinessDateService {
    private final OrderPromotionDetailMapper orderPromotionDetailMapper;
    @Override
    public BusinessAnalysisVO getBusinessMemberDiscountAnalysis(Date startTime, Date endTime) {
        List<MemberDiscountVO> businessMemberDiscountAnalysis = orderPromotionDetailMapper.getBusinessMemberDiscountAnalysis(startTime, endTime);
        if (CollUtil.isEmpty(businessMemberDiscountAnalysis)) {
            BusinessAnalysisVO businessAnalysisVO = new BusinessAnalysisVO();
            businessAnalysisVO.setBusinessOrderTotal(0L);
            businessAnalysisVO.setUpdateTime(new Date());
            businessAnalysisVO.setBusinessOrderPieChartVOS(Collections.emptyList());
            return businessAnalysisVO;
        }

        List<PieChartVO> result = new ArrayList<>();
        for (MemberDiscountVO item :businessMemberDiscountAnalysis){
            PieChartVO init = PieChartVO.init(item.getStatisticsLabelName());
            init.setCount(item.getCount());
            result.add(init);
        }
        BusinessAnalysisVO init = new BusinessAnalysisVO().init(result, result.stream().map(PieChartVO::getLabel).collect(Collectors.toList()));
        init.setUpdateTime(new Date());
        return init;
    }
}
