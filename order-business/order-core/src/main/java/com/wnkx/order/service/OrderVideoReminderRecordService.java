package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderCountVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListVO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoReminderRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:13
 */
public interface OrderVideoReminderRecordService extends IService<OrderVideoReminderRecord> {


    /**
     * 商家催一催
     */
    Boolean reminder(Long videoId);

    /**
     * 催单列表
     */
    List<OrderVideoReminderRecordListVO> selectListByCondition(OrderVideoReminderRecordListDTO dto);

    /**
     * 总催单次数统计
     */
    OrderVideoReminderCountVO reminderCount();

    /**
     * 更新催单记录为已完成
     */
    void updateReminderStatusToFinished(List<Long> videoIds);

    /**
     * 通过视频订单id查询催单记录
     */
    List<OrderVideoReminderRecord> selectListByVideoIds(List<Long> videoIds);

    /**
     * 运营已催单
     */
    void urged(Long videoId);

    /**
     * 获取催一催Redis缓存key
     */
    String getOrderVideoReminderKey(Long videoId);
}
