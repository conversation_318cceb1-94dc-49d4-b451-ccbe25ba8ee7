package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsDay;
import com.wnkx.order.mapper.CustomerServiceDataStatisticsDayMapper;
import com.wnkx.order.service.CustomerServiceDataStatisticsDayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 
 * @Date 2025-05-16 09:34:38 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerServiceDataStatisticsDayServiceImpl extends ServiceImpl<CustomerServiceDataStatisticsDayMapper, CustomerServiceDataStatisticsDay> implements CustomerServiceDataStatisticsDayService {

    /**
     * 通过日期查询客服数据
     */
    @Override
    public CustomerServiceDataStatisticsDay getByWriteTime(String date) {
        return baseMapper.getByWriteTime(date);
    }
}
