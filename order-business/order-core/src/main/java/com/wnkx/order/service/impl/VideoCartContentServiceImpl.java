package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.VideoCartContent;
import com.wnkx.order.mapper.VideoCartContentMapper;
import com.wnkx.order.service.VideoCartContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:45
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VideoCartContentServiceImpl extends ServiceImpl<VideoCartContentMapper, VideoCartContent> implements VideoCartContentService {


    /**
     * 清除购物车关联内容
     */
    @Override
    public void deleteByCartIds(Collection<Long> cartIds) {
        baseMapper.deleteByCartId(cartIds);
    }

    /**
     * 批量保存视频关联内容（先删后加）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchContents(Long cartId, List<VideoCartContent> videoCartContents) {
        baseMapper.deleteByCartId(Collections.singleton(cartId));
        if (CollUtil.isNotEmpty(videoCartContents)) {
            baseMapper.saveBatch(videoCartContents);
        }

    }

    /**
     * 通过购物车id查询关联内容
     */
    @Override
    public List<VideoCartContent> selectListByCartId(Collection<Long> cartIds) {
        return baseMapper.selectListByCartId(cartIds);
    }
}
