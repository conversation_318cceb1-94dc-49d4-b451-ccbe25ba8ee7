package com.wnkx.order.service.impl;

import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelOrderCommissionAnalysisVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO;
import com.wnkx.order.service.ModelDataStatisticsService;
import com.wnkx.order.service.OrderVideoMatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/5/19 9:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ModelDataStatisticsServiceImpl implements ModelDataStatisticsService {

    private final OrderVideoMatchService orderVideoMatchService;



    /**
     * 模特数据-成功匹配次数
     */
    @Override
    public ModelSuccessMatchCountVO getModelSuccessMatchCount() {
        return orderVideoMatchService.getModelSuccessMatchCount();
    }

    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    @Override
    public ModelOrderCommissionAnalysisVO getModelOrderCommissionAnalysis(ModelOrderCommissionAnalysisDTO dto) {
        return orderVideoMatchService.getModelOrderCommissionAnalysis(dto);
    }
}
