package com.wnkx.order.service;

import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.HandleStatusEnum;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFollowListDTO;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.logistic.*;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_video_logistic_follow(物流跟进表)】的数据库操作Service
* @createDate 2025-04-22 16:26:39
*/
public interface OrderVideoLogisticFollowService extends IService<OrderVideoLogisticFollow> {

    /**
     * 获取物流跟进数据列表
     * @param orderVideoLogisticFollowDTO
     * @return
     */
    List<OrderVideoLogisticFollowVO> selectOrderVideoLogisticFollowList(OrderVideoLogisticFollowListDTO orderVideoLogisticFollowDTO);

    /**
     * 获取跟进记录详情
     * @param id
     * @return
     */
    OrderVideoLogisticFollowDetailVO getDetailById(Long id);

    /**
     * 检查处理状态是否正确
     * @param orderVideoLogisticFollows
     * @param statusEnums
     */
    void checkHandleStatus(List<OrderVideoLogisticFollow> orderVideoLogisticFollows, HandleStatusEnum... statusEnums);

    /**
     * 获取物流跟进数据列表
     * @param orderVideoLogisticFollowDTO
     * @return
     */
    List<OrderVideoLogisticFollow> queryListBase(OrderVideoLogisticFollowDTO orderVideoLogisticFollowDTO);

    /**
     * 重置消息
     * @param entity
     */
    void resetEntity(OrderVideoLogisticFollow entity);

    /**
     * 根据视频订单id列表 删除物流跟进数据
     * @param videoIds
     */
    void deleteByVideoIds(List<Long> videoIds);

    /**
     * 获取物流跟进统计信息
     * @return
     */
    OrderVideoLogisticFollowStatisticsVO getStatisticsVO();


    /**
     * 订单列表-获取对接人下拉框
     */
    List<UserVO> orderContactSelect(String keyword, Integer followStatus);


    /**
     * 订单列表-获取出单人下拉框
     */
    List<UserVO> orderIssueSelect(String keyword, Integer followStatus);

    /**
     * 获取商家编码及数量
     * @param dto
     * @return
     */
    List<MemberCodeListVO> memberCodeList(OrderVideoLogisticFollowListDTO dto);

    /**
     * 获取商家模特及数量
     * @param dto
     * @return
     */
    List<LogisticFollowModelListVO> modelListSelect(OrderVideoLogisticFollowListDTO dto);

}
