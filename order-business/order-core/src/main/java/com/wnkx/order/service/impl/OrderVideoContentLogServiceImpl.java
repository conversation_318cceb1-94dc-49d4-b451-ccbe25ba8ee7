package com.wnkx.order.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContentLog;
import com.wnkx.order.mapper.OrderVideoContentLogMapper;
import com.wnkx.order.service.OrderVideoContentLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class OrderVideoContentLogServiceImpl extends ServiceImpl<OrderVideoContentLogMapper, OrderVideoContentLog> implements OrderVideoContentLogService {
    @Override
    public List<OrderVideoContentLog> selectListByVideoIdOrTypes(Long id, List<Integer> list) {
        return baseMapper.selectList(Wrappers.lambdaQuery(OrderVideoContentLog.class).eq(OrderVideoContentLog::getVideoId, id).in(OrderVideoContentLog::getType, list));
    }
}
