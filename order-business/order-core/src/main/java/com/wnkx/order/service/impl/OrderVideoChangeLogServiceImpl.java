package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoChangeLogDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLog;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLogInfo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogVO;
import com.wnkx.order.mapper.OrderVideoChangeLogMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoChangeLogInfoService;
import com.wnkx.order.service.OrderVideoChangeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoChangeLogServiceImpl extends ServiceImpl<OrderVideoChangeLogMapper, OrderVideoChangeLog> implements OrderVideoChangeLogService {

    private final OrderVideoChangeLogInfoService orderVideoChangeLogInfoService;
    private final RemoteService remoteService;

    /**
     * 添加变更记录
     */
    @Override
    public void addVideoChangeLog(OrderVideoChangeLogDTO dto) {
        SpringUtils.getAopProxy(this).addVideoChangeLog(Collections.singletonList(dto));
    }

    /**
     * 查询视频订单是否有变更记录
     */
    @Override
    public boolean videoHasChangeLog(Long videoId) {
        return baseMapper.videoHasChangeLog(videoId);
    }

    /**
     * 添加变更记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addVideoChangeLog(List<OrderVideoChangeLogDTO> dtoList) {
        List<OrderVideoChangeLogInfo> orderVideoChangeLogInfos = new ArrayList<>();

        //  收集意向模特ID 替换为意向模特账号
        List<Long> intentionModelIds = new ArrayList<>();
        for (OrderVideoChangeLogDTO orderVideoChangeLogDTO : dtoList) {
            if (CollUtil.isEmpty(orderVideoChangeLogDTO.getData()) || !orderVideoChangeLogDTO.getData().containsKey("intentionModelId")) {
                continue;
            }
            Object intentionModelId = orderVideoChangeLogDTO.getData().get("intentionModelId");
            intentionModelIds.add(Convert.toLong(intentionModelId));
        }
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(intentionModelIds);

        for (OrderVideoChangeLogDTO orderVideoChangeLogDTO : dtoList) {
            if (CollUtil.isEmpty(orderVideoChangeLogDTO.getData())) {
                continue;
            }
            OrderVideoChangeLog orderVideoChangeLog = BeanUtil.copyProperties(orderVideoChangeLogDTO, OrderVideoChangeLog.class);
            orderVideoChangeLog.setChangeTime(DateUtil.date());
            orderVideoChangeLog.setChangeUserId(SecurityUtils.getUserId());
            baseMapper.insert(orderVideoChangeLog);

            for (Map.Entry<String, Object> entry : orderVideoChangeLogDTO.getData().entrySet()) {
                OrderVideoChangeLogInfo orderVideoChangeLogInfo = new OrderVideoChangeLogInfo();
                orderVideoChangeLogInfo.setChangeLogId(orderVideoChangeLog.getId());
                orderVideoChangeLogInfo.setFieldName(entry.getKey());
                if ("intentionModelId".equals(entry.getKey()) && ObjectUtil.isNotNull(entry.getValue())) {
                    entry.setValue(modelSimpleMap.getOrDefault(Convert.toLong(entry.getValue()), new ModelOrderSimpleVO()).getAccount());
                }
                Object value = entry.getValue();
                if (ObjectUtil.isNotNull(value)) {
                    orderVideoChangeLogInfo.setFieldType(value.getClass().getName());
                    orderVideoChangeLogInfo.setValue(JSON.toJSONString(value));
                }
                orderVideoChangeLogInfos.add(orderVideoChangeLogInfo);
            }
        }
        orderVideoChangeLogInfoService.saveBatch(orderVideoChangeLogInfos);
    }

    /**
     * 订单详情-变更记录
     */
    @Override
    public List<OrderVideoChangeLogVO> selectVideoChangeListByVideoId(Long videoId) {
        List<OrderVideoChangeLog> changeList = baseMapper.selectVideoChangeListByVideoId(videoId);
        if (CollUtil.isEmpty(changeList)) {
            return Collections.emptyList();
        }
        List<Long> changeLogIds = changeList.stream().map(OrderVideoChangeLog::getId).collect(Collectors.toList());
        List<OrderVideoChangeLogInfo> logInfoList = orderVideoChangeLogInfoService.selectChangeLogInfoListByChangeLogIds(changeLogIds);
        if (CollUtil.isEmpty(logInfoList)) {
            return Collections.emptyList();
        }

        List<OrderVideoChangeLogVO> result = BeanUtil.copyToList(changeList, OrderVideoChangeLogVO.class);

        List<Long> changeUserIds = result.stream().map(OrderVideoChangeLogVO::getChangeUserId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(changeUserIds).build());

        List<OrderVideoChangeLogInfoVO> logInfoListVOS = BeanUtil.copyToList(logInfoList, OrderVideoChangeLogInfoVO.class);

        Map<Long, List<OrderVideoChangeLogInfoVO>> logInfoListVOMap = logInfoListVOS.stream().collect(Collectors.groupingBy(OrderVideoChangeLogInfoVO::getChangeLogId));

        for (OrderVideoChangeLogVO orderVideoChangeLogVO : result) {
            orderVideoChangeLogVO.setChangeUser(userMap.get(orderVideoChangeLogVO.getChangeUserId()));
            orderVideoChangeLogVO.setChangeLogInfoList(logInfoListVOMap.get(orderVideoChangeLogVO.getId()));
        }
        return result;
    }
}
