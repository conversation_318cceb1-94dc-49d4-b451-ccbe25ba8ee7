package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderVideoFeedBackTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackDTO;
import com.ruoyi.system.api.domain.dto.order.VideoScoreListDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailSimpleVO;
import com.ruoyi.system.api.domain.vo.order.VideoScoreListVO;
import com.wnkx.order.mapper.OrderVideoFeedBackMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoFeedBackMaterialInfoService;
import com.wnkx.order.service.OrderResourceService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialInfoTaskDetailService;
import com.wnkx.order.service.OrderVideoFeedBackService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back(订单反馈表(商家))】的数据库操作Service实现
 * @createDate 2024-06-15 16:43:02
 */
@Service
@RequiredArgsConstructor
public class OrderVideoFeedBackServiceImpl extends ServiceImpl<OrderVideoFeedBackMapper, OrderVideoFeedBack> implements OrderVideoFeedBackService {

    private final RemoteService remoteService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final OrderResourceService orderResourceService;

    /**
     * 通过视频订单ID获取每个视频订单最新一条的反馈给商家素材
     */
    @Override
    public List<OrderVideoFeedBack> selectLatestFeedBackListByVideoIds(List<Long> videoIds) {
        return baseMapper.selectLatestFeedBackListByVideoIds(videoIds);
    }

    /**
     * 通过视频订单ID查询反馈给商家的素材（部分字段）
     */
    @Override
    public List<OrderVideoFeedBack> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(videoIds);
    }

    /**
     * 通过视频订单ID和回退ID查询反馈给商家的素材
     */
    @Override
    public List<OrderVideoFeedBack> selectListByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return baseMapper.selectListByVideoIdAndRollbackId(videoId, rollbackId);
    }

    /**
     * 通过模特反馈素材详情ID查询数据
     */
    @Override
    public List<OrderVideoFeedBack> selectListByMaterialInfoIds(List<Long> materialInfoIds) {
        if (CollUtil.isEmpty(materialInfoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectListByMaterialInfoIds(materialInfoIds);
    }

    /**
     * 视频评价记录 评价人下拉框
     */
    @Override
    public List<UserVO> videoScoreListSelectEvaluatePerson() {
        List<Long> videoScoreByIds = baseMapper.getVideoScoreByIds();
        if (CollUtil.isEmpty(videoScoreByIds)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(videoScoreByIds);
        return remoteService.customerServiceSelect(dto);
    }

    /**
     * 视频评价记录 拍摄模特下拉框
     */
    @Override
    public List<ModelSelectVO> videoScoreListSelectShootModel() {
        List<Long> shootModelIds = baseMapper.getShootModelId();
        if (CollUtil.isEmpty(shootModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(shootModelIds);
        return remoteService.modelSelect(modelListDTO);
    }

    /**
     * 视频评价记录 列表
     */
    @Override
    public List<VideoScoreListVO> selectVideoScoreListByCondition(VideoScoreListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovfb.video_score_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<VideoScoreListVO> videoScoreListVOS = baseMapper.selectVideoScoreListByCondition(dto);
        if (CollUtil.isEmpty(videoScoreListVOS)) {
            return videoScoreListVOS;
        }

        List<String> referencePicIds = videoScoreListVOS.stream().map(VideoScoreListVO::getReferencePicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        Set<Long> shootModelIds = videoScoreListVOS.stream().map(VideoScoreListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        Set<Long> videoScoreByIds = videoScoreListVOS.stream().map(VideoScoreListVO::getVideoScoreById).collect(Collectors.toSet());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(videoScoreByIds).build());

        for (VideoScoreListVO videoScoreListVO : videoScoreListVOS) {
            //  参考图片
            List<Long> referencePicIdList = StringUtils.splitToLong(videoScoreListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicIdList) {
                OrderResource resource = resourceMap.get(referencePicId);
                if (resource != null) {
                    videoScoreListVO.getReferencePic().add(resource.getObjectKey());
                }
            }

            videoScoreListVO.setShootModel(modelSimpleMap.get(videoScoreListVO.getShootModelId()));

            videoScoreListVO.setVideoScoreBy(userMap.getOrDefault(videoScoreListVO.getVideoScoreById(), new UserVO()).getName());
        }

        return videoScoreListVOS;
    }

    @Override
    public void addFeedBack(OrderVideoFeedBackDTO dto) {
        SpringUtils.getBean(IOrderVideoFeedBackMaterialInfoService.class).addFeedBack(dto);
    }

    @Override
    public List<OrderVideoFeedBack> getFeedBackList(Long videoId, Long rollbackId) {
        return baseMapper.getFeedBackList(videoId, rollbackId);
    }

    @Override
    public List<OrderVideoFeedBack> getFeedBackListByVideoIds(List<Long> videoIds) {
        return baseMapper.selectFeedBackListByVideoIds(videoIds);
    }

    @Override
    public List<OrderFeedBackVO> getFeedBack(List<Long> videoIds) {
        List<OrderVideoFeedBack> list = baseMapper.selectFeedBackListByVideoIds(videoIds);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        Set<Long> createUserIds = list.stream().map(OrderVideoFeedBack::getCreateUserId).collect(Collectors.toSet());
        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(createUserIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(sysUserListDTO);

        List<OrderFeedBackVO> orderFeedBackList = new ArrayList<>();
        for (OrderVideoFeedBack orderVideoFeedBack : list) {
            OrderFeedBackVO orderFeedBackVO = BeanUtil.copyProperties(orderVideoFeedBack, OrderFeedBackVO.class);
            orderFeedBackVO.setCreateUser(userMap.get(orderFeedBackVO.getCreateUserId()));
            orderFeedBackVO.setCanModify(StatusTypeEnum.YES.getCode().equals(orderVideoFeedBack.getIsNew()) && CharSequenceUtil.isBlank(orderVideoFeedBack.getModifyReason()));

            orderFeedBackList.add(orderFeedBackVO);
        }

        return orderFeedBackList;
    }


    @Override
    public List<OrderFeedBackVO> getFeedBackList(List<Long> videoIds) {
        List<OrderVideoFeedBack> list = baseMapper.selectFeedBackListByVideoIds(videoIds);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

       // 分组
        Map<Integer, List<OrderVideoFeedBack>> groupedByType = list.stream()
                .collect(Collectors.groupingBy(OrderVideoFeedBack::getType));

        // 拆分
        List<OrderFeedBackVO> finalList = groupedByType.values().stream()
                .flatMap(feedbacks -> feedbacks.stream().flatMap(feedback -> {
                    List<OrderFeedBackVO> resultList = new ArrayList<>();
                    OrderFeedBackVO vo = BeanUtil.copyProperties(feedback, OrderFeedBackVO.class);

                    if (OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode().equals(feedback.getType())) {
                        if (StrUtil.isNotBlank(feedback.getVideoUrl())) {
                            OrderFeedBackVO videoVo = BeanUtil.copyProperties(vo, OrderFeedBackVO.class);
                            videoVo.setType(OrderVideoFeedBackTypeEnum.VIDEO.getCode());
                            videoVo.setPicUrl(null);
                            resultList.add(videoVo);
                        }
                        if (StrUtil.isNotBlank(feedback.getPicUrl())) {
                            OrderFeedBackVO picVo = BeanUtil.copyProperties(vo, OrderFeedBackVO.class);
                            picVo.setType(OrderVideoFeedBackTypeEnum.PIC.getCode());
                            picVo.setVideoUrl(null);
                            resultList.add(picVo);
                        }
                    } else {
                        resultList.add(vo);
                    }

                    return resultList.stream();
                }))
                .collect(Collectors.toList());

        Map<Integer, List<OrderFeedBackVO>> groupType = finalList.stream()
                .collect(Collectors.groupingBy(OrderFeedBackVO::getType));

        Optional<OrderFeedBackVO> max = finalList.stream()
                .max(Comparator.comparing(OrderFeedBackVO::getCreateTime));

        Map<Integer, Date> latestTimeByType = finalList.stream()
                .collect(Collectors.groupingBy(OrderFeedBackVO::getType,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(OrderFeedBackVO::getCreateTime)),
                                optional -> optional.map(OrderFeedBackVO::getCreateTime).orElse(null))));

        finalList = groupType.values().stream().flatMap(items -> items.stream().peek(item -> {
            Date latestTime = latestTimeByType.get(item.getType());
            if (latestTime != null) {
                item.setIsNew(items.size() > 1 && item.getCreateTime().compareTo(latestTime) >= 0);
            }
        })).collect(Collectors.toList());

        Set<Long> createUserIds = finalList.stream()
                .map(OrderFeedBackVO::getCreateUserId)
                .collect(Collectors.toSet());

        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(createUserIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(sysUserListDTO);

        finalList.forEach(vo -> vo.setCreateUser(userMap.get(vo.getCreateUserId())));

        return finalList.stream()
                .sorted(Comparator.comparing(OrderFeedBackVO::getCreateTime).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<OrderFeedBackSimpleVO> getOrderFeedBackSimpleVO(Long videoId) {
        List<OrderFeedBackVO> feedBack = getFeedBack(List.of(videoId));
        if (CollUtil.isEmpty(feedBack)){
            return Collections.emptyList();
        }
        List<Long> feedBackIds = feedBack.stream().map(OrderFeedBackVO::getId).collect(Collectors.toList());
        List<OrderVideoTaskDetailSimpleVO> orderVideoTaskDetailSimpleVOS = orderVideoFeedBackMaterialInfoTaskDetailService.selectListByFeedBackIds(feedBackIds);
        Map<Long, List<OrderVideoTaskDetailSimpleVO>> orderVideoTaskDetailSimpleVOMap = orderVideoTaskDetailSimpleVOS.stream().collect(Collectors.groupingBy(OrderVideoTaskDetailSimpleVO::getFeedBackId));

        List<OrderFeedBackSimpleVO> orderFeedBackSimpleVos = new ArrayList<>();
        for (OrderFeedBackVO item : feedBack){
            OrderFeedBackSimpleVO vo = new OrderFeedBackSimpleVO();
            List<OrderVideoTaskDetailSimpleVO> taskDetailSimpleVOS = orderVideoTaskDetailSimpleVOMap.get(item.getId());
            if (CollUtil.isNotEmpty(taskDetailSimpleVOS)) {
                taskDetailSimpleVOS.sort(Comparator.comparing(OrderVideoTaskDetailSimpleVO::getSubmitTime));
                vo.setOrderVideoTaskDetailSimpleVOS(taskDetailSimpleVOS);
            }

            if (OrderVideoFeedBackTypeEnum.VIDEO.getCode().equals(item.getType())){
                vo.setUrl(item.getVideoUrl());
                vo.setType(OrderVideoFeedBackTypeEnum.VIDEO.getCode());
                vo.setRollbackId(item.getRollbackId());
                vo.setCreateUser(item.getCreateUser());
                vo.setCreateTime(item.getCreateTime());
            }else if (OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode().equals(item.getType())){
                //产品需要将一条数据拆分成2个
                if (StrUtil.isNotBlank(item.getVideoUrl())){
                    OrderFeedBackSimpleVO orderFeedBackSimpleVO = BeanUtil.copyProperties(vo, OrderFeedBackSimpleVO.class);
                    orderFeedBackSimpleVO.setUrl(item.getVideoUrl());
                    orderFeedBackSimpleVO.setType(OrderVideoFeedBackTypeEnum.VIDEO.getCode());
                    orderFeedBackSimpleVO.setRollbackId(item.getRollbackId());
                    orderFeedBackSimpleVO.setCreateUser(item.getCreateUser());
                    orderFeedBackSimpleVO.setCreateTime(item.getCreateTime());
                    orderFeedBackSimpleVos.add(orderFeedBackSimpleVO);
                }
                if (StrUtil.isNotBlank(item.getPicUrl())){
                    vo.setUrl(item.getPicUrl());
                    vo.setType(OrderVideoFeedBackTypeEnum.PIC.getCode());
                    vo.setRollbackId(item.getRollbackId());
                    vo.setCreateUser(item.getCreateUser());
                    vo.setCreateTime(item.getCreateTime());
                }
            }else if (OrderVideoFeedBackTypeEnum.PIC.getCode().equals(item.getType())){
                vo.setUrl(item.getPicUrl());
                vo.setType(OrderVideoFeedBackTypeEnum.PIC.getCode());
                vo.setRollbackId(item.getRollbackId());
                vo.setCreateUser(item.getCreateUser());
                vo.setCreateTime(item.getCreateTime());
            }
            orderFeedBackSimpleVos.add(vo);
        }
        return orderFeedBackSimpleVos;
    }


    private OrderFeedBackVO createFeedBackVO(OrderVideoFeedBack feedback, Integer type, boolean isVideo) {
        OrderFeedBackVO vo = BeanUtil.copyProperties(feedback, OrderFeedBackVO.class);
        vo.setType(type);
        if (isVideo) {
            vo.setPicUrl(null);
        } else {
            vo.setVideoUrl(null);
        }
        return vo;
    }

    private boolean isLatestFeedback(OrderFeedBackVO item, Date latestTime, Date maxCreateTime) {
        Date itemCreateTime = item.getCreateTime();
        boolean isLatest = false;

        if (latestTime != null) {
            isLatest = itemCreateTime.compareTo(latestTime) >= 0;
            if (maxCreateTime != null) {
                isLatest = isLatest && itemCreateTime.compareTo(maxCreateTime) >= 0;
            }
        } else if (maxCreateTime != null) {
            isLatest = itemCreateTime.compareTo(maxCreateTime) >= 0;
        }

        return isLatest;
    }
}




