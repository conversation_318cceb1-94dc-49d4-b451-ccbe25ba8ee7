package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderCommentDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoComment;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_comment(订单备注表)】的数据库操作Service
 * @createDate 2024-06-17 19:43:43
 */
public interface OrderVideoCommentService extends IService<OrderVideoComment> {

    void addOrderComment(OrderCommentDTO orderCommentDTO);

    List<OrderVideoComment> orderCommentList(Long videoId);

    List<OrderVideoComment> orderCommentListByVideoIds(List<Long> videoIds);

    List<Long> getVideoIds();
}
