package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.OrderModelMyOrderListDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkDTO;
import com.ruoyi.system.api.domain.vo.order.*;

import java.util.List;

public interface ModelPersonService {

    /**
     * 我的订单
     */
    List<OrderModelMyOrderListVO> myOrder(OrderModelMyOrderListDTO dto);

    /**
     * 售后订单
     */
    List<OrderModelWorkbenchAfterSalesListVO> afterSales(OrderModelMyOrderListDTO dto);


    /**
     * 我的订单徽标
     */
    OrderModelMyOrderBadgeVO myOrderBadge();

    /**
     * 订单详情（非售后状态）
     */
    OrderModelNormalInfoVO getOrderModelNormalInfo(Long videoId);

    /**
     * 根据视频编码获取订单详情（非售后状态）
     * @param videoCode
     * @return
     */
    OrderModelNormalInfoVO getOrderModelNormalInfoByVideoCode(String videoCode);

    /**
     * 售后订单详情
     */
    OrderModelAfterSalesInfoVO afterSalesDetailInfo(Long id);

    /**
     * 上传链接
     */
    void uploadLink(UploadLinkDTO dto);


    /**
     * 上传链接记录
     */
    List<OrderFeedBackMaterialVO> getUploadLinkRecord(Long videoId);

    /**
     * 根据视频编码获取上传链接记录
     * @param videoCode
     * @return
     */
    List<OrderFeedBackMaterialVO> getUploadLinkRecordByVideoCode(String videoCode);

    /**
     * 上传链接记录(售后)
     */
    OrderFeedBackMaterialVO getUploadLinkRecordAfterSale(Long materialId);

    /**
     * 模特选择记录
     */
    List<OrderVideoModelSelectListVO> getModelSelectRecord();

    /**
     * 校验模特数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    Boolean checkModelDataScope(List<Long> videoIds, Long userid);

    /**
     * 模特撤销申请
     */
    void cancelApply(Long videoId);
}
