package com.wnkx.order.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;
import com.wnkx.order.service.IWechatOrderTableService;
import com.wnkx.order.mapper.WechatOrderTableMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【wechat_order_table(微信订单表)】的数据库操作Service实现
 * @createDate 2024-10-25 09:28:00
 */
@Service
public class WechatOrderTableServiceImpl extends ServiceImpl<WechatOrderTableMapper, WechatOrderTable>
        implements IWechatOrderTableService {

    @Override
    public WechatOrderTable getValidWechatOrderTable(String orderNum) {
        if (StrUtil.isBlank(orderNum)) {
            return null;
        }
        return baseMapper.getValidWechatOrderTable(orderNum);
    }

    @Override
    public WechatOrderTable getByOutTradeNo(String outTradeNo) {
        if (StrUtil.isBlank(outTradeNo)) {
            return null;
        }
        return baseMapper.getByOutTradeNo(outTradeNo);
    }

    @Override
    public Boolean banWechatOrderTable(Long id) {
        return this.updateById(WechatOrderTable.builder().id(id).status(StatusTypeEnum.NO.getCode()).build());
    }

    @Override
    public Boolean banWechatOrderTableByOrderNum(String orderNum) {
        Assert.notNull(orderNum, "内部订单号不能为空");
        return this.update(new LambdaUpdateWrapper<WechatOrderTable>()
                .set(WechatOrderTable::getStatus, StatusTypeEnum.NO.getCode())
                .eq(WechatOrderTable::getOrderNum, orderNum)
        );
    }

    @Override
    public List<WechatOrderTable> getValidWechatOrderTableList(String orderNum) {
        if (StrUtil.isBlank(orderNum)) {
            return Collections.emptyList();
        }
        return baseMapper.getValidFyOrderTableListByOrderNum(orderNum);
    }
}




