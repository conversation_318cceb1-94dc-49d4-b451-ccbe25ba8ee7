package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig;
import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfigChangelog;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigVO;
import com.wnkx.order.service.OrderBasePriceConfigChangelogService;
import com.wnkx.order.mapper.OrderBasePriceConfigChangelogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_base_price_config_changelog(订单基础价格配置表_更改记录)】的数据库操作Service实现
 * @createDate 2025-05-16 16:58:03
 */
@Service
public class OrderBasePriceConfigChangelogServiceImpl extends ServiceImpl<OrderBasePriceConfigChangelogMapper, OrderBasePriceConfigChangelog>
        implements OrderBasePriceConfigChangelogService {

    @Override
    public void addChangeLog(OrderBasePriceConfigVO previousConfig, OrderBasePriceConfig orderBasePriceConfig) {
        if (previousConfig.getOriginPrice().compareTo(orderBasePriceConfig.getOriginPrice()) == 0 &&
                previousConfig.getOriginPriceProxy().compareTo(orderBasePriceConfig.getOriginPriceProxy()) == 0 &&
                previousConfig.getCurrentPrice().compareTo(orderBasePriceConfig.getCurrentPrice()) == 0 &&
                previousConfig.getCurrentPriceProxy().compareTo(orderBasePriceConfig.getCurrentPriceProxy()) == 0
                && previousConfig.getSinceTime().compareTo(orderBasePriceConfig.getSinceTime()) == 0
        ) {
            return;
        }
        OrderBasePriceConfigChangelog orderBasePriceConfigChangelog = new OrderBasePriceConfigChangelog();
        orderBasePriceConfigChangelog.setPriceType(orderBasePriceConfig.getPriceType());

        orderBasePriceConfigChangelog.setOriginPrice(orderBasePriceConfig.getOriginPrice());
        orderBasePriceConfigChangelog.setOriginPricePrevious(previousConfig.getOriginPrice());

        orderBasePriceConfigChangelog.setOriginPriceProxy(orderBasePriceConfig.getOriginPriceProxy());
        orderBasePriceConfigChangelog.setOriginPriceProxyPrevious(previousConfig.getOriginPriceProxy());

        orderBasePriceConfigChangelog.setCurrentPrice(orderBasePriceConfig.getCurrentPrice());
        orderBasePriceConfigChangelog.setCurrentPricePrevious(previousConfig.getCurrentPrice());

        orderBasePriceConfigChangelog.setCurrentPriceProxy(orderBasePriceConfig.getCurrentPriceProxy());
        orderBasePriceConfigChangelog.setCurrentPriceProxyPrevious(previousConfig.getCurrentPriceProxy());

        orderBasePriceConfigChangelog.setSinceTime(orderBasePriceConfig.getSinceTime());
        orderBasePriceConfigChangelog.setCreateBy(orderBasePriceConfig.getCreateBy());
        orderBasePriceConfigChangelog.setCreateById(orderBasePriceConfig.getCreateById());
        orderBasePriceConfigChangelog.setUpdateBy(orderBasePriceConfig.getUpdateBy());
        orderBasePriceConfigChangelog.setUpdateById(orderBasePriceConfig.getUpdateById());
        save(orderBasePriceConfigChangelog);
    }

    @Override
    public List<OrderBasePriceConfigChangelogVO> getConfigChangeLogList(Integer type) {
        if (type == null || !type.equals(OrderConstant.TYPE_SERVICE_PRICE)) {
            return null;
        }
        return baseMapper.getConfigChangeLogList(type);
    }
}




