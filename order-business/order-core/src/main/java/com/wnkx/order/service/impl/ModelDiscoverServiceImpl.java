package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.config.ModelProperties;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.message.MessageDTO;
import com.ruoyi.system.api.domain.dto.biz.message.ModelSelectMessageDTO;
import com.ruoyi.system.api.domain.dto.order.OrderModelListDTO;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelLoginVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.model.LoginModel;
import com.wnkx.order.config.MessageConfig;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.ModelDiscoverMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/2 9:27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelDiscoverServiceImpl implements ModelDiscoverService {

    private final ModelDiscoverMapper modelDiscoverMapper;

    private final OrderVideoProperties orderVideoProperties;

    private final RemoteService remoteService;
    /**
     * 订单服务
     */
    private final IOrderService orderService;
    /**
     * 视频订单服务
     */
    private final IOrderVideoService orderVideoService;
    /**
     * 视频内容服务
     */
    private final IOrderVideoContentService videoContentService;
    /**
     * 视频订单预选模特服务
     */
    private final IOrderVideoMatchPreselectModelService orderVideoPreselectModelService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final OrderResourceService orderResourceService;
    private final RedisService redisService;
    private final MessageConfig messageConfig;

    private final ModelWorkbenchService orderModelWorkbenchService;
    private final ModelProperties modelProperties;

    /**
     * RECOMMEND页面
     */
    @Override
    public List<RecommendModelListVO> selectRecommendList() {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovmpm.add_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<RecommendModelListVO> list = modelDiscoverMapper.selectRecommendList(SecurityUtils.getUserId());
        for (RecommendModelListVO recommendModelListVO : list) {
            recommendModelListVO.setSurplusPicCount(Math.max((PicCountEnum.getValue(recommendModelListVO.getPicCount()) - recommendModelListVO.getRefundPicCount()), 0));
        }
        return list;
    }

    /**
     * 首页订单数量统计
     */
    @Override
    public ModelHomePageOrderStatisticsVO orderStatistics() {
        OrderModelListDTO orderModelListDTO = new OrderModelListDTO();
        assembleCondition(orderModelListDTO);

        Long allCount;
        if (StatusTypeEnum.NO.getCode().equals(orderModelListDTO.getIsShow())) {
            allCount = 0L;
        } else {
            if (Boolean.TRUE.equals(orderModelListDTO.getIsQuality())) {
                allCount = modelDiscoverMapper.getOrderQualityModelCountByCondition(orderModelListDTO);
            } else {
                allCount = modelDiscoverMapper.getOrderOrdinaryModelCountByCondition(orderModelListDTO);
            }
        }
        return ModelHomePageOrderStatisticsVO
                .builder()
                .forMeCount(orderModelWorkbenchService.getForMeCount())
                .allCount(allCount)
                .recommendCount(modelDiscoverMapper.getRecommendCount(SecurityUtils.getUserId()))
                .build();
    }

    /**
     * 模特端-首页-模特报名
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long apply(Long videoId) {
        OrderVideoMatch orderVideoMatch = orderVideoMatchService.getActiveByVideoId(videoId);
        Assert.notNull(orderVideoMatch, "Order does not exist, please try again later!");
        Assert.isTrue(orderVideoMatch.getStatus().equals(OrderVideoMatchStatusEnum.NORMAL.getCode()), "The order has been matched, please try again later!");
        Assert.isNull(orderVideoMatch.getEndTime(), "The order has been matched, please try again later!");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId()), "The system is busy. Please try again later");

            LoginModel loginModel = (LoginModel) SecurityUtils.getLoginUser();
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setId(Arrays.asList(loginModel.getModelLoginVO().getId()));
            List<ModelInfoVO> models = remoteService.innerList(modelListDTO);
            Assert.isTrue(CollUtil.isNotEmpty(models), "system error, Please contact the administrator");

            ModelInfoVO modelInfoVO = models.get(0);
            Integer applyOrderNum = modelProperties.getApplyOrderNum().getApplyOrderNum(modelInfoVO.getCooperation());
            Integer appliedOrderNum = redisService.getCacheIncr(CacheConstants.MODEL_APPLY_KEY + modelInfoVO.getId());
            Assert.isTrue(appliedOrderNum < applyOrderNum, "There are no orders available for application today.");// 中文释义：今天没有可以申请的订单了

            //  校验模特能不能报名这笔订单
            List<OrderModelListVO> orderModelListVOS = selectOrderModelListByCondition(OrderModelListDTO.builder().videoId(videoId).build());
            Assert.notEmpty(orderModelListVOS, "Assigned to another creator.");//    中文释义：来晚啦~订单被别人选走了

            Collection<Long> cannotModelIds = orderService.checkModelCanAccept(Collections.singletonList(SecurityUtils.getUserId()), orderModelListVOS.get(0).getCreateOrderBizUserId());
            Assert.isTrue(CollUtil.isEmpty(cannotModelIds), "You are no longer eligible to apply");//  中文释义：您已无法进行申请~

            AddDistributionErrorVO addDistributionErrorVO = orderVideoPreselectModelService.addPreselectModel(null, orderVideoMatch.getId(), Collections.singletonList(SecurityUtils.getUserId()), PreselectModelAddTypeEnum.MODEL_OPTIONAL, PreselectStatusEnum.UN_JOINTED, null);
            Assert.notEmpty(addDistributionErrorVO.getPreselectModelIds(), "operation failure");
            List<Long> preselectModelIds = addDistributionErrorVO.getPreselectModelIds();

            //模特报名需要发布消息
            try {
                List<UserVO> persons = modelInfoVO.getPersons();
                if (CollUtil.isEmpty(persons)) {
                    return 0L;
                }
                ModelSelectMessageDTO modelSelectMessageDTO = new ModelSelectMessageDTO();
                modelSelectMessageDTO.setUserId(persons.get(0).getId());
                MessageDTO messageDTO = new MessageDTO();
                messageDTO.setType(MessageTypeEnum.MODEL_SELECT_ORDER.getCode());
                messageDTO.setUrl(MessageTypeEnum.MODEL_SELECT_ORDER.getUrl());
                messageDTO.setModelId(modelInfoVO.getId());
                modelSelectMessageDTO.setData(messageDTO);

                redisService.publish(messageConfig.getRedisModelSelectTopic(), modelSelectMessageDTO);
            } catch (Exception e) {
                //发布消息失败不影响模特报名
                log.error("发布消息失败", e);
            }

            redisService.setIncr(CacheConstants.MODEL_APPLY_KEY + modelInfoVO.getId());
            return preselectModelIds.get(0);
        }finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 模特端-首页-订单详情（首页及forme的订单详情）
     */
    @Override
    public OrderModelApplyInfoVO getOrderModelInfo(Long videoId, Long preselectModelId) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        if (ObjectUtil.isNull(orderVideo)) {
            return null;
        }

        return getOrderModelApplyInfoVO(videoId, preselectModelId, orderVideo);
    }

    private OrderModelApplyInfoVO getOrderModelApplyInfoVO(Long videoId, Long preselectModelId, OrderVideo orderVideo) {
        OrderModelApplyInfoVO info = BeanUtil.copyProperties(orderVideo, OrderModelApplyInfoVO.class);
        assembleBaseInfo(info);

        OrderModelListDTO orderModelListDTO = new OrderModelListDTO();
        assembleCondition(orderModelListDTO);
        orderModelListDTO.setVideoId(videoId);

        Long allCount;
        if (StatusTypeEnum.NO.getCode().equals(orderModelListDTO.getIsShow())) {
            allCount = 0L;
        } else {
            if (Boolean.TRUE.equals(orderModelListDTO.getIsQuality())) {
                allCount = modelDiscoverMapper.getOrderQualityModelCountByCondition(orderModelListDTO);
            } else {
                allCount = modelDiscoverMapper.getOrderOrdinaryModelCountByCondition(orderModelListDTO);
            }
        }
        info.setCanApply(allCount > 0);

        if (ObjectUtil.isNotNull(preselectModelId)) {
            OrderVideoMatchPreselectModel preselectModel = orderVideoPreselectModelService.getById(preselectModelId);

            OrderVideoMatchPreselectModel selectedModelByMatchId = orderVideoPreselectModelService.getSelectedModelByMatchId(preselectModel.getMatchId());

            Assert.notNull(preselectModel, "Order does not exist, please refresh and try again");
            Assert.isTrue(preselectModel.getModelId().equals(SecurityUtils.getUserId()), "Please exit the refresh and try again");
            if (PreselectModelAddTypeEnum.MODEL_OPTIONAL.getCode().equals(preselectModel.getAddType())
                    && OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(preselectModel.getSelectStatus())
                    && ObjectUtil.isNull(selectedModelByMatchId)
            ) {
                info.setCanCancel(true);
            } else if (
                    (
                            PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(preselectModel.getAddType())
                                    || PreselectModelAddTypeEnum.OPERATION.getCode().equals(preselectModel.getAddType())
                                    || PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(preselectModel.getAddType())
                    )
                            &&
                            (
                                    PreselectStatusEnum.UN_JOINTED.getCode().equals(preselectModel.getStatus())
                                            || PreselectStatusEnum.JOINTED.getCode().equals(preselectModel.getStatus())
                            )
                            && ObjectUtil.isNull(selectedModelByMatchId)
                            && ModelIntentionEnum.MT_UN_CONFIRM.getCode().equals(preselectModel.getModelIntention())
            ) {
                info.setCanYesOrPass(true);
            }

            if (ModelIntentionEnum.WANT.getCode().equals(preselectModel.getModelIntention())
                    && OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(preselectModel.getSelectStatus())) {
                info.setOperateStatus(1);
            } else if (ModelIntentionEnum.WANT_NOT.getCode().equals(preselectModel.getModelIntention())
                    && OrderVideoModelSelectStatusEnum.REJECT.getCode().equals(preselectModel.getSelectStatus())) {
                info.setOperateStatus(2);
            } else if (ObjectUtil.isNotNull(selectedModelByMatchId)
                    && !selectedModelByMatchId.getModelId().equals(SecurityUtils.getUserId())) {
                info.setOperateStatus(3);
            } else if (ObjectUtil.isNotNull(selectedModelByMatchId)
                    && selectedModelByMatchId.getModelId().equals(SecurityUtils.getUserId())) {
                info.setOperateStatus(4);
            }
            info.setShootAttention(preselectModel.getShootAttention());
            info.setShootAttentionObjectKey(preselectModel.getShootAttentionObjectKey());
            info.setNeedRemindShootAttention(preselectModel.getNeedRemindShootAttention());
        } else {
            Assert.isTrue(OrderStatusEnum.UN_MATCH.getCode().equals(orderVideo.getStatus()), "Please exit the refresh and try again");
        }
        info.setPreselectModelId(preselectModelId);

        return info;
    }

    @Override
    public OrderModelApplyInfoVO getOrderModelInfoByVideoCode(String videoCode, Long preselectModelId) {
        Assert.isTrue(StrUtil.isNotBlank(videoCode), "videoCode is not blank");
        List<OrderVideo> orderVideoList = orderVideoService.selectListByVideoCodes(Arrays.asList(videoCode));
        if (CollUtil.isEmpty(orderVideoList)) {
            return null;
        }
        OrderVideo orderVideo = orderVideoList.get(0);

        return getOrderModelApplyInfoVO(orderVideo.getId(), preselectModelId, orderVideo);
    }

    /**
     * 模特端-首页-订单列表
     */
    @Override
    public List<OrderModelListVO> selectOrderModelListByCondition(OrderModelListDTO dto) {
        assembleCondition(dto);
        if (StatusTypeEnum.NO.getCode().equals(dto.getIsShow())) {
            return Collections.emptyList();
        }

        List<OrderModelListVO> orderModelListVOS;
        if (Boolean.TRUE.equals(dto.getIsQuality())) {
            orderModelListVOS = modelDiscoverMapper.selectOrderQualityModelListByCondition(dto);
        } else {
            orderModelListVOS = modelDiscoverMapper.selectOrderOrdinaryModelListByCondition(dto);
        }

        for (OrderModelListVO orderModelListVO : orderModelListVOS) {
            orderModelListVO.setSurplusPicCount(Math.max((PicCountEnum.getValue(orderModelListVO.getPicCount()) - orderModelListVO.getRefundPicCount()), 0));
        }
        return orderModelListVOS;
    }

    /**
     * 组装 模特端-首页-订单列表 条件
     */
    private void assembleCondition(OrderModelListDTO dto) {
        // 当前登录的模特信息
        LoginModel loginModel = (LoginModel) SecurityUtils.getLoginUser();
        ModelLoginVO modelLoginVO = loginModel.getModelLoginVO();
        dto.setCurModelId(modelLoginVO.getId());
        dto.setShootingCountry(modelLoginVO.getNation());
        dto.setModelType(modelLoginVO.getType());
        dto.setPlatform(StringUtils.splitToLong(modelLoginVO.getPlatform(), StrUtil.COMMA));
        dto.setIsShow(modelLoginVO.getIsShow());
        // 获取当前登录模特 判断是不是优质模特 如果不是 拼接此sql
        dto.setIsQuality(ModelCooperationEnum.QUALITY.getCode().equals(modelLoginVO.getCooperation()));

        dto.setPreselectModelNumberOfPits(orderVideoProperties.getPreselectModelNumberOfPits());
        dto.setReleaseOverTime(orderVideoProperties.getReleaseOverTime());
        dto.setModelTerminalAllListOldDataEndTime(orderVideoProperties.getModelTerminalAllListOldDataEndTime());
        dto.setBlackModelBusinessIds(remoteService.getBlackModelBusinessIdsByModelId(modelLoginVO.getId()));
    }

    /**
     * 组装 模特端-首页-订单列表 过滤24小时sql
     */
    private String assembleSql() {
        StringBuilder sb = new StringBuilder();
        sb.append("AND ( ov.release_time <= NOW() - INTERVAL ");
        sb.append(orderVideoProperties.getReleaseOverTime());
        sb.append(" HOUR ");
        // sb.append("OR ov.release_flag = ");
        // sb.append(FlagEnum.FLAG.getCode());
        sb.append(" )");
        return sb.toString();
    }

    /**
     * 组装 模特端-首页-订单详情 数据
     *
     * @param info 订单详情
     */
    @Override
    public void assembleBaseInfo(OrderModelBaseInfoVO info) {
        info.setSurplusPicCount(Math.max((PicCountEnum.getValue(info.getPicCount()) - info.getRefundPicCount()), 0));
        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIdOrTypes(info.getId(), List.of(VideoContentTypeEnum.REQUIRE.getCode(), VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode()));

        List<OrderVideoContent> shootRequired = orderVideoContents.stream().filter(item -> VideoContentTypeEnum.REQUIRE.getCode().equals(item.getType())).collect(Collectors.toList());
        OrderVideoContent orderVideoContent = orderVideoContents.stream().filter(item -> VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode().equals(item.getType())).findFirst().orElse(new OrderVideoContent());
        info.setShootRequired(shootRequired);
        info.setSellingPointProduct(orderVideoContent.getContent());

        //  获取关联的资源
        List<Long> referencePicIds = StringUtils.splitToLong(info.getReferencePicId(), StrUtil.COMMA);

        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(referencePicIds);

        for (Long reference_picId : referencePicIds) {
            info.getReferencePic().add(resourceMap.getOrDefault(reference_picId, new OrderResource()).getObjectKey());
        }
    }

}
