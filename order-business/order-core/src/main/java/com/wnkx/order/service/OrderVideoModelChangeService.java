package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoModelChangeDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelChange;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelChangeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15 11:55
 */
public interface OrderVideoModelChangeService extends IService<OrderVideoModelChange> {


    /**
     * 新增模特变更记录
     */
    void saveOrderVideoModelChange(List<OrderVideoModelChangeDTO> dtoList);

    /**
     * 新增模特变更记录
     */
    void saveOrderVideoModelChange(OrderVideoModelChangeDTO dto);

    /**
     * 查询模特变更记录
     */
    List<OrderVideoModelChangeVO> selectOrderVideoModelChangeListByVideoId(Long videoId);

    /**
     * 查询模特变更记录数据
     * @param videoId
     * @param modelId
     * @return
     */
    List<OrderVideoModelChangeVO> selectOrderVideoModelChangeListByVideoIdAndModelId(Long videoId, Long modelId);

    /**
     * 更新模特变更记录 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoModelChangeBatchFieldNullToNull(List<OrderVideoModelChange> orderVideoModelChanges);
}
