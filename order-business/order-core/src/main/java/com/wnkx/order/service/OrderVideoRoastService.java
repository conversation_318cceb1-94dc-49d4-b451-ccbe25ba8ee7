package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastListDTO;
import com.ruoyi.system.api.domain.dto.order.task.OrderVideoRoastHandleDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRoast;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:14
 */
public interface OrderVideoRoastService extends IService<OrderVideoRoast> {

    /**
     * 查询视频订单吐槽列表*
     *
     * @param dto
     * @return
     */
    List<OrderVideoRoastVO> getOrderVideoRoastVO(OrderVideoRoastListDTO dto);

    /**
     * 新增视频吐槽
     */
    void addOrderVideoRoast(OrderVideoRoastDTO dto);

    /**
     * 查询视频订单是否有存在吐槽
     */
    boolean isRoast(Long videoId);

    List<Long> isRoast(List<Long> videoIds);

    /**
     * 处理吐槽*
     *
     * @param dto
     */
    void handleRoast(OrderVideoRoastHandleDTO dto);

    /**
     * 吐槽列表-获取中文部客服下拉框
     *
     * @param keyword
     * @return
     */
    List<UserVO> roastContactSelect(String keyword);

    /**
     * 吐槽列表-获取英文部客服下拉框
     *
     * @param keyword
     * @return
     */
    List<UserVO> roastIssueSelect(String keyword);


    /**
     * 吐槽列表-统计
     *
     * @return
     */
    OrderVideoRoastStatisticsVO orderVideoRoastStatisticsVO();
}
