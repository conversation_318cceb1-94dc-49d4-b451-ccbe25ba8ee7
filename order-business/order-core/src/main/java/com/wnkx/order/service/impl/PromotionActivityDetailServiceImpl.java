package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityDetail;
import com.wnkx.order.mapper.PromotionActivityDetailMapper;
import com.wnkx.order.service.PromotionActivityDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 
 * @Date 2025-05-21 14:34:36 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PromotionActivityDetailServiceImpl extends ServiceImpl<PromotionActivityDetailMapper, PromotionActivityDetail> implements PromotionActivityDetailService {

    /**
     * 通过活动ID查询单条数据
     */
    @Override
    public PromotionActivityDetail getByActivityId(Long activityId) {
        return baseMapper.getByActivityId(activityId);
    }
}
