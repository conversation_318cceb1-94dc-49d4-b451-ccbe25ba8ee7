package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRecord;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:30
 */
public interface OrderInvoiceRecordService extends IService<OrderInvoiceRecord> {

    /**
     * 新增发票开票记录
     */
    void saveInvoiceRecord(OrderInvoiceRecord orderInvoiceRecord);

    /**
     * 运营端-发票管理-开票记录
     */
    List<OrderInvoiceRecordVO> getInvoiceRecord(Long invoiceId);
}
