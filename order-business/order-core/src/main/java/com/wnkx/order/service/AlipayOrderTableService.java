package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:15
 */
public interface AlipayOrderTableService extends IService<AlipayOrderTable> {

    /**
     * 根据商户订单号获取订单*
     */
    AlipayOrderTable getAlipayOrderTableByOutTradeNo(String out_trade_no);

    /**
     * 根据商户订单号获取有效订单*
     */
    AlipayOrderTable getValidAlipayOrderTableByOutTradeNo(String out_trade_no);
    /**
     * 根据内部订单号获取有效订单*
     */
    AlipayOrderTable getValidAlipayOrderTableByOrderNum(String orderNum);

    /**
     * 设置订单为无效*
     */
    void banFyOrderTable(Long id);

    /**
     * 根据orderNUm获取订单列表*
     * @param order_num
     */
    List<AlipayOrderTable> getValidAlipayOrderTableList(String order_num);


    /**
     * 根据订单号无效支付宝订单数据*
     * @param order_num
     */
    void banAlipayOrderTableByOrderNum(String order_num);
}
