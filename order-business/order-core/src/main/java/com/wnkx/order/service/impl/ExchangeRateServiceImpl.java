package com.wnkx.order.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO;
import com.wnkx.order.service.ExchangeRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * 汇率服务
 *
 * <AUTHOR>
 * @date 2024/6/3
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeRateServiceImpl implements ExchangeRateService {

    private final RedisService redisService;

    private final OkHttpClient okHttpClient;

    private BigDecimal defaultExchangeRate = new BigDecimal("7.2345");

    @Value("${exchangeRate.min}")
    private String min;

    @Value("${exchangeRate.max}")
    private String max;


    @Override
    public RealTimeExchangeRateVO getCurrentExchange() {
        if (hasCachePrice()) {
            return getCachePrice();
        }
        return getRealTimeExchangeRate();
    }

    @Override
    public RealTimeExchangeRateVO getRealTimeExchangeRate() {
        Request request = new Request.Builder()
                .url("https://finance.pae.baidu.com/selfselect/sug?wd=%E7%BE%8E%E5%85%83%E4%BA%BA%E6%B0%91%E5%B8%81&skip_login=1&finClientType=pc")
                .addHeader("Accept", "application/vnd.finance-web.v1+json")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9")
                .addHeader("Cache-Control", "no-cache")
                .addHeader("Connection", "keep-alive")
                .addHeader("Origin", "https://gushitong.baidu.com")
                .addHeader("Pragma", "no-cache")
                .addHeader("Referer", "https://gushitong.baidu.com/")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-site")
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("sec-ch-ua", "\"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            final JSONObject jsonObject = JSON.parseObject(response.body().string());
            final String string = jsonObject.getJSONObject("Result").getJSONArray("stock").getJSONObject(0).getString("price");
            final BigDecimal exchangeRate = new BigDecimal(string);
            if (exchangeRate.compareTo(new BigDecimal(min)) < 0 || exchangeRate.compareTo(new BigDecimal(max)) > 0) {
                log.warn("实时汇率{}不在合理范围内！将采用默认汇率7.2345", exchangeRate);
                return RealTimeExchangeRateVO.builder().realTimeExchangeRate(defaultExchangeRate).isDefault(true).build();
            }

            ThreadUtil.execAsync(() -> updateCachePrice(exchangeRate));
            return RealTimeExchangeRateVO.builder().realTimeExchangeRate(exchangeRate).build();
        } catch (Exception e) {
            log.warn("实时汇率获取失败！将采用默认汇率7.2345,{}", e.getMessage());
        }
        return RealTimeExchangeRateVO.builder().realTimeExchangeRate(defaultExchangeRate).isDefault(true).build();
    }

    private boolean hasCachePrice() {
        return redisService.hasKey(CacheConstants.EXCHANGE_RATE_KEY);
    }

    private RealTimeExchangeRateVO getCachePrice() {
        return RealTimeExchangeRateVO.builder().realTimeExchangeRate(redisService.getCacheObject(CacheConstants.EXCHANGE_RATE_KEY)).build();
    }

    private void updateCachePrice(BigDecimal realTimeExchangeRate) {
        redisService.setCacheObject(CacheConstants.EXCHANGE_RATE_KEY, realTimeExchangeRate);
    }
}
