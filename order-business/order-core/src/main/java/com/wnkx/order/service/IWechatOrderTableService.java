package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【wechat_order_table(微信订单表)】的数据库操作Service
 * @createDate 2024-10-25 09:28:00
 */
public interface IWechatOrderTableService extends IService<WechatOrderTable> {

    /**
     * 根据订单号获取有效订单数据
     *
     * @param orderNum
     * @return
     */
    WechatOrderTable getValidWechatOrderTable(String orderNum);

    /**
     * 根据商户订单号获取数据
     * @param outTradeNo
     * @return
     */
    WechatOrderTable getByOutTradeNo(String outTradeNo);

    /**
     * * 设置订单未无效
     *
     * @param id
     * @return
     */
    Boolean banWechatOrderTable(Long id);

    /**
     * 根据订单号无效数据
     *
     * @param orderNum
     * @return
     */
    Boolean banWechatOrderTableByOrderNum(String orderNum);

    /**
     * 根据orderNUm获取订单列表
     *
     * @param orderNum
     * @return
     */
    List<WechatOrderTable> getValidWechatOrderTableList(String orderNum);
}
