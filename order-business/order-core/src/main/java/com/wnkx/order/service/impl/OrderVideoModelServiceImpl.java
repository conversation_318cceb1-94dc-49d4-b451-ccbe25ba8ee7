package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.ModelOrderDTO;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderVideoModelMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoModelService;
import com.wnkx.order.service.IOrderVideoTaskDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 9:19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoModelServiceImpl extends ServiceImpl<OrderVideoModelMapper, OrderVideoModel> implements IOrderVideoModelService {

    private final RemoteService remoteService;
    private final OrderVideoProperties orderVideoProperties;


    /**
     * 通过视频订单ID删除模特关联订单
     */
    @Override
    public void deleteOrderVideoModelByVideoId(Long videoId) {
        baseMapper.deleteOrderVideoModelByVideoId(videoId);
    }

    /**
     * 获取模特超时率、售后率
     */
    @Override
    public List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate() {
        //  获取模特所有订单
        List<OrderVideoModel> orderVideoModels = baseMapper.getModelAllOrder();

        Map<Long, List<OrderVideoModel>> orderVideoModelMap = orderVideoModels.stream().collect(Collectors.groupingBy(OrderVideoModel::getModelId));

        //  获取所有模特的售后单（重拍视频、补拍视频）（去重、多个视频订单算一个）
        List<OrderVideo> orderVideoTaskDetails = SpringUtils.getBean(IOrderVideoTaskDetailService.class).getModelAfterSaleDistinctTask();
        Map<Long, List<OrderVideo>> orderTaskMap = orderVideoTaskDetails.stream().collect(Collectors.groupingBy(OrderVideo::getShootModelId));

        List<OrderModelTimeoutVO> orderModelTimeoutVOS = new ArrayList<>();
        for (Map.Entry<Long, List<OrderVideoModel>> entry : orderVideoModelMap.entrySet()) {
            Long modelId = entry.getKey();
            List<OrderVideoModel> videoModels = entry.getValue();

            OrderModelTimeoutVO orderModelTimeoutVO = new OrderModelTimeoutVO();
            orderModelTimeoutVO.setModelId(modelId);

            //  超时率
            long timeoutCount = videoModels.stream().filter(orderVideoModel -> ObjectUtil.isNotNull(orderVideoModel.getOverTime())).count();
            orderModelTimeoutVO.setOvertimeRate(BigDecimal.valueOf(timeoutCount).divide(BigDecimal.valueOf(videoModels.size()), 2, RoundingMode.DOWN));

            //  售后率
            List<OrderVideo> orderVideoTaskDetail = orderTaskMap.getOrDefault(modelId, new ArrayList<>());
            orderModelTimeoutVO.setAfterSaleRate(BigDecimal.valueOf(orderVideoTaskDetail.size()).divide(BigDecimal.valueOf(videoModels.size()), 2, RoundingMode.DOWN));

            orderModelTimeoutVOS.add(orderModelTimeoutVO);
        }

        return orderModelTimeoutVOS;
    }

    /**
     * 根据视频订单id查询接单模特
     */
    @Override
    public OrderVideoModel getModelIdByVideoId(Long videoId) {
        return baseMapper.getModelIdByVideoId(videoId);
    }

    /**
     * 查询传入的模特是否可以接单
     *
     * @return 无法接单的模特
     */
    @Override
    public List<Long> checkModelAcceptability(Collection<Long> modelId) {
        Map<Long, List<OrderVideoModel>> orderModel = this.getUnfinishedOrderModelByModelId(modelId);

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(modelId);
        List<ModelInfoVO> models = remoteService.innerList(modelListDTO);

        List<Long> cannotAcceptModelId = new ArrayList<>();
//       待拍数校验
//        for (ModelInfoVO model : models) {
//            if (model.getAcceptability() <= orderModel.getOrDefault(model.getId(), new ArrayList<>()).size()) {
//                cannotAcceptModelId.add(model.getId());
//            }
//        }

        return cannotAcceptModelId;
    }

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 有逾期未反馈素材和无法接单的模特id
     */
    @Override
    public List<Long> checkAbnormalModelId(Collection<Long> modelId) {
        //  逾期的模特id
        List<Long> overdueModelId = this.checkModelOverdueVideo(modelId);

        //  无法接单的模特id
        // List<Long> acceptability = this.checkModelAcceptability(modelId);

        List<Long> cannotModel = new ArrayList<>();
        cannotModel.addAll(overdueModelId);
        // cannotModel.addAll(acceptability);
        return cannotModel;
    }

    /**
     * 添加模特关联订单
     */
    @Override
    public void addOrderVideoModel(List<ModelOrderDTO> modelOrderDTOS) {
        //  校验视频订单是否已有模特接单
        List<Long> videoIds = modelOrderDTOS.stream().map(ModelOrderDTO::getVideoId).collect(Collectors.toList());
        List<OrderVideoModel> orderVideoModels = baseMapper.listByVideoId(videoIds);
        Assert.isTrue(CollUtil.isEmpty(orderVideoModels), "视频订单已有关联模特，请勿重复操作！");

        // Map<Long, List<ModelOrderDTO>> map = modelOrderDTOS.stream().collect(Collectors.groupingBy(ModelOrderDTO::getModelId));

        //  校验会不会超过模特可接单数
        // List<Long> modelIds = modelOrderDTOS.stream().map(ModelOrderDTO::getModelId).collect(Collectors.toList());

        // Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelIds);

        // Map<Long, List<OrderVideoModel>> modelOrderMap = getUnfinishedOrderModelByModelId(modelIds);

        // for (Long modelId : modelIds) {
            //  模特可接单的数
            // ModelInfoVO model = modelMap.get(modelId);
            // Integer acceptability = model.getAcceptability();

            //  本次模特要接的订单
            // List<ModelOrderDTO> curModelOrder = map.get(modelId);

            //  模特已接的订单
            // List<OrderVideoModel> hisModelOrder = modelOrderMap.getOrDefault(modelId, new ArrayList<>());

//            if ((hisModelOrder.size() + curModelOrder.size()) > acceptability) {
//                log.error("已超过{}模特的可接单数量，操作失败！", model.getName());
//                throw new ServiceException(StrUtil.format("已超过{}模特的可接单数量，操作失败！", model.getName()));
//            }
//         }

        List<OrderVideoModel> saveMos = BeanUtil.copyToList(modelOrderDTOS, OrderVideoModel.class);
        baseMapper.saveBatch(saveMos);
    }

    /**
     * 确认模特接单时间
     *
     * @param videoIds 视频订单id
     */
    @Override
    public void acceptModelOrder(List<Long> videoIds) {
        List<OrderVideoModel> modelOrders = baseMapper.listByVideoId(videoIds);
        if (CollUtil.isEmpty(modelOrders)) {
            return;
        }

        modelOrders.forEach(modelOrder -> modelOrder.setAcceptTime(new Date()));

        baseMapper.updateBatchById(modelOrders);
    }

    /**
     * 获取订单关联模特map
     */
    @Override
    public Map<Long, List<OrderVideoModel>> getOrderModelByModelId(Collection<Long> modelIds) {
        List<OrderVideoModel> orderVideoModels = baseMapper.listByModelId(modelIds);

        if (CollUtil.isEmpty(orderVideoModels)) {
            return new HashMap<>();
        }

        return orderVideoModels.stream().collect(Collectors.groupingBy(OrderVideoModel::getModelId));
    }

    /**
     * 获取模特已接收的订单 不包括已完成、订单关闭
     */
    @Override
    public Map<Long, List<OrderVideoModel>> getUnfinishedOrderModelByModelId(Collection<Long> modelIds) {
        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();
        List<OrderVideoModel> unfinishedOrderModelByModelId = baseMapper.getUnfinishedOrderModelByModelId(modelIds,codeMap);

        if (CollUtil.isEmpty(unfinishedOrderModelByModelId)) {
            return new HashMap<>();
        }

        return unfinishedOrderModelByModelId.stream().collect(Collectors.groupingBy(OrderVideoModel::getModelId));
    }

    @Override
    public List<OrderVideoModel> getListByVideoIds(Collection<Long> videoIds) {
        return baseMapper.listByVideoId((List<Long>) videoIds);
    }

    /**
     * 查询传入的模特是否有逾期未反馈的订单
     *
     * @return 逾期的模特id
     */
    @Override
    public List<Long> checkModelOverdueVideo(Collection<Long> modelId) {
        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();
        return baseMapper.checkModelOverdueVideo(modelId, orderVideoProperties.getOrderFeedbackOverdueTime(), codeMap);
    }
}
