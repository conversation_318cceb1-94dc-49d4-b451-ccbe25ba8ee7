package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.WechatPayLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【wechat_pay_log(微信回调记录表)】的数据库操作Service
 * @createDate 2024-10-25 09:28:00
 */
public interface IWechatPayLogService extends IService<WechatPayLog> {

    /**
     * 根据商户订单号查询是否不存在数据
     *
     * @param outTradeNo
     * @return 如果存在数据则返回false
     */
    boolean checkOrderNotExist(String outTradeNo);


    /**
     * 根据系统订单号查询是否不存在数据
     *
     * @param orderNumber
     * @return 如果存在数据 则返回false
     */
    boolean checkOrderNumberNotExist(String orderNumber);
}
