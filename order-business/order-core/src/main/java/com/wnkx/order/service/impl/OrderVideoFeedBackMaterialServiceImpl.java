package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.EditCloseReasonEnum;
import com.ruoyi.common.core.enums.MaterialStatusEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.MaterialRejectDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackMaterialDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialInfoSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackMaterialVO;
import com.wnkx.order.mapper.OrderVideoFeedBackMaterialMapper;
import com.wnkx.order.service.IOrderVideoFeedBackMaterialInfoService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.ModelPersonService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back_material(订单反馈表(模特))】的数据库操作Service实现
 * @createDate 2024-06-18 17:43:06
 */
@Service
@RequiredArgsConstructor
public class OrderVideoFeedBackMaterialServiceImpl extends ServiceImpl<OrderVideoFeedBackMaterialMapper, OrderVideoFeedBackMaterial> implements OrderVideoFeedBackMaterialService {

    private final IOrderVideoFeedBackMaterialInfoService orderFeedBackMaterialInfoService;


    /**
     * 通过视频订单ID和回退ID查询是否有模特反馈素材
     */
    @Override
    public Boolean checkExistByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        return baseMapper.checkExistByVideoIdAndRollbackId(videoId, rollbackId);
    }

    /**
     * 通过视频订单ID关闭模特反馈素材详情
     */
    @Override
    public void closeMaterialInfoByVideoIds(List<Long> videoIds, EditCloseReasonEnum editCloseReasonEnum) {
        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = baseMapper.selectListByVideoIds(videoIds);
        if (CollUtil.isEmpty(orderVideoFeedBackMaterials)) {
            return;
        }

        List<Long> materialIds = orderVideoFeedBackMaterials.stream().map(OrderVideoFeedBackMaterial::getId).collect(Collectors.toList());
        orderFeedBackMaterialInfoService.closeMaterialInfoByMaterialIds(materialIds, editCloseReasonEnum);
    }

    /**
     * 通过视频订单ID和回退ID关闭模特反馈素材详情
     */
    @Override
    public void closeMaterialInfoByVideoIdAndRollbackId(Long videoId, Long rollbackId, EditCloseReasonEnum editCloseReasonEnum) {
        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = baseMapper.selectListByVideoIdAndRollbackId(videoId, rollbackId);
        if (CollUtil.isEmpty(orderVideoFeedBackMaterials)) {
            return;
        }

        List<Long> materialIds = orderVideoFeedBackMaterials.stream().map(OrderVideoFeedBackMaterial::getId).collect(Collectors.toList());
        orderFeedBackMaterialInfoService.closeMaterialInfoByMaterialIds(materialIds, editCloseReasonEnum);
    }

    /**
     * 上传链接
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadLink(UploadLinkDTO dto) {
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(dto.getVideoId());
        Assert.notNull(orderVideo, "订单不存在");
        SpringUtils.getBean(IOrderVideoService.class).checkVideoStatus(orderVideo, OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM);
        OrderVideoFeedBackMaterial lastModelFeedBack = baseMapper.getLastModelFeedBack(dto.getVideoId(), orderVideo.getRollbackId());
        OrderVideoFeedBackMaterial orderVideoFeedBackMaterial;
        if (ObjectUtil.isNull(lastModelFeedBack)) {
            orderVideoFeedBackMaterial = baseMapper.addOrderFeedBackMaterialByVideoId(dto.getVideoId(), orderVideo.getRollbackId());
        } else {
            Assert.isFalse(MaterialStatusEnum.FINISHED.getCode().equals(lastModelFeedBack.getStatus()), SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE) ? "订单已完成，无需再上传链接！" : "Order completed, no need to upload links!");//   订单已完成，无需再上传链接！

            if (MaterialStatusEnum.REJECT.getCode().equals(lastModelFeedBack.getStatus())) {
                orderVideoFeedBackMaterial = baseMapper.addOrderFeedBackMaterialByVideoId(dto.getVideoId(), orderVideo.getRollbackId());
            } else {
                orderVideoFeedBackMaterial = lastModelFeedBack;
            }
        }
        dto.setShootModelId(orderVideo.getShootModelId());
        orderFeedBackMaterialInfoService.saveOrderFeedBackMaterialInfo(orderVideoFeedBackMaterial.getId(), dto);

        if (ObjectUtil.isNotNull(dto.getMaterialId())) {
            baseMapper.updateById(OrderVideoFeedBackMaterial.builder().id(dto.getMaterialId()).replyStatus(StatusEnum.ENABLED.getCode()).build());
        }
    }

    /**
     * 标记下载状态
     * @param id id
     */
    @Override
    public void markDownload(Long id) {
        baseMapper.markDownload(id);
    }

    /**
     * 查询模特有反馈素材的视频订单id
     */
    @Override
    public List<Long> selectHasFeedBackMaterialByVideoIds(List<Long> videoIds) {
        return baseMapper.selectHasFeedBackMaterialByVideoIds(videoIds);
    }

    @Override
    public List<Long> selectHasFeedBackMaterialByDto(OrderVideoFeedBackMaterialDTO dto) {
        return baseMapper.selectHasFeedBackMaterialByDto(dto);
    }

    /**
     * 设置模特反馈素材状态为已完成
     */
    @Override
    public void finishModelMaterial(List<Long> videoIds) {
        baseMapper.finishModelMaterial(videoIds);
    }

    /**
     * 获取单个反馈素材
     */
    @Override
    public OrderFeedBackMaterialVO getModelFeedBack(Long id) {
        OrderVideoFeedBackMaterial orderVideoFeedBackMaterial = getById(id);
        if (ObjectUtil.isNull(orderVideoFeedBackMaterial)) {
            return null;
        }
        Assert.isFalse(SpringUtils.getBean(ModelPersonService.class).checkModelDataScope(List.of(orderVideoFeedBackMaterial.getVideoId()), SecurityUtils.getUserId()), ErrorConstants.ERROR_TIPS_ENGLISH);

        OrderFeedBackMaterialVO orderFeedBackMaterialVO = BeanUtil.copyProperties(orderVideoFeedBackMaterial, OrderFeedBackMaterialVO.class);

        assembleOrderFeedBackMaterialList(Collections.singletonList(orderFeedBackMaterialVO));
        return orderFeedBackMaterialVO;
    }


    @Override
    public List<OrderVideoFeedBackMaterial> selectListByVideoId(Long videoId) {
        return baseMapper.modelFeedBackList(videoId);
    }

    @Override
    public List<OrderFeedBackMaterialVO> modelFeedBackListByVideoIds(List<Long> videoIds) {
        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = baseMapper.modelFeedBackListByVideoIds(videoIds);
        if (CollUtil.isEmpty(orderVideoFeedBackMaterials)) return new ArrayList<>();

        List<OrderFeedBackMaterialVO> orderFeedBackMaterialVOS = BeanUtil.copyToList(orderVideoFeedBackMaterials, OrderFeedBackMaterialVO.class);
        assembleOrderFeedBackMaterialList(orderFeedBackMaterialVOS);

        return orderFeedBackMaterialVOS;
    }

    @Override
    public List<OrderFeedBackMaterialVO> modelFeedBackList(Long videoId) {
        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = baseMapper.modelFeedBackList(videoId);
        if (CollUtil.isEmpty(orderVideoFeedBackMaterials)) {
            return new ArrayList<>();
        }

        List<OrderFeedBackMaterialVO> orderFeedBackMaterialVOS = BeanUtil.copyToList(orderVideoFeedBackMaterials, OrderFeedBackMaterialVO.class);
        assembleOrderFeedBackMaterialList(orderFeedBackMaterialVOS);

        return orderFeedBackMaterialVOS;
    }

    @Override
    public List<OrderFeedBackMaterialInfoSimpleVO> selectOrderFeedBackMaterialInfoSimpleVOListByVideoId(Long videoId) {
        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = baseMapper.modelFeedBackList(videoId);
        if (CollUtil.isEmpty(orderVideoFeedBackMaterials)) {
            return new ArrayList<>();
        }

        List<Long> materialIds = orderVideoFeedBackMaterials.stream().map(OrderVideoFeedBackMaterial::getId).collect(Collectors.toList());
        List<OrderFeedBackMaterialInfoVO> materialInfos = orderFeedBackMaterialInfoService.selectListByMaterialId(materialIds);

        Map<Long, OrderVideoFeedBackMaterial> orderVideoFeedBackMaterialMap = orderVideoFeedBackMaterials.stream().collect(Collectors.toMap(OrderVideoFeedBackMaterial::getId, Function.identity()));
        List<OrderFeedBackMaterialInfoSimpleVO> orderFeedBackMaterialInfoSimpleVOS = BeanUtil.copyToList(materialInfos, OrderFeedBackMaterialInfoSimpleVO.class);
        for (OrderFeedBackMaterialInfoSimpleVO orderFeedBackMaterialInfoSimpleVO : orderFeedBackMaterialInfoSimpleVOS) {
            OrderVideoFeedBackMaterial orderVideoFeedBackMaterial = orderVideoFeedBackMaterialMap.get(orderFeedBackMaterialInfoSimpleVO.getMaterialId());
            orderFeedBackMaterialInfoSimpleVO.setRollbackId(orderVideoFeedBackMaterial.getRollbackId());
        }
        return orderFeedBackMaterialInfoSimpleVOS;
    }

    @Override
    public void rejectModelMaterial(MaterialRejectDTO materialRejectDTO) {
        baseMapper.rejectModelMaterial(materialRejectDTO);
    }


    /**
     * 组装数据
     */
    private void assembleOrderFeedBackMaterialList(List<OrderFeedBackMaterialVO> orderFeedBackMaterialVOS) {
        List<Long> materialIds = orderFeedBackMaterialVOS.stream().map(OrderFeedBackMaterialVO::getId).collect(Collectors.toList());

        List<OrderFeedBackMaterialInfoVO> materialInfos = orderFeedBackMaterialInfoService.selectListByMaterialId(materialIds);
        Map<Long, List<OrderFeedBackMaterialInfoVO>> groupedAndSortedMap = materialInfos.stream()
                .collect(Collectors.groupingBy(OrderFeedBackMaterialInfoVO::getMaterialId))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().stream()
                                .sorted(Comparator.comparing(
                                        OrderFeedBackMaterialInfoVO::getUploadTime,
                                        Comparator.nullsLast(Comparator.reverseOrder()) // 先降序排序，null 放后面
                                ))
                                .collect(Collectors.toList())
                ));

        for (OrderFeedBackMaterialVO orderFeedBackMaterialVO : orderFeedBackMaterialVOS) {
            orderFeedBackMaterialVO.setMaterialInfos(groupedAndSortedMap.get(orderFeedBackMaterialVO.getId()));
        }
    }
}




