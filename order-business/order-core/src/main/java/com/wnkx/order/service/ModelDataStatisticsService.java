package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelOrderCommissionAnalysisVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO;

/**
 * <AUTHOR>
 * @date 2025/5/19 9:36
 */
public interface ModelDataStatisticsService {


    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    ModelOrderCommissionAnalysisVO getModelOrderCommissionAnalysis(ModelOrderCommissionAnalysisDTO dto);

    /**
     * 模特数据-成功匹配次数
     */
    ModelSuccessMatchCountVO getModelSuccessMatchCount();
}
