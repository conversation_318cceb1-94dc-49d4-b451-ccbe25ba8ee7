package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.OrderVideoFlowNodeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFlowNodeDiagram;
import com.ruoyi.system.api.domain.vo.order.OrderVideoFlowNodeDiagramVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14 17:20
 */
public interface OrderVideoFlowNodeDiagramService extends IService<OrderVideoFlowNodeDiagram> {

    /**
     * 初始化视频订单节点图
     */
    void initOrderVideoFlowNodeDiagram(List<Long> videoIds);

    /**
     * 重启初始化视频订单节点图
     * @param videoIds
     */
    void reopenInitOrderVideoFlowNodeDiagram(List<Long> videoIds);

    /**
     * 设置节点完成时间
     */
    void setNodeCompleteTime(List<Long> videoIds, OrderVideoFlowNodeEnum... nodeEnum);

    /**
     * 设置节点完成时间
     */
    void setNodeCompleteTime(Long videoId, OrderVideoFlowNodeEnum... nodeEnum);

    /**
     * 终止视频订单节点
     */
    void terminateOrderVideoFlowNode(List<Long> videoIds);

    /**
     * 设置某一节点的完成时间为null
     */
    void setNodeCompleteTimeNull(Long videoId, OrderVideoFlowNodeEnum nodeEnum);

    /**
     * 查询视频订单节点图
     */
    List<OrderVideoFlowNodeDiagramVO> selectOrderVideoFlowNodeDiagramListByVideoId(Long videoId);

    /**
     * 订单回退设置除下单支付外节点完成时间为null
     */
    void setNodeCompleteTimeNullExceptOrderPay(Long videoId);
}
