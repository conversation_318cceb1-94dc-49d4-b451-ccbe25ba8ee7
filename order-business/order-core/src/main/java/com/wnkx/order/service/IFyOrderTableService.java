package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.fy.FyOrderTableDTO;
import com.ruoyi.system.api.domain.entity.order.FyOrderTable;
import com.ruoyi.system.api.domain.vo.order.FyOrderTableVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【fy_order_table(富友订单表)】的数据库操作Service
* @createDate 2024-08-14 14:01:11
*/
public interface IFyOrderTableService extends IService<FyOrderTable> {

    /**
     * 获取有效订单*
     * @param dto
     * @return
     */
    FyOrderTableVO getValidFyOrderTableVO(FyOrderTableDTO dto);

    /**
     * 根据orderNUm获取订单列表*
     * @param orderNum
     * @return
     */
    List<FyOrderTableVO> getValidFyOrderTableVOList(String orderNum);

    /**
     * 设置订单未无效*
     * @param id
     * @return
     */
    Boolean banFyOrderTable(Long id);

    /**
     * 根据订单号无效富友订单数据*
     * @param orderNum
     * @return
     */
    Boolean banFyOrderTableByOrderNum(String orderNum);
}
