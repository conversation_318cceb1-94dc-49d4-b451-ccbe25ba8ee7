package com.wnkx.order.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.wnkx.order.mapper.OrderPayeeAccountMapper;
import com.wnkx.order.service.IOrderPayeeAccountService;
import com.wnkx.order.service.OrderPayeeAccountConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account(订单收款账号表)】的数据库操作Service实现
 * @createDate 2024-10-29 11:12:22
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class OrderPayeeAccountServiceImpl extends ServiceImpl<OrderPayeeAccountMapper, OrderPayeeAccount>
        implements IOrderPayeeAccountService {
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;
    private final OrderPayeeAccountServiceImpl self;

    @Override
    public void saveOrderPayeeAccount(List<String> orderNums, Long payeeId, Integer accountType) {
        Assert.notEmpty(orderNums, "添加订单收款账号：订单号不能为空~");
        saveOrderPayeeAccount(orderPayeeAccountConfigService.getPayeeInfoById(payeeId), orderNums, accountType);
    }

    @Override
    public void saveOrderPayeeAccount(List<Order> orders, Integer payType) {
        Assert.isTrue(orders.stream().allMatch(order -> CharSequenceUtil.isNotBlank(order.getOrderNum())), "添加订单收款账号：订单号不能为空~");
        if (payType.equals(PayTypeEnum.ALIPAY.getCode())) {
            OrderPayeeAccountConfigInfoDTO payeeInfoByBankAccount = orderPayeeAccountConfigService.getPayeeInfoByBankAccount(orders.get(0).getAlipayPayAppId());
            saveOrderPayeeAccount(payeeInfoByBankAccount, orders.stream().map(Order::getOrderNum).collect(Collectors.toList()), payType);
        }
        if (payType.equals(PayTypeEnum.WECHAT.getCode())) {
            OrderPayeeAccountConfigInfoDTO payeeInfoByBankAccount = orderPayeeAccountConfigService.getPayeeInfoByBankAccount(orders.get(0).getWechatPayAppId());
            saveOrderPayeeAccount(payeeInfoByBankAccount, orders.stream().map(Order::getOrderNum).collect(Collectors.toList()), payType);
        }
    }

    @Override
    public void saveOrderPayeeAccount(OrderPayAccountDTO dto) {
        Assert.notNull(dto.getOrderNum(), "添加订单收款账号：订单号不能为空~");
        Assert.notNull(dto.getAccountType(), "添加订单收款账号：账号类型不能为空~");
        Assert.notNull(dto.getPayeeAccountConfigInfoId(), "添加订单收款账号：收款人账号配置Id不能为空~");
        saveOrderPayeeAccount(orderPayeeAccountConfigService.getPayeeInfoByInfoId(dto.getPayeeAccountConfigInfoId()), List.of(dto.getOrderNum()), dto.getAccountType());
    }


    private void saveOrderPayeeAccount(OrderPayeeAccountConfigInfoDTO payeeInfoById, List<String> orderNums, Integer accountType) {
        List<OrderPayeeAccount> orderPayeeAccounts = new ArrayList<>();
        for (String orderNum : orderNums) {
            OrderPayeeAccount orderPayeeAccount = initEntity(orderNum, accountType, payeeInfoById);
            orderPayeeAccounts.add(orderPayeeAccount);
        }

        List<OrderPayeeAccount> oldOrderPayeeAccounts = baseMapper.selectList(new LambdaQueryWrapper<OrderPayeeAccount>().in(OrderPayeeAccount::getOrderNum, orderPayeeAccounts.stream().map(OrderPayeeAccount::getOrderNum).collect(Collectors.toList())));
        Map<String, OrderPayeeAccount> oldOrderPayeeAccountMap = oldOrderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, Function.identity()));
        for (OrderPayeeAccount orderPayeeAccount : orderPayeeAccounts) {
            OrderPayeeAccount oldOrderPayeeAccount = oldOrderPayeeAccountMap.get(orderPayeeAccount.getOrderNum());
            if (ObjectUtil.isNotNull(oldOrderPayeeAccount)) {
                orderPayeeAccount.setId(oldOrderPayeeAccount.getId());
            }
        }
        self.saveOrUpdateBatch(orderPayeeAccounts);
    }


    private OrderPayeeAccount initEntity(String orderNum, Integer accountType, OrderPayeeAccountConfigInfoDTO payeeInfoById) {
        Assert.notNull(payeeInfoById, "收款账号配置不能为空~");
//        Assert.notNull(PayeeAccountTypeEnum.getPayeeAccountTypeEnumByCode(accountType), "支付类型不能为空~");
//        Assert.notNull(PayeeAccountTypeEnum.getPayeeAccountTypeEnumByCode(accountType).getCode().equals(payeeInfoById.getType()), "收款账号与订单支付类型不一致~");
        OrderPayeeAccount orderPayeeAccount = new OrderPayeeAccount();
        orderPayeeAccount.setOrderNum(orderNum);
        orderPayeeAccount.setAccountType(accountType);
        orderPayeeAccount.setAccountName(payeeInfoById.getAccountName());
        orderPayeeAccount.setBankAccount(payeeInfoById.getBankAccount());
        orderPayeeAccount.setBankName(payeeInfoById.getBankName());
        orderPayeeAccount.setCompanyAccountType(payeeInfoById.getCompanyAccountType());
        orderPayeeAccount.setCompanyBankCode(payeeInfoById.getCompanyBankCode());
        orderPayeeAccount.setCompanyBankSubCode(payeeInfoById.getCompanyBankSubCode());
        orderPayeeAccount.setCompanyBankSwiftCode(payeeInfoById.getCompanyBankSwiftCode());
        orderPayeeAccount.setCompanyBankPayeeAddress(payeeInfoById.getCompanyBankPayeeAddress());
        return orderPayeeAccount;
    }


    @Override
    public List<OrderPayeeAccount> queryListByOrderNums(List<String> orderNums) {
        return baseMapper.queryListByOrderNums(orderNums);
    }

    @Override
    public void updateByOrderNum(OrderPayeeAccount orderPayeeAccount) {
        baseMapper.updateByOrderNum(orderPayeeAccount);
    }

    @Override
    public void updateByOrderNums(OrderPayeeAccount orderPayeeAccount, List<String> orderNums) {
        baseMapper.updateByOrderNums(orderPayeeAccount, orderNums);
    }
}




