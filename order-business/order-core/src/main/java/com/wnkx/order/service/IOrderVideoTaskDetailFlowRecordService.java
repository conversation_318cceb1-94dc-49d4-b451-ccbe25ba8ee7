package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailFlowRecord;
import com.ruoyi.system.api.domain.vo.order.OrderTaskFlowRecordVO;

import java.util.List;

/**
 * 订单_工单任务_流转记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOrderVideoTaskDetailFlowRecordService extends IService<OrderVideoTaskDetailFlowRecord>
{
    /**
     * 新增订单_工单任务_流转记录
     * 
     * @param orderVideoTaskDetailFlowRecord 订单_工单任务_流转记录
     * @return 结果
     */
    int insertOrderTaskFlowRecord(OrderVideoTaskDetailFlowRecord orderVideoTaskDetailFlowRecord);

    /**
     * 通过工单编号查询流转记录
     */
    List<OrderTaskFlowRecordVO> selectOrderTaskFlowRecordListByTaskNum(String taskNum);

    /**
     * 关闭任务单添加流转记录
     */
    void rollbackOrderCloseTaskSaveFlowRecord(List<String> taskNum, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum);
}
