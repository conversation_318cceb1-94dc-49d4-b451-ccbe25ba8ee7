package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMerge;
import com.ruoyi.system.api.domain.vo.order.CheckOrderMergeVO;
import com.ruoyi.system.api.domain.vo.order.OrderMergeListVO;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-04 10:00:45 
 */
public interface OrderMergeService extends IService<OrderMerge> {

    /**
     * 通过 合并单ID或者支付单号 获取合并订单下的订单号
     */
    List<String> getOrderNumsByMergeIdOrPayNum(Long mergeId, String payNum);

    /**
     * 创建合并单
     */
    Long createOrderMerge(List<String> orderNums);

    /**
     * 校验订单是否已合并
     */
    void checkOrderMerge(List<String> orderNums);

    /**
     * 校验订单是否已合并
     */
    Boolean checkOrderMerge(String orderNum);

    /**
     * 校验订单是否已合并
     */
    CheckOrderMergeVO closeOrderCheckOrderMerge(String orderNum);

    /**
     * 通过 合并单ID或者支付单号 获取正常合并单
     */
    OrderMerge getOrderMergeNormalByIdOrPayNum(Long mergeId, String payNum);

    /**
     * 通过 合并单ID或者支付单号 获取合并单
     */
    OrderMerge getOrderMergeByIdOrPayNum(Long mergeId, String payNum);

    /**
     * 通过 合并单ID或者支付单号 完成合并单
     */
    void completeOrderMergeByMergeIdOrPayNum(Long mergeId, String payNum);

    /**
     * 商家端-需支付订单列表
     */
    List<OrderMergeListVO> selectNeedPayOrderListByCondition(OrderListDTO orderListDTO);

    /**
     * 获取取消合并校验会到期的订单
     */
    List<String> checkCancelMerge(Long mergeId);

    /**
     * 取消合并
     */
    void cancelMerge(Long mergeId, boolean isTask);

    /**
     *
     * 根据支付单号获取合并单
     * @param payNum
     * @return
     */
    OrderMerge getOrderMergeByPayNum(String payNum);

    /**
     * 通过订单号获取正常状态下的合并单
     */
    List<OrderMergeListVO> selectOrderMergeNormalListByOrderNums(List<String> orderNums);

    /**
     * 通过订单号获取同个合并单下的订单号
     */
    List<String> getMergeOrderNumsByOrderNum(String orderNum);
}
