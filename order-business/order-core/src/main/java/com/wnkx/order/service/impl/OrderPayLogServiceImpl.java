package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderPayLogDTO;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.wnkx.order.mapper.OrderPayLogMapper;
import com.wnkx.order.service.IOrderPayLogService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_pay_log(订单支付记录表)】的数据库操作Service实现
* @createDate 2024-12-18 09:10:26
*/
@Service
public class OrderPayLogServiceImpl extends ServiceImpl<OrderPayLogMapper, OrderPayLog>
    implements IOrderPayLogService {

    @Override
    public OrderPayLog saveOrderPayLog(OrderPayLogDTO dto) {
        OrderPayLog orderPayLog = BeanUtil.copyProperties(dto, OrderPayLog.class);
        baseMapper.insert(orderPayLog);
        return orderPayLog;
    }

    @Override
    public void setBusinessId(String orderNum, Long businessId) {
        baseMapper.setBusinessId(orderNum, businessId);
    }

    @Override
    public List<OrderPayLog> getOrderPayLogListByOrderNums(List<String> orderNums) {
        Assert.notEmpty(orderNums, "订单号不能为空");
        return baseMapper.getOrderPayLogListByOrderNums(orderNums);
    }
}




