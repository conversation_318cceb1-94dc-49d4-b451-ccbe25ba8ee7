package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.mapper.OrderInvoiceRedMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:31
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceRedServiceImpl extends ServiceImpl<OrderInvoiceRedMapper, OrderInvoiceRed> implements OrderInvoiceRedService {

    private final OrderInvoiceRedOrderService orderInvoiceRedOrderService;
    private final OrderInvoiceRedOrderVideoService orderInvoiceRedOrderVideoService;
    private final OrderInvoiceOrderService orderInvoiceOrderService;
    private final OrderInvoiceOrderVideoService orderInvoiceOrderVideoService;
    private final OrderInvoiceOperateService orderInvoiceOperateService;
    private final RemoteService remoteService;
    private final OrderInvoiceRecordService orderInvoiceRecordService;
    private final OrderInvoiceReopenService orderInvoiceReopenService;


    /**
     * 提现申请通过后 对发票的处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawalSuccess(Map<Long, List<WithdrawalSuccessDTO>> needRedPushInvoice) {
        if (CollUtil.isEmpty(needRedPushInvoice)) {
            return;
        }

        Set<Long> invoiceIds = needRedPushInvoice.keySet();

        List<OrderInvoiceRed> orderInvoiceReds = baseMapper.selectNeedRedPushListByInvoiceIds(invoiceIds);
        Map<Long, OrderInvoiceRed> orderInvoiceRedMap = orderInvoiceReds.stream().collect(Collectors.toMap(OrderInvoiceRed::getInvoiceId, Function.identity()));

        List<Long> orderInvoiceRedIds = orderInvoiceReds.stream().map(OrderInvoiceRed::getId).collect(Collectors.toList());
        List<OrderInvoiceRedOrder> orderInvoiceRedOrderVideos = orderInvoiceRedOrderService.selectListByInvoiceRedIds(orderInvoiceRedIds);
        Map<Long, List<OrderInvoiceRedOrder>> orderInvoiceRedOrderVideoMap = orderInvoiceRedOrderVideos.stream().collect(Collectors.groupingBy(OrderInvoiceRedOrder::getInvoiceRedId));


        List<OrderInvoiceRedOrderVideo> saveOrderInvoiceRedOrderVideos = new ArrayList<>();

        Date date = DateUtil.date();
        needRedPushInvoice.forEach((invoiceId, orderInvoiceVideos) -> {
            if (CollUtil.isNotEmpty(orderInvoiceVideos)) {
                OrderInvoiceRed invoiceRed = orderInvoiceRedMap.get(invoiceId);
                if (ObjectUtil.isNull(invoiceRed)) {
                    invoiceRed = new OrderInvoiceRed();
                    invoiceRed.setInvoiceId(invoiceId);
                    invoiceRed.setInvoiceRedStatus(OrderInvoiceRedStatusEnum.WAITING_TO_BE_FLUSHED.getCode());
                    invoiceRed.setInvoiceRedCause(OrderInvoiceRedCauseEnum.MERCHANT_WITHDRAWAL.getCode());
                    invoiceRed.setApplyTime(date);
                    baseMapper.insert(invoiceRed);
                } else if (OrderInvoiceRedCauseEnum.REBILLING.getCode().equals(invoiceRed.getInvoiceRedCause())) {
                    invoiceRed.setInvoiceRedCause(OrderInvoiceRedCauseEnum.MERCHANT_WITHDRAWAL.getCode());
                    baseMapper.updateById(invoiceRed);
                }
                if (BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode().equals(orderInvoiceVideos.get(0).getRefundType())){
                    List<OrderInvoiceRedOrder> orderInvoiceRedOrders = orderInvoiceRedOrderVideoMap.getOrDefault(invoiceRed.getId(), new ArrayList<>());
                    Map<String, OrderInvoiceRedOrder> collectMap = CollUtil.isEmpty(orderInvoiceRedOrders) ? new HashMap<>() : orderInvoiceRedOrders.stream().collect(Collectors.toMap(OrderInvoiceRedOrder::getOrderNum, Function.identity()));
                    for (WithdrawalSuccessDTO withdrawalSuccessDTO : orderInvoiceVideos) {
                        OrderInvoiceRedOrder orderInvoiceRedOrder;
                        OrderInvoiceRedOrder invoiceRedOrder = collectMap.get(withdrawalSuccessDTO.getOrderNum());
                        if (ObjectUtil.isNotNull(invoiceRedOrder)) {
                            orderInvoiceRedOrder = invoiceRedOrder;
                        } else {
                            orderInvoiceRedOrder = new OrderInvoiceRedOrder();
                            orderInvoiceRedOrder.setInvoiceRedId(invoiceRed.getId());
                            orderInvoiceRedOrder.setOrderNum(withdrawalSuccessDTO.getOrderNum());
                            orderInvoiceRedOrderService.save(orderInvoiceRedOrder);
                        }
                        if (StrUtil.isNotBlank(withdrawalSuccessDTO.getPrepayNum())){
                            OrderInvoiceRedOrderVideo orderInvoiceRedOrderVideo = new OrderInvoiceRedOrderVideo();
                            orderInvoiceRedOrderVideo.setInvoiceRedOrderId(orderInvoiceRedOrder.getId());
                            orderInvoiceRedOrderVideo.setOrderNum(withdrawalSuccessDTO.getPrepayNum());
                            orderInvoiceRedOrderVideo.setRefundType(withdrawalSuccessDTO.getRefundType());
                            orderInvoiceRedOrderVideo.setWithdrawDepositTime(withdrawalSuccessDTO.getWithdrawDepositTime());
                            orderInvoiceRedOrderVideo.setWithdrawDepositAmount(withdrawalSuccessDTO.getWithdrawDepositAmount());
                            saveOrderInvoiceRedOrderVideos.add(orderInvoiceRedOrderVideo);
                        }
                    }
                }else {
                    OrderInvoiceRedOrder orderInvoiceRedOrder;
                    List<OrderInvoiceRedOrder> orderInvoiceRedOrders = orderInvoiceRedOrderVideoMap.get(invoiceRed.getId());
                    if (CollUtil.isNotEmpty(orderInvoiceRedOrders)) {
                        List<OrderInvoiceRedOrder> collect = orderInvoiceRedOrders.stream().filter(item -> item.getOrderNum().equals(orderInvoiceVideos.get(0).getOrderNum())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(collect)) {
                            orderInvoiceRedOrder = collect.get(0);
                        } else {
                            orderInvoiceRedOrder = new OrderInvoiceRedOrder();
                            orderInvoiceRedOrder.setInvoiceRedId(invoiceRed.getId());
                            orderInvoiceRedOrder.setOrderNum(orderInvoiceVideos.get(0).getOrderNum());
                            orderInvoiceRedOrderService.save(orderInvoiceRedOrder);
                        }
                    } else {
                        orderInvoiceRedOrder = new OrderInvoiceRedOrder();
                        orderInvoiceRedOrder.setInvoiceRedId(invoiceRed.getId());
                        orderInvoiceRedOrder.setOrderNum(orderInvoiceVideos.get(0).getOrderNum());
                        orderInvoiceRedOrderService.save(orderInvoiceRedOrder);
                    }

                    for (WithdrawalSuccessDTO orderInvoiceVideo : orderInvoiceVideos) {
                        if (StrUtil.isNotBlank(orderInvoiceVideo.getVideoCode())){
                            OrderInvoiceRedOrderVideo orderInvoiceRedOrderVideo = new OrderInvoiceRedOrderVideo();
                            orderInvoiceRedOrderVideo.setInvoiceRedOrderId(orderInvoiceRedOrder.getId());
                            orderInvoiceRedOrderVideo.setVideoId(orderInvoiceVideo.getVideoId());
                            orderInvoiceRedOrderVideo.setVideoCode(orderInvoiceVideo.getVideoCode());
                            orderInvoiceRedOrderVideo.setRefundType(orderInvoiceVideo.getRefundType());
                            orderInvoiceRedOrderVideo.setWithdrawDepositTime(orderInvoiceVideo.getWithdrawDepositTime());
                            orderInvoiceRedOrderVideo.setWithdrawDepositAmount(orderInvoiceVideo.getWithdrawDepositAmount());
                            saveOrderInvoiceRedOrderVideos.add(orderInvoiceRedOrderVideo);
                        }

                    }
                }
            }
        });

        if (CollUtil.isNotEmpty(saveOrderInvoiceRedOrderVideos)){
            orderInvoiceRedOrderVideoService.saveBatch(saveOrderInvoiceRedOrderVideos);
        }
    }

    /**
     * 获取待红冲金额
     */
    @Override
    public BigDecimal getToBeRedInvoiceAmount() {
        return baseMapper.getToBeRedInvoiceAmount();
    }

    /**
     * 获取待红冲数量
     */
    @Override
    public Long getToBeRedInvoiceCount() {
        return baseMapper.getToBeRedInvoiceCount();
    }

    /**
     * 商家端-发票管理-申请重开
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyForReopening(ApplyForReopeningDTO dto) {
        OrderInvoiceRed exist = baseMapper.getNoRedPushInvoiceByInvoiceId(dto.getId());
        Assert.isNull(exist, "该发票已申请红冲");

        OrderInvoiceRed invoiceRed = BeanUtil.copyProperties(dto, OrderInvoiceRed.class, "id");
        invoiceRed.setInvoiceId(dto.getId());
        invoiceRed.setInvoiceRedStatus(OrderInvoiceRedStatusEnum.WAITING_TO_BE_FLUSHED.getCode());
        invoiceRed.setInvoiceRedCause(OrderInvoiceRedCauseEnum.REBILLING.getCode());
        invoiceRed.setApplyBy(SecurityUtils.getUsername());
        invoiceRed.setApplyById(SecurityUtils.getUserId());
        invoiceRed.setApplyTime(DateUtil.date());
        invoiceRed.setInvoiceAmount(dto.getInvoiceAmount());
        baseMapper.insert(invoiceRed);

        List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByInvoiceIds(List.of(dto.getId()));
        Assert.notEmpty(orderInvoiceOrders, "该发票没有关联订单");

        // Map<Long, List<OrderInvoiceOrderVideo>> orderInvoiceOrderVideoMap = new HashMap<>();
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(dto.getOrderType())) {
            List<Long> invoiceOrderIds = orderInvoiceOrders.stream().map(OrderInvoiceOrder::getId).collect(Collectors.toList());
            List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = orderInvoiceOrderVideoService.selectListByOrderInvoiceOrderIds(invoiceOrderIds);

            //  校验有没有正在申请提现
            List<WithdrawDepositRecordVO> businessBalanceDetailLocks = remoteService.withdrawDepositRecord(WithdrawDepositRecordDTO.builder()
                    .status(List.of(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode()))
                    .businessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
                    .videoCodes(orderInvoiceOrderVideos.stream().map(OrderInvoiceOrderVideo::getVideoCode).collect(Collectors.toList()))
                    .build());
            Assert.isTrue(CollUtil.isEmpty(businessBalanceDetailLocks), "选择的订单存在正在申请提现，无法申请重开~");

            // orderInvoiceOrderVideoMap = orderInvoiceOrderVideos.stream().collect(Collectors.groupingBy(OrderInvoiceOrderVideo::getInvoiceOrderId));
        }

        // List<OrderInvoiceRedOrderVideo> orderInvoiceRedOrderVideos = new ArrayList<>();
        // for (OrderInvoiceOrder orderInvoiceOrder : orderInvoiceOrders) {
        //     OrderInvoiceRedOrder orderInvoiceRedOrder = new OrderInvoiceRedOrder();
        //     orderInvoiceRedOrder.setInvoiceRedId(invoiceRed.getId());
        //     orderInvoiceRedOrder.setOrderNum(orderInvoiceOrder.getOrderNum());
        //     orderInvoiceRedOrderService.save(orderInvoiceRedOrder);
        //
        //     if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(dto.getOrderType())) {
        //         List<OrderInvoiceOrderVideo> orderInvoiceOrderVideos = orderInvoiceOrderVideoMap.get(orderInvoiceOrder.getId());
        //         if (CollUtil.isEmpty(orderInvoiceOrderVideos)) {
        //             continue;
        //         }
        //         for (OrderInvoiceOrderVideo orderInvoiceOrderVideo : orderInvoiceOrderVideos) {
        //             OrderInvoiceRedOrderVideo orderInvoiceRedOrderVideo = new OrderInvoiceRedOrderVideo();
        //             orderInvoiceRedOrderVideo.setInvoiceRedOrderId(orderInvoiceRedOrder.getId());
        //             orderInvoiceRedOrderVideo.setVideoId(orderInvoiceOrderVideo.getVideoId());
        //             orderInvoiceRedOrderVideo.setVideoCode(orderInvoiceOrderVideo.getVideoCode());
        //             orderInvoiceRedOrderVideos.add(orderInvoiceRedOrderVideo);
        //         }
        //     }
        // }
        //
        // orderInvoiceRedOrderVideoService.saveBatch(orderInvoiceRedOrderVideos);
    }

    /**
     * 通过发票ID获取发票红冲关联订单
     */
    @Override
    public List<OrderInvoiceRedOrderVideoVO> selectRedInvoiceVideoListByInvoiceId(Long invoiceId) {
        OrderInvoiceRed invoiceRed = baseMapper.getRedPushInvoiceByInvoiceId(invoiceId);
        if (ObjectUtil.isNull(invoiceRed)) {
            return Collections.emptyList();
        }

        return orderInvoiceRedOrderService.selectRedOrderVideoListByInvoiceRedIds(List.of(invoiceRed.getId()));
    }

    /**
     * 运营端-发票管理-红冲详情
     */
    @Override
    public OrderInvoiceRedDetailVO getRedInvoiceDetail(Long invoiceRedId) {
        OrderInvoiceRed orderInvoiceRed = baseMapper.selectById(invoiceRedId);
        Assert.notNull(orderInvoiceRed, "发票红冲不存在");

        OrderInvoice orderInvoice = SpringUtils.getBean(IOrderInvoiceService.class).getById(orderInvoiceRed.getInvoiceId());
        Assert.notNull(orderInvoice, "发票不存在");

        OrderInvoiceRedDetailVO detailVO = BeanUtil.copyProperties(orderInvoiceRed, OrderInvoiceRedDetailVO.class);
        detailVO.setOrderInvoiceDetailVO(BeanUtil.copyProperties(orderInvoice, OrderInvoiceDetailVO.class));

        // if (OrderInvoiceRedCauseEnum.MERCHANT_WITHDRAWAL.getCode().equals(orderInvoiceRed.getInvoiceRedCause())) {
            detailVO.setOrderInvoiceRedOrderVideoVOS(orderInvoiceRedOrderService.selectRedOrderVideoListByInvoiceRedIds(List.of(orderInvoiceRed.getId())));
        // }

        return detailVO;
    }

    /**
     * 运营端-发票管理-标记红冲
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markRedInvoice(MarkRedInvoiceDTO dto) {
        OrderInvoiceRed orderInvoiceRed = baseMapper.selectById(dto.getId());
        Assert.notNull(orderInvoiceRed, "发票红冲不存在");
        Assert.isTrue(OrderInvoiceRedStatusEnum.WAITING_TO_BE_FLUSHED.getCode().equals(orderInvoiceRed.getInvoiceRedStatus()), "发票红冲状态不是待红冲");
        // if (OrderInvoiceRedCauseEnum.REBILLING.getCode().equals(orderInvoiceRed.getInvoiceRedCause())) {
        //     Assert.isTrue(OrderInvoiceRedStatusEnum.RED_STAMP.getCode().equals(dto.getInvoiceRedStatus()) && StatusTypeEnum.YES.getCode().equals(dto.getIsReopen()), "红冲原因为商家申请重开，必须选择红冲且重开");
        // }

        BeanUtil.copyProperties(dto, orderInvoiceRed);
        if (OrderInvoiceRedStatusEnum.RED_STAMP.getCode().equals(dto.getInvoiceRedStatus())) {
            orderInvoiceRed.setInvoiceRedInvoicingTime(DateUtil.date());
        }
        baseMapper.updateById(orderInvoiceRed);

        IOrderInvoiceService orderInvoiceService = SpringUtils.getBean(IOrderInvoiceService.class);
        OrderInvoice orderInvoice = orderInvoiceService.getById(orderInvoiceRed.getInvoiceId());
        Assert.notNull(orderInvoice, "发票不存在");
        Assert.isTrue(OrderInvoiceStatusEnum.DELIVER.getCode().equals(orderInvoice.getStatus()), "发票状态不是已完成");

        //  保存操作记录
        if (OrderInvoiceRedStatusEnum.NO_RED_PUNCH.getCode().equals(dto.getInvoiceRedStatus())) {
            orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_NO_RED_PUNCH.getCode(), CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_NO_RED_PUNCH.getEventContent(), dto.getRemark()));
        } else if (StatusTypeEnum.YES.getCode().equals(dto.getIsReopen())) {
            orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_A_NEW_TICKET_IS_REQUIRED.getCode(), CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_A_NEW_TICKET_IS_REQUIRED.getEventContent(), dto.getInvoiceRedAmount(), dto.getReopenAmount(), dto.getRemark()));
        } else {
            orderInvoiceOperateService.saveOrderInvoiceOperate(orderInvoice.getId(), OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_NO_NEED_TO_RE_INVOICE.getCode(), CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.MARK_RED_PUNCH_NO_NEED_TO_RE_INVOICE.getEventContent(), dto.getInvoiceRedAmount(), dto.getRemark()));
        }

        if (OrderInvoiceRedStatusEnum.NO_RED_PUNCH.getCode().equals(dto.getInvoiceRedStatus())) {
            return;
        }

        OrderInvoiceRecord orderInvoiceRecord = new OrderInvoiceRecord();
        orderInvoiceRecord.setInvoiceId(orderInvoice.getId());
        orderInvoiceRecord.setType(OrderInvoiceRecordTypeEnum.RED_PUNCH.getCode());
        orderInvoiceRecord.setInvoiceAmount(orderInvoiceRed.getInvoiceRedAmount());
        orderInvoiceRecord.setNumber(orderInvoiceRed.getInvoiceRedNumber());
        orderInvoiceRecord.setInvoicingTime(orderInvoiceRed.getInvoiceRedInvoicingTime());
        orderInvoiceRecord.setObjectKey(orderInvoiceRed.getObjectKey());
        orderInvoiceRecord.setRemark(orderInvoiceRed.getRemark());
        orderInvoiceRecordService.saveInvoiceRecord(orderInvoiceRecord);

        if (StatusTypeEnum.YES.getCode().equals(dto.getIsReopen())) {
            List<OrderInvoiceOrder> orderInvoiceOrders = orderInvoiceOrderService.selectListByInvoiceIds(List.of(orderInvoice.getId()));
            Assert.notEmpty(orderInvoiceOrders, "发票关联订单不存在");

            OrderInvoice reopenOrderInvoice = new OrderInvoice();
            Long invoiceCount = orderInvoiceService.getInvoiceCount();
            reopenOrderInvoice.setTicketCode(OrderConstant.ORDER_INVOICE_CODE_PREFIX + (invoiceCount + 1));
            reopenOrderInvoice.setSource(OrderInvoiceSourceEnum.RED_FLUSH_REOPENS.getCode());
            reopenOrderInvoice.setOrderType(orderInvoice.getOrderType());
            reopenOrderInvoice.setMerchantId(orderInvoice.getMerchantId());
            reopenOrderInvoice.setMerchantCode(orderInvoice.getMerchantCode());
            reopenOrderInvoice.setInvoiceAmount(orderInvoiceRed.getReopenAmount());
            reopenOrderInvoice.setStatus(OrderInvoiceStatusEnum.TO_BE_REVIEWED.getCode());
            reopenOrderInvoice.setIsApplyReopen(orderInvoice.getIsApplyReopen());
            if (OrderInvoiceRedCauseEnum.REBILLING.getCode().equals(orderInvoiceRed.getInvoiceRedCause())) {
                reopenOrderInvoice.setSubmitBy(orderInvoiceRed.getApplyBy());
                reopenOrderInvoice.setSubmitById(orderInvoiceRed.getApplyById());
            } else {
                reopenOrderInvoice.setSubmitBy(orderInvoice.getSubmitBy());
                reopenOrderInvoice.setSubmitById(orderInvoice.getSubmitById());
            }
            reopenOrderInvoice.setSubmitTime(DateUtil.date());
            reopenOrderInvoice.setIsNew(StatusTypeEnum.YES.getCode());
            if (OrderInvoiceRedCauseEnum.REBILLING.getCode().equals(orderInvoiceRed.getInvoiceRedCause())) {
                reopenOrderInvoice.setInvoiceType(orderInvoiceRed.getInvoiceType());
                reopenOrderInvoice.setTitleType(orderInvoiceRed.getTitleType());
                reopenOrderInvoice.setTitle(orderInvoiceRed.getTitle());
                reopenOrderInvoice.setDutyParagraph(orderInvoiceRed.getDutyParagraph());
                reopenOrderInvoice.setCompanyName(orderInvoiceRed.getCompanyName());
                reopenOrderInvoice.setCompanyAddress(orderInvoiceRed.getCompanyAddress());
                reopenOrderInvoice.setCompanyPhone(orderInvoiceRed.getCompanyPhone());
                reopenOrderInvoice.setCompanyContact(orderInvoiceRed.getCompanyContact());
                reopenOrderInvoice.setAttachmentObjectKey(orderInvoiceRed.getAttachmentObjectKey());
                reopenOrderInvoice.setContent(orderInvoiceRed.getContent());
                reopenOrderInvoice.setInvoiceRemark(orderInvoiceRed.getInvoiceRemark());
            } else {
                reopenOrderInvoice.setInvoiceType(orderInvoice.getInvoiceType());
                reopenOrderInvoice.setTitleType(orderInvoice.getTitleType());
                reopenOrderInvoice.setTitle(orderInvoice.getTitle());
                reopenOrderInvoice.setDutyParagraph(orderInvoice.getDutyParagraph());
                reopenOrderInvoice.setCompanyName(orderInvoice.getCompanyName());
                reopenOrderInvoice.setCompanyAddress(orderInvoice.getCompanyAddress());
                reopenOrderInvoice.setCompanyPhone(orderInvoice.getCompanyPhone());
                reopenOrderInvoice.setCompanyContact(orderInvoice.getCompanyContact());
                reopenOrderInvoice.setAttachmentObjectKey(orderInvoice.getAttachmentObjectKey());
                reopenOrderInvoice.setContent(orderInvoice.getContent());
                reopenOrderInvoice.setInvoiceRemark(orderInvoice.getInvoiceRemark());
            }

            orderInvoiceService.save(reopenOrderInvoice);
            if (OrderInvoiceRedCauseEnum.REBILLING.getCode().equals(orderInvoiceRed.getInvoiceRedCause())) {
                OrderInvoiceReopen orderInvoiceReopen = new OrderInvoiceReopen();
                orderInvoiceReopen.setOldInvoiceId(orderInvoice.getId());
                orderInvoiceReopen.setNewInvoiceId(reopenOrderInvoice.getId());
                orderInvoiceReopenService.saveOrderInvoiceReopen(orderInvoiceReopen);
            }

            //  保存开票操作记录
            orderInvoiceOperateService.saveOrderInvoiceOperate(reopenOrderInvoice.getId(), OrderInvoiceOperateTypeEnum.RED_FLUSH_REOPENS.getCode(), CharSequenceUtil.format(OrderInvoiceOperateTypeEnum.RED_FLUSH_REOPENS.getEventContent(), orderInvoice.getTicketCode()));

            //  批量添加发票关联视频订单
            orderInvoiceOrderService.saveBatchOrderInvoiceVideo(reopenOrderInvoice, orderInvoiceOrders.stream().map(OrderInvoiceOrder::getOrderNum).collect(Collectors.toSet()),true);
        }

        orderInvoice.setStatus(OrderInvoiceStatusEnum.CANCELLATION.getCode());
        orderInvoice.setOperatorByType(SecurityUtils.getLoginUserType());
        orderInvoice.setOperatorById(SecurityUtils.getUserId());
        orderInvoice.setOperatorTime(DateUtil.date());
        orderInvoice.setIsNew(StatusTypeEnum.NO.getCode());
        orderInvoice.setIsApplyReopen(StatusTypeEnum.NO.getCode());
        orderInvoiceService.updateById(orderInvoice);
    }

    /**
     * 运营端-发票管理-待红冲列表-导出
     */
    @Override
    public void exportToBeRedInvoiceList(ToBeRedInvoiceListDTO dto, HttpServletResponse response) {
        final List<ToBeRedInvoiceListVO> toBeRedInvoiceListVOS = selectToBeRedInvoiceListByCondition(dto);

        String lf = StrPool.LF;
        String defaultValue = StrPool.DASHED;

        List<ToBeRedInvoiceListExportVO> toBeRedInvoiceListExportVOS = toBeRedInvoiceListVOS.stream().map(item -> {
            ToBeRedInvoiceListExportVO toBeRedInvoiceListExportVO = BeanUtil.copyProperties(item, ToBeRedInvoiceListExportVO.class);
            if (CollUtil.isNotEmpty(item.getVideoCodes())) {
                StringBuilder videoCodeStr = StrUtil.builder();
                for (String videoCode : item.getVideoCodes()) {
                    videoCodeStr.append(videoCode).append(lf);
                }
                toBeRedInvoiceListExportVO.setVideoCodeStr(videoCodeStr.toString());
            }

            String invoiceInfo;
            if (OrderInvoiceTypeEnum.VALUE_ADDED_TAX_INVOICE.getCode().equals(item.getInvoiceType())) {
                StringBuilder builder = StrUtil.builder()
                        .append("发票抬头：").append(CharSequenceUtil.isNotBlank(item.getTitle()) ? item.getTitle() : defaultValue)
                        .append(lf)
                        .append("税号：").append(CharSequenceUtil.isNotBlank(item.getDutyParagraph()) ? item.getDutyParagraph() : defaultValue);
                if (CharSequenceUtil.isNotBlank(item.getCautions())) {
                    builder.append(lf)
                            .append("开票注意事项：").append(CharSequenceUtil.isNotBlank(item.getCautions()) ? item.getCautions() : CharSequenceUtil.EMPTY);
                }
                invoiceInfo = builder.toString();
            } else {
                invoiceInfo = StrUtil.builder()
                        .append("公司名称：").append(CharSequenceUtil.isNotBlank(item.getCompanyName()) ? item.getCompanyName() : defaultValue)
                        .append(lf)
                        .append("公司地址：").append(CharSequenceUtil.isNotBlank(item.getCompanyAddress()) ? item.getCompanyAddress() : defaultValue)
                        .append(lf)
                        .append("联系电话：").append(CharSequenceUtil.isNotBlank(item.getCompanyPhone()) ? item.getCompanyPhone() : defaultValue)
                        .append(lf)
                        .append("联系人：").append(CharSequenceUtil.isNotBlank(item.getCompanyContact()) ? item.getCompanyContact() : defaultValue)
                        .toString();
            }
            toBeRedInvoiceListExportVO.setInvoiceInfo(invoiceInfo);

            return toBeRedInvoiceListExportVO;
        }).collect(Collectors.toList());

        ExcelUtil<ToBeRedInvoiceListExportVO> util = new ExcelUtil<>(ToBeRedInvoiceListExportVO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "待红冲列表");
        util.exportExcel(response, toBeRedInvoiceListExportVOS, "待红冲列表");
    }

    /**
     * 运营端-发票管理-待红冲列表
     */
    @Override
    public List<ToBeRedInvoiceListVO> selectToBeRedInvoiceListByCondition(ToBeRedInvoiceListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("oir.apply_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<ToBeRedInvoiceListVO> list = baseMapper.selectToBeRedInvoiceListByCondition(dto);
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        List<Long> invoiceRedIds = list.stream().map(ToBeRedInvoiceListVO::getId).collect(Collectors.toList());
        List<OrderInvoiceRedOrderVideoVO> invoiceRedVideos = orderInvoiceRedOrderService.selectRedOrderVideoListByInvoiceRedIds(invoiceRedIds);
        Map<Long, List<OrderInvoiceRedOrderVideoVO>> redVideoMap = invoiceRedVideos.stream().collect(Collectors.groupingBy(OrderInvoiceRedOrderVideoVO::getInvoiceRedId));

        for (ToBeRedInvoiceListVO toBeRedInvoiceListVO : list) {
            List<OrderInvoiceRedOrderVideoVO> redVideos = redVideoMap.get(toBeRedInvoiceListVO.getId());
            if (CollUtil.isNotEmpty(redVideos)) {
                Set<String> nums = new HashSet<>();
                for (OrderInvoiceRedOrderVideoVO item : redVideos){
                    if (StrUtil.isNotBlank(item.getVideoCode())){
                        nums.add(item.getVideoCode());
                    }else {
                        nums.add(item.getOrderNum());
                    }
                }
                toBeRedInvoiceListVO.setVideoCodes(nums);
            }
        }

        return list;
    }

}
