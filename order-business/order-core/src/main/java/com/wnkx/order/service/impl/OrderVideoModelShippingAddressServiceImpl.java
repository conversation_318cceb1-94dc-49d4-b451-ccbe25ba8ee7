package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.FlagEnum;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.order.FlagShippingDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoModelShippingAddressDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelShippingAddressVO;
import com.wnkx.order.mapper.OrderVideoModelShippingAddressMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/9 18:32
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoModelShippingAddressServiceImpl extends ServiceImpl<OrderVideoModelShippingAddressMapper, OrderVideoModelShippingAddress> implements OrderVideoModelShippingAddressService {

    private final OrderResourceService orderResourceService;
    private final RemoteService remoteService;
    private final IOrderVideoLogisticService orderVideoLogisticService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final CopyOptions copyOptions = CopyOptions.create()
            .setIgnoreNullValue(true)
            .setIgnoreProperties(ignoreProperties);
    private static final String[] ignoreProperties = new String[]{"id", "createBy", "createTime", "updateBy", "updateTime"};

    /**
     * 通过物流单号最新的视频订单模特收件地址
     */
    @Override
    public List<OrderVideoModelShippingAddress> selectModelShippingAddressByLogisticNumber(Collection<String> logisticNumbers) {
        if (CollUtil.isEmpty(logisticNumbers)) {
            return Collections.emptyList();
        }
        return baseMapper.selectModelShippingAddressByLogisticNumber(logisticNumbers);
    }

    /**
     * 删除模特收件地址
     */
    @Override
    public void removeOrderVideoModelShippingAddressByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        baseMapper.removeOrderVideoModelShippingAddressByVideoIdAndRollbackId(videoId, rollbackId);
    }

    /**
     * 更新模特收件地址
     */
    @Override
    public void updateOrderVideoModelShippingAddress(Long videoId, Long rollbackId) {
        OrderVideoModelShippingAddress modelShippingAddress = baseMapper.getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(videoId, rollbackId);
        if (ObjectUtil.isNull(modelShippingAddress)) {
            return;
        }
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(List.of(modelShippingAddress.getShootModelId()));
        ModelInfoVO modelInfoVO = modelMap.get(modelShippingAddress.getShootModelId());

        modelShippingAddress.setNation(modelInfoVO.getNation());
        modelShippingAddress.setRecipient(modelInfoVO.getRecipient());
        modelShippingAddress.setCity(modelInfoVO.getCity());
        modelShippingAddress.setState(modelInfoVO.getState());
        modelShippingAddress.setZipcode(modelInfoVO.getZipcode());
        modelShippingAddress.setDetailAddress(modelInfoVO.getDetailAddress());
        modelShippingAddress.setPhone(modelInfoVO.getPhone());
        baseMapper.updateById(modelShippingAddress);
    }

    /**
     * 添加模特收货信息
     */
    @Override
    public OrderVideoModelShippingAddress saveOrderVideoModelShippingAddress(OrderVideoModelShippingAddress orderVideoModelShippingAddress) {
        OrderVideoModelShippingAddress modelShippingAddress = baseMapper.getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(orderVideoModelShippingAddress.getVideoId(), orderVideoModelShippingAddress.getRollbackId());
        if (ObjectUtil.isNotNull(modelShippingAddress)) {
            BeanUtil.copyProperties(orderVideoModelShippingAddress, modelShippingAddress, copyOptions);
            baseMapper.updateById(modelShippingAddress);
            return modelShippingAddress;
        }
        baseMapper.insert(orderVideoModelShippingAddress);
        return orderVideoModelShippingAddress;
    }

    /**
     * 获取视频订单最新的发货信息
     */
    @Override
    public List<OrderVideoModelShippingAddress> selectLastOrderVideoModelShippingAddressListByVideoId(List<Long> videoIds) {
        return baseMapper.selectLastOrderVideoModelShippingAddressListByVideoId(videoIds);
    }

    /**
     * 标记发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shippingFlag(FlagShippingDTO flagShippingDTO) {
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(flagShippingDTO.getVideoId());
        Assert.notNull(orderVideo, "订单不存在！");
//        Assert.isTrue(OrderStatusEnum.NEED_FILLED.getCode().equals(orderVideo.getStatus()), "订单已发货，请刷新页面！");
        SpringUtils.getBean(IOrderVideoService.class).checkVideoStatus(orderVideo, OrderStatusEnum.NEED_FILLED);

        OrderVideoModelShippingAddress modelShippingAddress = baseMapper.getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(flagShippingDTO.getVideoId(), flagShippingDTO.getRollbackId());
        Assert.isFalse(FlagEnum.FLAG.getCode().equals(modelShippingAddress.getLogisticFlag()), "该订单已标记发货，请勿重复操作！");

        modelShippingAddress.setLogisticFlag(FlagEnum.FLAG.getCode());
        modelShippingAddress.setLogisticFlagRemark(flagShippingDTO.getRemark());
        modelShippingAddress.setLogisticFlagTime(DateUtil.date());
        baseMapper.updateById(modelShippingAddress);
        orderVideoLogisticCore.shippingFlag(flagShippingDTO);
        orderVideoOperateService.createOrderVideoOperate(
                OrderVideoOperateTypeEnum.MERCHANT_FLAG_DELIVERY.getEventName(),
                OrderVideoOperateTypeEnum.MERCHANT_FLAG_DELIVERY.getIsPublic(),
                OrderVideoOperateDTO.builder()
                        .videoId(flagShippingDTO.getVideoId())
                        .eventContent(OrderVideoOperateTypeEnum.MERCHANT_FLAG_DELIVERY.getEventContent())
                        .build());
    }

    /**
     * 查询收件地址下有几个发货单号
     */
    @Override
    public Long getLogisticCountByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        OrderVideoModelShippingAddress orderVideoModelShippingAddressByVideoIdAndRollbackId = baseMapper.getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(videoId, rollbackId);
        Assert.notNull(orderVideoModelShippingAddressByVideoIdAndRollbackId, "模特收件信息不存在");

        return orderVideoLogisticService.selectCountByShippingAddressId(orderVideoModelShippingAddressByVideoIdAndRollbackId.getId());
    }

    /**
     * 通过视频订单ID获取发货信息列表
     */
    @Override
    public List<OrderVideoModelShippingAddressVO> selectListByVideoId(Long videoId) {
        List<OrderVideoModelShippingAddress> list = baseMapper.selectListByVideoId(videoId);

        List<OrderVideoModelShippingAddressVO> orderVideoModelShippingAddressVOS = BeanUtil.copyToList(list, OrderVideoModelShippingAddressVO.class);

        assembleOrderVideoModelShippingAddressVO(orderVideoModelShippingAddressVOS);

        return orderVideoModelShippingAddressVOS;
    }

    /**
     * 批量添加模特收货信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchOrderVideoModelShippingAddress(List<OrderVideoModelShippingAddressDTO> dtoList) {
        baseMapper.saveBatch(BeanUtil.copyToList(dtoList, OrderVideoModelShippingAddress.class));
    }

    /**
     * 获取视频订单最新的发货信息
     */
    @Override
    public OrderVideoModelShippingAddressVO getLastOrderVideoModelShippingAddressByVideoId(Long videoId, Long rollbackId) {
        OrderVideoModelShippingAddress orderVideoModelShippingAddress = baseMapper.getLastOrderVideoModelShippingAddressByVideoIdAndRollbackId(videoId, rollbackId);

        if (ObjectUtil.isNull(orderVideoModelShippingAddress)) {
            return new OrderVideoModelShippingAddressVO();
        }
        OrderVideoModelShippingAddressVO orderVideoModelShippingAddressVO = BeanUtil.copyProperties(orderVideoModelShippingAddress, OrderVideoModelShippingAddressVO.class);
        assembleOrderVideoModelShippingAddressVO(List.of(orderVideoModelShippingAddressVO));

        return orderVideoModelShippingAddressVO;
    }

    private void assembleOrderVideoModelShippingAddressVO(List<OrderVideoModelShippingAddressVO> orderVideoModelShippingAddressVOS) {
        List<String> shippingPicIds = orderVideoModelShippingAddressVOS.stream().map(OrderVideoModelShippingAddressVO::getShippingPic).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        List<OrderResource> orderResources = orderResourceService.selectListByIds(StringUtils.splitToLong(shippingPicIds, StrUtil.COMMA));
        Map<Long, OrderResource> orderResourceMap = orderResources.stream().collect(Collectors.toMap(OrderResource::getId, Function.identity()));

        Set<Long> modelIds = orderVideoModelShippingAddressVOS.stream().map(OrderVideoModelShippingAddressVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(modelIds);

        for (OrderVideoModelShippingAddressVO orderVideoModelShippingAddressVO : orderVideoModelShippingAddressVOS) {
            if (StrUtil.isNotBlank(orderVideoModelShippingAddressVO.getShippingPic())) {
                for (Long shippingPicId : StringUtils.splitToLong(orderVideoModelShippingAddressVO.getShippingPic(), StrUtil.COMMA)) {
                    orderVideoModelShippingAddressVO.getShippingPics().add(orderResourceMap.getOrDefault(shippingPicId, new OrderResource()).getObjectKey());
                }
            }
            ModelInfoVO modelInfoVO = modelMap.get(orderVideoModelShippingAddressVO.getShootModelId());
            // if (StatusTypeEnum.NO.getCode().equals(orderVideoModelShippingAddressVO.getPhoneVisible())) {
            //     orderVideoModelShippingAddressVO.setPhone(null);
            //     if (ObjectUtil.isNotNull(modelInfoVO)) {
            //         modelInfoVO.setPhone(null);
            //     }
            // }
            orderVideoModelShippingAddressVO.setShootModel(modelInfoVO);
        }
    }
}
