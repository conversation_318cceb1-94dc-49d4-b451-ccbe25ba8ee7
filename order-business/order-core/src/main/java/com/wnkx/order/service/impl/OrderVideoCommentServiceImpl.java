package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderCommentDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoComment;
import com.wnkx.order.mapper.OrderVideoCommentMapper;
import com.wnkx.order.service.OrderVideoCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_comment(订单备注表)】的数据库操作Service实现
 * @createDate 2024-06-17 19:43:43
 */
@Service
@RequiredArgsConstructor
public class OrderVideoCommentServiceImpl extends ServiceImpl<OrderVideoCommentMapper, OrderVideoComment>
        implements OrderVideoCommentService {

    @Override
    public List<Long> getVideoIds() {
        return baseMapper.getVideoIds();
    }

    @Override
    public void addOrderComment(OrderCommentDTO orderCommentDTO) {
        baseMapper.addOrderComment(orderCommentDTO);
    }

    @Override
    public List<OrderVideoComment> orderCommentList(Long videoId) {
        return baseMapper.getOrderCommentList(videoId);
    }
    @Override
    public List<OrderVideoComment> orderCommentListByVideoIds(List<Long> videoIds) {
        return baseMapper.orderCommentListByVideoIds(videoIds);
    }
}




