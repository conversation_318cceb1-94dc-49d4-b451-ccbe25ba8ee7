package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfig;
import com.wnkx.order.service.OrderPayeeAccountConfigChangelogService;
import com.wnkx.order.service.OrderPayeeAccountConfigInfoService;
import com.wnkx.order.service.OrderPayeeAccountConfigService;
import com.wnkx.order.mapper.OrderPayeeAccountConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account_config(收款人账号关联表)】的数据库操作Service实现
 * @createDate 2024-12-17 16:04:06
 */
@Service
@RequiredArgsConstructor
public class OrderPayeeAccountConfigServiceImpl extends ServiceImpl<OrderPayeeAccountConfigMapper, OrderPayeeAccountConfig>
        implements OrderPayeeAccountConfigService {

    private final OrderPayeeAccountConfigInfoService orderPayeeAccountConfigInfoService;

    private final OrderPayeeAccountConfigChangelogService payeeAccountConfigChangelogService;

    @Override
    public List<OrderPayeeAccountConfigDTO> datalist() {
        return baseMapper.dataList();
    }

    @Override
    public List<OrderPayeeAccountConfigDTO> typeList(Integer type) {
        List<OrderPayeeAccountConfigDTO> orderPayeeAccountConfigDTOS = baseMapper.typeList(type);
        orderPayeeAccountConfigDTOS.forEach(orderPayeeAccountConfigDTO -> {
            OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = baseMapper.getHistoryInfo(orderPayeeAccountConfigDTO.getDetailId());
            orderPayeeAccountConfigDTO.setOrderPayeeAccountConfigInfoDTO(orderPayeeAccountConfigInfoDTO);
        });
        return orderPayeeAccountConfigDTOS;
    }

    @Override
    public OrderPayeeAccountConfigInfoDTO typeInfo(Integer type) {
        return baseMapper.typeInfo(type);
    }

    @Override
    public OrderPayeeAccountConfigInfoDTO getPayeeInfoById(Long id) {
        OrderPayeeAccountConfig orderPayeeAccountConfig = baseMapper.selectById(id);
        return baseMapper.getHistoryInfo(orderPayeeAccountConfig.getDetailId());
    }

    @Override
    public OrderPayeeAccountConfigInfoDTO getPayeeInfoByInfoId(Long id) {
        return BeanUtil.copyProperties(orderPayeeAccountConfigInfoService.getById(id), OrderPayeeAccountConfigInfoDTO.class);
    }

    @Override
    @Transactional
    public void newInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
//        1.主表 2.附表 3.记录
        Long id = baseMapper.saveInfo(orderPayeeAccountConfigInfoDTO);
        baseMapper.saveDetailId(id, orderPayeeAccountConfigInfoService.saveInfo(orderPayeeAccountConfigInfoDTO, id));
        payeeAccountConfigChangelogService.saveNewChangeLog(orderPayeeAccountConfigInfoDTO);
    }

    @Override
    @Transactional
    public void updateInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
//        1.主表 2.附表 3.记录
        OrderPayeeAccountConfig dataById = getById(orderPayeeAccountConfigInfoDTO.getId());
        payeeAccountConfigChangelogService.saveModifyChangeLog(orderPayeeAccountConfigInfoDTO.getAccountName(), orderPayeeAccountConfigInfoDTO.getType());
        baseMapper.updateInfo(orderPayeeAccountConfigInfoDTO, orderPayeeAccountConfigInfoService.updateInfo(orderPayeeAccountConfigInfoDTO, dataById.getId()));
    }

    @Override
    public List<OrderPayeeAccountConfigChangelogDTO> historyLog(Long type) {
        return payeeAccountConfigChangelogService.historyLog(type);
    }

    @Override
    @Transactional
    public void changeActive(Long id) {
        OrderPayeeAccountConfig orderPayeeAccountConfig = baseMapper.selectById(id);
        String oldCompanyName = baseMapper.typeInfo(orderPayeeAccountConfig.getType()).getAccountName();
        baseMapper.updateOldStatus(orderPayeeAccountConfig.getType());
        orderPayeeAccountConfig.setStatus(StatusTypeEnum.YES.getCode());
        orderPayeeAccountConfig.setUpdateTime(new Date());
        orderPayeeAccountConfig.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfig.setUpdateById(SecurityUtils.getUserId());
        baseMapper.updateById(orderPayeeAccountConfig);
        payeeAccountConfigChangelogService.saveUpdateChangeLog(oldCompanyName, orderPayeeAccountConfig.getAccountName(), orderPayeeAccountConfig.getType());

    }

    @Override
    public OrderPayeeAccountConfigInfoDTO getPayeeInfoByBankAccount(String appId) {
        return orderPayeeAccountConfigInfoService.getByBankAccount(appId);
    }

}




