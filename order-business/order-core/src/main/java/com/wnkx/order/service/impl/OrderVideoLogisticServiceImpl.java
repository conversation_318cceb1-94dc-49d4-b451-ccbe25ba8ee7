package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.order.CheckDataScopeDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.dto.order.ShippingDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OperationVideoLogisticDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.wnkx.order.mapper.OrderVideoLogisticFollowMapper;
import com.wnkx.order.mapper.OrderVideoLogisticMapper;
import com.wnkx.order.mapper.OrderVideoMapper;
import com.wnkx.order.mapper.OrderVideoModelShippingAddressMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoLogisticService;
import com.wnkx.order.service.OrderVideoOperateService;
import com.wnkx.order.service.core.OrderDataScopeService;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单_视频_物流关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
@RequiredArgsConstructor
public class OrderVideoLogisticServiceImpl extends ServiceImpl<OrderVideoLogisticMapper, OrderVideoLogistic> implements IOrderVideoLogisticService {

    private final OrderDataScopeService orderDataScopeService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final OrderVideoLogisticFollowMapper orderVideoLogisticFollowMapper;
    private final RedisService redisService;
    private final RemoteService remoteService;
    private final OrderVideoModelShippingAddressMapper orderVideoModelShippingAddressMapper;
    private final OrderVideoMapper orderVideoMapper;
    /**
     * 通过模特收件地址ID获取发货物流
     */
    @Override
    public List<OrderVideoLogistic> selectListByShippingAddressId(Long shippingAddressId) {
        return baseMapper.selectListByShippingAddressId(shippingAddressId);
    }

    /**
     * 查询视频订单有几个发货物流
     */
    @Override
    public Long selectCountByShippingAddressId(Long shippingAddressId) {
        return baseMapper.selectCountByShippingAddressId(shippingAddressId);
    }

    /**
     * 通过视频订单id获取首次发货物流单号
     */
    @Override
    public List<OrderVideoLogistic> selectFirstListByVideoId(List<Long> videoIds) {
        return baseMapper.selectFirstListByVideoId(videoIds);
    }

    @Override
    public List<OrderVideoLogistic> selectListByNumber(String number) {
        return baseMapper.selectListByNumber(number);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationVideoLogistic(OperationVideoLogisticDTO dto) {
        //修改订单_视频_物流关联表
        OrderVideoLogistic orderVideoLogistic = baseMapper.selectById(dto.getId());
        Assert.notNull(orderVideoLogistic, "物流数据不能为空");
        Assert.notNull(StatusTypeEnum.YES.getCode().equals(orderVideoLogistic.getIsCancel()), "物流已作废");

        OrderVideoModelShippingAddress orderVideoModelShippingAddress = orderVideoModelShippingAddressMapper.selectById(orderVideoLogistic.getShippingAddressId());
        Assert.notNull(orderVideoModelShippingAddress, "收件地址数据不能为空");
        OrderVideo orderVideo = orderVideoMapper.selectById(orderVideoLogistic.getVideoId());
        Assert.notNull(orderVideo, "视频订单数据不能为空");

        Assert.isFalse(List.of(OrderStatusEnum.FINISHED.getCode(), OrderStatusEnum.TRADE_CLOSE.getCode()).contains(orderVideo.getStatus()), "订单状态已更新，请刷新页面~");
        if (ObjectUtil.isNotNull(orderVideo.getRollbackId()) && (ObjectUtil.isNull(orderVideoModelShippingAddress.getRollbackId()) || !orderVideoModelShippingAddress.getRollbackId().equals(orderVideo.getRollbackId()))){
            //视频订单已回退  地址回退数据为空 或者地址回退id ！= 视频订单 则代表视频订单已回退 无法处理物流
            throw new ServiceException("订单已回退，无法修改/作废该物流");
        }

        OrderVideoLogistic updateEntity = new OrderVideoLogistic();
        updateEntity.setId(orderVideoLogistic.getId());
        if (LogisticOperationTypeEnum.UPDATE.getCode().equals(dto.getType())) {
            updateEntity.setNumber(dto.getNumber());
        } else if (LogisticOperationTypeEnum.CANCEL.getCode().equals(dto.getType())) {
            List<OrderVideoLogistic> orderVideoLogistics = baseMapper.selectListByVideoIdAndRollbackId(orderVideoLogistic.getVideoId(), orderVideoModelShippingAddress.getRollbackId());
            Assert.isTrue(CollUtil.isNotEmpty(orderVideoLogistics) && orderVideoLogistics.size() > 1, "当前仅一个物流单，不可作废");
            updateEntity.setIsCancel(StatusTypeEnum.YES.getCode());
        }
        baseMapper.updateById(updateEntity);

        List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
        OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
        orderVideoOperateDTO.setVideoId(orderVideoLogistic.getVideoId());
        if (LogisticOperationTypeEnum.CANCEL.getCode().equals(dto.getType())) {
            orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.CANCEL_LOGISTIC.getEventContent(), orderVideoLogistic.getNumber(), dto.getRemark()));
            orderVideoOperateDTOS.add(orderVideoOperateDTO);
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.CANCEL_LOGISTIC.getEventName(),
                    null,
                    OrderVideoOperateTypeEnum.CANCEL_LOGISTIC.getIsPublic(),
                    null,
                    orderVideoOperateDTOS
            );
        } else {
            baseMapper.cancelConfirmReceipt(orderVideoLogistic.getId());
            orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.UPDATE_LOGISTIC.getEventContent(), orderVideoLogistic.getNumber(), dto.getRemark()));
            orderVideoOperateDTOS.add(orderVideoOperateDTO);
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.UPDATE_LOGISTIC.getEventName(),
                    null,
                    OrderVideoOperateTypeEnum.UPDATE_LOGISTIC.getIsPublic(),
                    null,
                    orderVideoOperateDTOS
            );
        }
        remoteService.register(Collections.singletonList(dto.getNumber()));
        //添加订单流转记录
        OrderVideoLogisticFollow orderVideoLogisticFollow = orderVideoLogisticFollowMapper.getOneByOrderVideoLogisticId(orderVideoLogistic.getId());
        if (ObjectUtil.isNull(orderVideoLogisticFollow)) {
            return;
        }
        Assert.isFalse(FollowStatusEnum.DELETE.getCode().equals(orderVideoLogisticFollow.getFollowStatus()), "物流跟进流转中，请稍后重试！");
        Assert.isFalse(FollowStatusEnum.CLOSE.getCode().equals(orderVideoLogisticFollow.getFollowStatus()) && ModelResultEnum.ROLLBACK.getCode().equals(orderVideoLogisticFollow.getModelResult()), "物流跟进流转中，请稍后重试！");
        //处理物流跟进表
        if (LogisticOperationTypeEnum.CANCEL.getCode().equals(dto.getType())) {
            OrderVideoLogisticFlowDTO orderVideoLogisticFlowDto = new OrderVideoLogisticFlowDTO();
            orderVideoLogisticFlowDto.setVideoId(orderVideoLogisticFollow.getVideoId());
            orderVideoLogisticFlowDto.setBusinessId(orderVideoLogisticFollow.getBusinessId());
            orderVideoLogisticFlowDto.setOrderVideoLogisticFollowList(Arrays.asList(orderVideoLogisticFollow));
            orderVideoLogisticFlowDto.setIsCancel(StatusTypeEnum.YES.getCode());
            orderVideoLogisticFlowDto.setRemark(dto.getRemark());
            orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDto, FollowStatusEnum.DELETE);
        } else {
            String key = CacheConstants.ORDER_LOGISTIC_FOLLOW_FLOW_KEY + orderVideoLogisticFollow.getBusinessId() + "_" + orderVideoLogisticFollow.getId();
            try {
                Assert.isTrue(redisService.getLock(key, 60L), "物流跟进流转中，请稍后重试！");
                orderVideoLogisticFollowMapper.update(new OrderVideoLogisticFollow(), new LambdaUpdateWrapper<OrderVideoLogisticFollow>()
                                .set(OrderVideoLogisticFollow::getNumber, dto.getNumber())
                                .set(OrderVideoLogisticFollow::getFollowStatus, FollowStatusEnum.SHIP.getCode())
                                .set(OrderVideoLogisticFollow::getSignTime, null)
                                .set(OrderVideoLogisticFollow::getIsCallBack, StatusTypeEnum.YES.getCode())
                                .set(OrderVideoLogisticFollow::getModelResult, ModelResultEnum.PENDING.getCode())
                                .set(OrderVideoLogisticFollow::getLatestMainStatus, null)
                                .set(OrderVideoLogisticFollow::getLogisticUpdateTime, null)
                        .eq(OrderVideoLogisticFollow::getId, orderVideoLogisticFollow.getId())
                );
            } finally {
                redisService.releaseLock(key);
            }
        }

    }

    /**
     * 通过物流单号查询订单关联物流的视频id
     */
    @Override
    public List<Long> selectVideoIdsByLogisticNumber(Collection<String> numbers) {
        if (CollUtil.isEmpty(numbers)) {
            return Collections.emptyList();
        }
        return baseMapper.selectVideoIdsByLogisticNumber(numbers);
    }

    /**
     * 添加视频订单与物流关联信息
     */
    @Override
    public OrderVideoLogistic saveVideoLogistic(ShippingDTO shippingDTO) {
        OrderVideoLogistic videoLogistic = new OrderVideoLogistic();
        videoLogistic.setVideoId(shippingDTO.getVideoId());
        videoLogistic.setShippingAddressId(shippingDTO.getShippingAddressId());
        videoLogistic.setNumber(shippingDTO.getNumber());
        videoLogistic.setRemark(shippingDTO.getRemark());
        videoLogistic.setReissue(shippingDTO.getReissue());
        videoLogistic.setReissueCause(shippingDTO.getReissueCause());
        videoLogistic.setShippingTime(DateUtil.date());
        videoLogistic.setLogisticFlag(shippingDTO.getLogisticFlag());
        videoLogistic.setLogisticFlagRemark(shippingDTO.getLogisticFlagRemark());
        videoLogistic.setLogisticFlagTime(shippingDTO.getLogisticFlagTime());
        baseMapper.insert(videoLogistic);
        return videoLogistic;
    }

    /**
     * 通过视频订单id获取订单关联物流
     *
     * @param videoId 视频订单id
     * @return 订单关联物流
     */
    @Override
    public List<OrderVideoLogistic> selectListByVideoId(Collection<Long> videoId) {
        if (CollUtil.isEmpty(videoId)) {
            return Collections.emptyList();
        }
        return baseMapper.selectListByVideoId(videoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmReceipt(Long logisticId, Date signTime) {
        OrderVideoLogistic orderVideoLogistic = baseMapper.selectById(logisticId);
//        orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(List.of(orderVideoLogistic.getVideoId())).backUserType(BackUserTypeEnum.ENGLISH).build());

        Assert.notNull(orderVideoLogistic, "物流信息不存在");
//        Assert.isFalse(StatusTypeEnum.YES.getCode().equals(orderVideoLogistic.getReceipt()), "已确认收货，无需再次确认~");
        baseMapper.confirmReceipt(logisticId, Optional.ofNullable(signTime).orElse(new Date()));

        if (StatusTypeEnum.YES.getCode().equals(orderVideoLogistic.getReceipt())){
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.CONFIRM_RECEIPT_SYSTEM.getEventName(),
                    OrderVideoOperateTypeEnum.CONFIRM_RECEIPT_SYSTEM.getIsPublic(),
                    OrderVideoOperateDTO.builder().videoId(orderVideoLogistic.getVideoId()).eventContent(OrderVideoOperateTypeEnum.CONFIRM_RECEIPT_SYSTEM.getEventContent()).build());
        }else {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.CONFIRM_RECEIPT.getEventName(),
                    OrderVideoOperateTypeEnum.CONFIRM_RECEIPT.getIsPublic(),
                    OrderVideoOperateDTO.builder().videoId(orderVideoLogistic.getVideoId()).eventContent(OrderVideoOperateTypeEnum.CONFIRM_RECEIPT.getEventContent()).build());
        }
    }

    @Override
    public List<OrderVideoLogistic> selectLastListByDto(List<Long> videoIds, Integer receipt) {
        return baseMapper.selectLastListByDto(videoIds, receipt);
    }

    @Override
    public Collection<Long> selectLastVideoIdsByDto(Collection<Long> videoIds, Integer receipt) {
        List<OrderVideoLogistic> orderVideoLogistics = baseMapper.selectLastListByDto(videoIds, receipt);
        if (CollUtil.isEmpty(orderVideoLogistics)) {
            return Collections.emptyList();
        }
        return orderVideoLogistics.stream().map(OrderVideoLogistic::getVideoId).collect(Collectors.toList());
    }
}
