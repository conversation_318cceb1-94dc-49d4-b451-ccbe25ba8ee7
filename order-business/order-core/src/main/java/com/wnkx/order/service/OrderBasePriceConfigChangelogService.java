package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig;
import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfigChangelog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_base_price_config_changelog(订单基础价格配置表_更改记录)】的数据库操作Service
* @createDate 2025-05-16 16:58:03
*/
public interface OrderBasePriceConfigChangelogService extends IService<OrderBasePriceConfigChangelog> {

    void addChangeLog(OrderBasePriceConfigVO previousConfig, OrderBasePriceConfig orderBasePriceConfig);

    List<OrderBasePriceConfigChangelogVO> getConfigChangeLogList(Integer type);
}
