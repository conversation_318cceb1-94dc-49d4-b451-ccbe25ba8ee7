package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.AlipayPayLog;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:15
 */
public interface AlipayPayLogService extends IService<AlipayPayLog> {

    /**
     * 通过notify_id查询是否已收到过支付宝的回调信息
     */
    Boolean checkExistByNotifyId(String notify_id);

    /**
     * 通过商户订单号查询是否已存在订单的数据
     */
    Boolean checkExistByOutTradeNo(String out_trade_no);
}
