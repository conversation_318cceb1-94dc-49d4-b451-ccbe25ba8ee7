package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.ReplyVideoCaseDTO;
import com.ruoyi.system.api.domain.dto.order.SendVideoCaseDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoCase;
import com.ruoyi.system.api.domain.vo.order.OrderVideoCaseVO;

import java.util.List;
import java.util.Map;

/**
 * 订单_视频_匹配情况反馈Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IOrderVideoCaseService extends IService<OrderVideoCase>
{
    /**
     * 获取未回复的匹配情况map
     *
     * @return 未回复的匹配情况map  key 视频订单id value 未回复的匹配情况
     */
    Map<Long, List<OrderVideoCase>> getNoReplyMapByVideoId(List<Long> videoIds);


    /**
     * 通过视频订单id查询视频订单匹配情况反馈
     */
    public List<OrderVideoCaseVO> selectListByVideoId(Long videoId);

    /**
     * 商家回复匹配情况反馈
     */
    void replyVideoCase(ReplyVideoCaseDTO replyVideoCaseDTO);

    /**
     * 运营发起反馈
     */
    void sendOrderVideoCase(SendVideoCaseDTO sendVideoCaseDTO);

    /**
     * 通过主键和视频id查询反馈情况
     */
    OrderVideoCase getByIdAndVideoId(Long id, Long videoId);
}
