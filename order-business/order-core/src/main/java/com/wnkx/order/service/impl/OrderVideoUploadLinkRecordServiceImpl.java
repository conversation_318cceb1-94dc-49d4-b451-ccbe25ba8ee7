package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.UploadObjectEnum;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLinkRecord;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.order.HistoryUploadRecordVO;
import com.wnkx.order.mapper.OrderVideoUploadLinkRecordMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoUploadLinkRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 
 * @Date 2025-03-20 15:22:48 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoUploadLinkRecordServiceImpl extends ServiceImpl<OrderVideoUploadLinkRecordMapper, OrderVideoUploadLinkRecord> implements OrderVideoUploadLinkRecordService {


    private final RemoteService remoteService;

    /**
     * 通过上传链接ID获取未上传的记录
     */
    @Override
    public OrderVideoUploadLinkRecord getHavenTUploadedByUploadLinkId(Long uploadLinkId) {
        return baseMapper.getHavenTUploadedByUploadLinkId(uploadLinkId);
    }

    /**
     * 更新最新的上传记录
     */
    @Override
    public void updateLatestUploadLinkRecord(OrderVideoUploadLinkRecord orderVideoUploadLinkRecord) {
        OrderVideoUploadLinkRecord latestUploadLinkRecord = baseMapper.getLatestUploadLinkRecordByUploadLinkId(orderVideoUploadLinkRecord.getUploadLinkId());
        if (ObjectUtil.isNull(latestUploadLinkRecord)) {
            return;
        }

        orderVideoUploadLinkRecord.setId(latestUploadLinkRecord.getId());
        baseMapper.updateById(orderVideoUploadLinkRecord);
    }

    /**
     * 新增上传记录
     */
    @Override
    public void insertUploadLinkRecord(OrderVideoUploadLinkRecord orderVideoUploadLinkRecord) {
        Long count = baseMapper.getCountByUploadLinkId(orderVideoUploadLinkRecord.getUploadLinkId());

        orderVideoUploadLinkRecord.setCount(Convert.toInt(count) + 1);
        baseMapper.insert(orderVideoUploadLinkRecord);
    }

    /**
     * 剪辑管理-历史上传记录
     */
    @Override
    public List<HistoryUploadRecordVO> getHistoryUploadRecord(Long uploadLinkId) {
        List<HistoryUploadRecordVO> historyUploadRecordVOS = baseMapper.getHistoryUploadRecord(uploadLinkId);

        if (CollUtil.isEmpty(historyUploadRecordVOS)) {
            return historyUploadRecordVOS;
        }
        Set<Long> uploadUserIds = historyUploadRecordVOS.stream().map(HistoryUploadRecordVO::getUploadUserId).collect(Collectors.toSet());
        Set<Long> backUserIds = historyUploadRecordVOS.stream().filter(item -> UploadObjectEnum.BACK.getCode().equals(item.getObject())).map(HistoryUploadRecordVO::getUserId).collect(Collectors.toSet());
        uploadUserIds.addAll(backUserIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(uploadUserIds).build());

        Set<Long> companyUserIds = historyUploadRecordVOS.stream().filter(item -> UploadObjectEnum.COMPANY.getCode().equals(item.getObject())).map(HistoryUploadRecordVO::getUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, companyUserIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);

        for (HistoryUploadRecordVO historyUploadRecordVO : historyUploadRecordVOS) {
            if (UploadObjectEnum.BACK.getCode().equals(historyUploadRecordVO.getObject())) {
                historyUploadRecordVO.setUserName(userMap.getOrDefault(historyUploadRecordVO.getUserId(), new UserVO()).getName());
            } else {
                historyUploadRecordVO.setUserName(accountMap.getOrDefault(historyUploadRecordVO.getUserId(), new BusinessAccountDetailVO()).getName());
            }

            historyUploadRecordVO.setUploadUserName(userMap.getOrDefault(historyUploadRecordVO.getUploadUserId(), new UserVO()).getName());
        }
        return historyUploadRecordVOS;
    }
}
