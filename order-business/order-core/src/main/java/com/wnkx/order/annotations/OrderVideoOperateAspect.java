package com.wnkx.order.annotations;

import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.wnkx.order.service.OrderVideoOperateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Collections;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderVideoOperateAspect {

    // 定义一个 SpEL 解析器
    private final ExpressionParser parser = new SpelExpressionParser();
    private final OrderVideoOperateService orderVideoOperateService;

    @Around("@annotation(com.wnkx.order.annotations.OrderVideoOperate)")
    public Object orderVideoFlow(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的 OrderVideoFlow 注解
        OrderVideoOperate orderVideoOperate = method.getAnnotation(OrderVideoOperate.class);

        // 获取注解中的 SpEL 表达式
        OrderVideoOperateTypeEnum operateType = orderVideoOperate.operateType();
        int isCart = orderVideoOperate.isCart();
        String videoIdSpel = orderVideoOperate.videoId();

        // 获取方法入参
        Object[] args = joinPoint.getArgs();
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] params = discoverer.getParameterNames(method);
        if (params == null) {
            return joinPoint.proceed();
        }

        String[] paramNames = signature.getParameterNames();

        // 创建 SpEL 上下文
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 将方法参数加入到 SpEL 上下文中
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        // 解析 SpEL 表达式，获取 videoId 的值
        Long videoId = parser.parseExpression(videoIdSpel).getValue(context, Long.class);

        // 执行目标方法
        Object result = joinPoint.proceed();

        orderVideoOperateService.createOrderVideoOperate(
                operateType.getEventName(),
                isCart == 0 ? null : isCart,
                operateType.getIsPublic(),
                null,
                Collections.singletonList(OrderVideoOperateDTO.builder().videoId(videoId).eventContent(operateType.getEventContent()).build()));

        return result;
    }
}
