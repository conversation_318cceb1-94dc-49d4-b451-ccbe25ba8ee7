package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderAuditFlow;
import com.ruoyi.system.api.domain.vo.order.OrderAuditFlowVO;
import com.wnkx.order.mapper.OrderAuditFlowMapper;
import com.wnkx.order.service.IOrderAuditFlowService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_audit_flow(订单财务审核流水)】的数据库操作Service实现
* @createDate 2024-12-18 18:09:39
*/
@Service
public class OrderAuditFlowServiceImpl extends ServiceImpl<OrderAuditFlowMapper, OrderAuditFlow>
    implements IOrderAuditFlowService {

    @Override
    public List<OrderAuditFlowVO> getListByOrderNumOrPayNum(String orderNum, String payNum) {
        Assert.isFalse(StrUtil.isAllBlank(orderNum, payNum), "订单号和支付号不能同时为空");
        List<OrderAuditFlow> listByOrderNum = baseMapper.getListByOrderNumOrPayNum(orderNum, payNum);
        if (CollUtil.isEmpty(listByOrderNum)){
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(listByOrderNum, OrderAuditFlowVO.class);
    }
}




