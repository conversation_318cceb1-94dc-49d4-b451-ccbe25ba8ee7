package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.OrderInvoiceOperateTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOperate;
import com.wnkx.order.mapper.OrderInvoiceOperateMapper;
import com.wnkx.order.service.OrderInvoiceOperateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceOperateServiceImpl extends ServiceImpl<OrderInvoiceOperateMapper, OrderInvoiceOperate> implements OrderInvoiceOperateService {


    /**
     * 运营端-发票管理-流转记录
     */
    @Override
    public List<OrderInvoiceOperate> getInvoiceOperateRecord(Long invoiceId) {
        return baseMapper.getInvoiceOperateRecord(invoiceId);
    }

    /**
     * 保存开票操作记录
     */
    @Override
    public void saveBatchOrderInvoiceOperate(List<OrderInvoiceOperate> orderInvoiceOperates) {
        if (CollUtil.isEmpty(orderInvoiceOperates)) {
            return;
        }

        for (OrderInvoiceOperate orderInvoiceOperate : orderInvoiceOperates) {
            orderInvoiceOperate.setContent(StrUtil.replace(orderInvoiceOperate.getContent(), OrderConstant.ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT, SecurityUtils.getUsername()));
        }

        baseMapper.saveBatch(orderInvoiceOperates);
    }

    /**
     * 保存开票操作记录
     */
    @Override
    public void saveOrderInvoiceOperate(Long invoiceId, Integer code, String eventContent) {
        OrderInvoiceOperate orderInvoiceOperate = new OrderInvoiceOperate();
        orderInvoiceOperate.setInvoiceId(invoiceId);
        orderInvoiceOperate.setType(code);
        orderInvoiceOperate.setContent(StrUtil.replace(eventContent, OrderConstant.ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT, SecurityUtils.getUsername()));
        baseMapper.insert(orderInvoiceOperate);
    }

    /**
     * 保存开票操作记录
     */
    @Override
    public void saveOrderInvoiceOperate(Long invoiceId, OrderInvoiceOperateTypeEnum operateType, List<String> objectKeys) {
        OrderInvoiceOperate orderInvoiceOperate = new OrderInvoiceOperate();
        orderInvoiceOperate.setInvoiceId(invoiceId);
        orderInvoiceOperate.setType(operateType.getCode());
        orderInvoiceOperate.setContent(StrUtil.replace(operateType.getEventContent(), OrderConstant.ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT, SecurityUtils.getUsername()));
        orderInvoiceOperate.setObjectKey(StrUtil.join(StrUtil.COMMA, objectKeys));
        baseMapper.insert(orderInvoiceOperate);
    }
}
