package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.OrderInvoiceOperateTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOperate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:29
 */
public interface OrderInvoiceOperateService extends IService<OrderInvoiceOperate> {

    /**
     * 保存开票操作记录
     */
    void saveOrderInvoiceOperate(Long invoiceId, OrderInvoiceOperateTypeEnum operateType, List<String> objectKeys);

    /**
     * 保存开票操作记录
     */
    void saveOrderInvoiceOperate(Long invoiceId, Integer code, String eventContent);

    /**
     * 保存开票操作记录
     */
    void saveBatchOrderInvoiceOperate(List<OrderInvoiceOperate> orderInvoiceOperates);

    /**
     * 运营端-发票管理-流转记录
     */
    List<OrderInvoiceOperate> getInvoiceOperateRecord(Long invoiceId);
}
