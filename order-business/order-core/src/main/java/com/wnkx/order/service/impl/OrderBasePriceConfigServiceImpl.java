package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigVO;
import com.wnkx.order.mapper.OrderBasePriceConfigMapper;
import com.wnkx.order.service.OrderBasePriceConfigChangelogService;
import com.wnkx.order.service.OrderBasePriceConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_base_price_config(订单基础价格配置表)】的数据库操作Service实现
 * @createDate 2025-05-16 16:58:03
 */
@Service
@Slf4j
@RequiredArgsConstructor

public class OrderBasePriceConfigServiceImpl extends ServiceImpl<OrderBasePriceConfigMapper, OrderBasePriceConfig>
        implements OrderBasePriceConfigService {

    private final OrderBasePriceConfigChangelogService orderBasePriceConfigChangelogService;
    private final RedisService redisService;

    @Override
    @Transactional
    public void changeConfigByType(OrderBasePriceConfigVO orderBasePriceConfigVO, Integer type) {
        if (type == null || !type.equals(OrderConstant.TYPE_SERVICE_PRICE)) {
            return;
        }
        OrderBasePriceConfigVO previousConfig = getCurrentConfigByte(OrderConstant.TYPE_SERVICE_PRICE);
        OrderBasePriceConfig orderBasePriceConfig = new OrderBasePriceConfig();
        orderBasePriceConfig.setPriceType(type);
        orderBasePriceConfig.setOriginPrice(orderBasePriceConfigVO.getOriginPrice());
        orderBasePriceConfig.setOriginPriceProxy(orderBasePriceConfigVO.getOriginPriceProxy());
        orderBasePriceConfig.setCurrentPrice(orderBasePriceConfigVO.getCurrentPrice());
        orderBasePriceConfig.setCurrentPriceProxy(orderBasePriceConfigVO.getCurrentPriceProxy());
        orderBasePriceConfig.setSinceTime(orderBasePriceConfigVO.getSinceTime());
        orderBasePriceConfig.setCreateBy(SecurityUtils.getUsername());
        orderBasePriceConfig.setCreateById(SecurityUtils.getUserId());
        orderBasePriceConfig.setUpdateBy(SecurityUtils.getUsername());
        orderBasePriceConfig.setUpdateById(SecurityUtils.getUserId());
        baseMapper.updateByType(orderBasePriceConfig);
        orderBasePriceConfigChangelogService.addChangeLog(previousConfig, orderBasePriceConfig);
//        更新缓存
        redisService.setCacheObject(CacheConstants.PENDING_EFFECT_SERVICE_FEE_CACHE_KEY, orderBasePriceConfig);

    }

    @Override
    public OrderBasePriceConfigVO getCurrentConfigByte(Integer type) {
        if (type == null || !type.equals(OrderConstant.TYPE_SERVICE_PRICE)) {
            return null;
        }
//        优先从缓存中获取
        if (redisService.hasKey(CacheConstants.PENDING_EFFECT_SERVICE_FEE_CACHE_KEY)) {
            return getOrderBasePriceConfigVO(redisService.getCacheObject(CacheConstants.PENDING_EFFECT_SERVICE_FEE_CACHE_KEY));
        }
        OrderBasePriceConfig orderBasePriceConfig = baseMapper.getCurrentConfigByte(type);
        if (orderBasePriceConfig != null) {
            redisService.setCacheObject(CacheConstants.PENDING_EFFECT_SERVICE_FEE_CACHE_KEY, orderBasePriceConfig);
            return getOrderBasePriceConfigVO(orderBasePriceConfig);
        }
        throw new ServiceException("系统繁忙,请稍后再试");
    }

    @Override
    public OrderBasePriceConfigVO getCurrentConfigByteActive(Integer type) {
        OrderBasePriceConfigVO currentConfigByte = getCurrentConfigByte(type);
        if (currentConfigByte.getSinceTime().before(new Date())) {
            redisService.setCacheObject(CacheConstants.CURRENT_SERVICE_FEE_CACHE_KEY, currentConfigByte);
            return currentConfigByte;
        } else {
            return redisService.getCacheObject(CacheConstants.CURRENT_SERVICE_FEE_CACHE_KEY);
        }
    }

    private OrderBasePriceConfigVO getOrderBasePriceConfigVO(OrderBasePriceConfig orderBasePriceConfig) {
        OrderBasePriceConfigVO orderBasePriceConfigVO = new OrderBasePriceConfigVO();
        orderBasePriceConfigVO.setOriginPrice(orderBasePriceConfig.getOriginPrice());
        orderBasePriceConfigVO.setOriginPriceProxy(orderBasePriceConfig.getOriginPriceProxy());
        orderBasePriceConfigVO.setCurrentPrice(orderBasePriceConfig.getCurrentPrice());
        orderBasePriceConfigVO.setCurrentPriceProxy(orderBasePriceConfig.getCurrentPriceProxy());
        orderBasePriceConfigVO.setSinceTime(orderBasePriceConfig.getSinceTime());
        orderBasePriceConfigVO.setCreateBy(orderBasePriceConfig.getCreateBy());
        orderBasePriceConfigVO.setCreateTime(orderBasePriceConfig.getCreateTime());
        return orderBasePriceConfigVO;
    }

    @Override
    public List<OrderBasePriceConfigChangelogVO> getConfigChangeLogList(Integer type) {
        if (type == null || !type.equals(OrderConstant.TYPE_SERVICE_PRICE)) {
            return null;
        }
        return orderBasePriceConfigChangelogService.getConfigChangeLogList(type);
    }


    @Override
    public BigDecimal getCurrentServicePriceProxy() {
        return getCurrentConfigByteActive(OrderConstant.TYPE_SERVICE_PRICE).getCurrentPriceProxy();
    }

    @Override
    public BigDecimal getCurrentServicePrice() {
        return getCurrentConfigByteActive(OrderConstant.TYPE_SERVICE_PRICE).getCurrentPrice();
    }
}




