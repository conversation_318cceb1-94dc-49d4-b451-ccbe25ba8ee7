package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRedOrderVideo;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO;
import com.wnkx.order.mapper.OrderInvoiceRedOrderVideoMapper;
import com.wnkx.order.service.OrderInvoiceRedOrderVideoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceRedOrderVideoServiceImpl extends ServiceImpl<OrderInvoiceRedOrderVideoMapper, OrderInvoiceRedOrderVideo> implements OrderInvoiceRedOrderVideoService {


    /**
     * 根据发票红冲id查询发票红冲视频订单
     */
    @Override
    public List<OrderInvoiceRedOrderVideoVO> selectRedOrderVideoVOListByRedOrderIds(List<Long> invoiceRedOrderIds) {
        return baseMapper.selectRedOrderVideoVOListByRedOrderIds(invoiceRedOrderIds);
    }

}
