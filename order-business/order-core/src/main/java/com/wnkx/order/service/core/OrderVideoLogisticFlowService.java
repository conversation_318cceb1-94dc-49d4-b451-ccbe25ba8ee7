package com.wnkx.order.service.core;

import com.ruoyi.common.core.enums.FollowStatusEnum;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :物流流转
 * @create :2025-04-23 09:26
 **/
public interface OrderVideoLogisticFlowService {

    /**
     * 状态流转
     *
     * @param dto
     */
    void OrderVideoLogisticFlow(OrderVideoLogisticFlowDTO dto);

    /**
     * 检查状态数据
     * @param dto
     */
    void checkStatus(OrderVideoLogisticFlowDTO dto);

    /**
     * 获取状态类型
     *
     * @return
     */
    FollowStatusEnum getFollowStatusType();
}
