package com.wnkx.order.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.enums.ReminderStatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderCountVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoReminderRecordListVO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoReminderRecord;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderVideoReminderRecordMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoReminderRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoReminderRecordServiceImpl extends ServiceImpl<OrderVideoReminderRecordMapper, OrderVideoReminderRecord> implements OrderVideoReminderRecordService {

    private final OrderVideoProperties orderVideoProperties;
    private final RedisService redisService;
    private final RemoteService remoteService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;

    /**
     * 运营已催单
     */
    @Override
    public void urged(Long videoId) {
        baseMapper.urged(videoId);
    }

    /**
     * 通过视频订单id查询催单记录
     */
    @Override
    public List<OrderVideoReminderRecord> selectListByVideoIds(List<Long> videoIds) {
        return baseMapper.selectListByVideoIds(videoIds);
    }

    /**
     * 更新催单记录为已完成
     */
    @Override
    public void updateReminderStatusToFinished(List<Long> videoIds) {
        baseMapper.updateReminderStatusToFinished(videoIds);
    }

    /**
     * 总催单次数统计
     */
    @Override
    public OrderVideoReminderCountVO reminderCount() {
        Map<String, Integer> codeMap = ReminderStatusEnum.getCodeMap();
        return baseMapper.reminderCount(codeMap);
    }

    /**
     * 催单列表
     */
    @Override
    public List<OrderVideoReminderRecordListVO> selectListByCondition(OrderVideoReminderRecordListDTO dto) {
        try {
            initCondition(dto);

            OrderByDto orderByDto = new OrderByDto();
            orderByDto.setField("ovrr.reminder_time", OrderByDto.DIRECTION.DESC);
            PageUtils.startPage(orderByDto);
            List<OrderVideoReminderRecordListVO> list = baseMapper.selectListByCondition(dto);

            assembleReminderRecordList(list);
            return list;
        } catch (Exception e) {
            log.error("获取催单列表异常", e);
            throw new ServiceException("获取催单列表异常");
        }
    }

    /**
     * 商家催一催
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reminder(Long videoId) {
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(videoId);
        Assert.isTrue(OrderStatusEnum.UN_FINISHED.getCode().equals(orderVideo.getStatus()), "订单未进入到待完成状态，请稍安勿躁~");
        Assert.notNull(orderVideo.getUnFinishedTime(), "旧数据验证报错，测试此功能请重新创建订单~");
        Assert.isTrue(DateUtil.compare(DateUtil.date(), DateUtil.offset(orderVideo.getUnFinishedTime(), DateField.HOUR_OF_DAY, orderVideoProperties.getReminderTime())) > 0, "运营正在处理您的订单，请耐心等候~");
        if (ObjectUtil.isNotNull(redisService.getCacheObject(getOrderVideoReminderKey(videoId)))) {
            return false;
        }
        OrderVideoReminderRecord orderVideoReminderRecord = new OrderVideoReminderRecord();
        orderVideoReminderRecord.setVideoId(videoId);
        Long count = baseMapper.selectByVideoCount(videoId);
        orderVideoReminderRecord.setReminder(count == 0 ? 1 : Convert.toInt(count + 1));
        orderVideoReminderRecord.setReminderTime(DateUtil.date());
        baseMapper.insert(orderVideoReminderRecord);

        //  获取到明天0点的秒数
        long between = DateUtil.between(DateUtil.date(), DateUtil.beginOfDay(DateUtil.tomorrow()), DateUnit.SECOND);
        redisService.setCacheObject(getOrderVideoReminderKey(videoId), 1, between, TimeUnit.SECONDS);
        return true;
    }

    /**
     * 获取催一催Redis缓存key
     */
    @Override
    public String getOrderVideoReminderKey(Long videoId) {
        return OrderConstant.ORDER_VIDEO_REMINDER_KEY + videoId;
    }

    private void assembleReminderRecordList(List<OrderVideoReminderRecordListVO> list) throws ExecutionException, InterruptedException {
        CompletableFuture<List<BusinessAccountDetailVO>> businessFuture = CompletableFuture.supplyAsync(() -> {
            Set<Long> businessIds = list.stream().map(OrderVideoReminderRecordListVO::getCreateOrderBusinessId).collect(Collectors.toSet());
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setBusinessIds(businessIds);
            return remoteService.queryMerchant(businessAccountDetailDTO);
        }, asyncPoolTaskExecutor);

        CompletableFuture<Map<Long, UserVO>> userMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> issueIds = list.stream().map(OrderVideoReminderRecordListVO::getIssueId).collect(Collectors.toList());
            SysUserListDTO sysUserListDTO = new SysUserListDTO();
            sysUserListDTO.setUserId(issueIds);
            return remoteService.getUserMap(sysUserListDTO);
        }, asyncPoolTaskExecutor);

        CompletableFuture.allOf(businessFuture, userMapFuture).join();

        Map<Long, UserVO> userMap = userMapFuture.get();
        List<BusinessAccountDetailVO> business = businessFuture.get();
        Map<Long, BusinessAccountDetailVO> businessMap = RemoteService.RemoteUtil.getMerchantMap(business);

        for (OrderVideoReminderRecordListVO vo : list) {
            vo.setCreateOrderBusiness(businessMap.get(vo.getCreateOrderBusinessId()));
            vo.setIssue(userMap.get(vo.getIssueId()));
        }
    }

    private void initCondition(OrderVideoReminderRecordListDTO dto) throws ExecutionException, InterruptedException {
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            CompletableFuture<List<Long>> businessIdsFuture = CompletableFuture.supplyAsync(() -> {
                BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
                businessAccountDetailDTO.setBusinessName(dto.getKeyword());
                List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(businessAccountDetailDTO);
                return businessAccountDetailVOS.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toList());
            }, asyncPoolTaskExecutor);

            CompletableFuture<List<Long>> issueIdsFuture = CompletableFuture.supplyAsync(() -> {
                SysUserListDTO sysUserListDTO = new SysUserListDTO();
                sysUserListDTO.setUserName(dto.getKeyword());
                List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
                return userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }, asyncPoolTaskExecutor);

            CompletableFuture.allOf(businessIdsFuture, issueIdsFuture).join();

            List<Long> businessIds = businessIdsFuture.get();
            List<Long> issueIds = issueIdsFuture.get();

            dto.setBusinessIds(businessIds);
            dto.setIssueIds(issueIds);
        }
    }
}
