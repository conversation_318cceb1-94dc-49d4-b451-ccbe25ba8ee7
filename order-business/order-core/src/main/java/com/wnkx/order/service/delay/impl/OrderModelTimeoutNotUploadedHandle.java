
package com.wnkx.order.service.delay.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.constant.DelayQueueConstant;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterial;
import com.wnkx.order.service.IOrderVideoModelService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialService;
import com.wnkx.order.service.delay.OrderQueueHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单模特超时未上传素材
 *
 * <AUTHOR>
 * @date 2024/8/13
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderModelTimeoutNotUploadedHandle implements OrderQueueHandleService {

    @Override
    public void processTask(String json) {
        Long videoId = Long.parseLong(json);
        log.debug("订单模特超时未上传素材队列{}任务处理，videoId：{}", DelayQueueConstant.ORDER_MODEL_TIMEOUT_NOT_UPLOAD_QUEUE_NAME, videoId);

        //  任务处理逻辑。。。
        OrderVideoModel orderVideoModel = SpringUtils.getBean(IOrderVideoModelService.class).getModelIdByVideoId(videoId);
        if (orderVideoModel == null) {
            log.warn("订单模特超时未上传素材队列{}任务处理失败，视频订单：{}未被模特接单", DelayQueueConstant.ORDER_MODEL_TIMEOUT_NOT_UPLOAD_QUEUE_NAME, videoId);
            return;
        }

        List<OrderVideoFeedBackMaterial> orderVideoFeedBackMaterials = SpringUtils.getBean(OrderVideoFeedBackMaterialService.class).selectListByVideoId(videoId);
        if (CollUtil.isNotEmpty(orderVideoFeedBackMaterials)) {
            log.debug("当前订单无需处理，视频订单：{}，模特已上传素材", videoId);
            return;
        }

        orderVideoModel.setOverTime(DateUtil.date());
        SpringUtils.getBean(IOrderVideoModelService.class).updateById(orderVideoModel);
    }

    @Override
    public String getQueueName() {
        return DelayQueueConstant.ORDER_MODEL_TIMEOUT_NOT_UPLOAD_QUEUE_NAME;
    }
}
