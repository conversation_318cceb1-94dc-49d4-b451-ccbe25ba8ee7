package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.promotion.UpdatePromotionActivityDTO;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【promotion_activity(活动信息表)】的数据库操作Service
 * @createDate 2025-03-19 15:50:19
 */
public interface PromotionActivityService extends IService<PromotionActivity> {

    /**
     * 根据活动类型获取有效的活动
     */
    PromotionActivityVO getValidPromotionActivityByType(Integer type);

    /**
     * 获取当前商家可参与的活动
     */
    List<BusinessParticipatoryActivityVO> getBusinessParticipatoryActivityVOS();

    /**
     * 获取是否需要提醒当前用户会员半价续费
     */
    Boolean getRemindBusinessMemberHalfPriceRenewal();

    /**
     * 修改活动配置
     */
    void updatePromotionActivity(UpdatePromotionActivityDTO dto);

    /**
     * 根据活动类型获取活动
     */
    PromotionActivityVO getPromotionActivityByType(Integer type);

    /**
     * 获取进行中的活动列表
     */
    List<PromotionActivityVO> selectValidPromotionActivityList();
}
