package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateCartToOrderVideoOperateDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoOperate;
import com.ruoyi.system.api.domain.vo.order.OrderVideoOperateVO;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/14 9:35
 */
public interface OrderVideoOperateService extends IService<OrderVideoOperate> {

    /**
     * 新增视频订单操作记录
     */
    void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                 Integer isCart,
                                 Integer isPublic,
                                 String eventExecuteUserName,
                                 @Valid @NotEmpty(message = "[orderVideoFlowDTOS]不能为空") List<OrderVideoOperateDTO> orderVideoFlowDTOS);

    default void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                         Integer isPublic,
                                         @Valid @NotEmpty(message = "[orderVideoFlowDTOS]不能为空") List<OrderVideoOperateDTO> orderVideoFlowDTOS) {
        createOrderVideoOperate(eventName, null, isPublic, null, orderVideoFlowDTOS);
    }

    default void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                         Integer isPublic,
                                         String eventExecuteUserName,
                                         @Valid @NotEmpty(message = "[orderVideoFlowDTOS]不能为空") List<OrderVideoOperateDTO> orderVideoFlowDTOS) {
        createOrderVideoOperate(eventName, null, isPublic, eventExecuteUserName, orderVideoFlowDTOS);
    }

    /**
     * 新增视频订单操作记录
     */
    void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                 Integer isCart,
                                 Integer isPublic,
                                 String eventExecuteUserName,
                                 @Valid OrderVideoOperateDTO orderVideoFlowDTO);

    default void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                         Integer isPublic,
                                         @Valid OrderVideoOperateDTO orderVideoFlowDTO) {
        createOrderVideoOperate(eventName, null, isPublic, null, orderVideoFlowDTO);
    }

    default void createOrderVideoOperate(@NotBlank(message = "[事件名称]不能为空") String eventName,
                                         Integer isPublic,
                                         String eventExecuteUserName,
                                         @Valid OrderVideoOperateDTO orderVideoFlowDTO) {
        createOrderVideoOperate(eventName, null, isPublic, eventExecuteUserName, orderVideoFlowDTO);
    }


    /**
     * 将购物车操作记录更新为视频订单操作记录
     */
    void updateCartToOrderVideoOperate(List<UpdateCartToOrderVideoOperateDTO> updateCartToOrderVideoOperateDTOS);

    /**
     * 将[订单退款]操作记录开放给商家可看
     */
    void updateOrderVideoOperateToPublic(List<Long> videoIds);

    /**
     * 查询视频订单操作记录
     */
    List<OrderVideoOperateVO> selectOrderVideoOperateListByVideoId(Long videoId);
}
