package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceReopen;

/**
 * <AUTHOR>
 * @date 2025/1/14 19:09
 */
public interface OrderInvoiceReopenService extends IService<OrderInvoiceReopen> {

    /**
     * 保存发票重开记录
     */
    void saveOrderInvoiceReopen(OrderInvoiceReopen orderInvoiceReopen);

    /**
     * 通过新发票ID获取旧发票ID
     */
    Long getOldInvoiceReopenByNewInvoiceId(Long newInvoiceId);
}
