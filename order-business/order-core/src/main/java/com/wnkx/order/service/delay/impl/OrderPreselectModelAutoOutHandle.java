package com.wnkx.order.service.delay.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.DelayQueueConstant;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import com.wnkx.order.service.delay.OrderQueueHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 预选模特到期自动淘汰
 *
 * <AUTHOR>
 * @date 2024/7/9 15:42
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderPreselectModelAutoOutHandle implements OrderQueueHandleService {
    @Override
    public void processTask(String json) {
        Long preselectModelId = Long.parseLong(json);
        log.debug("==========预选模特到期自动淘汰队列{}任务处理，preselectModelId：{}", DelayQueueConstant.ORDER_PRESELECT_MODEL_AUTO_OUT_QUEUE_NAME, preselectModelId);

        OrderVideoMatchPreselectModel preselectModel = SpringUtils.getBean(IOrderVideoMatchPreselectModelService.class).getById(preselectModelId);
        if (ObjectUtil.isNull(preselectModel)) {
            log.warn("订单自动完成处理队列{}任务处理失败，视频订单不存在，preselectModelId：{}", DelayQueueConstant.ORDER_PRESELECT_MODEL_AUTO_OUT_QUEUE_NAME, preselectModelId);
        }
        if ((!PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(preselectModel.getAddType()) && !PreselectModelAddTypeEnum.OPERATION.getCode().equals(preselectModel.getAddType()))
                || !PreselectStatusEnum.UN_JOINTED.getCode().equals(preselectModel.getStatus())) {
            log.debug("当前预选模特无需处理，不是意向模特或者不是未对接，preselectModelId：{}", preselectModelId);
            return;
        }
        DateTime date = DateUtil.date();
        preselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
        preselectModel.setRemark(OrderConstant.ORDER_PRESELECT_MODEL_OVERTIME_REMARK);
        preselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.EXPIRE.getCode());
        preselectModel.setSelectTime(date);
        preselectModel.setProcessTime(date);
        preselectModel.setOustType(PreselectModelOustTypeEnum.TIMEOUT_INTENTION_NOT_SELECTED.getCode());
        preselectModel.setOustTime(date);
        preselectModel.setModelIntention(ModelIntentionEnum.TIMEOUT_NOT_SELECTED.getCode());
        SpringUtils.getBean(IOrderVideoMatchPreselectModelService.class).updateById(preselectModel);
    }

    @Override
    public String getQueueName() {
        return DelayQueueConstant.ORDER_PRESELECT_MODEL_AUTO_OUT_QUEUE_NAME;
    }
}
