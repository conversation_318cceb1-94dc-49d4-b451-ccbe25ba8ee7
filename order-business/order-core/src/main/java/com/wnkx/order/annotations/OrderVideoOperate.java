package com.wnkx.order.annotations;

import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OrderVideoOperate {
    OrderVideoOperateTypeEnum operateType();          // 事件名称

    String videoId();            // 通过SpEL表达式获取videoId，比如#shippingDTO.videoId

    int isCart() default 0; // 是否是购物车订单
}
