package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeCloseRequest;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.PayConstant;
import com.ruoyi.common.core.enums.PayTranStatusEnum;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalancePrepayListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.PrepayUpdateAppIdDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.ruoyi.system.api.domain.entity.order.AlipayPayLog;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.wnkx.order.config.AlipayProperties;
import com.wnkx.order.config.PayConfig;
import com.wnkx.order.factory.PayClientFactory;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 支付宝相关处理逻辑
 *
 * <AUTHOR>
 * @date 2024/10/24
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AlipayServiceImpl implements AlipayService {

    private final PayConfig payConfig;
    private final RedisService redisService;
    private final TransactionTemplate transactionTemplate;
    private final IOrderService orderService;
    private final AsyncTaskService asyncTaskService;
    private final AlipayOrderTableService alipayOrderTableService;
    private final AlipayPayLogService alipayPayLogService;
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;
    private final OrderMergeService orderMergeService;
    private final RemoteService remoteService;

    @Value("${pay.debugger.enable}")
    private String debuggerEnable;

    /**
     * 通过内部订单号关闭支付宝订单
     */
    @Override
    public void closeAlipayOrder(String orderNum, String appId) {
        AlipayOrderTable alipayOrderTable = alipayOrderTableService.getValidAlipayOrderTableByOrderNum(orderNum);
        if (ObjectUtil.isNull(alipayOrderTable)) {
            return;
        }
        log.debug("调用支付宝关单服务{}", alipayOrderTable.getMchntOrderNo());
        AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
        Map<String, String> hashmap = new HashMap<>();
        hashmap.put("out_trade_no", alipayOrderTable.getMchntOrderNo());
        hashmap.put("operator_id", "system");
        request.setBizContent(JSON.toJSONString(hashmap));
        try {
            AlipayTradeCloseResponse response = PayClientFactory.getAlipayProperties(appId).getAlipayClient().execute(request);
            if (response.isSuccess()) {
                log.info("调用支付宝关单服务成功,{},{}", response.getSubCode(), response.getOutTradeNo());
            } else {
                log.info("调用支付宝关单服务失败,{},{}", response.getSubCode(), response.getOutTradeNo());
            }
        } catch (AlipayApiException e) {
            log.info("调用支付宝关单服务失败,api异常,{},{},{}", e.getErrCode(), e.getErrMsg(), alipayOrderTable.getMchntOrderNo());
        }
    }

    /**
     * 支付宝支付回调
     */
    @Override
    public String alipayCallback(HttpServletRequest request) {
        log.debug("===================接收到支付宝回调数据===================");

        String out_trade_no = CharSequenceUtil.EMPTY;
        try {
            // 获取请求体中的内容
            StringBuilder sb = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            String body = sb.toString();
            log.debug("===================支付宝回调请求体：{}", body);

            Map<String, String> callbackData = parseAndDecode(body);
            AlipayPayLog alipayPayLog = BeanUtil.copyProperties(callbackData, AlipayPayLog.class);
            log.debug("===================解析后的数据：{}", alipayPayLog);

            out_trade_no = alipayPayLog.getOut_trade_no();
            if (!redisService.getLock(CacheConstants.THIRD_PAY_KEY + out_trade_no, 60L)) {
                log.warn("===================获取锁失败，业务正在处理中，out_trade_no：{}", out_trade_no);
                return "failure";
            }
            // 调用SDK验证签名
            AlipayProperties alipayProperties = sdkVerifySign(callbackData);
            if (alipayProperties != null) {
                log.debug("===================验签成功===================");
                // 验签成功后，按照支付结果异步通知中的描述，对支付结果中的业务内容进行二次校验，校验成功后在response中返回success并继续商户自身业务处理，校验失败返回failure
                if (!quadraticCheck(alipayPayLog, alipayProperties)) {
                    log.error("===================二次校验失败===================");
                    return "failure";
                }
                //  在支付宝的业务通知中，只有交易通知状态为 TRADE_SUCCESS 或 TRADE_FINISHED 时，支付宝才会认定为买家付款成功。
                if (PayTranStatusEnum.SUCCESS.getAliPayStatus().equals(alipayPayLog.getTrade_status()) || PayTranStatusEnum.FINISH.getAliPayStatus().equals(alipayPayLog.getTrade_status())) {
                    alipayPayLog.setRemark(JSONObject.toJSONString(callbackData));
                    orderProcess(alipayPayLog);
                } else {
                    //  非支付成功和交易完成 直接返回success
                    return "success";
                }
            } else {
                log.error("===================验签失败===================");
                return "failure";
            }
        } catch (Exception e) {
            log.error("处理支付宝回调数据时出错", e);
        } finally {
            redisService.releaseLock(CacheConstants.THIRD_PAY_KEY + out_trade_no);
        }
        return "success";
    }

    /**
     * 调用支付宝电脑网站支付
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreatePayVo alipayWebPay(Boolean isMerge, String payNum, List<String> orderNums, String appId, BigDecimal finalPayAmount, Long payUserId) {
        String requestAppId = appId;
        // 获取支付宝订单表数据 如果数据不为空 且未过期则直接返回
        AlipayOrderTable alipayOrderTable = Optional.ofNullable(alipayOrderTableService.getValidAlipayOrderTableByOrderNum(isMerge ? payNum : orderNums.get(0))).orElse(new AlipayOrderTable());
        if (BeanUtil.isNotEmpty(alipayOrderTable)) {
            Long datePoorSec = DateUtils.getDatePoorSec(new Date(), alipayOrderTable.getCreateTime());
            // 检查数据
            if (alipayOrderTable.getOrderAmount().compareTo(finalPayAmount) != 0 ||
                    datePoorSec >= PayConstant.QRCODE_VALID_TIME
            ) {
                // 支付宝订单表支付金额与现有数据不一致！
                log.debug("请求新二维码，取消原有二维码");
                OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(PayTypeEnum.ALIPAY.getCode());
                Assert.notNull(orderPayeeAccountConfigInfoDTO, "获取收款账号配置信息失败，请联系蜗牛客服处理~");
                if (!orderPayeeAccountConfigInfoDTO.getBankAccount().equals(appId)) {
                    //如果是钱包充值订单
                    if (alipayOrderTable.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)) {
                        //变更预付款支付主体
                        remoteService.innerBusinessBalancePrepayUpdateAppId(PrepayUpdateAppIdDTO.builder()
                                .appId(appId)
                                .payUserId(payUserId)
                                .accountId(orderPayeeAccountConfigInfoDTO.getDetailId())
                                .payType(PayTypeEnum.ALIPAY.getCode())
                                .prepayNums(orderNums).build());
                    } else {
                        orderService.updateWechatPayAppId(orderNums, orderPayeeAccountConfigInfoDTO);
                    }
                    requestAppId = orderPayeeAccountConfigInfoDTO.getBankAccount();
                    orderService.updateAliPayAppId(orderNums, orderPayeeAccountConfigInfoDTO);
                }
                alipayOrderTableService.banFyOrderTable(alipayOrderTable.getId());
                asyncTaskService.closeAlipayOrder(alipayOrderTable.getMchntOrderNo(), appId);
            } else {
                saveOrderLinkToRedis(alipayOrderTable.getOrderNum(), alipayOrderTable.getMchntOrderNo());
                return CreatePayVo.builder().pageRedirectionData(alipayOrderTable.getPageRedirectionData()).build();
            }
        }

        alipayOrderTable.setId(null);
        alipayOrderTable.setAppId(requestAppId);
        alipayOrderTable.setOrderNum(isMerge ? payNum : orderNums.get(0));
        alipayOrderTable.setOrderAmount(finalPayAmount);
        alipayOrderTable.setCreateTime(null);
        return CreatePayVo.builder().pageRedirectionData(pay(alipayOrderTable)).build();
    }

    /**
     * 设置订单与真实订单的关联关系（用于查单）
     *
     * @param orderNum   订单号
     * @param outTradeNo 外部交易号
     */
    private void saveOrderLinkToRedis(String orderNum, String outTradeNo) {
        redisService.setCacheObject(CacheConstants.ORDER_NUMBER_PREFIX + orderNum, outTradeNo, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    private String pay(AlipayOrderTable alipayOrderTable) {
        final String outTradeNo = convertToOutTradeNo(alipayOrderTable.getOrderNum());

        try {
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(outTradeNo);
            model.setTotalAmount(alipayOrderTable.getOrderAmount().toString());
            if (Boolean.TRUE.toString().equals(debuggerEnable)) {
                model.setTotalAmount("0.01");
            }
            model.setSubject("蜗牛海拍");
            model.setProductCode("FAST_INSTANT_TRADE_PAY");
            model.setTimeExpire(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.SECOND, Convert.toInt(PayConstant.QRCODE_VALID_TIME)), DatePattern.NORM_DATETIME_PATTERN));
            model.setQrPayMode("4");
            model.setQrcodeWidth(180L);
            request.setBizModel(model);
            AlipayProperties alipayProperties = PayClientFactory.getAlipayProperties(alipayOrderTable.getAppId());
            request.setNotifyUrl(alipayProperties.getNotifyUrl());
            AlipayTradePagePayResponse response = alipayProperties.getAlipayClient().pageExecute(request, "POST");
            String pageRedirectionData = response.getBody();
            log.debug("===================跳转页面数据：{}", pageRedirectionData);
            if (!response.isSuccess()) {
                log.error("===================调用失败");
                throw new ServiceException("系统繁忙，请稍后重试~");
            }

            // 加入数据库
            alipayOrderTable.setMchntOrderNo(outTradeNo);
            alipayOrderTable.setPageRedirectionData(pageRedirectionData);
            alipayOrderTableService.save(alipayOrderTable);
            return pageRedirectionData;
        } catch (Exception e) {
            log.error("===================请求获取支付宝跳转页面数据报错", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 将订单号转为外部订单号
     * <p>视频订单 DDWN1155 -> 商户码+1155+3</p>
     * <p>会员订单 HYWN1155 -> 商户码+1155+2</p>
     * 富有方要求订单号唯一，故加入随机数
     *
     * @param orderNumber 订单号
     * @return 精简过后的内部订单号
     */
    private String convertToOutTradeNo(String orderNumber) {
        // 视频订单
        if (orderNumber.startsWith(OrderConstant.ORDER_NUM_PREFIX_DD + OrderConstant.ORDER_NUM_PREFIX_WN)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.ORDER_NUM_PREFIX_DD.length()
                            + OrderConstant.ORDER_NUM_PREFIX_WN.length()) + RandomUtil.randomString(3);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 会员订单
        else if (orderNumber.startsWith(OrderConstant.ORDER_NUM_PREFIX_HY + OrderConstant.ORDER_NUM_PREFIX_WN)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.ORDER_NUM_PREFIX_HY.length()
                            + OrderConstant.ORDER_NUM_PREFIX_WN.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 支付单
        else if (orderNumber.startsWith(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 线上钱包充值
        else if (orderNumber.startsWith(OrderConstant.PREPAY_NUM)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.PREPAY_NUM.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        throw new ServiceException("订单号类型错误");
    }

    /**
     * 二次校验支付宝回调数据
     *
     * @param payLog           支付宝回调数据转成java对象
     * @param alipayProperties
     */
    private boolean quadraticCheck(AlipayPayLog payLog, AlipayProperties alipayProperties) {
        AlipayOrderTable alipayOrderTable = alipayOrderTableService.getAlipayOrderTableByOutTradeNo(payLog.getOut_trade_no());
        //  1、商家需要验证该通知数据中的 out_trade_no 是否为商家系统中创建的订单号。
        if (ObjectUtil.isNull(alipayOrderTable)) {
            log.error("===================支付宝订单表数据不存在，out_trade_no：{}", payLog.getOut_trade_no());
            return false;
        }
        //  2、判断 total_amount 是否确实为该订单的实际金额（即商家订单创建时的金额）。
        if (Boolean.TRUE.toString().equals(debuggerEnable)) {
            if (payLog.getTotal_amount().compareTo(new BigDecimal("0.01")) != 0) {
                log.error("===================测试环境金额与回调金额不一致，回调金额：{}", payLog.getTotal_amount());
                return false;
            }
        } else if (payLog.getTotal_amount().compareTo(alipayOrderTable.getOrderAmount()) != 0) {
            log.error("===================订单应付金额与回调金额不一致，应付金额：{}，回调金额：{}", alipayOrderTable.getOrderAmount(), payLog.getTotal_amount());
            return false;
        }
        //  3、校验通知中的 seller_id（或者 seller_email）是否为 out_trade_no 这笔单据的对应的操作方（有的时候，一个商家可能有多个 seller_id/seller_email）。
        if (!alipayProperties.getSellerId().equals(payLog.getSeller_id())) {
            log.error("===================seller_id不一致，nacos配置的seller_id：{}，回调的seller_id：{}", alipayProperties.getSellerId(), payLog.getSeller_id());
            return false;
        }

        //  4、验证 app_id 是否为该商家本身。
        if (!alipayProperties.getAlipayConfig().getAppId().equals(payLog.getApp_id())) {
            log.error("===================app_id不一致，nacos配置的app_id：{}，回调的app_id：{}", alipayProperties.getAlipayConfig().getAppId(), payLog.getApp_id());
            return false;
        }

        return true;
    }

    private static Map<String, String> parseAndDecode(String rawData) {
        Map<String, String> resultMap = new HashMap<>();

        // 分割键值对
        String[] pairs = rawData.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                String key = URLDecoder.decode(keyValue[0], StandardCharsets.UTF_8);
                String value = URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8);
                resultMap.put(key, value);
            }
        }
        return resultMap;
    }

    @Override
    @SneakyThrows
    public PayTranStatusEnum checkOrderStatus(String orderNumber, String alipayPayAppId) {
        Assert.isTrue(orderNumber.length() == 26||orderNumber.length() == 22 || orderNumber.length() == 7, "订单号长度错误");
        final String outTradeNoFromRedis = getOutTradeNoFromRedis(orderNumber);
        if (StringUtils.isBlank(outTradeNoFromRedis)) {
            log.error("订单号:{}未找到对应外部订单号", orderNumber);
            return PayTranStatusEnum.PAYERROR;
        }
        try {
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            AlipayTradeQueryModel model = new AlipayTradeQueryModel();
            // 设置支付宝交易号
            model.setOutTradeNo(outTradeNoFromRedis);
            // 设置查询选项
            List<String> queryOptions = new ArrayList<>();
            queryOptions.add("trade_settle_info");
            model.setQueryOptions(queryOptions);

            request.setBizModel(model);
            AlipayTradeQueryResponse response = PayClientFactory.getAlipayProperties(alipayPayAppId).getAlipayClient().execute(request);
            if (response.isSuccess()) {
                PayTranStatusEnum payTranStatusEnum = PayTranStatusEnum.getEnumByAliPayStatus(response.getTradeStatus());
                if (!payTranStatusEnum.equals(PayTranStatusEnum.SUCCESS)) {
                    return payTranStatusEnum;
                }
                // 当前是否正在更新订单 保证幂等性
                if (isOrderUpdating(orderNumber)) {
                    return PayTranStatusEnum.NOTPAY;
                }

                if (!redisService.getLock(CacheConstants.THIRD_PAY_KEY + response.getOutTradeNo(), 60L)) {
                    return PayTranStatusEnum.NOTPAY;
                }
                List<String> orderNums;
                if (orderNumber.startsWith(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF)) {
                    orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(null, orderNumber);
                } else if (orderNumber.startsWith(OrderConstant.PREPAY_NUM)){
                    List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(Arrays.asList(orderNumber)).build());
                    if (CollUtil.isEmpty(businessBalancePrepayVOS)){
                        return PayTranStatusEnum.NOTPAY;
                    }
                    if (businessBalancePrepayVOS.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime()))) {
                        String body = response.getBody();
                        log.debug("支付宝查单结果：{}", body);
                        if (StrUtil.isNotBlank(body)) {
                            JSONObject jsonObject = JSONObject.parseObject(body);
                            JSONObject alipayTradeQueryResponse = jsonObject.getJSONObject("alipay_trade_query_response");
                            if (CollUtil.isNotEmpty(alipayTradeQueryResponse)) {
                                AlipayPayLog alipayPayLog = BeanUtil.copyProperties(alipayTradeQueryResponse, AlipayPayLog.class);
                                alipayPayLog.setRemark(alipayTradeQueryResponse.toJSONString());
                                alipayPayLog.setOrder_num(orderNumber);
                                alipayPayLog.setSign(jsonObject.getString("sign"));
                                orderProcess(alipayPayLog);
                            }
                        }
                    }
                    return PayTranStatusEnum.NOTPAY;
                } else {
                    orderNums = List.of(orderNumber);
                }
                if (CollUtil.isEmpty(orderNums)) {
                    return PayTranStatusEnum.NOTPAY;
                }
                final List<Order> orders = orderService.selectListByOrderNums(orderNums);
                Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
                if (orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime()))) {
                    //  支付掉单，通知失效，记录缓存表，手动更新支付状态
                    String body = response.getBody();
                    log.debug("支付宝查单结果：{}", body);
                    if (StrUtil.isNotBlank(body)) {
                        JSONObject jsonObject = JSONObject.parseObject(body);
                        JSONObject alipayTradeQueryResponse = jsonObject.getJSONObject("alipay_trade_query_response");
                        if (CollUtil.isNotEmpty(alipayTradeQueryResponse)) {
                            AlipayPayLog alipayPayLog = BeanUtil.copyProperties(alipayTradeQueryResponse, AlipayPayLog.class);
                            alipayPayLog.setRemark(alipayTradeQueryResponse.toJSONString());
                            alipayPayLog.setOrder_num(orderNumber);
                            alipayPayLog.setSign(jsonObject.getString("sign"));
                            orderProcess(alipayPayLog);
                        }
                    }
                }

                return payTranStatusEnum;
            } else {
                if (CharSequenceUtil.isNotBlank(response.getBody())) {
                    log.debug("支付宝查单调用失败，接收到的响应体：{}", response.getBody());
                }
            }
        } catch (Exception e) {
            log.error("支付宝查单调用失败", e);
            return PayTranStatusEnum.USERPAYING;
        } finally {
            redisService.releaseLock(CacheConstants.THIRD_PAY_KEY + outTradeNoFromRedis);
        }
        return PayTranStatusEnum.NOTPAY;
    }

    /**
     * 从redis中获取订单与真实订单的关联关系（用于查单）
     *
     * @param orderNumber 订单号
     * @return 外部交易号
     */
    private String getOutTradeNoFromRedis(String orderNumber) {
        return redisService.getCacheObject(CacheConstants.ORDER_NUMBER_PREFIX + orderNumber);
    }

    private boolean isOrderUpdating(String orderNumber) {
        return redisService.hasKey(orderNumber);
    }

    /**
     * 还原订单号
     * <p>视频订单 商户码+1155+3 -> DDWN1155</p>
     * <p>会员订单 商户码+1155+2 -> HYWN1155</p>
     *
     * @param orderNumber
     * @return
     */
    private String resumeOrderNumber(String orderNumber) {
        if (orderNumber.startsWith(payConfig.getOrderPrefix())) {
            // 订单号: 商户号 +14位日期(yyyyMMddHHmmss)+8位随机数+(2位 / 3位随机字符)
            int outOrderNumber = orderNumber.substring(payConfig.getOrderPrefix().length()).length();
            // 视频订单  14位日期(yyyyMMddHHmmss)+8位随机数+3位随机字符 = 14+8=25
            // 会员订单  14位日期(yyyyMMddHHmmss)+8位随机数+2位随机字符 = 14+8=24
            if (outOrderNumber == 25) {
                // 视频订单
                return OrderConstant.ORDER_NUM_PREFIX_DD + OrderConstant.ORDER_NUM_PREFIX_WN + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 3);
            }
            if (outOrderNumber == 24) {
                // 会员订单
                return OrderConstant.ORDER_NUM_PREFIX_HY + OrderConstant.ORDER_NUM_PREFIX_WN + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }
            if (outOrderNumber == 20) {
                // 支付单
                return OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }

            if (outOrderNumber == 7) {
                // 预付单
                return OrderConstant.PREPAY_NUM + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }
        }
        log.warn("回调订单号类型错误,外部orderNumber为:{}", orderNumber);
        throw new ServiceException("回调订单号类型错误");
    }

    private void orderProcess(AlipayPayLog alipayPayLog) {
        log.info("支付回调记录:{}", alipayPayLog);
        alipayPayLog.setOrder_num(resumeOrderNumber(alipayPayLog.getOut_trade_no()));

        //  在上述验证通过后商家必须根据支付宝不同类型的业务通知，正确的进行不同的业务处理，并且过滤重复的通知结果数据。
        if (alipayPayLogService.checkExistByNotifyId(alipayPayLog.getNotify_id())) {
            //  已处理过该条支付宝回调通知 此处直接结束
            log.warn("===================该条通知已处理过，此处直接结束，notify_id：{}", alipayPayLog.getNotify_id());
            return;
        }
        if (alipayPayLogService.checkExistByOutTradeNo(alipayPayLog.getOut_trade_no())) {
            //  已处理过该条商户订单号 此处直接结束
            log.warn("===================该条商户订单号已处理过，此处直接结束，out_trade_no：{}", alipayPayLog.getOut_trade_no());
            return;
        }
        // 业务处理
        // 1.保存进表
        // 2.更新订单状态
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            alipayPayLogService.save(alipayPayLog);
            alipayOrderTableService.banAlipayOrderTableByOrderNum(alipayPayLog.getOrder_num());
            orderService.updateOrderPayStatus(alipayPayLog.getOrder_num(), alipayPayLog.getTotal_amount(), PayTypeEnum.ALIPAY.getCode(), alipayPayLog.getTrade_no(), alipayPayLog.getApp_id());
        });
        try {
            SpringUtils.getBean(AsyncTaskService.class).closeAllOrder(alipayPayLog.getOrder_num());
        } catch (BeansException e) {
            log.error("===================修改支付宝订单数据失败", e);
        }
    }

    private AlipayProperties sdkVerifySign(Map<String, String> callbackData) {
        Collection<AlipayProperties> allAlipayProperties = PayClientFactory.getAllAlipayProperties();
        for (AlipayProperties properties : allAlipayProperties) {
            // 注意： 支付宝验签时会修改原数据内容，不可直接传递原数据
            try {
                if (AlipaySignature.rsaCheckV1(new HashMap<>(callbackData), properties.getAlipayPublicKey(), properties.getAlipayConfig().getCharset(), properties.getAlipayConfig().getSignType())) {
                    return properties;
                }
            } catch (AlipayApiException e) {
                log.debug("支付宝回调验签失败{}", e.getMessage());
            }
        }
        log.warn("全部支付宝回调验签失败");
        return null;
    }
}
