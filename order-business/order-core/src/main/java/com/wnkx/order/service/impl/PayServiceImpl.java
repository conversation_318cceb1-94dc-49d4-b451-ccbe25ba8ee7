package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.CommonConstant;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.OrderPayProperties;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalancePrepayListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoExportDTO;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoOrderExportDTO;
import com.ruoyi.system.api.domain.dto.order.pay.PayInfoVideoExportDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderDataScopeService;
import com.wnkx.order.utlis.PayInfoExportUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付相关业务
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayServiceImpl implements PayService {

    private final RedisService redisService;
    private final ExchangeRateService exchangeRateService;
    private final IOrderService orderService;
    private final IOrderVideoService orderVideoService;
    private final IOrderMemberService orderMemberService;
    private final RemoteService remoteService;

    private final OrderPayProperties orderPayProperties;
    private final OrderDataScopeService orderDataScopeService;
    private final WeChatService weChatService;

    private final AlipayService alipayService;
    private final OrderAnotherPayService orderAnotherPayService;
    private final OrderMergeService orderMergeService;
    private final OrderPromotionDetailService orderPromotionDetailService;
    private final PromotionActivityService promotionActivityService;
    private final OrderBasePriceConfigService orderBasePriceConfigService;

    //  最低需支付金额
    private final static BigDecimal theMinimumAmountToBePaid = new BigDecimal("0.01");


    /**
     * 下载请款清单
     */
    @Override
    public void downloadPayInfo(Long mergeId, String orderNum, boolean isAnotherPay, HttpServletResponse response) throws IOException {
        List<String> orderNums;
        if (ObjectUtil.isNotNull(mergeId)) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(mergeId, null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        } else {
            orderNums = List.of(orderNum);
        }
        List<Order> orders = orderService.selectListByOrderNums(orderNums);
        Assert.notEmpty(orders, "订单不存在~");
        if (!isAnotherPay) {
            if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE)) {
                Assert.isTrue(orders.stream().allMatch(order -> order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())), "订单不存在~");
                Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime())), "订单已支付~");
            }
        }
        Integer orderType = orders.get(0).getOrderType();
        Assert.isTrue(OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType), "订单类型错误~");

        List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orderNums);
        Assert.notEmpty(orderVideos, "订单下没有视频~");

        Map<String, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));

        List<Order> sortedOrders = orders.stream()
                .sorted(Comparator.comparing(Order::getOrderTimeSign, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());

        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        List<PayInfoOrderExportDTO> payInfoOrderExportDTOS = sortedOrders.stream().map(order -> {
            PayInfoOrderExportDTO payInfoOrderExportDTO = new PayInfoOrderExportDTO();

            List<OrderVideo> videos = orderVideoMap.get(order.getOrderNum());
            List<PayInfoVideoExportDTO> payInfoVideoExportDTOS = videos.stream().map(video -> {
                PayInfoVideoExportDTO payInfoVideoExportDTO = new PayInfoVideoExportDTO();
                payInfoVideoExportDTO.setProductChinese(video.getProductChinese());
                payInfoVideoExportDTO.setOrderUserName(CharSequenceUtil.isNotBlank(order.getOrderUserName()) ? order.getOrderUserName() : order.getOrderUserNickName());
                payInfoVideoExportDTO.setVideoPrice(video.getVideoPrice().toString());
                payInfoVideoExportDTO.setPicPrice(video.getPicPrice().toString());
                payInfoVideoExportDTO.setCommissionPaysTaxes(video.getCommissionPaysTaxes().toString());
                payInfoVideoExportDTO.setExchangePrice(video.getExchangePrice().toString());
                payInfoVideoExportDTO.setServicePrice(video.getServicePrice().toString());
                payInfoVideoExportDTO.setCurrentExchangeRate(order.getCurrentExchangeRate().toString());
                payInfoVideoExportDTO.setSubtotalUSD(video.getAmountDollar().toString());
                payInfoVideoExportDTO.setSubtotalCNY(video.getAmount().toString());
                return payInfoVideoExportDTO;
            }).collect(Collectors.toList());

            payInfoOrderExportDTO.setOrderNum(order.getOrderNum());
            payInfoOrderExportDTO.setOrderTime(DateUtil.format(order.getOrderTime(), DatePattern.NORM_DATETIME_PATTERN));
            payInfoOrderExportDTO.setTotalUSD(videos.stream().map(OrderVideo::getAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
            payInfoOrderExportDTO.setTotalCNY(videos.stream().map(OrderVideo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
            payInfoOrderExportDTO.setPayAmount(order.getPayAmount().toString());
            payInfoOrderExportDTO.setOrderPromotionAmount(orderDiscountDetailVOMap.getOrDefault(order.getOrderNum(), new ArrayList<>()).stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add).toString());
            payInfoOrderExportDTO.setPayInfoVideoExportDTOS(payInfoVideoExportDTOS);
            return payInfoOrderExportDTO;
        }).collect(Collectors.toList());


        BigDecimal ordersPayAmount = orders.stream().map(Order::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderAmountTotal = orders.stream().map(Order::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal ordersUseBalance = orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal promotionAmountSum = orderDiscountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        PayInfoExportDTO payInfoExportDTO = new PayInfoExportDTO();
        payInfoExportDTO.setMerchantCode(orders.get(0).getMerchantCode());
        payInfoExportDTO.setMergePayAmount(orderAmountTotal.toString());
        payInfoExportDTO.setMergeUseBalance(ordersUseBalance.toString());
        payInfoExportDTO.setMergePromotionAmountSum(promotionAmountSum.toString());
        payInfoExportDTO.setMergeNeedPayAmount(ordersPayAmount.subtract(ordersUseBalance).toString());
        payInfoExportDTO.setPayInfoOrderExportDTOS(payInfoOrderExportDTOS);

        PayInfoExportUtil.exportPayInfo(payInfoExportDTO, response);
    }
//
//    /**
//     * 校验种草码
//     */
//    @Override
//    public Boolean checkSeedCode(String seedCode, String orderNum) {
//        Order order = orderService.getOrderByOrderNum(orderNum);
//        Assert.isFalse(order.getIsDefaultExchangeRate(), "订单百度汇率异常，请联系客服处理！");
//        Assert.isTrue(ObjectUtil.isNotNull(order) && order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), "查询不到订单，请检查后重试~");
//        Assert.isNull(order.getPayTime(), "订单已支付~");
//        Assert.isTrue(ObjectUtil.isNull(order.getSubmitCredentialTime()), "订单已提交审核~");
//        //  检查是否是首次购买
//        if (order.getMerchantId().compareTo(0L) != 0) {
//            //登已初始化数据无法使用渠道码
//            log.debug("已初始化数据无法使用渠道码");
//            return false;
//        }
//
//        Boolean isFirstBuy = orderMemberService.checkFirstBuy(SecurityUtils.getBizUserId(), orderNum);
//        if (Boolean.FALSE.equals(isFirstBuy)) {
//            log.debug("登录用户bizUserId:{}，非首次购买会员，种草码:{}", SecurityUtils.getBizUserId(), seedCode);
//            return false;
//        }
//
//        //  校验种草码是否存在
//        FissionBrokeRageVO fissionBrokeRageVO = remoteService.getDistributionChannelBySeedCodeV1(seedCode);
//        if (ObjectUtil.isNull(fissionBrokeRageVO)) {
//            log.debug("该种草码查询不到分销渠道，种草码:{}", seedCode);
//            return false;
//        }
//        return true;
//    }

    @Override
    public CheckSeedCodeVO checkSeedCodeV1(String seedCode, String orderNum) {
        CheckSeedCodeVO result = new CheckSeedCodeVO();
        result.setResult(false);

        Order order = orderService.getOrderByOrderNum(orderNum);
        Assert.isFalse(order.getIsDefaultExchangeRate(), "订单百度汇率异常，请联系客服处理！");
        Assert.isTrue(ObjectUtil.isNotNull(order) && order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), "查询不到订单，请检查后重试~");
        Assert.isNull(order.getPayTime(), "订单已支付~");
        Assert.isTrue(ObjectUtil.isNull(order.getSubmitCredentialTime()), "订单已提交审核~");
        //  检查是否是首次购买
        if (order.getMerchantId().compareTo(0L) != 0) {
            //登已初始化数据无法使用渠道码
            log.debug("已初始化数据无法使用渠道码");
            result.setErrorMessage("该种草码已失效，无法使用；");
//            throw new ServiceException("已初始化数据无法使用渠道码");
            return result;
        }
        Boolean isFirstBuy = orderMemberService.checkFirstBuy(SecurityUtils.getBizUserId(), orderNum);
        if (Boolean.FALSE.equals(isFirstBuy)) {
            log.debug("登录用户bizUserId:{}，非首次购买会员，种草码:{}", SecurityUtils.getBizUserId(), seedCode);
            result.setErrorMessage("该种草码已失效，无法使用；");
//            throw new ServiceException("登录用户bizUserId:" + SecurityUtils.getBizUserId() + "，非首次购买会员，种草码:" + seedCode);
            return result;
        }

        //  校验种草码是否存在
        ChannelBrokeRageVO channelBrokeRageVO = remoteService.getDistributionChannelBySeedCodeV1(seedCode);
        if (ObjectUtil.isNull(channelBrokeRageVO)) {
            log.debug("该种草码查询不到分销渠道，种草码:{}", seedCode);
            result.setErrorMessage("该种草码已失效，无法使用；");
//            throw new ServiceException("该种草码查询不到分销渠道，种草码:" + seedCode);
            return result;
        }
        if (ObjectUtil.isNull(channelBrokeRageVO.getDistributionChannel())){
            log.debug("该种草码查询不到分销渠道，种草码:{}", seedCode);
            result.setErrorMessage("该种草码已失效，无法使用；");
//            throw new ServiceException("该种草码查询不到分销渠道，种草码:" + seedCode);
            return result;
        }

        if (ObjectUtil.isNotNull(SecurityUtils.getBizUserId())
                && ObjectUtil.isNotNull(channelBrokeRageVO.getDistributionChannel().getBizUserId())
                && ObjectUtil.equals(SecurityUtils.getBizUserId(), channelBrokeRageVO.getDistributionChannel().getBizUserId())) {
            {
                log.debug("不能使用本人种草码，种草码:{}", seedCode);
                result.setErrorMessage("不能使用本人种草码");
//                throw new ServiceException("不能使用本人种草码");
                return result;
            }
        }
        result.setResult(true);
        return result;
    }

    /**
     * 货币单位转换（美元 -> 人民币）
     */
    @Override
    public Currency2RMBVO dollarToRmb(List<BigDecimal> dollars) {
        final RealTimeExchangeRateVO currentExchange = exchangeRateService.getCurrentExchange();
        BigDecimal reduce = dollars.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal rmb = reduce.multiply(currentExchange.getRealTimeExchangeRate()).setScale(2, RoundingMode.DOWN);

        return Currency2RMBVO.builder().currentExchange(currentExchange.getRealTimeExchangeRate()).RMB(rmb).build();
    }

    /**
     * 订单支付页信息
     */
    @Override
    public OrderPayInfoVO payInfo(PayInfoDTO payInfoDTO) {
        OrderPayInfoVO orderPayInfoVO = new OrderPayInfoVO();

        List<String> orderNums;
        if (ObjectUtil.isNotNull(payInfoDTO.getMergeId())) {
            orderPayInfoVO.setIsMerge(true);
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(payInfoDTO.getMergeId(), null);
            if (CollUtil.isEmpty(orderNums)) {
                return orderPayInfoVO;
            }
        } else {
            orderNums = List.of(payInfoDTO.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
            orderPayInfoVO.setIsMerge(false);
        }

        List<OrderVideo> orderVideos = orderVideoService.selectValidByOrderNums(orderNums);
        if (CollUtil.isEmpty(orderVideos)) {
            return orderPayInfoVO;
        }

        //  不是代付 校验数据权限
        if (!payInfoDTO.getIsAnother()) {
            orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList())).businessShare(true).build());
        }

        List<Order> orders = orderService.selectListByOrderNums(orderNums);
        if (CollUtil.isEmpty(orders)) {
            return orderPayInfoVO;
        }

        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime())), "订单已支付~");
        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getSubmitCredentialTime())), "订单已提交审核~");
        Assert.isTrue(orders.stream().allMatch(item -> OrderTypeEnum.VIDEO_ORDER.getCode().equals(item.getOrderType())), "订单非视频订单~");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");

        BigDecimal ordersUseBalance = orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (ordersUseBalance.compareTo(BigDecimal.ZERO) > 0) {
            payInfoDTO.setUseBalance(ordersUseBalance);
        }
        orderPayInfoVO.setOrderBalance(ordersUseBalance);
        orderPayInfoVO.setMergeId(payInfoDTO.getMergeId());

        if (ObjectUtil.isNull(payInfoDTO.getMergeId()) && CharSequenceUtil.isBlank(orders.get(0).getPayNum())) {
            orderService.setPayNum(orders.get(0).getOrderNum());
        }

        Map<String, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));
        Map<String, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getOrderNum, Function.identity()));

        BigDecimal orderAmountResultSum = BigDecimal.ZERO;
        BigDecimal discountsAmountSum = BigDecimal.ZERO;
        BigDecimal orderPromotionAmountSum = BigDecimal.ZERO;
        BigDecimal orderOriginAmountSum = BigDecimal.ZERO;

        //  大订单参与的优惠活动
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        for (Map.Entry<String, List<OrderVideo>> orderEntry : orderVideoMap.entrySet()) {
            List<OrderVideo> value = orderEntry.getValue();

            final BigDecimal serviceOriginPrice = orderBasePriceConfigService.getCurrentServicePrice().multiply(BigDecimal.valueOf(value.size()));

            //  视频订单每个费用美金总额
            final BigDecimal videoPrice = value.stream().map(OrderVideo::getVideoPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            final BigDecimal picPrice = value.stream().map(OrderVideo::getPicPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            final BigDecimal commissionPaysTaxes = value.stream().map(OrderVideo::getCommissionPaysTaxes).reduce(BigDecimal.ZERO, BigDecimal::add);
            final BigDecimal exchangePrice = value.stream().map(OrderVideo::getExchangePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            final BigDecimal servicePrice = value.stream().map(OrderVideo::getServicePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //  汇率
            Order order = orderMap.get(orderEntry.getKey());
            final BigDecimal currentExchangeRate = order.getCurrentExchangeRate();

            List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(orderEntry.getKey(), new ArrayList<>());
            final BigDecimal orderPromotionAmount = discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            OrderPayInfoDetailVO orderPayInfoDetailVO = BeanUtil.copyProperties(order, OrderPayInfoDetailVO.class);
            List<OrderVideoPayInfoVO> orderVideoPayInfoVOS = BeanUtil.copyToList(value, OrderVideoPayInfoVO.class);
            for (OrderVideoPayInfoVO orderVideoPayInfoVO : orderVideoPayInfoVOS) {
                orderVideoPayInfoVO.setCurrentExchangeRate(currentExchangeRate);
            }
            orderPayInfoDetailVO.setOrderPromotionAmount(orderPromotionAmount);
            orderPayInfoDetailVO.setAmount(value.stream().map(OrderVideo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderPayInfoDetailVO.setAmountDollar(value.stream().map(OrderVideo::getAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderPayInfoDetailVO.setOrderVideoPayInfos(orderVideoPayInfoVOS);

            orderPayInfoVO.getOrderPayInfoDetailVOS().add(orderPayInfoDetailVO);

            /*
             * 此处注释 v1.1.4 取消收取对公转账税点费用
             */
            // BigDecimal taxPoint = orderPayProperties.getTaxPoint();
            // if (StatusEnum.ENABLED.getCode().equals(isPublic)) {
            //     BigDecimal taxPointCost = needPayAmount.multiply(taxPoint.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
            //     orderPayInfoVO.setTaxPoint(taxPoint);
            //     orderPayInfoVO.setTaxPointCost(taxPointCost);
            //     orderPayInfoVO.setPayAmount(needPayAmount.add(taxPointCost));
            // } else {
            //     orderPayInfoVO.setTaxPoint(taxPoint);
            //     orderPayInfoVO.setTaxPointCost(BigDecimal.ZERO);
            //     orderPayInfoVO.setPayAmount(needPayAmount);
            // }
            BigDecimal orderAmountResult = videoPrice.add(picPrice).add(exchangePrice).add(servicePrice).add(commissionPaysTaxes);
            BigDecimal discountsAmount = serviceOriginPrice.subtract(servicePrice);
            BigDecimal orderOriginAmount = orderAmountResult.add(discountsAmount);

            orderAmountResultSum = orderAmountResultSum.add(orderAmountResult);
            discountsAmountSum = discountsAmountSum.add(discountsAmount);
            orderOriginAmountSum = orderOriginAmountSum.add(orderOriginAmount);
            orderPromotionAmountSum = orderPromotionAmountSum.add(orderPromotionAmount);
        }
        orderPayInfoVO.getOrderPayInfoDetailVOS().sort(Comparator.comparing(OrderPayInfoDetailVO::getOrderTimeSign).reversed());
        // 此处的支付方式 如果是单笔订单 应只返回order.getpaytype 即可 如果是合并单 应该是需要返回合并单的支付方式 因为合并的订单 不是每笔都会使用余额 所以有些支付方式包含余额 等paylock接口调整后再看看怎么处理
        // 结论 前端只选定支付方式 返回 1 或者 11  没有影响
        OptionalInt maxPayType = orders.stream()
                .filter(item -> ObjectUtil.isNotNull(item.getPayType()))
                .mapToInt(Order::getPayType)
                .max();
        orderPayInfoVO.setPayType(maxPayType.isPresent() ? maxPayType.getAsInt() : null);
        //  最终需支付金额
        BigDecimal needPayAmount = orders.stream().map(Order::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (payInfoDTO.getUseBalance() != null && payInfoDTO.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal subtract = needPayAmount.subtract(payInfoDTO.getUseBalance());
            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                needPayAmount = subtract;
            } else if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                needPayAmount = BigDecimal.ZERO;
            }
        }

        // todo  合并版本之后 似乎这些字段没用
        // orderPayInfoVO.setVideoPrice(videoPrice);
        // orderPayInfoVO.setPicPrice(picPrice);
        // orderPayInfoVO.setExchangePrice(exchangePrice);
        // orderPayInfoVO.setServicePrice(servicePrice);
        // orderPayInfoVO.setCurrentExchangeRate(currentExchangeRate);
        // orderPayInfoVO.setOrderNum(orderNum);

        orderPayInfoVO.setOrderOriginAmount(orderOriginAmountSum);
        orderPayInfoVO.setOrderPromotionAmountSum(orderPromotionAmountSum);
        orderPayInfoVO.setDiscountsAmount(discountsAmountSum);
        orderPayInfoVO.setOrderAmountResult(orderAmountResultSum);

        orderPayInfoVO.setPayAmount(needPayAmount);
        BigDecimal orginNeedPayAmount = orders.stream().map(Order::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal oldNeedPayAmount = orders.stream().map(Order::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal oldNeedPayAmountDollar = orders.stream().map(Order::getPayAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add);

        orderPayInfoVO.setAfterPromotionAmount(oldNeedPayAmount);
        orderPayInfoVO.setAfterPromotionAmountDollar(oldNeedPayAmountDollar);
        //needPayAmount payAmount(订单原价 - 优惠金额 - 余额支付金额)
        if (needPayAmount.compareTo(oldNeedPayAmount) == 0) {
            orderPayInfoVO.setPayAmountDollar(oldNeedPayAmountDollar);
        } else {
            BigDecimal surplusUseBalance = payInfoDTO.getUseBalance();
            BigDecimal payAmountDollar = BigDecimal.ZERO;
            for (Order order : orders) {
                if (surplusUseBalance.compareTo(order.getPayAmount()) >= 0) {
                    surplusUseBalance = surplusUseBalance.subtract(order.getPayAmount());
                } else if (surplusUseBalance.compareTo(BigDecimal.ZERO) > 0) {
                    payAmountDollar = payAmountDollar.add((order.getPayAmount().subtract(surplusUseBalance)).divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
                    surplusUseBalance = BigDecimal.ZERO;
                } else {
                    payAmountDollar = payAmountDollar.add(order.getPayAmountDollar());
                }
            }
            orderPayInfoVO.setPayAmountDollar(payAmountDollar);
        }

        orderPayInfoVO.setOldNeedPayAmount(orginNeedPayAmount);
        orderPayInfoVO.setOldNeedPayAmountDollar(orderAmountResultSum);

        // orderPayInfoVO.setDefaultExchangeRate(order.getIsDefaultExchangeRate());
        // orderPayInfoVO.setOrderVideoPayInfos(orderVideoPayInfoVOS);
        orderPayInfoVO.setPayeeId(orders.get(0).getPayeeId());

        Map<Integer, List<OrderDiscountDetailVO>> orderDiscountDetailVOGroupByType = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getType));
        List<OrderDiscountDetailVO> discountDetailVOS = new ArrayList<>();
        orderDiscountDetailVOGroupByType.forEach((key, value) -> {
            OrderDiscountDetailVO orderDiscountDetailVO = new OrderDiscountDetailVO();
            orderDiscountDetailVO.setType(key);
            orderDiscountDetailVO.setDiscountType(value.get(0).getDiscountType());
            orderDiscountDetailVO.setAmount(value.stream().map(OrderDiscountDetailVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderDiscountDetailVO.setCurrency(value.get(0).getCurrency());
            discountDetailVOS.add(orderDiscountDetailVO);
        });
        orderPayInfoVO.setOrderDiscountDetailVOS(discountDetailVOS);

        return orderPayInfoVO;
    }

    @Override
    public BigDecimal getTaxPoint() {
        return Optional.ofNullable(orderPayProperties.getTaxPoint()).orElse(BigDecimal.ZERO);
    }

    @Override
    public OrderPayInfoVO payMemberInfo(PayMemberInfoDTO dto, Boolean isAnother) {
        if (StringUtils.isNotBlank(dto.getSeedCode())) {
            dto.setSeedCode(dto.getSeedCode().toUpperCase());
        }
        OrderPayInfoVO orderPayInfoVO = new OrderPayInfoVO();
        Order order = orderService.getOrderByOrderNum(dto.getOrderNum());
        if (ObjectUtil.isNull(order) || OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType())) {
            return orderPayInfoVO;
        }
        Assert.isNull(order.getPayTime(), "订单已支付~");
        Assert.isTrue(ObjectUtil.isNull(order.getSubmitCredentialTime()), "订单已提交审核~");

        if (order.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
            dto.setUseBalance(order.getUseBalance());
            orderPayInfoVO.setOrderBalance(dto.getUseBalance());
        }
        final BigDecimal currentExchangeRate = order.getCurrentExchangeRate();

        BigDecimal needPayAmount = order.getPayAmount();

        List<OrderDiscountDetailVO> orderDiscountDetailVOS = new ArrayList<>();

        //  不是代付 做对应处理
        if (!isAnother) {
            Assert.isTrue(order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), "查询不到订单，请检查后重试~");

            OrderAnotherPay orderAnotherPay = orderAnotherPayService.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), null);
            if (ObjectUtil.isNull(orderAnotherPay) && order.getMerchantId().compareTo(0L) == 0) {
                //  种草码处理
                Boolean isFirstBuy = orderMemberService.checkFirstBuy(SecurityUtils.getBizUserId(), dto.getOrderNum());
                orderPayInfoVO.setCanInputSeedCode(isFirstBuy);
                if (Boolean.TRUE.equals(isFirstBuy) && StrUtil.isNotBlank(dto.getSeedCode())) {
                    DistributionChannel distributionChannelEntityBySeedCode = new DistributionChannel();
                    ChannelBrokeRageVO channelBrokeRageVO = new ChannelBrokeRageVO();
                    PromotionActivityVO promotionActivityVO = orderService.loadChannel(dto.getSeedCode(), distributionChannelEntityBySeedCode, channelBrokeRageVO);
                    BigDecimal seedCodeDiscount = getSeedCodeDiscountV1(channelBrokeRageVO.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscount(), needPayAmount);

                    orderPayInfoVO.setSeedCodeDiscount(seedCodeDiscount);
                    order.setMemberDiscountType(channelBrokeRageVO.getMemberDiscountType());
                    orderPayInfoVO.setAfterSeedCodeDiscount(needPayAmount.subtract(seedCodeDiscount));
                    if (seedCodeDiscount.compareTo(BigDecimal.ZERO) == 0){
                        orderPayInfoVO.setAfterSeedCodeDiscountDollar(order.getPayAmountDollar());
                    }else {
                        orderPayInfoVO.setAfterSeedCodeDiscountDollar((needPayAmount.subtract(seedCodeDiscount)).divide(currentExchangeRate, 2, RoundingMode.DOWN));
                    }
                    needPayAmount = needPayAmount.subtract(seedCodeDiscount);

                    if (ObjectUtil.isNotNull(promotionActivityVO)){
                        OrderDiscountDetailVO orderDiscountDetailVO = new OrderDiscountDetailVO();
                        orderDiscountDetailVO.setType(promotionActivityVO.getType());
                        orderDiscountDetailVO.setOrderNum(order.getOrderNum());
                        orderDiscountDetailVO.setDiscountAmount(seedCodeDiscount);
                        orderDiscountDetailVO.setDiscountAmountDollar(order.getPayAmountDollar().subtract(orderPayInfoVO.getAfterSeedCodeDiscountDollar()));
                        orderDiscountDetailVOS.add(orderDiscountDetailVO);
                    }
                }
            }
        }

        if (dto.getUseBalance() != null && dto.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal subtract = needPayAmount.subtract(dto.getUseBalance());
            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                needPayAmount = subtract;
            } else if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
                needPayAmount = BigDecimal.ZERO;
            }
        }

        if (StringUtils.isNotNull(order.getPayType()) && order.getPayType() > 10) {
            orderPayInfoVO.setPayType(order.getPayType() - 10);
        } else {
            orderPayInfoVO.setPayType(order.getPayType());
        }
        /*
         此处注释 v1.1.4 取消收取对公转账税点费用
         */
        // BigDecimal taxPoint = orderPayProperties.getTaxPoint();
        // if (StatusEnum.ENABLED.getCode().equals(dto.getIsPublic())) {
        //     BigDecimal taxPointCost = needPayAmount.multiply(taxPoint.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
        //     orderPayInfoVO.setTaxPoint(taxPoint);
        //     orderPayInfoVO.setTaxPointCost(taxPointCost);
        //     orderPayInfoVO.setPayAmount(needPayAmount.add(taxPointCost));
        // } else {
        //     orderPayInfoVO.setTaxPoint(taxPoint);
        //     orderPayInfoVO.setTaxPointCost(BigDecimal.ZERO);
        //     orderPayInfoVO.setPayAmount(needPayAmount);
        // }

        orderPayInfoVO.setCurrentExchangeRate(currentExchangeRate);
        orderPayInfoVO.setOrderNum(dto.getOrderNum());
        orderPayInfoVO.setOldNeedPayAmount(order.getPayAmount());
        orderPayInfoVO.setOldNeedPayAmountDollar(order.getPayAmountDollar());
        orderPayInfoVO.setDefaultExchangeRate(order.getIsDefaultExchangeRate());
        orderPayInfoVO.setOrderPromotionAmountSum(order.getOrderPromotionAmount());
        orderPayInfoVO.setPayAmount(needPayAmount);
        orderPayInfoVO.setPayAmountDollar(needPayAmount.compareTo(order.getPayAmount()) == 0 ? order.getPayAmountDollar() : needPayAmount.divide(currentExchangeRate, 2, RoundingMode.DOWN));
        orderPayInfoVO.setPayeeId(order.getPayeeId());
        orderPayInfoVO.setCreateTime(order.getCreateTime());

        orderDiscountDetailVOS.addAll(orderPromotionDetailService.getOrderDiscountDetailByOrderNum(dto.getOrderNum()));
        orderPayInfoVO.setOrderDiscountDetailVOS(orderDiscountDetailVOS);

        return orderPayInfoVO;
    }

    @Override
    public void weChatCallback() {
        weChatService.weChatPayCallBack();
    }

    @Override
    public List<OrderVideoDTO> calculateOrderAmount(List<OrderVideoDTO> orderVideoDTOList) {
        List<OrderVideoDTO> result = new ArrayList<>();

        final RealTimeExchangeRateVO currentExchange = exchangeRateService.getCurrentExchange();
        //  获取当前商家可参与的活动
        final List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS = promotionActivityService.getBusinessParticipatoryActivityVOS();
        for (OrderVideoDTO videoDTO : orderVideoDTOList) {
            //  视频价格
            videoDTO.setVideoPrice(CommonConstant.VIDEO_PRICE);
            final Integer picCount = videoDTO.getPicCount();
            // 注意：照片数量（1:2张/$10,2:5张/$20） 1代表2张 10刀  2代表5张20刀
            //  照片费
            if (picCount != null) {
                if (picCount == 1) {
                    videoDTO.setPicPrice(BigDecimal.valueOf(CommonConstant.VIDEO_TWO_PIC_PRICE));
                }
                if (picCount == 2) {
                    videoDTO.setPicPrice(BigDecimal.valueOf(CommonConstant.VIDEO_FIVE_PIC_PRICE));
                }
            } else {
                videoDTO.setPicPrice(BigDecimal.ZERO);
            }
            //  手续费
            videoDTO.setExchangePrice(
                    ((videoDTO.getVideoPrice().add(videoDTO.getPicPrice()))
                            .multiply(BigDecimal.valueOf(CommonConstant.PAYPAL_EXCHANGE_RATE))
                            .add(BigDecimal.valueOf(CommonConstant.PAYPAL_EXCHANGE_PRICE))).setScale(2, RoundingMode.DOWN)
            );
            //  服务费
            if (Boolean.FALSE.equals(isUserStatusProxy())) {
                videoDTO.setServicePrice(orderBasePriceConfigService.getCurrentServicePrice());
            } else {
                videoDTO.setServicePrice(orderBasePriceConfigService.getCurrentServicePriceProxy());
            }
            //  佣金代缴税费
            videoDTO.setCommissionPaysTaxes((videoDTO.getVideoPrice().add(videoDTO.getPicPrice())).multiply(BigDecimal.valueOf(CommonConstant.COMMISSION_PAYS_TAXES_RATE)).setScale(2, RoundingMode.DOWN));

            //  当前汇率
            videoDTO.setCurrentExchangeRate(currentExchange.getRealTimeExchangeRate());
            videoDTO.setIsDefaultExchangeRate(currentExchange.isDefault());

            //  计算总价
            result.addAll(updateTotalPrice(videoDTO, businessParticipatoryActivityVOS));
        }
        return result;
    }

    /**
     * 只有支付方式为：微信支付、支付宝支付的
     * 且订单状态为：未支付的订单才能得到二维码
     *
     * @param dto
     * @return
     */
    @Override
    public CreatePayVo generateQrcode(PayCodeDTO dto, Boolean isAnother) {
        Assert.notNull(dto.getPayType(), "支付类型不存在，无法获取二维码！");
        Assert.isTrue(PayTypeEnum.WECHAT.getCode().equals(dto.getPayType())
                        || PayTypeEnum.ALIPAY.getCode().equals(dto.getPayType())
                        || PayTypeEnum.WECHAT_BALANCE.getCode().equals(dto.getPayType())
                        || PayTypeEnum.ALIPAY_BALANCE.getCode().equals(dto.getPayType())
                , "支付方式只能是微信或者支付宝");

        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        }else if (dto.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)){
            List<String> preNums = List.of(dto.getOrderNum());
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(preNums).build());
            Assert.notEmpty(businessBalancePrepayVOS, "订单不存在，请检查后重试！");
            if (!isAnother) {
                Assert.isTrue(businessBalancePrepayVOS.stream().allMatch(order -> order.getBusinessId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())), "查询不到订单，请检查后重试~");
            }
            List<String> orderLocks = new ArrayList<>();
            generateQrcodeLock(orderLocks, preNums);
            try {
                if (CollUtil.isNotEmpty(businessBalancePrepayVOS.stream().filter(item -> !OrderStatusEnum.UN_PAY.getCode().equals(item.getStatus())).collect(Collectors.toList()))){
                    return CreatePayVo.builder().message(ObjectUtil.isNotNull(businessBalancePrepayVOS.get(0).getPayTime()) ? "订单已支付" : "交易已关闭").build();
                }

                OrderAnotherPay orderAnotherPay = orderAnotherPayService.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), dto.getMergeId());
                Long payUserId = ObjectUtil.isNull(orderAnotherPay) ? SecurityUtils.getUserId() : orderAnotherPay.getCreateById();
                BigDecimal payAmount = businessBalancePrepayVOS.stream().map(BusinessBalancePrepayVO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal finalPayAmount = payAmount;
                if (PayTypeEnum.WECHAT.getCode().equals(dto.getPayType())) {
                    return weChatService.generateWeChatQrcode(false, null, preNums, businessBalancePrepayVOS.get(0).getWechatPayAppId(), finalPayAmount, payUserId);
                }
                return alipayService.alipayWebPay(false, null, preNums, businessBalancePrepayVOS.get(0).getAlipayPayAppId(), finalPayAmount, payUserId);
            } finally {
                generateQrcodeReleaseLock(preNums);
            }
        }else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }

        final List<Order> orders = orderService.selectListByOrderNums(orderNums);
        Assert.notEmpty(orders, "订单不存在，请检查后重试！");
        Assert.isFalse(orders.stream().anyMatch(Order::getIsDefaultExchangeRate), "订单百度汇率异常，请联系客服处理！");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        if (!isAnother) {
            Assert.isTrue(orders.stream().allMatch(order -> order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())), "查询不到订单，请检查后重试~");
        }

        // 添加redis锁防止频繁获取 订单表数据重入
        List<String> orderLocks = new ArrayList<>();

        List<String> orderNumList = orders.stream().map(Order::getOrderNum).collect(Collectors.toList());
        generateQrcodeLock(orderLocks, orderNumList);
        try {
            if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orders.get(0).getOrderType())) {
                List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
                if (!isAnother) {
                    orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList())).businessShare(true).build());
                }
                List<OrderVideo> orderVideoCollect = orderVideos.stream().filter(item -> !OrderStatusEnum.UN_PAY.getCode().equals(item.getStatus())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(orderVideoCollect)) {
                    List<Order> orderCollect = orders.stream().filter(item -> item.getOrderNum().equals(orderVideoCollect.get(0).getOrderNum())).collect(Collectors.toList());
                    return CreatePayVo.builder().message(ObjectUtil.isNotNull(orderCollect.get(0).getPayTime()) ? "订单已支付" : "交易已关闭").build();
                }
            } else {
                List<OrderMember> orderMembers = orderMemberService.getByOrderNums(orders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
                List<OrderMember> orderMemberCollect = orderMembers.stream().filter(item -> !OrderMemberStatusEnum.UN_PAY.getCode().equals(item.getStatus())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(orderMemberCollect)) {
                    List<Order> orderCollect = orders.stream().filter(item -> item.getOrderNum().equals(orderMemberCollect.get(0).getOrderNum())).collect(Collectors.toList());
                    return CreatePayVo.builder().message(ObjectUtil.isNotNull(orderCollect.get(0).getPayTime()) ? "订单已支付" : "交易已关闭").build();
                }
            }

            OrderAnotherPay orderAnotherPay = orderAnotherPayService.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), dto.getMergeId());

            BigDecimal payAmount = orders.stream().map(Order::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal finalPayAmount = payAmount;
            BigDecimal useBalance = orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (!isAnother) {
                if (orders.get(0).getOrderType().equals(OrderTypeEnum.VIP_ORDER.getCode())) {
                    Order order = orders.get(0);
                    //  种草码处理
                    Boolean isFirstBuy = orderMemberService.checkFirstBuy(SecurityUtils.getBizUserId(), dto.getOrderNum());
                    if (order.getMerchantId().compareTo(0L) == 0 && Boolean.TRUE.equals(isFirstBuy) && StrUtil.isNotBlank(dto.getSeedCode())) {
                        DistributionChannel distributionChannelEntityBySeedCode = new DistributionChannel();
                        ChannelBrokeRageVO channelBrokeRageVO = new ChannelBrokeRageVO();
                        orderService.loadChannel(dto.getSeedCode(), distributionChannelEntityBySeedCode, channelBrokeRageVO);
                        BigDecimal seedCodeDiscount = getSeedCodeDiscountV1(channelBrokeRageVO.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscount(), payAmount);
                        finalPayAmount = finalPayAmount.subtract(seedCodeDiscount);
                        if (payAmount.compareTo(finalPayAmount) >= 0) {
                            order.setSeedCode(dto.getSeedCode());
                            order.setMemberDiscountType(channelBrokeRageVO.getMemberDiscountType());
                            order.setSettleRage(channelBrokeRageVO.getMemberDiscount());
                        }
                    } else {
                        order.setSeedCode(StrUtil.EMPTY);
                    }
                }


                if (useBalance.compareTo(BigDecimal.ZERO) > 0) {
                    finalPayAmount = finalPayAmount.subtract(useBalance);
                }
            }

            for (Order order : orders) {
                order.setPayUserId(ObjectUtil.isNull(orderAnotherPay) ? SecurityUtils.getUserId() : orderAnotherPay.getCreateById());
                order.setPayUserName(ObjectUtil.isNull(orderAnotherPay) ? SecurityUtils.getUsername() : orderAnotherPay.getCreateBy());
                order.setPayUserNickName(ObjectUtil.isNull(orderAnotherPay) ? SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getNickName() : orderAnotherPay.getCreateBy());
                if (ObjectUtil.isNull(orderAnotherPay)) {
                    order.setPayUserAccount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount());
                }
            }
            orderService.updateBatchById(orders);

            Assert.isTrue(orders.stream().allMatch(order -> order.getOrderNum().length() == 26), "订单号长度错误");
            if (PayTypeEnum.WECHAT.getCode().equals(dto.getPayType()) || PayTypeEnum.WECHAT_BALANCE.getCode().equals(dto.getPayType())) {
                if (ObjectUtil.isNotNull(dto.getMergeId())) {
                    OrderMerge orderMerge = orderMergeService.getOrderMergeNormalByIdOrPayNum(dto.getMergeId(), null);
                    return weChatService.generateWeChatQrcode(true, orderMerge.getPayNum(), orderNums, orders.get(0).getWechatPayAppId(), finalPayAmount, null);
                } else {
                    return weChatService.generateWeChatQrcode(false, null, orderNums, orders.get(0).getWechatPayAppId(), finalPayAmount, null);
                }
            }
            if (ObjectUtil.isNotNull(dto.getMergeId())) {
                OrderMerge orderMerge = orderMergeService.getOrderMergeNormalByIdOrPayNum(dto.getMergeId(), null);
                return alipayService.alipayWebPay(true, orderMerge.getPayNum(), orderNums, orders.get(0).getAlipayPayAppId(), finalPayAmount, null);
            } else {
                return alipayService.alipayWebPay(false, null, orderNums, orders.get(0).getAlipayPayAppId(), finalPayAmount, null);
            }

        } finally {
            generateQrcodeReleaseLock(orderNums);
        }
    }

    private void generateQrcodeReleaseLock(List<String> orderNums) {
        for (String orderNum : orderNums) {
            redisService.releaseLock(CacheConstants.FY_ORDER_TABLE_KEY + orderNum);
        }
    }

    private void generateQrcodeLock(List<String> orderLocks, List<String> orderNumList) {
        for (String orderNum : orderNumList) {
            try {
                Assert.isTrue(redisService.getLock(CacheConstants.FY_ORDER_TABLE_KEY + orderNum, 20L), "其他二维码正在操作中，请稍后重试！");
                orderLocks.add(orderNum);
            } catch (Exception e) {
                generateQrcodeReleaseLock(orderLocks);
            }
        }
    }

    @Override
    public OrderPayStatusDTO checkOrderStatus(CheckStatusDTO checkStatusDTO) {
        //  检查订单是否还需支付
        PayTranStatusEnum payTranStatusEnum = orderService.checkOrderNeedPay(checkStatusDTO);
        if (!PayTranStatusEnum.NOTPAY.equals(payTranStatusEnum)) {
            return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), checkStatusDTO.getOrderNum(), payTranStatusEnum.getCode());
        }

        return getOrderPayStatus(checkStatusDTO);
    }

    public OrderPayStatusDTO getOrderPayStatus(CheckStatusDTO checkStatusDTO) {
        if (ObjectUtil.isNotNull(checkStatusDTO.getPlatform())) {
            if (ObjectUtil.isNull(checkStatusDTO.getMergeId()) && checkStatusDTO.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)){
                List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(Arrays.asList(checkStatusDTO.getOrderNum())).build());
                if (CollUtil.isEmpty(businessBalancePrepayVOS)) {
                    return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), checkStatusDTO.getOrderNum(), PayTranStatusEnum.NOTPAY.getCode());
                }
//                if (PayPlatformTypeEnum.WECHAT.equals(PayPlatformTypeEnum.getByCode(checkStatusDTO.getPlatform()))) {
//                    return new OrderPayStatusDTO(null, checkStatusDTO.getOrderNum(), weChatService.checkOrderStatus(checkStatusDTO.getOrderNum(), businessBalancePrepayVOS.get(0).getWechatPayAppId()).getCode());
//                }else {
//                    return new OrderPayStatusDTO(null, checkStatusDTO.getOrderNum(), alipayService.checkOrderStatus(checkStatusDTO.getOrderNum(), businessBalancePrepayVOS.get(0).getAlipayPayAppId()).getCode());
//                }
            }
            List<Order> orders = orderService.getOrderPayAppIdInfo(checkStatusDTO.getOrderNums());
            if (CollUtil.isEmpty(orders)) {
                return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), checkStatusDTO.getOrderNum(), PayTranStatusEnum.NOTPAY.getCode());
            }
//            if (PayPlatformTypeEnum.WECHAT.equals(PayPlatformTypeEnum.getByCode(checkStatusDTO.getPlatform()))) {
//                if (ObjectUtil.isNotNull(checkStatusDTO.getMergeId())) {
//                    return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), null, weChatService.checkOrderStatus(checkStatusDTO.getOrderMerge().getPayNum(), orders.get(0).getWechatPayAppId()).getCode());
//                } else {
//                    return new OrderPayStatusDTO(null, checkStatusDTO.getOrderNum(), weChatService.checkOrderStatus(checkStatusDTO.getOrderNum(), orders.get(0).getWechatPayAppId()).getCode());
//                }
//            } else if (PayPlatformTypeEnum.ALIPAY.equals(PayPlatformTypeEnum.getByCode(checkStatusDTO.getPlatform()))) {
//                if (ObjectUtil.isNotNull(checkStatusDTO.getMergeId())) {
//                    return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), null, alipayService.checkOrderStatus(checkStatusDTO.getOrderMerge().getPayNum(), orders.get(0).getAlipayPayAppId()).getCode());
//                } else {
//                    return new OrderPayStatusDTO(null, checkStatusDTO.getOrderNum(), alipayService.checkOrderStatus(checkStatusDTO.getOrderNum(), orders.get(0).getAlipayPayAppId()).getCode());
//                }
//            }
        }
        return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), checkStatusDTO.getOrderNum(), PayTranStatusEnum.NOTPAY.getCode());
    }

    @Override
    public OrderPayStatusDTO checkAnotherPayStatus(CheckStatusDTO checkStatusDTO) {
        //  检查订单是否还需支付
        PayTranStatusEnum payTranStatusEnum = orderService.checkOrderAnotherPay(checkStatusDTO);
        if (!PayTranStatusEnum.NOTPAY.equals(payTranStatusEnum) && !PayTranStatusEnum.ANOTHER_PAY.equals(payTranStatusEnum)) {
            return new OrderPayStatusDTO(checkStatusDTO.getMergeId(), checkStatusDTO.getOrderNum(), payTranStatusEnum.getCode());
        }

        return getOrderPayStatus(checkStatusDTO);
    }

    private Boolean isUserStatusProxy() {
        return Objects.equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO()
                .getBusinessVO().getIsProxy(), StatusTypeEnum.YES.getCode());
    }

    /**
     * 更新总价
     * （（视频价格+照片价格+手续费+服务费）*当前汇率） 保留2位小数
     */
    @Override
    public List<OrderVideoDTO> updateTotalPrice(OrderVideoDTO videoDTO, List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS) {
        //  视频订单原价美金和人民币
        videoDTO.setAmountDollar(videoDTO.getVideoPrice().add(videoDTO.getPicPrice()).add(videoDTO.getCommissionPaysTaxes()).add(videoDTO.getExchangePrice()).add(videoDTO.getServicePrice()));
        videoDTO.setAmount(videoDTO.getAmountDollar().multiply(videoDTO.getCurrentExchangeRate()).setScale(2, RoundingMode.DOWN));

        return calculateActivityDiscounts(videoDTO, businessParticipatoryActivityVOS);
    }

    /**
     * 计算视频订单活动优惠
     */
    private List<OrderVideoDTO> calculateActivityDiscounts(OrderVideoDTO videoDTO, List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS) {
        List<OrderVideoDTO> orderVideoDTOS = new ArrayList<>();

        for (int i = 0; i < videoDTO.getShootCount(); i++) {
            OrderVideoDTO orderVideoDTO = BeanUtil.copyProperties(videoDTO, OrderVideoDTO.class);
            //  原需支付金额
            BigDecimal oldPayAmount = orderVideoDTO.getAmount();
            BigDecimal oldPayAmountDollar = orderVideoDTO.getAmountDollar();

            //  遍历商家可参与的活动
            Set<OrderPromotionDetail> orderPromotionDetails = new HashSet<>();
            for (BusinessParticipatoryActivityVO businessParticipatoryActivityVO : businessParticipatoryActivityVOS) {
                //  设置满5减100优惠金额
                if (PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode().equals(businessParticipatoryActivityVO.getType())) {
                    if (ObjectUtil.isNotNull(orderVideoDTO.getShootCount()) && orderVideoDTO.getShootCount() >= 5) {
                        orderVideoDTO.setVideoPromotionAmount(businessParticipatoryActivityVO.getAmount());
                        oldPayAmount = oldPayAmount.subtract(businessParticipatoryActivityVO.getAmount());
                        //  优惠后还需支付美金
                        BigDecimal afterPayAmountDollar = oldPayAmount.divide(orderVideoDTO.getCurrentExchangeRate(), 2, RoundingMode.DOWN);

                        OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                        orderPromotionDetail.setActivityId(businessParticipatoryActivityVO.getId());
                        orderPromotionDetail.setDiscountAmount(businessParticipatoryActivityVO.getAmount());
                        orderPromotionDetail.setDiscountAmountDollar(oldPayAmountDollar.subtract(afterPayAmountDollar));
                        orderPromotionDetail.setDiscountType(businessParticipatoryActivityVO.getDiscountType());
                        orderPromotionDetail.setAmount(businessParticipatoryActivityVO.getAmount());
                        orderPromotionDetail.setCurrency(businessParticipatoryActivityVO.getCurrency());
                        orderPromotionDetails.add(orderPromotionDetail);

                        oldPayAmountDollar = afterPayAmountDollar;
                    } else {
                        orderVideoDTO.setVideoPromotionAmount(BigDecimal.ZERO);
                    }
                }
                // 每月首单立减
                else if (PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode().equals(businessParticipatoryActivityVO.getType())) {
                    if (CurrencyEnum.USD.getUnit().equals(businessParticipatoryActivityVO.getCurrency())) {
                        //  优惠金额单位为美金
                        BigDecimal discountAmount = businessParticipatoryActivityVO.getAmount().multiply(orderVideoDTO.getCurrentExchangeRate()).setScale(2, RoundingMode.DOWN);
                        oldPayAmount = oldPayAmount.subtract(discountAmount);
                        //  优惠后还需支付美金
                        BigDecimal afterPayAmountDollar = oldPayAmount.divide(orderVideoDTO.getCurrentExchangeRate(), 2, RoundingMode.DOWN);

                        OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                        orderPromotionDetail.setActivityId(businessParticipatoryActivityVO.getId());
                        orderPromotionDetail.setDiscountAmount(discountAmount);
                        orderPromotionDetail.setDiscountAmountDollar(oldPayAmountDollar.subtract(afterPayAmountDollar));
                        orderPromotionDetail.setDiscountType(businessParticipatoryActivityVO.getDiscountType());
                        orderPromotionDetail.setAmount(businessParticipatoryActivityVO.getAmount());
                        orderPromotionDetail.setCurrency(businessParticipatoryActivityVO.getCurrency());
                        orderPromotionDetails.add(orderPromotionDetail);

                        oldPayAmountDollar = afterPayAmountDollar;
                    } else if (CurrencyEnum.CNY.getUnit().equals(businessParticipatoryActivityVO.getCurrency())) {
                        oldPayAmount = oldPayAmount.subtract(businessParticipatoryActivityVO.getAmount());
                        //  优惠后还需支付美金
                        BigDecimal afterPayAmountDollar = oldPayAmount.divide(orderVideoDTO.getCurrentExchangeRate(), 2, RoundingMode.DOWN);

                        OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                        orderPromotionDetail.setActivityId(businessParticipatoryActivityVO.getId());
                        orderPromotionDetail.setDiscountAmount(businessParticipatoryActivityVO.getAmount());
                        orderPromotionDetail.setDiscountAmountDollar(oldPayAmountDollar.subtract(afterPayAmountDollar));
                        orderPromotionDetail.setDiscountType(businessParticipatoryActivityVO.getDiscountType());
                        orderPromotionDetail.setAmount(businessParticipatoryActivityVO.getAmount());
                        orderPromotionDetail.setCurrency(businessParticipatoryActivityVO.getCurrency());
                        orderPromotionDetails.add(orderPromotionDetail);

                        oldPayAmountDollar = afterPayAmountDollar;
                    }
                }
            }
            businessParticipatoryActivityVOS.removeIf(item -> PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode().equals(item.getType()));

            if (BigDecimal.ZERO.compareTo(oldPayAmount) > 0) {
                oldPayAmount = theMinimumAmountToBePaid;
                oldPayAmountDollar = BigDecimal.ZERO;
            }
            orderVideoDTO.setPayAmount(oldPayAmount);
            orderVideoDTO.setPayAmountDollar(oldPayAmountDollar);
            orderVideoDTO.setOrderPromotionDetails(orderPromotionDetails);

            orderVideoDTOS.add(orderVideoDTO);
        }
        return orderVideoDTOS;
    }

//    /**
//     * 获取种草码优惠的金额
//     */
//    @Override
//    public BigDecimal getSeedCodeDiscount(String seedCode, BigDecimal orderAmount) {
//        if (CharSequenceUtil.isBlank(seedCode)) {
//            return BigDecimal.ZERO;
//        }
//        BigDecimal discount = remoteService.getDistributionChannelBySeedCode(seedCode);
//        if (ObjectUtil.isNotNull(discount) && BigDecimal.ZERO.compareTo(discount) < 0) {
//            return orderAmount.subtract(orderAmount.multiply(discount.divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN));
//        }
//        return BigDecimal.ZERO;
//    }
//
//    /**
//     * 获取种草码优惠的金额
//     */
//    @Override
//    public BigDecimal getSeedCodeDiscount(BigDecimal discount, BigDecimal orderAmount) {
//        if (ObjectUtil.isNotNull(discount) && BigDecimal.ZERO.compareTo(discount) < 0) {
//            return orderAmount.subtract(orderAmount.multiply(discount.divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN));
//        }
//        return BigDecimal.ZERO;
//    }

    @Override
    public BigDecimal getSeedCodeDiscountV1(Integer type, BigDecimal discount, BigDecimal payAmount) {
        if (ObjectUtil.isNull(type)){
            return BigDecimal.ZERO;
        }else if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(type)) {
            return ObjectUtil.isNotNull(discount) && BigDecimal.ZERO.compareTo(discount) < 0 ? discount : BigDecimal.ZERO;
        }
        if (ObjectUtil.isNotNull(discount) && BigDecimal.ZERO.compareTo(discount) < 0) {
            return payAmount.subtract(payAmount.multiply(discount.divide(BigDecimal.valueOf(100), 4, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN));
        }
        return BigDecimal.ZERO;
    }
}
