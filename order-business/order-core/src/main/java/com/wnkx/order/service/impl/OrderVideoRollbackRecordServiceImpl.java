package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.dto.order.RollbackOrderDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRollbackRecord;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRollbackRecordChange;
import com.wnkx.order.mapper.OrderVideoRollbackRecordMapper;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoRollbackRecordServiceImpl extends ServiceImpl<OrderVideoRollbackRecordMapper, OrderVideoRollbackRecord> implements OrderVideoRollbackRecordService {

    private final IOrderVideoService orderVideoService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoTaskService orderVideoTaskService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final IOrderVideoModelService orderVideoModelService;
    private final OrderVideoRollbackRecordChangeService orderVideoRollbackRecordChangeService;
    private final OrderVideoModelShippingAddressService orderVideoModelShippingAddressService;
    private final OrderVideoReminderRecordService orderVideoReminderRecordService;
    private final RedisService redisService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;

    private static final String[] ignoreProperties = new String[]{"id", "createBy", "createTime", "updateBy", "updateTime"};

    /**
     * 订单回退
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackOrder(RollbackOrderDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());
        Assert.notNull(orderVideo, "回退失败，视频订单不存在");
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.NEED_FILLED, OrderStatusEnum.UN_FINISHED, OrderStatusEnum.NEED_CONFIRM);

        //物流跟进订单回退
        orderVideoLogisticCore.logisticFollowRollback(orderVideo.getId());
        Long rollbackCount = baseMapper.getCountByVideoId(dto.getVideoId());

        //  插入回退记录
        OrderVideoRollbackRecord orderVideoRollbackRecord = new OrderVideoRollbackRecord();
        orderVideoRollbackRecord.setVideoId(dto.getVideoId());
        orderVideoRollbackRecord.setCount(Convert.toInt(rollbackCount) + 1);
        orderVideoRollbackRecord.setCause(dto.getCause());
        orderVideoRollbackRecord.setOperateBy(SecurityUtils.getUsername());
        orderVideoRollbackRecord.setOperateById(SecurityUtils.getUserId());
        orderVideoRollbackRecord.setOperateTime(DateUtil.date());
        baseMapper.insert(orderVideoRollbackRecord);

        //  新增回退记录变更数据
        OrderVideoRollbackRecordChange orderVideoRollbackRecordChange = BeanUtil.copyProperties(orderVideo, OrderVideoRollbackRecordChange.class, ignoreProperties);
        orderVideoRollbackRecordChange.setRollbackId(orderVideoRollbackRecord.getId());
        orderVideoRollbackRecordChangeService.insertOrderVideoRollbackRecordChange(orderVideoRollbackRecordChange);

        //  记录操作日志
        orderVideoOperateService.createOrderVideoOperate(
                OrderVideoOperateTypeEnum.ROLLBACK_ORDER.getEventName(),
                OrderVideoOperateTypeEnum.ROLLBACK_ORDER.getIsPublic(),
                OrderVideoOperateDTO.builder()
                        .videoId(dto.getVideoId())
                        .eventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.ROLLBACK_ORDER.getEventContent(), OrderStatusEnum.getLabel(orderVideo.getStatus())))
                        .build());
        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ROLLBACK_ORDER_REMARK.getEventName(),
                OrderVideoOperateTypeEnum.ROLLBACK_ORDER_REMARK.getIsPublic(),
                OrderVideoOperateDTO.builder()
                        .videoId(dto.getVideoId())
                        .eventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.ROLLBACK_ORDER_REMARK.getEventContent(), dto.getCause()))
                        .eventExecuteTime(DateUtil.offsetSecond(DateUtil.date(), 3))
                        .build()
        );

        // 视频订单回退淘汰预选模特
        orderVideoMatchService.rollbackOrderOustPreselectModel(dto.getVideoId(), dto.getCause());

        //  视频订单回退关闭任务单
        orderVideoTaskService.closeTaskByVideoIds(List.of(dto.getVideoId()), OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER);

        //  主携带订单 若底下有携带的订单 这些携带的订单调整为排单类型
        orderVideoMatchService.rollbackOrderUpdateCarryOrder(orderVideo.getId());

        //  订单回退设置除下单支付外节点完成时间为null
        orderVideoFlowNodeDiagramService.setNodeCompleteTimeNullExceptOrderPay(dto.getVideoId());

        // 删除模特关联订单
        orderVideoModelService.deleteOrderVideoModelByVideoId(dto.getVideoId());

        //  如果当前视频订单是需发货状态 更新他的模特收件地址为最新的
        if (OrderStatusEnum.NEED_FILLED.getCode().equals(orderVideo.getStatus()) && IsObjectEnum.OBJECT.getCode().equals(orderVideo.getIsObject())) {
            orderVideoModelShippingAddressService.updateOrderVideoModelShippingAddress(orderVideo.getId(), orderVideo.getRollbackId());
        }

        //  通过视频订单ID和回退ID关闭模特反馈素材详情
        orderVideoFeedBackMaterialService.closeMaterialInfoByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId(), EditCloseReasonEnum.ORDER_ROLLBACK);

        //  清除商家催一催Redis缓存Key
        redisService.deleteObject(orderVideoReminderRecordService.getOrderVideoReminderKey(orderVideo.getId()));

        //  设置视频订单回退ID
        orderVideo.setRollbackId(orderVideoRollbackRecord.getId());
        orderVideo.setIsRollback(true);
        //  视频订单回退清空字段值
        orderVideoUpdateRollback(orderVideo);
        orderVideoService.updateOrderVideoFieldNullToNull(orderVideo);


        //  订单流转到待匹配
        SpringUtils.getBean(IOrderService.class).createOrderFlow(orderVideo, OrderStatusEnum.UN_MATCH, OrderConstant.ROLLBACK_ORDER);
    }

    /**
     * 视频订单回退清空字段值
     */
    private void orderVideoUpdateRollback(OrderVideo orderVideo) {
        DateTime date = DateUtil.date();
        orderVideo.setShootModelId(null);
        orderVideo.setIssueId(null);
        orderVideo.setLogisticFlag(null);
        orderVideo.setUnConfirmTime(date);
        orderVideo.setUnFinishedTime(null);
        orderVideo.setNeedConfirmTime(null);
        orderVideo.setAutoCompleteTime(null);
        orderVideo.setLastModelSubmitTime(null);
        orderVideo.setReleaseFlag(0);
    }
}
