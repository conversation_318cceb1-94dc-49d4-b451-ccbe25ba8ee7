package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.OrderAuditFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.OrderAuditFlowVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【order_audit_flow(订单财务审核流水)】的数据库操作Service
* @createDate 2024-12-18 18:09:39
*/
public interface IOrderAuditFlowService extends IService<OrderAuditFlow> {

    /**
     * 根据订单号获取财务审核流水数据  completeOrderMergeByMergeIdOrPayNum
     * @param orderNum
     * @return
     */
    List<OrderAuditFlowVO> getListByOrderNumOrPayNum(String orderNum, String payNum);

}
