package com.wnkx.order.service;

import com.ruoyi.system.api.domain.vo.order.datastatistics.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/4 11:18
 */
public interface OrderVideoDataStatisticsService {

    /**
     * 视频订单数据-基础看板
     */
    OrderVideoBaseBoardVO getOrderVideoBaseBoard();

    /**
     * 视频订单数据-服务中订单数
     */
    OrderVideoServiceCountVO getOrderVideoServiceCount();

    /**
     * 视频订单数据-订单趋势
     */
    OrderVideoTrendVO getOrderVideoTrend(Date beginTime, Date endTime);

    /**
     * 视频订单数据-订单匹配时长数据分析
     */
    OrderVideoDurationDataVO getOrderVideoMatchDurationData(String date);

    /**
     * 视频订单数据-订单发货时长数据分析
     */
    OrderVideoDurationDataVO getOrderVideoDeliveryDurationData(String date);

    /**
     * 视频订单数据-订单素材反馈时长数据分析
     */
    OrderVideoDurationDataVO getOrderVideoFeedbackDurationData(String date);

    /**
     * 视频订单数据-平均审单/服务时长、任务单数及占比、拖单数及占比、烂单数及占比
     */
    OrderVideoAverageDurationDataVO getOrderVideoAverageDurationData();

    /**
     * 视频订单数据-售后类型分析
     */
    OrderVideoAfterSaleTypeAnalysisVO getOrderVideoAfterSaleTypeAnalysis(String date);

    /**
     * 视频订单数据-补偿订单情况
     */
    OrderVideoCompensationOrderSituationVO getOrderVideoCompensationOrderSituation(String date);
}
