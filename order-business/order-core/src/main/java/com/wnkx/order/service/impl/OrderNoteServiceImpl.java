package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.order.OrderNote;
import com.wnkx.order.mapper.OrderNoteMapper;
import com.wnkx.order.service.IOrderNoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单_商家开票信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@Service
@RequiredArgsConstructor
public class OrderNoteServiceImpl extends ServiceImpl<OrderNoteMapper, OrderNote> implements IOrderNoteService {
    private final OrderNoteMapper orderNoteMapper;

    /**
     * 新增订单开票信息
     */
    @Override
    public void saveOrderNote(OrderNote orderNote) {
        baseMapper.saveOrderNote(orderNote);
    }

    /**
     * 查询订单_商家开票信息
     *
     * @param id 订单_商家开票信息主键
     * @return 订单_商家开票信息
     */
    @Override
    public OrderNote selectOrderNoteById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询订单_商家开票信息列表
     *
     * @param orderNote 订单_商家开票信息
     * @return 订单_商家开票信息
     */
    @Override
    public List<OrderNote> selectOrderNoteList(OrderNote orderNote) {
        return orderNoteMapper.selectOrderNoteList(orderNote);
    }

    /**
     * 新增订单_商家开票信息
     *
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    @Override
    public int insertOrderNote(OrderNote orderNote) {
        orderNote.setCreateTime(DateUtils.getNowDate());
        return orderNoteMapper.insertOrderNote(orderNote);
    }

    /**
     * 修改订单_商家开票信息
     *
     * @param orderNote 订单_商家开票信息
     * @return 结果
     */
    @Override
    public int updateOrderNote(OrderNote orderNote) {
        orderNote.setUpdateTime(DateUtils.getNowDate());
        return orderNoteMapper.updateOrderNote(orderNote);
    }

    /**
     * 批量删除订单_商家开票信息
     *
     * @param ids 需要删除的订单_商家开票信息主键
     * @return 结果
     */
    @Override
    public int deleteOrderNoteByIds(Long[] ids) {
        return orderNoteMapper.deleteOrderNoteByIds(ids);
    }

    /**
     * 删除订单_商家开票信息信息
     *
     * @param id 订单_商家开票信息主键
     * @return 结果
     */
    @Override
    public int deleteOrderNoteById(Long id) {
        return orderNoteMapper.deleteOrderNoteById(id);
    }
}
