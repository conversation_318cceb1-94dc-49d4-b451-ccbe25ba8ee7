package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.ThirdPayLog;

/**
 * <AUTHOR>
 * @description 针对表【third_pay_log(支付回调记录表)】的数据库操作Service
 */
public interface ThirdPayLogService extends IService<ThirdPayLog> {

    /**
     * 根据富友订单号查询是否不存在数据
     * @param mchntOrderNo
     * @return 如果存在数据则返回false
     */
    boolean checkOrderNotExist(String mchntOrderNo);

    /**
     * 根据系统订单号查询是否不存在数据
     * @param orderNumber
     * @return 如果存在数据 则返回false
     */
    boolean checkOrderNumberNotExist(String orderNumber);

}
