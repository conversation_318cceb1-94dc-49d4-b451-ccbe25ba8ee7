package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.FlagShippingDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoModelShippingAddressDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelShippingAddress;
import com.ruoyi.system.api.domain.vo.order.OrderVideoModelShippingAddressVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/9 18:32
 */
@Validated
public interface OrderVideoModelShippingAddressService extends IService<OrderVideoModelShippingAddress> {

    /**
     * 添加模特收货信息
     */
    OrderVideoModelShippingAddress saveOrderVideoModelShippingAddress(OrderVideoModelShippingAddress orderVideoModelShippingAddress);

    /**
     * 批量添加模特收货信息
     */
    void saveBatchOrderVideoModelShippingAddress(@Valid List<OrderVideoModelShippingAddressDTO> dtoList);

    /**
     * 获取视频订单最新的发货信息
     */
    OrderVideoModelShippingAddressVO getLastOrderVideoModelShippingAddressByVideoId(Long videoId, Long rollbackId);

    /**
     * 获取视频订单最新的发货信息
     */
    List<OrderVideoModelShippingAddress> selectLastOrderVideoModelShippingAddressListByVideoId(List<Long> videoIds);

    /**
     * 通过视频订单ID获取发货信息列表
     */
    List<OrderVideoModelShippingAddressVO> selectListByVideoId(Long videoId);

    /**
     * 查询收件地址下有几个发货单号
     */
    Long getLogisticCountByVideoIdAndRollbackId(Long videoId, Long rollbackId);

    /**
     * 标记发货
     */
    void shippingFlag(FlagShippingDTO flagShippingDTO);

    /**
     * 更新模特收件地址
     */
    void updateOrderVideoModelShippingAddress(Long videoId, Long rollbackId);

    /**
     * 删除模特收件地址
     */
    void removeOrderVideoModelShippingAddressByVideoIdAndRollbackId(Long videoId, Long rollbackId);

    /**
     * 通过物流单号最新的视频订单模特收件地址
     */
    List<OrderVideoModelShippingAddress> selectModelShippingAddressByLogisticNumber(Collection<String> logisticNumbers);
}
