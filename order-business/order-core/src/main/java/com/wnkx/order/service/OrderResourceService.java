package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderResource;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/26 10:52
 */
public interface OrderResourceService extends IService<OrderResource> {

    /**
     * 新增数据
     */
    Long saveOrderResource(String objectKey);

    /**
     * 批量新增数据
     */
    List<Long> saveBatchOrderResource(Collection<String> objectKeys);

    /**
     * 通过id查询数据
     */
    List<OrderResource> selectListByIds(Collection<Long> ids);

    /**
     * 通过id查询数据(map)
     */
    Map<Long,OrderResource> getResourceMapByIds(Collection<Long> ids);
}
