package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.VideoContentTypeEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;
import com.wnkx.order.mapper.OrderVideoContentMapper;
import com.wnkx.order.service.IOrderVideoContentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 视频_关联内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
@RequiredArgsConstructor
public class OrderVideoContentServiceImpl extends ServiceImpl<OrderVideoContentMapper, OrderVideoContent> implements IOrderVideoContentService {


    /**
     * 根据视频id查询关联内容
     *
     * @param videoIds 视频订单id
     * @return 关联内容
     */
    @Override
    public List<OrderVideoContent> selectListByVideoIds(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectListByVideoIds(videoIds);
    }

    @Override
    public List<OrderVideoContent> selectListByVideoIdsAndTypesAsc(List<Long> videoIds, List<Integer> contentTypes) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectListByVideoIdsAndTypesAsc(videoIds, contentTypes);
    }

    @Override
    public Map<Integer, OrderVideoContent> selectMapByVideoIdsAndTypesAsc(List<Long> videoIds, List<Integer> contentTypes) {
        List<OrderVideoContent> orderVideoContentList = selectListByVideoIdsAndTypesAsc(videoIds, contentTypes);
        if (CollUtil.isEmpty(orderVideoContentList)){
            return Collections.emptyMap();
        }
        return orderVideoContentList.stream().collect(Collectors.toMap(OrderVideoContent::getType, a -> a, (k1, k2) -> k1));
    }

    @Override
    public void rollbackOrderVideoContent(Long videoId) {
        if (ObjectUtil.isNotNull(videoId)){
            baseMapper.rollbackOrderVideoContent(videoId);
        }
    }

    /**
     * 根据视频id或内容类型查询关联内容
     *
     * @param id 视频订单id
     * @return 关联内容
     */
    @Override
    public List<OrderVideoContent> selectListByVideoIdOrTypes(Long id, List<Integer> contentTypes) {
        return baseMapper.selectListByVideoIdOrType(id, contentTypes);
    }

    /**
     * 根据视频id查询关联内容
     *
     * @param id 视频订单id
     * @return 关联内容
     */
    @Override
    public List<OrderVideoContent> selectListByVideoIdOrType(Long id, Integer contentType) {
        return baseMapper.selectListByVideoIdOrType(id, CollUtil.toList(contentType));
    }

    /**
     * 新增视频_关联内容
     *
     * @param orderVideoContents 视频_关联内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchVideoContent(Collection<Long> videoIds, Collection<Integer> types, List<OrderVideoContent> orderVideoContents, Integer isFirstContent) {
        List<OrderVideoContent> orderVideoContentsFromTable = baseMapper.selectListByVideoIdsAndTypesAsc(videoIds, types);
        if (CollUtil.isNotEmpty(orderVideoContentsFromTable)) {
            baseMapper.removeByVideoId(videoIds, types);
        }

        if (CollUtil.isNotEmpty(orderVideoContents)) {
            //修改订单
            fillFirstContent(orderVideoContents, isFirstContent, orderVideoContentsFromTable);
            baseMapper.saveBatch(orderVideoContents);
        }else if (StatusTypeEnum.YES.getCode().equals(isFirstContent)){
            /**
             * 参数 模特要求（原匹配模特注意事项） 为空（审核订单代表首次订单 需要填充一个内容为null的数据）
             */
            List<OrderVideoContent> blankContentList = new ArrayList<>();
            for (Long videoId : videoIds){
                OrderVideoContent orderVideoContent = new OrderVideoContent();
                orderVideoContent.setVideoId(videoId);
                orderVideoContent.setType(VideoContentTypeEnum.CAUTIONS.getCode());
                orderVideoContent.setSort(0);
                orderVideoContent.setFirstContent("");
                orderVideoContent.setFirstEdit(StatusTypeEnum.NO.getCode());
                blankContentList.add(orderVideoContent);
            }
            baseMapper.saveBatch(orderVideoContents);
        }
    }

    private void fillFirstContent(List<OrderVideoContent> orderVideoContents, Integer isFirstContent, List<OrderVideoContent> orderVideoContentsFromTable) {
        /**
         * 首次订单（审核订单代表首次订单 只需要遍历设置数据就好）
         */
        if (StatusTypeEnum.YES.getCode().equals(isFirstContent)){
            for (OrderVideoContent item : orderVideoContents) {
                //获取原数据内容
                item.setFirstContent(item.getContent());
                item.setFirstEdit(StatusTypeEnum.NO.getCode());
            }
            return;
        }

        if (CollUtil.isEmpty(orderVideoContentsFromTable)){
            return;
        }


        Map<String, String> orderVideoContentMap = new HashMap<>(10);

        //记录 模特要求（原匹配模特注意事项） 为空的key数据
        List<String> cautionsKeysFromTable = new ArrayList<>();
        for (OrderVideoContent item : orderVideoContentsFromTable) {
            cautionsKeysFromTable.add(item.getVideoId() + "_" + item.getType());
            //获取首次数据内容
            String firstContent = orderVideoContentMap.get(item.getVideoId() + "_" + item.getType());
            if (StrUtil.isBlank(firstContent)) {
                firstContent = "";
            }
            if (StrUtil.isNotBlank(item.getFirstContent())) {
                firstContent += item.getFirstContent();
            }
            orderVideoContentMap.put(item.getVideoId() + "_" + item.getType(), firstContent);

        }

        //记录已处理 模特要求（原匹配模特注意事项）
        List<String> cautionsKeysFromDto = new ArrayList<>();
        //遍历请求数据 填充首次数据
        for (OrderVideoContent item : orderVideoContents) {
            //获取原数据内容
            String content = orderVideoContentMap.getOrDefault(item.getVideoId() + "_" + item.getType(), "");
            item.setFirstContent(content);
            //修改后数据内容
            String dtoContent = item.getContent();
            cautionsKeysFromDto.add(item.getVideoId() + "_" + item.getType());

            //修改后数据内容不为空 且与首次内容不相等 则设置为已修改
            if (StrUtil.isNotBlank(dtoContent) && !dtoContent.equals(content)) {
                item.setFirstEdit(StatusTypeEnum.YES.getCode());
            }else {
                item.setFirstEdit(StatusTypeEnum.NO.getCode());
            }
            //修改后数据内容为空 且首次内容不为空 则设置为已修改
            if (StrUtil.isBlank(dtoContent) && StrUtil.isNotBlank(content)) {
                item.setFirstEdit(StatusTypeEnum.YES.getCode());
            }
        }

        /**
         * 修改订单数据参数 模特要求（原匹配模特注意事项） 为空 填充 OrderVideoContent：FirstContent为原数据  Content为null
         */
        if (CollUtil.isNotEmpty(cautionsKeysFromTable)){
            if (CollUtil.isNotEmpty(cautionsKeysFromDto)){
                cautionsKeysFromTable.removeAll(cautionsKeysFromDto);
            }
            if (CollUtil.isEmpty(cautionsKeysFromTable)){
                return;
            }
            for (String key : cautionsKeysFromTable){
                String[] s = key.split("_");
                OrderVideoContent orderVideoContent = new OrderVideoContent();
                orderVideoContent.setVideoId(Convert.toLong(s[0]));
                orderVideoContent.setType(Convert.toInt(s[1]));
                orderVideoContent.setSort(0);
                orderVideoContent.setFirstContent(orderVideoContentMap.get(key));
                orderVideoContent.setFirstEdit(StatusTypeEnum.YES.getCode());
                orderVideoContents.add(orderVideoContent);
            }

        }

    }
}
