package com.wnkx.order.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.OrderPayProperties;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalancePrepayListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.OnlineRechargeSubmitCredentialDTO;
import com.ruoyi.system.api.domain.dto.order.pay.CreateAnotherPayLinkDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderAnotherPay;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.wnkx.order.config.AnotherPayProperties;
import com.wnkx.order.mapper.OrderAnotherPayMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/12/6 13:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderAnotherPayServiceImpl extends ServiceImpl<OrderAnotherPayMapper, OrderAnotherPay> implements OrderAnotherPayService {

    private final IOrderVideoService orderVideoService;
    private final IOrderMemberService orderMemberService;
    private final AnotherPayProperties anotherPayProperties;
    private final OrderMergeService orderMergeService;
    private final RemoteService remoteService;
    private final OrderPayProperties orderPayProperties;

    /**
     * 支付后关闭代付链接
     */
    @Override
    public void payCancel(String uuid) {
        baseMapper.payCancel(uuid);
    }

    /**
     * 通过code获取代付链接
     */
    @Override
    public OrderAnotherPay getOrderAnotherPayByUUID(String code) {
        return baseMapper.getOrderAnotherPayByUUID(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onlineSubmitCredential(OnlineRechargeSubmitCredentialDTO dto) {
        OrderAnotherPay validLinkByOrderNumOrMergeId = baseMapper.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), null);
        Assert.notNull(validLinkByOrderNumOrMergeId, "订单不存在");
        Assert.isTrue(StatusTypeEnum.NO.getCode().equals(validLinkByOrderNumOrMergeId.getIsPay()), "订单已支付");

        BusinessBalancePrepay innerOnlineDetail = getBusinessBalancePrepay(dto.getOrderNum());
        Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(innerOnlineDetail.getOrderType()), "订单类型错误~");

        Assert.isTrue(PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())
                        || PrepayPayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()),
                "支付方式只能是全币种或者对公");
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())) {
            Assert.isTrue(innerOnlineDetail.getPayAmountDollar().compareTo(orderPayProperties.getFullCurrencyUpperLimit()) >= 0, "应支付金额少于" + orderPayProperties.getFullCurrencyUpperLimit() + "USD，不支持使用全币种支付");
        }
        baseMapper.payCancel(validLinkByOrderNumOrMergeId.getUuid());
        SpringUtils.getBean(IOrderService.class).saveOrderPayeeAccount(OrderPayAccountDTO.builder()
                .orderNum(innerOnlineDetail.getPrepayNum())
                .payeeAccountConfigInfoId(innerOnlineDetail.getAccountId())
                .accountType(dto.getPayType()).build());

        dto.setIsAnother(StatusTypeEnum.YES.getCode());
        //提交凭证
        remoteService.innerSubmitCredential(dto);
    }

    @Override
    public BusinessBalancePrepay getOnlineDetailByOrderNum(String orderNum) {
        OrderAnotherPay validLinkByOrderNumOrMergeId = baseMapper.getValidLinkByOrderNumOrMergeId(orderNum, null);
        Assert.notNull(validLinkByOrderNumOrMergeId, "订单不存在");
        Assert.isTrue(StatusTypeEnum.NO.getCode().equals(validLinkByOrderNumOrMergeId.getIsPay()), "订单已支付");
        BusinessBalancePrepay innerOnlineDetail = getBusinessBalancePrepay(orderNum);
        return innerOnlineDetail;
    }

    @NotNull
    private BusinessBalancePrepay getBusinessBalancePrepay(String orderNum) {
        BusinessBalancePrepay innerOnlineDetail = remoteService.getInnerOnlineDetail(orderNum);
        Assert.isTrue(ObjectUtil.isNull(innerOnlineDetail.getPayTime()), "订单已支付~");
        Assert.isTrue(ObjectUtil.isNull(innerOnlineDetail.getSubmitCredentialTime()), "订单已提交审核~");
        Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(innerOnlineDetail.getOrderType()), "订单类型需要是线上钱包充值类型~");
        return innerOnlineDetail;
    }

    /**
     * 通过code获取有效代付链接
     */
    @Override
    public OrderAnotherPay getValidLinkByUUID(String code) {
        return baseMapper.getValidLinkByUUID(code);
    }

    /**
     * 通过 订单号或者合并单ID 获取代付链接
     */
    @Override
    public OrderAnotherPay getOrderAnotherPayByOrderNumOrMergeId(String orderNum, Long mergeId) {
        return baseMapper.getOrderAnotherPayByOrderNumOrMergeId(orderNum, mergeId);
    }

    /**
     * 取消代付
     */
    @Override
    public void cancel(String uuid) {
        baseMapper.cancel(uuid);
    }

    /**
     * 通过 订单号或合并单ID 获取有效代付链接
     */
    @Override
    public OrderAnotherPay getValidLinkByOrderNumOrMergeId(String orderNum, Long mergeId) {
        return baseMapper.getValidLinkByOrderNumOrMergeId(orderNum, mergeId);
    }

    /**
     * 创建或获取代付链接
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createLink(CreateAnotherPayLinkDTO dto) {
        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        }else if (dto.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)){
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(Arrays.asList(dto.getOrderNum())).build());
            Assert.notEmpty(businessBalancePrepayVOS, "订单不存在");
            BusinessBalancePrepayVO businessBalancePrepayVO = businessBalancePrepayVOS.get(0);
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getPayTime()), "订单已支付~");
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getSubmitCredentialTime()), "订单已提交审核~");
            Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(businessBalancePrepayVO.getOrderType()), "订单类型需要是线上钱包充值类型~");
            String uuid = insertOrderAnotherPay(dto, OrderTypeEnum.ONLINE_RECHARGE.getCode());
            remoteService.innerUpdateBatchFieldNullToNull(Arrays.asList(businessBalancePrepayVO.getPrepayNum()));
            return uuid;
        }else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }
        List<Order> orders = SpringUtils.getBean(IOrderService.class).selectListByOrderNums(orderNums);
        Assert.notEmpty(orders, "订单不存在");
        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime())), "订单已支付");
        Assert.isTrue(orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) == 0, "使用余额暂不支持代付~");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");

        Integer orderType = orders.get(0).getOrderType();
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orderNums);
            orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY);
        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(orderType)) {
            OrderMember orderMember = orderMemberService.getByOrderNum(dto.getOrderNum());
            Assert.isTrue(OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMember.getStatus()), "订单无需支付");
        } else {
            throw new ServiceException("创建代付链接失败，请联系管理员处理");
        }
        String uuid = insertOrderAnotherPay(dto, orderType);
        SpringUtils.getBean(IOrderService.class).releasePayLock(orders);

        return uuid;
    }

    public String insertOrderAnotherPay(CreateAnotherPayLinkDTO dto, Integer orderType) {
        OrderAnotherPay orderAnotherPay = baseMapper.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), dto.getMergeId());
        if (ObjectUtil.isNotNull(orderAnotherPay)) {
            return orderAnotherPay.getUuid();
        }

        String uuid = IdUtil.fastUUID();
        OrderAnotherPay saveOrderAnotherPay = new OrderAnotherPay();
        saveOrderAnotherPay.setUuid(uuid);
        saveOrderAnotherPay.setMergeId(dto.getMergeId());
        saveOrderAnotherPay.setOrderNum(dto.getOrderNum());
        saveOrderAnotherPay.setOrderType(ObjectUtil.isNotNull(dto.getMergeId()) ? OrderTypeEnum.MERGE_ORDER.getCode() : orderType);
        saveOrderAnotherPay.setCreateNickBy(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getNickName());
        saveOrderAnotherPay.setCreateBy(SecurityUtils.getUsername());
        saveOrderAnotherPay.setCreateById(SecurityUtils.getUserId());
        saveOrderAnotherPay.setBizUserId(SecurityUtils.getBizUserId());
        saveOrderAnotherPay.setSeedCode(dto.getSeedCode());
        baseMapper.insert(saveOrderAnotherPay);
        return uuid;
    }
}
