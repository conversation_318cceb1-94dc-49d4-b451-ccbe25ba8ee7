package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowCompletionModeEnum;
import com.ruoyi.common.core.enums.TaskDetailFlowRecordOperateByTypeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfoTaskDetail;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailSimpleVO;
import com.wnkx.order.mapper.OrderVideoFeedBackMaterialInfoTaskDetailMapper;
import com.wnkx.order.service.IOrderVideoTaskDetailService;
import com.wnkx.order.service.OrderVideoFeedBackMaterialInfoTaskDetailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-03-20 15:02:51
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFeedBackMaterialInfoTaskDetailServiceImpl extends ServiceImpl<OrderVideoFeedBackMaterialInfoTaskDetailMapper, OrderVideoFeedBackMaterialInfoTaskDetail> implements OrderVideoFeedBackMaterialInfoTaskDetailService {


    private final IOrderVideoTaskDetailService orderVideoTaskDetailService;

    /**
     * 通过反馈ID查询视频订单关联的模特反馈素材详情任务
     */
    @Override
    public List<OrderVideoTaskDetailSimpleVO> selectListByFeedBackIds(List<Long> feedBackIds) {
        return baseMapper.selectListByFeedBackIds(feedBackIds);
    }

    /**
     * 通过视频订单ID完结任务单
     */
    @Override
    public void finishTaskByVideoIds(List<Long> videoIds) {
        List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails = baseMapper.selectListByVideoIds(videoIds);
        if (CollUtil.isEmpty(materialInfoTaskDetails)) {
            return;
        }

        List<Long> taskDetailId = materialInfoTaskDetails.stream().map(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId).collect(Collectors.toList());
        List<OrderVideoTaskDetail> orderVideoTaskDetails = orderVideoTaskDetailService.listByIds(taskDetailId);
        if (CollUtil.isEmpty(orderVideoTaskDetails)) {
            return;
        }

        orderVideoTaskDetailService.finishWorkOrders(taskDetailId, OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION, TaskDetailFlowRecordOperateByTypeEnum.EDITOR, materialInfoTaskDetails);
    }

    /**
     * 通过视频订单ID和回退ID完结任务单
     */
    @Override
    public void finishTaskByVideoIdAndRollbackId(Long videoId, Long rollbackId) {
        List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails = baseMapper.selectListByMaterialInfoIdAndVideoIdAndRollbackId(null, videoId, rollbackId);
        if (CollUtil.isEmpty(materialInfoTaskDetails)) {
            return;
        }

        List<Long> taskDetailId = materialInfoTaskDetails.stream().map(OrderVideoFeedBackMaterialInfoTaskDetail::getTaskDetailId).collect(Collectors.toList());
        List<OrderVideoTaskDetail> orderVideoTaskDetails = orderVideoTaskDetailService.listByIds(taskDetailId);
        if (CollUtil.isEmpty(orderVideoTaskDetails)) {
            return;
        }

        orderVideoTaskDetailService.finishWorkOrders(taskDetailId, OrderTaskDetailFlowCompletionModeEnum.ACTIVE_COMPLETION, TaskDetailFlowRecordOperateByTypeEnum.EDITOR, materialInfoTaskDetails);
    }

    /**
     * 通过模特反馈素材ID和视频订单ID和回退ID查询视频订单关联的模特反馈素材详情任务
     */
    @Override
    public List<OrderVideoFeedBackMaterialInfoTaskDetail> selectListByMaterialInfoIdAndVideoIdAndRollbackId(Long materialInfoId, Long videoId, Long rollbackId) {
        return baseMapper.selectListByMaterialInfoIdAndVideoIdAndRollbackId(materialInfoId, videoId, rollbackId);
    }
}
