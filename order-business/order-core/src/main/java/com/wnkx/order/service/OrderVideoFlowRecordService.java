package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFlowDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFlow;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 16:57
 */
public interface OrderVideoFlowRecordService extends IService<OrderVideoFlow> {

    /**
     * @param eventName          事件名称
     * @param orderVideoFlowDTOS dto
     */
    void createOrderVideoFlow(String eventName, List<OrderVideoFlowDTO> orderVideoFlowDTOS);
}
