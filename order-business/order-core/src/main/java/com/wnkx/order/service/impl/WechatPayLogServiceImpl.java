package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.WechatPayLog;
import com.wnkx.order.service.IWechatPayLogService;
import com.wnkx.order.mapper.WechatPayLogMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【wechat_pay_log(微信回调记录表)】的数据库操作Service实现
 * @createDate 2024-10-25 09:28:00
 */
@Service
public class WechatPayLogServiceImpl extends ServiceImpl<WechatPayLogMapper, WechatPayLog>
        implements IWechatPayLogService {

    @Override
    public boolean checkOrderNotExist(String outTradeNo) {
        return baseMapper.checkOrderNotExist(outTradeNo);
    }

    @Override
    public boolean checkOrderNumberNotExist(String orderNumber) {
        return baseMapper.checkOrderNumberNotExist(orderNumber);
    }
}




