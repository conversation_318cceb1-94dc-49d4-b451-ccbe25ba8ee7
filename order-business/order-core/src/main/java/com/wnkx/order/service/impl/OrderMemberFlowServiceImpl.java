package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderMemberFlow;
import com.wnkx.order.mapper.OrderMemberFlowMapper;
import com.wnkx.order.service.IOrderMemberFlowService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【order_member_flow(订单_会员订单_流转记录表)】的数据库操作Service实现
* @createDate 2024-09-06 09:09:24
*/
@Service
public class OrderMemberFlowServiceImpl extends ServiceImpl<OrderMemberFlowMapper, OrderMemberFlow>
implements IOrderMemberFlowService {

}
