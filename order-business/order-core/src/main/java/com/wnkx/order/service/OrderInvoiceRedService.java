package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.ApplyForReopeningDTO;
import com.ruoyi.system.api.domain.dto.order.MarkRedInvoiceDTO;
import com.ruoyi.system.api.domain.dto.order.ToBeRedInvoiceListDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawalSuccessDTO;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceRed;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedDetailVO;
import com.ruoyi.system.api.domain.vo.order.OrderInvoiceRedOrderVideoVO;
import com.ruoyi.system.api.domain.vo.order.ToBeRedInvoiceListVO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/7 9:30
 */
public interface OrderInvoiceRedService extends IService<OrderInvoiceRed> {


    /**
     * 运营端-发票管理-待红冲列表
     */
    List<ToBeRedInvoiceListVO> selectToBeRedInvoiceListByCondition(ToBeRedInvoiceListDTO dto);

    /**
     * 运营端-发票管理-待红冲列表-导出
     */
    void exportToBeRedInvoiceList(ToBeRedInvoiceListDTO dto, HttpServletResponse response);

    /**
     * 运营端-发票管理-标记红冲
     */
    void markRedInvoice(MarkRedInvoiceDTO dto);

    /**
     * 运营端-发票管理-红冲详情
     */
    OrderInvoiceRedDetailVO getRedInvoiceDetail(Long invoiceRedId);

    /**
     * 通过发票ID获取发票红冲关联订单
     */
    List<OrderInvoiceRedOrderVideoVO> selectRedInvoiceVideoListByInvoiceId(Long invoiceId);

    /**
     * 商家端-发票管理-申请重开
     */
    void applyForReopening(ApplyForReopeningDTO dto);

    /**
     * 获取待红冲数量
     */
    Long getToBeRedInvoiceCount();

    /**
     * 获取待红冲金额
     */
    BigDecimal getToBeRedInvoiceAmount();

    /**
     * 提现申请通过后 对发票的处理
     */
    void withdrawalSuccess(Map<Long, List<WithdrawalSuccessDTO>> needRedPushInvoice);
}
