package com.wnkx.order.service;

import com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigChangelogVO;
import com.ruoyi.system.api.domain.vo.order.OrderBasePriceConfigVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_base_price_config(订单基础价格配置表)】的数据库操作Service
 * @createDate 2025-05-16 16:58:03
 */
public interface OrderBasePriceConfigService extends IService<OrderBasePriceConfig> {

    /**
     * 修改价格配置
     * @param orderBasePriceConfigVO 价格配置
     * @param type 类型
     */
    void changeConfigByType(OrderBasePriceConfigVO orderBasePriceConfigVO, Integer type);

    /**
     * 获取当前价格配置
     * @param type 类型
     * @return 当前价格配置
     */
    OrderBasePriceConfigVO getCurrentConfigByte(Integer type);

    /**
     * 获取价格配置修改记录
     * @param type 类型
     * @return 配置修改记录
     */
    List<OrderBasePriceConfigChangelogVO> getConfigChangeLogList(Integer type);

    /**
     * 获取当前代理服务费
     * @return 服务费
     */
    BigDecimal getCurrentServicePriceProxy();

    /**
     * 获取当前服务费
     * @return 服务费
     */
    BigDecimal getCurrentServicePrice();


    /**
     * 获取当前生效配置
     * @param type 类型
     * @return 当前价格配置
     */
    OrderBasePriceConfigVO getCurrentConfigByteActive(Integer type);
}
