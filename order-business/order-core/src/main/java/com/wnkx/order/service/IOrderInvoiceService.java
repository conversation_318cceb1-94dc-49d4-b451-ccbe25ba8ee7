package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOperate;
import com.ruoyi.system.api.domain.vo.order.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 订单_发票Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-17
 */
public interface IOrderInvoiceService extends IService<OrderInvoice> {

    /**
     * 根据订单号获取发票map
     * @param orderNums
     * @return
     */
    Map<String, OrderInvoice> getInvoiceMap(List<String> orderNums);

    /**
     * 根据 视频订单列表 获取视频订单发票数据
     * @param videoIds 视频订单列表
     * @return
     */
    Map<Long, OrderVideoInvoiceVO> getOrderVideoInvoiceVOMap(List<Long> videoIds);

    /**
     * 重置发票商家
     * @param orderNum
     * @param merchantId
     */
    void setMerchant(List<String> orderNum, Long merchantId);

    /**
     * 商家端-发票管理-未开票列表
     */
    List<CompanyNotInvoicedListVO> selectCompanyNotInvoicedListByCondition(CompanyNotInvoicedListDTO dto);

    /**
     * 商家端-发票管理-订单详情
     */
    CompanyNotInvoicedOrderDetailVO getCompanyNotInvoicedOrderDetail(Long orderId, Integer orderType);

    /**
     * 商家端-发票管理-申请开票
     */
    void applyForBilling(ApplyForBillingDTO dto);

    /**
     * 商家/运营端-发票管理-待开票列表
     */
    List<ToBeInvoicedListVO> selectToBeInvoicedListByCondition(ToBeInvoicedListDTO dto);

    /**
     * 运营端-发票管理-待开票列表-导出
     */
    void exportToBeInvoicedList(ToBeInvoicedListDTO dto, HttpServletResponse response);

    /**
     * 商家端-发票管理-发票详情
     */
    OrderInvoiceDetailVO getCompanyInvoiceDetail(Long invoiceId);

    /**
     * 运营端-发票管理-发票详情
     */
    OrderInvoiceDetailVO getBackInvoiceVideoList(Long invoiceId);

    /**
     * 运营端-发票管理-流转记录
     */
    List<OrderInvoiceOperate> getInvoiceOperateRecord(Long invoiceId);

    /**
     * 商家/运营端-发票管理-取消开票
     */
    void cancelInvoice(Long invoiceId);

    /**
     * 运营端-发票管理-审核发票
     */
    void auditInvoice(AuditInvoiceDTO dto);

    /**
     * 运营端-发票管理-修改发票
     */
    void updateInvoice(AuditInvoiceDTO dto);

    /**
     * 运营端-发票管理-上传发票
     */
    void uploadInvoice(UploadInvoiceDTO dto);

    /**
     * 运营端-发票管理-重新上传发票
     */
    void reUploadInvoice(UploadInvoiceDTO dto);

    /**
     * 运营端-发票管理-确认发票
     */
    void confirmInvoice(Long invoiceId);

    /**
     * 运营端-发票管理-待红冲列表
     */
    List<ToBeRedInvoiceListVO> selectToBeRedInvoiceListByCondition(ToBeRedInvoiceListDTO dto);

    /**
     * 运营端-发票管理-待红冲列表-导出
     */
    void exportToBeRedInvoiceList(ToBeRedInvoiceListDTO dto, HttpServletResponse response);

    /**
     * 运营端-发票管理-开票记录
     */
    List<OrderInvoiceRecordVO> getInvoiceRecord(Long invoiceId);

    /**
     * 运营端-发票管理-标记红冲
     */
    void markRedInvoice(MarkRedInvoiceDTO dto);

    /**
     * 获取发票数量
     */
    Long getInvoiceCount();

    /**
     * 运营端-发票管理-红冲详情
     */
    OrderInvoiceRedDetailVO getRedInvoiceDetail(Long invoiceRedId);

    /**
     * 商家/运营端-发票管理-已完成列表
     */
    List<InvoiceFinishListVO> selectInvoiceFinishListByCondition(InvoiceFinishListDTO dto);

    /**
     * 运营端-发票管理-已完成列表-导出
     */
    void exportInvoiceFinishList(InvoiceFinishListDTO dto, HttpServletResponse response);

    /**
     * 商家端-发票管理-去除发票新标记
     */
    void removeInvoiceNewFlag(Long invoiceId);

    /**
     * 商家端-发票管理-申请重开
     */
    void applyForReopening(ApplyForReopeningDTO dto);

    /**
     * 商家端-发票管理-数量统计
     */
    CompanyInvoiceStatisticsVO companyInvoiceStatistics();

    /**
     * 运营端-发票管理-数量统计
     */
    BackInvoiceStatisticsVO backInvoiceStatistics();

    /**
     * 运营端-发票管理-开票金额统计
     */
    InvoiceAmountStatisticsVO invoiceAmountStatistics();

    /**
     * 提现申请通过后 对发票的处理
     */
    void withdrawalSuccess(List<WithdrawalSuccessDTO> dtoList);

    /**
     * 通过订单号获取开票信息
     */
    OrderInvoice getLastInvoiceByOrderNum(String orderNum);
}
