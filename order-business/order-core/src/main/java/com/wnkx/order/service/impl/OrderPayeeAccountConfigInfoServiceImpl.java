package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo;
import com.wnkx.order.service.OrderPayeeAccountConfigInfoService;
import com.wnkx.order.mapper.OrderPayeeAccountConfigInfoMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【order_payee_account_config_info(收款人账号配置表)】的数据库操作Service实现
* @createDate 2024-12-18 10:48:48
*/
@Service
public class OrderPayeeAccountConfigInfoServiceImpl extends ServiceImpl<OrderPayeeAccountConfigInfoMapper, OrderPayeeAccountConfigInfo>
    implements OrderPayeeAccountConfigInfoService{

    @Override
    public OrderPayeeAccountConfigInfoDTO getByBankAccount(String appId) {
        return baseMapper.getByBankAccount(appId);
    }
}




