package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.PayConstant;
import com.ruoyi.common.core.enums.PayPlatformTypeEnum;
import com.ruoyi.common.core.enums.PayTranStatusEnum;
import com.ruoyi.common.core.enums.PayTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.exception.SystemException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.ServletUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalancePrepayListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.PrepayUpdateAppIdDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.dto.order.pay.WeChatPayDTO;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.WechatOrderTable;
import com.ruoyi.system.api.domain.entity.order.WechatPayLog;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import com.wnkx.order.config.PayConfig;
import com.wnkx.order.config.WeChatPayConfig;
import com.wnkx.order.factory.PayClientFactory;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-10-24 16:17
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class WeChatServiceImpl implements WeChatService {
    private final PayConfig payConfig;
    private final RedisService redisService;
    private final RemoteService remoteService;

    //todo 后续到同一方法中 本方法只处理微信支付接口

    private final IWechatOrderTableService wechatOrderTableService;
    private final IOrderService orderService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final IWechatPayLogService wechatPayLogService;
    private final TransactionTemplate transactionTemplate;
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;
    private final OrderMergeService orderMergeService;

    @Value("${pay.debugger.enable}")
    private String debuggerEnable;

    @Override
    public PrepayResponse prepay(WeChatPayDTO dto) {
        WeChatPayConfig weChatPayConfig = PayClientFactory.getWeChatPayConfig(dto.getAppId());
        log.debug("微信下单：{}", dto);
        PrepayRequest prepayRequest = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(dto.getOrderAmount().multiply(new BigDecimal(100)).toBigInteger().intValue());
        if (Boolean.TRUE.toString().equals(debuggerEnable)) {
            amount.setTotal(1);
        }
        prepayRequest.setAmount(amount);
        prepayRequest.setAppid(weChatPayConfig.appId);
        prepayRequest.setMchid(weChatPayConfig.merchantId);
        prepayRequest.setDescription("订单：" + dto.getOrderNum());
        prepayRequest.setNotifyUrl(weChatPayConfig.notifyUrl);
        prepayRequest.setOutTradeNo(dto.getOutTradeNo());
        return weChatPayConfig.getNativePayService().prepay(prepayRequest);
    }

    @Override
    public Transaction queryOrderByOutTradeNo(String outTradeNo, String wechatPayAppId) {
        log.debug("查询微信订单：{}", outTradeNo);
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        WeChatPayConfig weChatPayConfig = PayClientFactory.getWeChatPayConfig(wechatPayAppId);
        request.setMchid(weChatPayConfig.merchantId);
        request.setOutTradeNo(outTradeNo);
        return weChatPayConfig.getNativePayService().queryOrderByOutTradeNo(request);
    }

    @Override
    public void closeOrder(String outTradeNo, String appId) {
        try {
            log.debug("关闭微信订单：{}", outTradeNo);
            CloseOrderRequest request = new CloseOrderRequest();
            WeChatPayConfig weChatPayConfig = PayClientFactory.getWeChatPayConfig(appId);
            request.setMchid(weChatPayConfig.merchantId);
            request.setOutTradeNo(outTradeNo);
            weChatPayConfig.getNativePayService().closeOrder(request);
        }catch (Exception e){
            log.error("关闭微信订单失败：", e);
        }
    }

    @Override
    public Transaction callback() {
        String wechatTimestamp = ServletUtils.getRequest().getHeader("Wechatpay-Timestamp");
        String wechatpayNonce = ServletUtils.getRequest().getHeader("Wechatpay-Nonce");
        String wechatSignature = ServletUtils.getRequest().getHeader("Wechatpay-Signature");
        String wechatPaySerial = ServletUtils.getRequest().getHeader("Wechatpay-Serial");
        String signatureType = ServletUtils.getRequest().getHeader("Wechatpay-Signature-Type");
        log.debug("开始读取请求头的信息");
        //请求头
        log.debug("Wechatpay-Timestamp=" + wechatTimestamp);
        log.debug("Wechatpay-Nonce=" + wechatpayNonce);
        log.debug("Wechatpay-Signature=" + wechatSignature);
        log.debug("Wechatpay-Serial=" + wechatPaySerial);
        log.debug("Wechatpay-Signature-Type=" + signatureType);
        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(wechatPaySerial)
                .nonce(wechatpayNonce)
                .signature(wechatSignature)
                .timestamp(wechatTimestamp)
                .body(ServletUtils.getRequestBody(ServletUtils.getRequest()))
                .build();
        Collection<WeChatPayConfig> allWechatPayConfig = PayClientFactory.getAllWechatPayConfig();
        for (WeChatPayConfig weChatPayConfig : allWechatPayConfig) {
            try {
                return weChatPayConfig.getNotificationParser().parse(requestParam, Transaction.class);
            } catch (Exception e) {
                log.debug("微信回调解密失败：", e);
            }
        }
        log.error("微信回调全部解密失败{}", requestParam.toString());
        throw new ServiceException("解密失败");
    }


    /**
     * 将订单号转为外部订单号
     * <p>视频订单 DDWN1155 -> 商户码+1155+3</p>
     * <p>会员订单 HYWN1155 -> 商户码+1155+2</p>
     * 富有方要求订单号唯一，故加入随机数
     *
     * @param orderNumber 订单号
     * @return 精简过后的内部订单号
     */
    public String convertToOutTradeNo(String orderNumber) {
        //视频订单
        if (orderNumber.startsWith(OrderConstant.ORDER_NUM_PREFIX_DD + OrderConstant.ORDER_NUM_PREFIX_WN)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.ORDER_NUM_PREFIX_DD.length()
                            + OrderConstant.ORDER_NUM_PREFIX_WN.length()) + RandomUtil.randomString(3);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 会员订单
        else if (orderNumber.startsWith(OrderConstant.ORDER_NUM_PREFIX_HY + OrderConstant.ORDER_NUM_PREFIX_WN)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.ORDER_NUM_PREFIX_HY.length()
                            + OrderConstant.ORDER_NUM_PREFIX_WN.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 支付单
        else if (orderNumber.startsWith(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        // 线上钱包充值
        else if (orderNumber.startsWith(OrderConstant.PREPAY_NUM)) {
            final String outTradeNo = payConfig.getOrderPrefix() +
                    orderNumber.substring(OrderConstant.PREPAY_NUM.length()) + RandomUtil.randomString(2);
            saveOrderLinkToRedis(orderNumber, outTradeNo);
            return outTradeNo;
        }
        throw new ServiceException("订单号类型错误");
    }

    /**
     * 还原订单号
     * <p>视频订单 商户码+1155+3 -> DDWN1155</p>
     * <p>会员订单 商户码+1155+2 -> HYWN1155</p>
     *
     * @param orderNumber
     * @return
     */
    public String resumeOrderNumber(String orderNumber) {
        if (orderNumber.startsWith(payConfig.getOrderPrefix())) {
            // 订单号: 商户号 +14位日期(yyyyMMddHHmmss)+8位随机数+(2位 / 3位随机字符)
            int outOrderNumber = orderNumber.substring(payConfig.getOrderPrefix().length()).length();
            // 视频订单  14位日期(yyyyMMddHHmmss)+8位随机数+3位随机字符 = 14+8+3=25
            // 会员订单  14位日期(yyyyMMddHHmmss)+8位随机数+2位随机字符 = 14+8+2=24
            // 预付单    6位数字 + 2位随机字符                         = 6 + 2 = 8
            if (outOrderNumber == 25) {
                // 视频订单
                return OrderConstant.ORDER_NUM_PREFIX_DD + OrderConstant.ORDER_NUM_PREFIX_WN + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 3);
            }
            if (outOrderNumber == 24) {
                // 会员订单
                return OrderConstant.ORDER_NUM_PREFIX_HY + OrderConstant.ORDER_NUM_PREFIX_WN + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }
            if (outOrderNumber == 20) {
                // 支付单
                return OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }

            if (outOrderNumber == 7) {
                // 预付单
                return OrderConstant.PREPAY_NUM + orderNumber.substring(payConfig.getOrderPrefix().length(), orderNumber.length() - 2);
            }
        }
        log.warn("回调订单号类型错误,外部orderNumber为:{}", orderNumber);
        throw new ServiceException("回调订单号类型错误");
    }

    /**
     * 设置订单与真实订单的关联关系（用于查单）
     *
     * @param orderNumber 订单号
     * @param outTradeNo  外部交易号
     */
    private void saveOrderLinkToRedis(String orderNumber, String outTradeNo) {
        redisService.setCacheObject(CacheConstants.ORDER_NUMBER_PREFIX + orderNumber, outTradeNo, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    /**
     * 从redis中获取订单与真实订单的关联关系（用于查单）
     *
     * @param orderNumber 订单号
     * @return 外部交易号
     */
    private String getOutTradeNoFromRedis(String orderNumber) {
        return redisService.getCacheObject(CacheConstants.ORDER_NUMBER_PREFIX + orderNumber);
    }

    private boolean isOrderUpdating(String orderNumber) {
        return redisService.hasKey(orderNumber);
    }

//    --------------------------------------------以上实际调用微信接口-----------------------------------------------------------


    @Override
    public CreatePayVo weChatPay(CreatePayVo createPayVo) {
        Assert.isTrue(createPayVo.getOrderNumber().length() == 26 || createPayVo.getOrderNumber().length() == 22|| createPayVo.getOrderNumber().length() == 7, "订单号长度错误");
        Assert.notNull(createPayVo.getPayPlatformTypeEnum(), "支付平台不能为空");
        Assert.notNull(createPayVo.getOrderAmount(), "订单金额不能为空");
        Assert.notNull(createPayVo.getPayPlatformTypeEnum().compareTo(PayPlatformTypeEnum.WECHAT) == 0, "支付平台错误");
        String outTradeNo = convertToOutTradeNo(createPayVo.getOrderNumber());
        PrepayResponse prepay = prepay(
                WeChatPayDTO.builder()
                        .orderNum(createPayVo.getOrderNumber())
                        .outTradeNo(outTradeNo)
                        .orderAmount(createPayVo.getOrderAmount())
                        .appId(createPayVo.getAppId())
                        .build()
        );

        WechatOrderTable wechatOrderTable = new WechatOrderTable();
        wechatOrderTable.setAppId(createPayVo.getAppId());
        wechatOrderTable.setMchntOrderNo(outTradeNo);
        wechatOrderTable.setOrderNum(createPayVo.getOrderNumber());
        wechatOrderTable.setQrcode(prepay.getCodeUrl());
        wechatOrderTable.setOrderAmount(createPayVo.getOrderAmount());
        wechatOrderTable.setCreateTime(new Date());
        wechatOrderTableService.save(wechatOrderTable);

        createPayVo.setQrcode(prepay.getCodeUrl());
        return createPayVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreatePayVo generateWeChatQrcode(Boolean isMerge, String payNum, List<String> orderNums, String appId, BigDecimal finalPayAmount, Long payUserId) {
        // 获取微信订单表数据 如果数据不为空 且未过期则直接返回
        WechatOrderTable wechatOrderTable = wechatOrderTableService.getValidWechatOrderTable(isMerge ? payNum : orderNums.get(0));
        String requestAppId = appId;
        if (ObjectUtil.isNotNull(wechatOrderTable)) {
            Long datePoorSec = DateUtils.getDatePoorSec(new Date(), wechatOrderTable.getCreateTime());
            // 检查数据
            if (wechatOrderTable.getOrderAmount().compareTo(finalPayAmount) != 0 ||
                    datePoorSec >= PayConstant.QRCODE_VALID_TIME
            ) {
//                获取最新的支付payeeId，如果不一致的话更新order表
                log.debug("请求新二维码，取消原有二维码");
                OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(PayTypeEnum.WECHAT.getCode());
                Assert.notNull(orderPayeeAccountConfigInfoDTO, "获取收款账号配置信息失败，请联系蜗牛客服处理~");
                if (!orderPayeeAccountConfigInfoDTO.getBankAccount().equals(appId)) {
                    //如果是钱包充值订单
                    if (wechatOrderTable.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)) {
                        //变更预付款支付主体
                        remoteService.innerBusinessBalancePrepayUpdateAppId(PrepayUpdateAppIdDTO.builder()
                                .appId(appId)
                                .payUserId(payUserId)
                                .accountId(orderPayeeAccountConfigInfoDTO.getDetailId())
                                .payType(PayTypeEnum.WECHAT.getCode())
                                .prepayNums(orderNums).build());
                    } else {
                        orderService.updateWechatPayAppId(orderNums, orderPayeeAccountConfigInfoDTO);
                    }
                    requestAppId = orderPayeeAccountConfigInfoDTO.getBankAccount();
                }

                // 微信订单表支付金额与现有数据 或主体不一致！
                wechatOrderTableService.banWechatOrderTable(wechatOrderTable.getId());
                CompletableFuture.runAsync(() -> closeOrder(wechatOrderTable.getMchntOrderNo(), appId), asyncPoolTaskExecutor);
            } else {
                CreatePayVo createPayVo = new CreatePayVo();
                createPayVo.setPayPlatformTypeEnum(PayPlatformTypeEnum.WECHAT);
                createPayVo.setOrderNumber(wechatOrderTable.getOrderNum());
                createPayVo.setOrderAmount(wechatOrderTable.getOrderAmount());

                String descPrefix;
                if (isMerge) {
                    descPrefix = "支付单:";
                } else {
                    descPrefix = "订单:";
                }
                createPayVo.setDesc(descPrefix + wechatOrderTable.getOrderNum());
                createPayVo.setQrcode(wechatOrderTable.getQrcode());
                saveOrderLinkToRedis(wechatOrderTable.getOrderNum(), wechatOrderTable.getMchntOrderNo());
                return createPayVo;
            }
        }

        String descPrefix;
        String orderNum;
        if (isMerge) {
            descPrefix = "支付单:";
            orderNum = payNum;
        } else {
            descPrefix = "订单:";
            orderNum = orderNums.get(0);
        }
        CreatePayVo createPayVo = new CreatePayVo();
        createPayVo.setPayPlatformTypeEnum(PayPlatformTypeEnum.WECHAT);
        createPayVo.setOrderNumber(orderNum);
        createPayVo.setDesc(descPrefix + orderNum);
        createPayVo.setOrderAmount(finalPayAmount);
        createPayVo.setAppId(requestAppId);
        return weChatPay(createPayVo);
    }

    @Override
    public void weChatPayCallBack() {
        Transaction transaction = callback();
        if (!redisService.getLock(CacheConstants.THIRD_PAY_KEY + transaction.getOutTradeNo(), 60L)) {
            throw new ServiceException("业务正在处理中！");
        }

        WechatPayLog wechatPayLog = convertTransactionToWechatPayLog(transaction);
        try {
            log.info("微信支付回调:{}", wechatPayLog);
            wechatPayLog.setOrderNum(resumeOrderNumber(wechatPayLog.getOutTradeNo()));
            wechatPayLog.setRemark(JSON.toJSONString(transaction));

            if (wechatPayLogService.checkOrderNotExist(wechatPayLog.getOutTradeNo())) {
                orderProcess(wechatPayLog);
            }
        } catch (Exception e) {
            log.error("微信支付回调失败:{}", e.getMessage());
            throw new SystemException("微信支付回调失失败：" + e.getMessage());
        } finally {
            redisService.releaseLock(CacheConstants.THIRD_PAY_KEY + wechatPayLog.getOutTradeNo());
        }
    }

    @Override
    public PayTranStatusEnum checkOrderStatus(String orderNum, String wechatPayAppId) {
        if (wechatPayLogService.checkOrderNumberNotExist(orderNum)) {
            return queryWeChatPayTradeState(orderNum, wechatPayAppId);
        }
        return PayTranStatusEnum.NOTPAY;
    }


    @Override
    public PayTranStatusEnum queryWeChatPayTradeState(String orderNum, String wechatPayAppId) {
        Assert.isTrue(orderNum.length() == 26 || orderNum.length() == 22 || orderNum.length() == 7, "订单号长度错误");
        final String outTradeNoFromRedis = getOutTradeNoFromRedis(orderNum);
        if (StringUtils.isBlank(outTradeNoFromRedis)) {
            log.error("订单号:{}未找到对应外部订单号", orderNum);
            return PayTranStatusEnum.PAYERROR;
        }
        Transaction transaction = queryOrderByOutTradeNo(outTradeNoFromRedis, wechatPayAppId);
        WechatPayLog wechatPayLog = convertTransactionToWechatPayLog(transaction);
        log.debug("获取微信订单数据：{}", wechatPayLog.toString());
        final PayTranStatusEnum tranStatusEnum = PayTranStatusEnum.getEnum(wechatPayLog.getTradeState());

        if (tranStatusEnum.equals(PayTranStatusEnum.USERPAYING) ||
                tranStatusEnum.equals(PayTranStatusEnum.NOTPAY)
        ) {
            return PayTranStatusEnum.NOTPAY;
        }
        if (!tranStatusEnum.equals(PayTranStatusEnum.SUCCESS)
        ) {
            return tranStatusEnum;
        }
//        当前是否正在更新订单 返回未支付等待业务完成
        if (isOrderUpdating(orderNum)) {
            return PayTranStatusEnum.NOTPAY;
        }

        if (!redisService.getLock(CacheConstants.THIRD_PAY_KEY + wechatPayLog.getOutTradeNo(), 60L)) {
            return PayTranStatusEnum.NOTPAY;
        }
        try {
            List<String> orderNums;
            if (orderNum.startsWith(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF)) {
                orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(null, orderNum);
            } else if (orderNum.startsWith(OrderConstant.PREPAY_NUM)){
                List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(Arrays.asList(orderNum)).build());
                if (CollUtil.isEmpty(businessBalancePrepayVOS)){
                    return PayTranStatusEnum.NOTPAY;
                }
                if (businessBalancePrepayVOS.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime()))){
                    // 支付掉单，通知失效，手动更新支付状态
                    wechatPayLog.setOrderNum(resumeOrderNumber(wechatPayLog.getOutTradeNo()));
                    orderProcess(wechatPayLog);
                }
                return PayTranStatusEnum.NOTPAY;
            }else {
                orderNums = List.of(orderNum);
            }
            if (CollUtil.isEmpty(orderNums)) {
                return PayTranStatusEnum.NOTPAY;
            }
            final List<Order> orders = orderService.selectListByOrderNums(orderNums);
            Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
            if (orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime()))) {
                // 支付掉单，通知失效，手动更新支付状态
                wechatPayLog.setOrderNum(resumeOrderNumber(wechatPayLog.getOutTradeNo()));
                orderProcess(wechatPayLog);
            }
        } finally {
            redisService.releaseLock(CacheConstants.THIRD_PAY_KEY + wechatPayLog.getOutTradeNo());
        }
        return PayTranStatusEnum.NOTPAY;
    }

    @Override
    public void closeAllQrcode(String orderNum) {
        WechatOrderTable wechatOrderTable = wechatOrderTableService.getValidWechatOrderTable(orderNum);
        if (ObjectUtil.isNotNull(wechatOrderTable)) {
            closeOrder(wechatOrderTable.getMchntOrderNo(), wechatOrderTable.getAppId());
            wechatOrderTableService.banWechatOrderTableByOrderNum(orderNum);
        }
        try {
            AlipayOrderTableServiceImpl alipayOrderTableService = SpringUtils.getBean(AlipayOrderTableServiceImpl.class);
            AlipayOrderTable validAlipayOrderTable = alipayOrderTableService.getValidAlipayOrderTableByOrderNum(orderNum);
            if (ObjectUtil.isNotNull(validAlipayOrderTable)) {
                SpringUtils.getBean(AlipayServiceImpl.class).closeAlipayOrder(orderNum, validAlipayOrderTable.getAppId());
                alipayOrderTableService.banAlipayOrderTableByOrderNum(orderNum);
            }
        } catch (Exception e) {
            log.error("关闭支付宝订单失败:{}", e);
        }
    }


    private void orderProcess(WechatPayLog wechatPayLog) {
        log.debug("支付回调记录:{}", wechatPayLog);
        //业务处理
        //1.保存进表
        Assert.isTrue(wechatPayLogService.checkOrderNotExist(wechatPayLog.getOutTradeNo()), "订单已被处理");

        WechatOrderTable validWechatOrderTable = wechatOrderTableService.getByOutTradeNo(wechatPayLog.getOutTradeNo());
        if (Boolean.TRUE.toString().equals(debuggerEnable)) {
            if (wechatPayLog.getTotal().compareTo(1) != 0) {
                throw new ServiceException("订单支付失败，订单金额不一致");
            }
        } else if (wechatPayLog.getTotal().compareTo(validWechatOrderTable.getOrderAmount().multiply(BigDecimal.valueOf(100)).intValue()) != 0) {
            throw new ServiceException("订单支付失败，订单金额不一致");
        }
        // 2.更新订单状态
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            wechatPayLogService.save(wechatPayLog);
            orderService.updateOrderPayStatus(wechatPayLog.getOrderNum(), BigDecimal.valueOf(wechatPayLog.getTotal()).divide(new BigDecimal("100")), PayTypeEnum.WECHAT.getCode(), wechatPayLog.getTransactionId(), wechatPayLog.getMchid());
            wechatOrderTableService.banWechatOrderTableByOrderNum(wechatPayLog.getOrderNum());
        });

        try {
            SpringUtils.getBean(AsyncTaskService.class).closeAllOrder(wechatPayLog.getOrderNum());
        } catch (Exception e) {
            log.error("取消订单失败:{}", e);
        }

    }

    public WechatPayLog convertTransactionToWechatPayLog(Transaction transaction) {
        WechatPayLog wechatPayLog = new WechatPayLog();

        // 进行属性赋值
        wechatPayLog.setAppid(transaction.getAppid());
        wechatPayLog.setMchid(transaction.getMchid());
        wechatPayLog.setOutTradeNo(transaction.getOutTradeNo());
        wechatPayLog.setTransactionId(transaction.getTransactionId());
        wechatPayLog.setTradeType(ObjectUtil.isNotNull(transaction.getTradeType()) ? transaction.getTradeType().toString() : "");
        wechatPayLog.setTradeState(ObjectUtil.isNotNull(transaction.getTradeState()) ? transaction.getTradeState().toString() : "");
        wechatPayLog.setTradeStateDesc(transaction.getTradeStateDesc());
        wechatPayLog.setBankType(transaction.getBankType());
        wechatPayLog.setAttach(transaction.getAttach());
        wechatPayLog.setSuccessTime(ObjectUtil.isNotNull(transaction.getSuccessTime()) ? DateUtils.dateTime(DateUtils.YYYY_MM_DD_T_HH_MM_SSXXX, transaction.getSuccessTime()) : null);
        wechatPayLog.setOpenid(ObjectUtil.isNotNull(transaction.getPayer()) ? transaction.getPayer().getOpenid() : "");

        if (ObjectUtil.isNotNull(transaction.getAmount())) {
            wechatPayLog.setTotal(transaction.getAmount().getTotal());
            wechatPayLog.setPayerTotal(transaction.getAmount().getPayerTotal());
            wechatPayLog.setCurrency(transaction.getAmount().getCurrency());
            wechatPayLog.setPayerCurrency(transaction.getAmount().getPayerCurrency());
        }

        wechatPayLog.setRemark(JSON.toJSONString(transaction));

        return wechatPayLog;
    }

}
