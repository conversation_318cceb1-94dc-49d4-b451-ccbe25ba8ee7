package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTag;
import com.wnkx.order.mapper.OrderVideoTagMapper;
import com.wnkx.order.service.IOrderVideoTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单_视频_关联标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-14
 */
@Service
@RequiredArgsConstructor
public class OrderVideoTagServiceImpl extends ServiceImpl<OrderVideoTagMapper, OrderVideoTag> implements IOrderVideoTagService
{

    /**
     * 获取模特排的订单的产品品类
     */
    @Override
    public List<Long> selectModelTagIdsByModelId(Long modelId) {
        return baseMapper.selectModelTagIdsByModelId(modelId);
    }

    /**
     * 批量添加订单视频标签
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchOrderVideoTag(List<OrderVideoTag> orderVideoTags) {
        if (CollUtil.isEmpty(orderVideoTags)) {
            return;
        }
        Set<Long> videoIds = orderVideoTags.stream().map(OrderVideoTag::getVideoId).collect(Collectors.toSet());
        baseMapper.removeBatchByVideoIds(videoIds);

        baseMapper.saveBatch(orderVideoTags);
    }

    /**
     * 通过视频订单id获取单个
     */
    @Override
    public OrderVideoTag getOneByVideoId(Long videoId) {
        return baseMapper.getOneByVideoId(videoId);
    }

    /**
     * 通过视频订单id和分类id查询视频订单标签
     *
     * @param videoId 视频订单id
     * @param code    分类id
     * @return
     */
    @Override
    public List<OrderVideoTag> selectListByVideoIdAndCategory(Long videoId, Long code) {
        return baseMapper.selectListByVideoIdAndCategory(videoId, code);
    }
}
