package com.wnkx.order.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityAmendmentRecordVO;
import com.wnkx.order.mapper.PromotionActivityAmendmentRecordMapper;
import com.wnkx.order.service.PromotionActivityAmendmentRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-05-21 14:34:30 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PromotionActivityAmendmentRecordServiceImpl extends ServiceImpl<PromotionActivityAmendmentRecordMapper, PromotionActivityAmendmentRecord> implements PromotionActivityAmendmentRecordService {

    /**
     * 新增活动修改记录
     */
    @Override
    public void savePromotionActivityAmendmentRecord(PromotionActivityAmendmentRecord promotionActivityAmendmentRecord) {
        String username = SecurityUtils.getUsername();
        Long userId = SecurityUtils.getUserId();
        DateTime date = DateUtil.date();

        promotionActivityAmendmentRecord.setCreateBy(username);
        promotionActivityAmendmentRecord.setCreateById(userId);
        promotionActivityAmendmentRecord.setCreateTime(date);
        promotionActivityAmendmentRecord.setUpdateBy(username);
        promotionActivityAmendmentRecord.setUpdateById(userId);
        promotionActivityAmendmentRecord.setUpdateTime(date);
        baseMapper.insert(promotionActivityAmendmentRecord);
    }

    /**
     * 通过活动类型获取修改记录
     */
    @Override
    public List<PromotionActivityAmendmentRecordVO> selectPromotionActivityAmendmentRecordList(Integer type) {
        return baseMapper.selectPromotionActivityAmendmentRecordList(type);
    }
}
