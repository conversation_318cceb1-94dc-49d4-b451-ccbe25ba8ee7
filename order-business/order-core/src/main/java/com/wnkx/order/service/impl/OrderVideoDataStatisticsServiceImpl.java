package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.OrderVideoDeliveryDurationEnum;
import com.ruoyi.common.core.enums.OrderVideoFeedbackDurationEnum;
import com.ruoyi.common.core.enums.OrderVideoMatchDurationEnum;
import com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoDurationDataBO;
import com.ruoyi.system.api.domain.bo.order.datastatistics.OrderVideoTrendBO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoDataStatisticsDay;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.*;
import com.wnkx.order.mapper.OrderVideoDataStatisticsMapper;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoDataStatisticsDayService;
import com.wnkx.order.service.OrderVideoDataStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/4 11:18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderVideoDataStatisticsServiceImpl implements OrderVideoDataStatisticsService {

    private final IOrderVideoService orderVideoService;
    private final OrderVideoDataStatisticsDayService orderVideoDataStatisticsDayService;
    private final OrderVideoDataStatisticsMapper baseMapper;

    /**
     * 视频订单数据-补偿订单情况
     */
    @Override
    public OrderVideoCompensationOrderSituationVO getOrderVideoCompensationOrderSituation(String date) {
        List<BigDecimal> refundAmountList = baseMapper.selectRefundAmountListByDate(date);

        Map<String, List<BigDecimal>> groupTop3ByRefundAmount = groupTop3ByRefundAmount(refundAmountList);
        List<PieChartVO> refundAmountPieChartVOS = new ArrayList<>();
        for (Map.Entry<String, List<BigDecimal>> refundAmountEntry : groupTop3ByRefundAmount.entrySet()) {
            PieChartVO pieChartVO = new PieChartVO();
            pieChartVO.setLabel(refundAmountEntry.getKey());
            pieChartVO.setCount(Convert.toLong(refundAmountEntry.getValue().size()));
            pieChartVO.setRatio(BigDecimal.valueOf(refundAmountEntry.getValue().size())
                    .divide(BigDecimal.valueOf(refundAmountList.size()), 2, RoundingMode.HALF_UP));
            refundAmountPieChartVOS.add(pieChartVO);

            if (refundAmountEntry.getKey().equals("其他")) {
                Map<BigDecimal, List<BigDecimal>> other = refundAmountEntry.getValue().stream().collect(Collectors.groupingBy(Function.identity()));
                List<PieChartVO> otherRefundAmountPieChartVOS = new ArrayList<>();
                for (Map.Entry<BigDecimal, List<BigDecimal>> otherEntry : other.entrySet()) {
                    PieChartVO otherPieChartVO = new PieChartVO();
                    otherPieChartVO.setLabel("补偿" + otherEntry.getKey().stripTrailingZeros().toPlainString() + "元");
                    otherPieChartVO.setCount(Convert.toLong(otherEntry.getValue().size()));
                    otherPieChartVO.setRatio(BigDecimal.valueOf(otherEntry.getValue().size())
                            .divide(BigDecimal.valueOf(refundAmountList.size()), 2, RoundingMode.HALF_UP));
                    otherRefundAmountPieChartVOS.add(otherPieChartVO);
                }
                otherRefundAmountPieChartVOS.sort((a, b) -> {
                    BigDecimal aValue = new BigDecimal(a.getLabel().replace("补偿", "").replace("元", ""));
                    BigDecimal bValue = new BigDecimal(b.getLabel().replace("补偿", "").replace("元", ""));
                    return bValue.compareTo(aValue);
                });
                pieChartVO.setPieChartVOS(otherRefundAmountPieChartVOS);
            }
        }

        OrderVideoCompensationOrderSituationVO orderVideoCompensationOrderSituationVO = new OrderVideoCompensationOrderSituationVO();
        if (DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN).equals(date)) {
            orderVideoCompensationOrderSituationVO.setWriteTimeEnd(DateUtil.date());
        } else {
            orderVideoCompensationOrderSituationVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        }
        orderVideoCompensationOrderSituationVO.setCompensationOrderCount(Convert.toLong(refundAmountList.size()));
        orderVideoCompensationOrderSituationVO.setCompensationAmount(refundAmountList.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        orderVideoCompensationOrderSituationVO.setPieChartVOS(refundAmountPieChartVOS);
        return orderVideoCompensationOrderSituationVO.echo();
    }

    /**
     * 视频订单数据-售后类型分析
     */
    @Override
    public OrderVideoAfterSaleTypeAnalysisVO getOrderVideoAfterSaleTypeAnalysis(String date) {
        List<OrderVideoAfterSaleTypeAnalysisDetailVO> orderVideoAfterSaleTypeAnalysisDetailVOS = baseMapper.getOrderVideoAfterSaleTypeAnalysis(date);
        Map<String, OrderVideoAfterSaleTypeAnalysisDetailVO> orderVideoAfterSaleTypeAnalysisDetailVOMap = orderVideoAfterSaleTypeAnalysisDetailVOS.stream().collect(Collectors.toMap(OrderVideoAfterSaleTypeAnalysisDetailVO::getDate, Function.identity()));

        List<DateTime> dates = DateUtil.rangeToList(DateUtil.beginOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)), DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)), DateField.DAY_OF_MONTH);
        List<OrderVideoAfterSaleTypeAnalysisDetailVO> collect = dates.stream().map(item -> {
            String format = DateUtil.format(item, "M.d");

            OrderVideoAfterSaleTypeAnalysisDetailVO orderVideoAfterSaleTypeAnalysisDetailVO = new OrderVideoAfterSaleTypeAnalysisDetailVO();
            orderVideoAfterSaleTypeAnalysisDetailVO.setDate(format);

            OrderVideoAfterSaleTypeAnalysisDetailVO afterSaleTypeAnalysisDetailVO = orderVideoAfterSaleTypeAnalysisDetailVOMap.get(format);
            BeanUtil.copyProperties(afterSaleTypeAnalysisDetailVO, orderVideoAfterSaleTypeAnalysisDetailVO);
            return orderVideoAfterSaleTypeAnalysisDetailVO;
        }).collect(Collectors.toList());

        OrderVideoAfterSaleTypeAnalysisVO orderVideoAfterSaleTypeAnalysisVO = new OrderVideoAfterSaleTypeAnalysisVO();
        if (DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN).equals(date)) {
            orderVideoAfterSaleTypeAnalysisVO.setWriteTimeEnd(DateUtil.date());
        } else {
            orderVideoAfterSaleTypeAnalysisVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        }
        orderVideoAfterSaleTypeAnalysisVO.setOrderVideoAfterSaleTypeAnalysisDetailVOS(collect);
        return orderVideoAfterSaleTypeAnalysisVO.echo();
    }

    /**
     * 视频订单数据-平均审单/服务时长、任务单数及占比、拖单数及占比、烂单数及占比
     */
    @Override
    public OrderVideoAverageDurationDataVO getOrderVideoAverageDurationData() {
        return baseMapper.getOrderVideoAverageDurationData().echo();
    }

    /**
     * 视频订单数据-订单素材反馈时长数据分析
     */
    @Override
    public OrderVideoDurationDataVO getOrderVideoFeedbackDurationData(String date) {
        OrderVideoDurationDataVO orderVideoDurationDataVO = new OrderVideoDurationDataVO();

        List<OrderVideoDurationDataBO> orderVideoDurationDataBOS = baseMapper.selectOrderVideoFeedbackDurationDataList(date);

        List<PieChartVO> pieChartVOS = new ArrayList<>();
        for (String label : OrderVideoFeedbackDurationEnum.getLabels()) {
            PieChartVO pieChartVO = new PieChartVO();

            pieChartVO.setLabel(label);
            if (CollUtil.isNotEmpty(orderVideoDurationDataBOS)) {
                List<OrderVideoDurationDataBO> result = orderVideoDurationDataBOS.stream().filter(item -> OrderVideoFeedbackDurationEnum.isInSection(label, item.getDuration())).collect(Collectors.toList());
                pieChartVO.setCount(Convert.toLong(result.size()));
                pieChartVO.setRatio(BigDecimal.valueOf(result.size())
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
                orderVideoDurationDataVO.setAverageDuration(orderVideoDurationDataBOS.stream().map(OrderVideoDurationDataBO::getDuration).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
            } else {
                pieChartVO.setCount(0L);
                pieChartVO.setRatio(BigDecimal.ZERO);
            }

            pieChartVOS.add(pieChartVO);
        }

        if (DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN).equals(date)) {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.date());
        } else {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        }
        orderVideoDurationDataVO.setPieChartVOS(pieChartVOS);
        return orderVideoDurationDataVO.echo();
    }

    /**
     * 视频订单数据-订单发货时长数据分析
     */
    @Override
    public OrderVideoDurationDataVO getOrderVideoDeliveryDurationData(String date) {
        OrderVideoDurationDataVO orderVideoDurationDataVO = new OrderVideoDurationDataVO();

        List<OrderVideoDurationDataBO> orderVideoDurationDataBOS = baseMapper.selectOrderVideoDeliveryDurationDataList(date);

        List<PieChartVO> pieChartVOS = new ArrayList<>();
        for (String label : OrderVideoDeliveryDurationEnum.getLabels()) {
            PieChartVO pieChartVO = new PieChartVO();

            pieChartVO.setLabel(label);
            if (CollUtil.isNotEmpty(orderVideoDurationDataBOS)) {
                List<OrderVideoDurationDataBO> result = orderVideoDurationDataBOS.stream().filter(item -> OrderVideoDeliveryDurationEnum.isInSection(label, item.getDuration())).collect(Collectors.toList());
                pieChartVO.setCount(Convert.toLong(result.size()));
                pieChartVO.setRatio(BigDecimal.valueOf(result.size())
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
                orderVideoDurationDataVO.setAverageDuration(orderVideoDurationDataBOS.stream().map(OrderVideoDurationDataBO::getDuration).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
            } else {
                pieChartVO.setCount(0L);
                pieChartVO.setRatio(BigDecimal.ZERO);
            }

            pieChartVOS.add(pieChartVO);
        }

        if (DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN).equals(date)) {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.date());
        } else {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        }
        orderVideoDurationDataVO.setPieChartVOS(pieChartVOS);
        return orderVideoDurationDataVO.echo();
    }

    /**
     * 视频订单数据-订单匹配时长数据分析
     */
    @Override
    public OrderVideoDurationDataVO getOrderVideoMatchDurationData(String date) {
        OrderVideoDurationDataVO orderVideoDurationDataVO = new OrderVideoDurationDataVO();

        List<OrderVideoDurationDataBO> orderVideoDurationDataBOS = baseMapper.selectOrderVideoMatchDurationDataList(date);

        List<PieChartVO> pieChartVOS = new ArrayList<>();
        for (String label : OrderVideoMatchDurationEnum.getLabels()) {
            PieChartVO pieChartVO = new PieChartVO();

            pieChartVO.setLabel(label);
            if (CollUtil.isNotEmpty(orderVideoDurationDataBOS)) {
                List<OrderVideoDurationDataBO> result = orderVideoDurationDataBOS.stream().filter(item -> OrderVideoMatchDurationEnum.isInSection(label, item.getDuration())).collect(Collectors.toList());
                pieChartVO.setCount(Convert.toLong(result.size()));
                pieChartVO.setRatio(BigDecimal.valueOf(result.size())
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
                orderVideoDurationDataVO.setAverageDuration(orderVideoDurationDataBOS.stream().map(OrderVideoDurationDataBO::getDuration).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(BigDecimal.valueOf(orderVideoDurationDataBOS.size()), 2, RoundingMode.HALF_UP));
            } else {
                pieChartVO.setCount(0L);
                pieChartVO.setRatio(BigDecimal.ZERO);
            }

            pieChartVOS.add(pieChartVO);
        }

        if (DateUtil.format(DateUtil.date(), DatePattern.NORM_MONTH_PATTERN).equals(date)) {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.date());
        } else {
            orderVideoDurationDataVO.setWriteTimeEnd(DateUtil.endOfMonth(DateUtil.parse(date, DatePattern.NORM_MONTH_PATTERN)));
        }
        orderVideoDurationDataVO.setPieChartVOS(pieChartVOS);
        return orderVideoDurationDataVO.echo();
    }

    /**
     * 视频订单数据-订单趋势
     */
    @Override
    public OrderVideoTrendVO getOrderVideoTrend(Date beginTime, Date endTime) {
        OrderVideoTrendVO orderVideoTrendVO = new OrderVideoTrendVO();

        List<String> dateArray;
        List<Long> orderNewCountArray;
        List<Long> orderCancelCountArray;
        List<Long> orderScheduledCountArray;
        List<OrderVideoTrendBO> orderNewCountList;
        List<OrderVideoTrendBO> orderCancelCountList;
        List<OrderVideoTrendBO> orderScheduledCountList;

        if (DateUtil.betweenDay(beginTime, endTime, true) > 30) {
            List<DateTime> dateTimes = DateUtil.rangeToList(beginTime, endTime, DateField.MONTH);
            dateArray = dateTimes.stream().map(item -> DateUtil.format(item, "yyyy.M")).collect(Collectors.toList());
            orderNewCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            orderCancelCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            orderScheduledCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));

            orderNewCountList = baseMapper.getOrderNewCountByTimeBetween(beginTime, endTime, "'%Y.%c'");
            orderCancelCountList = baseMapper.getOrderCancelCountByTimeBetween(beginTime, endTime, "'%Y.%c'");
            orderScheduledCountList = baseMapper.getOrderScheduledCountByTimeBetween(beginTime, endTime, "'%Y.%c'");
        } else {
            List<DateTime> dateTimes = DateUtil.rangeToList(beginTime, endTime, DateField.DAY_OF_YEAR);
            dateArray = dateTimes.stream().map(item -> DateUtil.format(item, "M.d")).collect(Collectors.toList());
            orderNewCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            orderCancelCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));
            orderScheduledCountArray = new ArrayList<>(Collections.nCopies(dateArray.size(), 0L));

            orderNewCountList = baseMapper.getOrderNewCountByTimeBetween(beginTime, endTime, "'%c.%e'");
            orderCancelCountList = baseMapper.getOrderCancelCountByTimeBetween(beginTime, endTime, "'%c.%e'");
            orderScheduledCountList = baseMapper.getOrderScheduledCountByTimeBetween(beginTime, endTime, "'%c.%e'");
        }

        Map<String, Long> orderNewCountMap = orderNewCountList.stream().collect(Collectors.toMap(OrderVideoTrendBO::getDate, OrderVideoTrendBO::getCount));
        Map<String, Long> orderCancelCountMap = orderCancelCountList.stream().collect(Collectors.toMap(OrderVideoTrendBO::getDate, OrderVideoTrendBO::getCount));
        Map<String, Long> orderScheduledCountMap = orderScheduledCountList.stream().collect(Collectors.toMap(OrderVideoTrendBO::getDate, OrderVideoTrendBO::getCount));

        for (int i = 0; i < dateArray.size(); i++) {
            String date = dateArray.get(i);
            orderNewCountArray.set(i, orderNewCountMap.getOrDefault(date, 0L));
            orderCancelCountArray.set(i, orderCancelCountMap.getOrDefault(date, 0L));
            orderScheduledCountArray.set(i, orderScheduledCountMap.getOrDefault(date, 0L));
        }

        orderVideoTrendVO.setWriteTimeEnd(DateUtil.endOfDay(DateUtil.yesterday()));
        orderVideoTrendVO.setDateArray(dateArray);
        orderVideoTrendVO.setOrderNewCountArray(orderNewCountArray);
        orderVideoTrendVO.setOrderCancelCountArray(orderCancelCountArray);
        orderVideoTrendVO.setOrderScheduledCountArray(orderScheduledCountArray);
        return orderVideoTrendVO;
    }

    /**
     * 视频订单数据-服务中订单数
     */
    @Override
    public OrderVideoServiceCountVO getOrderVideoServiceCount() {
        OrderVideoServiceCountVO orderVideoServiceCountVO = baseMapper.getOrderVideoServiceCount();

        DateTime dateTime = DateUtil.date();
        orderVideoServiceCountVO.setDateTime(dateTime);
        return orderVideoServiceCountVO;
    }

    /**
     * 视频订单数据-基础看板
     */
    @Override
    public OrderVideoBaseBoardVO getOrderVideoBaseBoard() {
        OrderVideoBaseBoardVO orderVideoBaseBoardVO = orderVideoService.getOrderVideoBaseBoard();

        DateTime dateTime = DateUtil.date();
        orderVideoBaseBoardVO.setDateTime(dateTime);

        OrderVideoDataStatisticsDay orderVideoDataStatisticsDay = orderVideoDataStatisticsDayService.getByWriteTime(DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN));
        if (ObjectUtil.isNotNull(orderVideoDataStatisticsDay)) {
            orderVideoBaseBoardVO.setAddedOrderVideoTaskCount(orderVideoDataStatisticsDay.getAddedOrderVideoTaskCount());
            orderVideoBaseBoardVO.setAddedOrderVideoCount(orderVideoDataStatisticsDay.getAddedOrderVideoCount());
            orderVideoBaseBoardVO.setWriteTimeEnd(orderVideoDataStatisticsDay.getWriteTimeEnd());
        }
        orderVideoBaseBoardVO.echo();
        return orderVideoBaseBoardVO;
    }

    private Map<String, List<BigDecimal>> groupTop3ByRefundAmount(List<BigDecimal> refundAmountList) {
        // 规范化 BigDecimal（去除尾部0）并统计每种 refundAmount 出现次数
        Map<BigDecimal, Long> refundAmountCount = refundAmountList.stream()
                .map(BigDecimal::stripTrailingZeros)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

        // 找出数量最多的前三个 refundAmount 值
        Set<BigDecimal> top3RefundAmount = refundAmountCount.entrySet().stream()
                .sorted(Map.Entry.<BigDecimal, Long>comparingByValue().reversed())
                .limit(3)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 按照前三个归类，其余归为 "其他"
        return refundAmountList.stream()
                .collect(Collectors.groupingBy(item -> top3RefundAmount.contains(item.stripTrailingZeros()) ? "补偿" + item.stripTrailingZeros().toPlainString() + "元" : "其他"));
    }
}
