package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.AlipayPayLog;
import com.wnkx.order.mapper.AlipayPayLogMapper;
import com.wnkx.order.service.AlipayPayLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AlipayPayLogServiceImpl extends ServiceImpl<AlipayPayLogMapper, AlipayPayLog> implements AlipayPayLogService {

    /**
     * 通过商户订单号查询是否已存在订单的数据
     */
    @Override
    public Boolean checkExistByOutTradeNo(String out_trade_no) {
        return baseMapper.checkExistByOutTradeNo(out_trade_no);
    }

    /**
     * 通过notify_id查询是否已收到过支付宝的回调信息
     */
    @Override
    public Boolean checkExistByNotifyId(String notify_id) {
        return baseMapper.checkExistByNotifyId(notify_id);
    }

}
