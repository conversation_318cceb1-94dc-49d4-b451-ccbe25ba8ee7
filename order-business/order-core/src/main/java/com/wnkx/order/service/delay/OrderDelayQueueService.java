package com.wnkx.order.service.delay;

import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.redis.service.DelayQueueService;
import com.ruoyi.common.redis.service.RedisService;
import com.wnkx.order.factory.OrderDelayQueueServiceFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrderDelayQueueService extends DelayQueueService {

    private final RedissonClient redissonClient;
    private final RedisService redisService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final OrderDelayQueueServiceFactory orderDelayQueueServiceFactory;


    /**
     * 开始执行延迟队列
     *
     * @param queueName 队列名称
     */
    @Override
    public void startProcessing(String queueName) {
        if (Boolean.FALSE.equals(redisService.getLock(CacheConstants.DELAY_QUEUE_PROCESS_QUEUE_LOCK_KEY + queueName))) {
            return; // 队列已经在处理中
        }
        log.info("队列已开启：{}", queueName);

        RQueue<String> queue = redissonClient.getQueue(queueName);
        //  获取延迟队列 不获取会导致服务重启后 旧的消息没处理
        redissonClient.getDelayedQueue(queue);

        asyncPoolTaskExecutor.submit(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                String task = queue.poll();// 从普通队列中拉取消息
                if (task != null) {
                    //  检查队列是否被关闭
                    if (Boolean.FALSE.equals(redisService.hasKey(CacheConstants.DELAY_QUEUE_PROCESS_QUEUE_LOCK_KEY + queueName))) {
                        log.warn("队列{}已关闭，结束监听这个队列", queueName);
                        Thread.currentThread().interrupt();
                        break;
                    }

                    RLock lock = redissonClient.getLock(CacheConstants.DELAY_QUEUE_PROCESS_TASK_LOCK_KEY + task);
                    try {
                        if (lock.tryLock(0, 1, TimeUnit.MINUTES)) {
                            OrderQueueHandleService delayQueueService = orderDelayQueueServiceFactory.getOrderDelayQueueService(queueName);
                            delayQueueService.processTask(task);
                        }
                    } catch (Exception e) {
                        log.error("任务处理失败：{} ", task, e);
                    } finally {
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                } else {
                    try {
                        Thread.sleep(100); // 在再次检查队列之前，先睡一会儿
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        });
    }
}
