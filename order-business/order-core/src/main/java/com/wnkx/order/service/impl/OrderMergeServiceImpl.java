package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.OrderMergeStatusEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderListDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMerge;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.order.CheckOrderMergeVO;
import com.ruoyi.system.api.domain.vo.order.OrderListVO;
import com.ruoyi.system.api.domain.vo.order.OrderMergeListVO;
import com.ruoyi.system.api.domain.vo.order.OrderVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderMergeMapper;
import com.wnkx.order.service.IOrderService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderMergeDetailService;
import com.wnkx.order.service.OrderMergeService;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 
 * @Date 2025-03-04 10:01:01 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderMergeServiceImpl extends ServiceImpl<OrderMergeMapper, OrderMerge> implements OrderMergeService {

    private final OrderMergeDetailService orderMergeDetailService;
    private final RedisService redisService;
    private final AsyncTaskService asyncTaskService;
    private final IOrderVideoService orderVideoService;
    private final OrderVideoProperties orderVideoProperties;


    /**
     * 通过订单号获取同个合并单下的订单号
     */
    @Override
    public List<String> getMergeOrderNumsByOrderNum(String orderNum) {
        return baseMapper.getMergeOrderNumsByOrderNum(orderNum);
    }

    /**
     * 通过订单号获取正常状态下的合并单
     */
    @Override
    public List<OrderMergeListVO> selectOrderMergeNormalListByOrderNums(List<String> orderNums) {
        return baseMapper.selectOrderMergeNormalListByOrderNums(orderNums);
    }

    /**
     * 取消合并
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMerge(Long mergeId, boolean isTask) {
        OrderMerge orderMerge = baseMapper.selectById(mergeId);
        Assert.notNull(orderMerge, "合并单不存在~");
        Assert.isTrue(orderMerge.getStatus().equals(OrderMergeStatusEnum.NORMAL.getCode()), "订单状态发生变化，请刷新页面后重试~");
        if (!isTask) {
            BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
            Assert.isTrue(businessAccountVO.getIsOwnerAccount().equals(StatusTypeEnum.YES.getCode()) || businessAccountVO.getId().equals(orderMerge.getMergeById()), "对不起，您没有操作权限");
        }

        List<String> mergeOrderNums = baseMapper.getOrderNumsByMergeIdOrPayNum(mergeId, null);
        Assert.notEmpty(mergeOrderNums, "关联订单不存在~");

        IOrderService orderService = SpringUtils.getBean(IOrderService.class);
        List<Order> orders = orderService.selectListByOrderNums(mergeOrderNums);
        Assert.notEmpty(orders, "订单不存在~");
        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime())), "订单状态发生变化，请刷新页面后重试~");
        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getSubmitCredentialTime())), "订单状态发生变化，请刷新页面后重试~");
        Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getCloseOrderTime())), "订单状态发生变化，请刷新页面后重试~");

        redisService.getLock(CacheConstants.ORDER_MERGE_LOCK_KEY + mergeId, CacheConstants.ORDER_MERGE_LOCK_KEY_SECOND);
        try {
            orderMerge.setStatus(OrderMergeStatusEnum.CLOSE.getCode());
            orderMerge.setCloseMergeTime(DateUtil.date());
            baseMapper.updateById(orderMerge);

            List<Order> releasePayLockOrders = new ArrayList<>();
            for (Order order : orders) {
                if (ObjectUtil.isNotNull(order.getPayTime()) || ObjectUtil.isNotNull(order.getSubmitCredentialTime()) || ObjectUtil.isNotNull(order.getCloseOrderTime())) {
                    continue;
                }
                if (DateUtil.compare(DateUtil.endOfDay(DateUtil.offsetHour(order.getOrderTimeSign(), orderVideoProperties.getCloseOrderHours())), DateUtil.date()) <= 0) {
                    orderService.cancelOrder(order.getOrderNum(), true);
                } else {
                    releasePayLockOrders.add(order);
                }
            }
            if (CollUtil.isNotEmpty(releasePayLockOrders)) {
                //  1、清除支付号
                orderService.emptyPayNum(releasePayLockOrders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
                //  2、清除支付类型
                //  3、释放所使用的余额
                orderService.releasePayLock(releasePayLockOrders);
                //  4、关闭支付单
                asyncTaskService.closeAllOrder(releasePayLockOrders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
            }
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MERGE_LOCK_KEY + mergeId);
        }
    }

    /**
     * 获取取消合并校验会到期的订单
     */
    @Override
    public List<String> checkCancelMerge(Long mergeId) {
        List<String> mergeOrderNums = baseMapper.getOrderNumsByMergeIdOrPayNum(mergeId, null);
        if (CollUtil.isEmpty(mergeOrderNums)) {
            return Collections.emptyList();
        }

        List<Order> orders = SpringUtils.getBean(IOrderService.class).selectListByOrderNums(mergeOrderNums);
        if (CollUtil.isEmpty(orders)) {
            return Collections.emptyList();
        }

        List<String> expireOrderNums = new ArrayList<>();
        for (Order order : orders) {
            if (ObjectUtil.isNotNull(order.getPayTime()) || ObjectUtil.isNotNull(order.getSubmitCredentialTime()) || ObjectUtil.isNotNull(order.getCloseOrderTime())) {
                continue;
            }
            if (DateUtil.compare(DateUtil.endOfDay(DateUtil.offsetHour(order.getOrderTimeSign(), orderVideoProperties.getCloseOrderHours())), DateUtil.date()) <= 0) {
                expireOrderNums.add(order.getOrderNum());
            }
        }
        return expireOrderNums;
    }

    /**
     * 商家端-需支付订单列表
     */
    @Override
    public List<OrderMergeListVO> selectNeedPayOrderListByCondition(OrderListDTO orderListDTO) {
        if (Boolean.FALSE.equals(SpringUtils.getBean(IOrderService.class).wrapperCondition(orderListDTO))) {
            return Collections.emptyList();
        }

        PageUtils.startPage();
        List<OrderMergeListVO> orderMergeListVOS = baseMapper.selectNeedPayOrderListByCondition(orderListDTO);
        if (CollUtil.isEmpty(orderMergeListVOS)) {
            return Collections.emptyList();
        }

        Set<String> orderNums = orderMergeListVOS.stream()
                .map(OrderMergeListVO::getOrderNums)
                .filter(CharSequenceUtil::isNotBlank)
                .flatMap(innerOrderNums -> Arrays.stream(innerOrderNums.split(StrPool.COMMA)))
                .map(String::trim)
                .filter(CharSequenceUtil::isNotBlank)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(orderNums)) {
            return Collections.emptyList();
        }

        orderListDTO.setOrderNums(orderNums);
        List<OrderListVO> orderListVOS = SpringUtils.getBean(IOrderService.class).selectOrderListVOS(orderListDTO);

        Map<String, OrderListVO> orderListVOMap = orderListVOS.stream().collect(Collectors.toMap(OrderListVO::getOrderNum, Function.identity()));
        for (OrderMergeListVO orderMergeListVO : orderMergeListVOS) {
            if (CharSequenceUtil.isBlank(orderMergeListVO.getOrderNums())) {
                continue;
            }
            List<String> resultOrderNums = CharSequenceUtil.split(orderMergeListVO.getOrderNums(), StrPool.COMMA);
            for (String resultOrderNum : resultOrderNums) {
                OrderListVO orderListVO = orderListVOMap.get(resultOrderNum);
                if (ObjectUtil.isNotNull(orderListVO)) {
                    orderMergeListVO.getOrderListVOS().add(orderListVO);
                }
            }
            orderMergeListVO.getOrderListVOS().sort(Comparator.comparing(OrderListVO::getOrderTimeSign).reversed());
            orderMergeListVO.setMergePayAmount(orderMergeListVO.getOrderListVOS().stream().map(OrderListVO::getPayAmount).filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        return orderMergeListVOS;
    }

    /**
     * 通过 合并单ID或者支付单号 完成合并单
     */
    @Override
    public void completeOrderMergeByMergeIdOrPayNum(Long mergeId, String payNum) {
        baseMapper.completeOrderMergeByMergeIdOrPayNum(mergeId, payNum);
    }

    /**
     * 通过 合并单ID或者支付单号 获取合并单
     */
    @Override
    public OrderMerge getOrderMergeByIdOrPayNum(Long mergeId, String payNum) {
        return baseMapper.getOrderMergeByIdOrPayNum(mergeId, payNum);
    }

    /**
     * 通过 合并单ID或者支付单号 获取合并单
     */
    @Override
    public OrderMerge getOrderMergeNormalByIdOrPayNum(Long mergeId, String payNum) {
        return baseMapper.getOrderMergeNormalByIdOrPayNum(mergeId, payNum);
    }

    /**
     * 校验订单是否已合并
     */
    @Override
    public CheckOrderMergeVO closeOrderCheckOrderMerge(String orderNum) {
        // 获取订单数据
        IOrderService orderService = SpringUtils.getBean(IOrderService.class);
        final Order order = orderService.getOrderByOrderNum(orderNum);
        Assert.notNull(order, "订单数据不能为空");
        Assert.isNull(order.getPayTime(), "订单已支付");
        Assert.isNull(order.getCloseOrderTime(), "订单已关闭");

        CheckOrderMergeVO checkOrderMergeVO = new CheckOrderMergeVO();
        Boolean existMerge = checkOrderMerge(orderNum);
        if (existMerge) {
            checkOrderMergeVO.setErrorMessage("订单已合并");
            if (ObjectUtil.isNotNull(order.getSubmitCredentialTime())) {
                List<Order> orders = orderService.selectListByOrderNums(getMergeOrderNumsByOrderNum(orderNum));
                checkOrderMergeVO.setOrderVOS(BeanUtil.copyToList(orders, OrderVO.class));
            }
        }
        return checkOrderMergeVO;
    }

    /**
     * 校验订单是否已合并
     */
    @Override
    public Boolean checkOrderMerge(String orderNum) {
        return baseMapper.checkOrderMerge(List.of(orderNum));
    }

    /**
     * 校验订单是否已合并
     */
    @Override
    public void checkOrderMerge(List<String> orderNums) {
        Assert.isFalse(baseMapper.checkOrderMerge(orderNums), "订单已合并");
    }

    /**
     * 创建合并单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrderMerge(List<String> orderNums) {
        List<String> orderLocks = new ArrayList<>();
        try {
            for (String orderNum : orderNums) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MERGE_LOCK_KEY + orderNum, CacheConstants.ORDER_MERGE_LOCK_KEY_SECOND), "合并订单处理中，请稍后重试~");
                orderLocks.add(orderNum);
            }
        } catch (Exception e) {
            for (String orderLock : orderLocks) {
                redisService.releaseLock(CacheConstants.ORDER_MERGE_LOCK_KEY + orderLock);
            }
        }
        try {
            IOrderService orderService = SpringUtils.getBean(IOrderService.class);
            List<Order> orders = orderService.selectListByOrderNums(orderNums);
            Assert.notEmpty(orders, "订单不存在~");
            Assert.isTrue(orders.size() == orderNums.size(), "订单不存在~");
            Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getPayTime())), "订单已支付~");
            Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getSubmitCredentialTime())), "订单已提交审核~");
            Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNull(item.getCloseOrderTime())), "订单已关闭~");
            Assert.isTrue(orders.stream().allMatch(item -> OrderTypeEnum.VIDEO_ORDER.getCode().equals(item.getOrderType())), "订单非视频订单~");
            Assert.isTrue(orders.stream().noneMatch(Order::getIsDefaultExchangeRate), "订单汇率异常~");
            Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
            Assert.isTrue(orders.stream().allMatch(item -> SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId().equals(item.getMerchantId())), "对不起，您没有操作权限");

            checkOrderMerge(orderNums);
            OrderMerge orderMerge = new OrderMerge();
            orderMerge.setPayNum(IdUtils.createOrderNum(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF, 4));
            orderMerge.setMergeNickBy(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getNickName());
            orderMerge.setMergeBy(SecurityUtils.getUsername());
            orderMerge.setMergeById(SecurityUtils.getUserId());

            Optional<Date> maxOrderTimeSign = orders.stream()
                    .map(Order::getOrderTimeSign) // 提取 orderTimeSign 字段
                    .filter(Objects::nonNull) // 过滤掉 null 值
                    .max(Date::compareTo); // 获取最大值
            Date latestOrderTimeSign = maxOrderTimeSign.orElse(null); // 如果为空，则返回 null
            orderMerge.setMergeTime(latestOrderTimeSign);

            orderMerge.setMergeBizUserId(SecurityUtils.getBizUserId());
            orderMerge.setMergeBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
            baseMapper.insert(orderMerge);

            orderMergeDetailService.createOrderMergeDetail(orderMerge.getId(), orderNums);

            //  1、设置支付号
            orderService.setPayNum(orderNums, orderMerge.getPayNum());
            //  2、清除支付类型
            //  3、释放所使用的余额
            orderService.releasePayLock(orders);
            //  4、关闭支付单
            asyncTaskService.closeAllOrder(orderNums);
            return orderMerge.getId();
        } finally {
            for (String orderNum : orderNums) {
                redisService.releaseLock(CacheConstants.ORDER_MERGE_LOCK_KEY + orderNum);
            }
        }
    }

    /**
     * 通过 合并单ID或者支付单号 获取合并订单下的订单号
     */
    @Override
    public List<String> getOrderNumsByMergeIdOrPayNum(Long mergeId, String payNum) {
        return baseMapper.getOrderNumsByMergeIdOrPayNum(mergeId, payNum);
    }

    @Override
    public OrderMerge getOrderMergeByPayNum(String payNum) {
        return baseMapper.getOrderMergeByPayNum(payNum);
    }

}
