package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.OrderPayProperties;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderInvoice;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import com.wnkx.order.mapper.OrderMapper;
import com.wnkx.order.mapper.OrderMemberMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【order_member(订单_会员表)】的数据库操作Service实现
 * @createDate 2024-06-24 16:00:58
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class OrderMemberServiceImpl extends ServiceImpl<OrderMemberMapper, OrderMember> implements IOrderMemberService {

    private final ExchangeRateService exchangeRateService;

    private final RedisService redisService;

    private final OrderMapper orderMapper;

    /**
     * 远程服务
     */
    private final RemoteService remoteService;

    private final IOrderInvoiceService orderInvoiceService;

    private final IOrderMemberService self;

    private final AsyncTaskService asyncTaskService;

    private final OrderPayProperties orderPayProperties;

    private final IOrderPayeeAccountService orderPayeeAccountService;

    private final IOrderPayLogService orderPayLogService;

    private final PromotionActivityService promotionActivityService;
    private final OrderPromotionDetailService orderPromotionDetailService;


    /**
     * 获取会员有效订单
     */
    @Override
    public List<OrderMember> getValidOrderMemberList(Long bizUserId) {
        return baseMapper.getValidOrderMemberList(bizUserId);
    }

    /**
     * 通过订单号获取会员订单
     */
    @Override
    public OrderMember getByOrderNum(String orderNum) {
        return baseMapper.getByOrderNum(orderNum);
    }

    /**
     * 检查是否是首次购买
     *
     * @return true:首次购买 false:非首次购买
     */
    @Override
    public Boolean checkFirstBuy(Long bizUserId, String orderNum) {
        return baseMapper.getBizUserOrderCount(bizUserId, orderNum) == 0;
    }

    @Override
    public MemberUnPayVO getMemberUnPay() {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ot.order_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderMemberVO> orderMembers = Optional.ofNullable(baseMapper.getMemberUnPayOrderNum(
                SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId(),
                SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId()
        )).orElse(Collections.emptyList());
        return MemberUnPayVO.builder()
                .unPayOrderList(orderMembers)
                .unPayNum(orderMembers.size())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderMemberVO createOrder(CreateOrderMemberDTO dto) {
        BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        Assert.isTrue(BusinessStatusEnum.ENABLED.getCode().equals(businessAccountVO.getUserStatus()), "您当前无法创建订单，账号已被禁用~");

        RealTimeExchangeRateVO currentExchange = exchangeRateService.getCurrentExchange();
        //  生成订单号
        String orderNum = IdUtils.createOrderNum(OrderConstant.ORDER_NUM_PREFIX_HY);
        Order order = new Order();
        order.setOrderNum(orderNum);
        order.setOrderAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(dto.getPackageType())).multiply(currentExchange.getRealTimeExchangeRate()).setScale(2, RoundingMode.DOWN));
        order.setOrderAmountDollar(new BigDecimal(PackageTypeEnum.getUSDByCode(dto.getPackageType())));
        order.setPayAmount(order.getOrderAmount());
        order.setPayAmountDollar(new BigDecimal(PackageTypeEnum.getUSDByCode(dto.getPackageType())));
        order.setOrderUserId(SecurityUtils.getUserId());
        order.setOrderUserAccount(businessAccountVO.getAccount());
        order.setOrderUserName(SecurityUtils.getUsername());
        order.setOrderUserNickName(businessAccountVO.getNickName());
        order.setPayUserId(SecurityUtils.getUserId());
        order.setPayUserAccount(businessAccountVO.getAccount());
        order.setPayUserName(SecurityUtils.getUsername());
        order.setPayUserNickName(businessAccountVO.getNickName());
        order.setOrderTime(DateUtil.date());
        order.setTaxPoint(orderPayProperties.getTaxPoint());
        order.setMerchantId(businessAccountVO.getBusinessVO().getId());
        order.setMerchantCode(businessAccountVO.getBusinessVO().getMemberCode());
        order.setOrderType(OrderTypeEnum.VIP_ORDER.getCode());
        order.setBizUserId(businessAccountVO.getBizUserId());
        order.setCurrentExchangeRate(currentExchange.getRealTimeExchangeRate());
        order.setIsDefaultExchangeRate(currentExchange.isDefault());

        //  获取当前商家可参与的活动
        List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS = promotionActivityService.getBusinessParticipatoryActivityVOS();
        if (CollUtil.isNotEmpty(businessParticipatoryActivityVOS)
                && businessParticipatoryActivityVOS.stream().anyMatch(item -> PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode().equals(item.getType()))) {
            BigDecimal payAmountDollar = order.getPayAmountDollar();
            BigDecimal payAmount = order.getPayAmount();

            BusinessParticipatoryActivityVO businessParticipatoryActivityVO = businessParticipatoryActivityVOS.stream().filter(item -> PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode().equals(item.getType())).findFirst().get();

            //  优惠后美金
            BigDecimal theAmountAfterTheDiscountDollar = payAmountDollar.multiply((businessParticipatoryActivityVO.getAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)));
            order.setPayAmountDollar(theAmountAfterTheDiscountDollar.setScale(2, RoundingMode.DOWN));
            order.setPayAmount(theAmountAfterTheDiscountDollar.multiply(currentExchange.getRealTimeExchangeRate()).setScale(2, RoundingMode.DOWN));

            OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
            orderPromotionDetail.setActivityId(businessParticipatoryActivityVO.getId());
            orderPromotionDetail.setOrderNum(order.getOrderNum());
            orderPromotionDetail.setDiscountAmount(payAmount.subtract(order.getPayAmount()));
            orderPromotionDetail.setDiscountAmountDollar(payAmountDollar.subtract(order.getPayAmountDollar()));
            orderPromotionDetail.setDiscountType(businessParticipatoryActivityVO.getDiscountType());
            orderPromotionDetail.setAmount(businessParticipatoryActivityVO.getAmount());
            orderPromotionDetail.setCurrency(businessParticipatoryActivityVO.getCurrency());
            orderPromotionDetailService.saveOrderPromotionDetail(orderPromotionDetail);
        }

        SpringUtils.getBean(IOrderService.class).save(order);

        // 创建会员订单
        OrderMember orderMember = new OrderMember();
        orderMember.setOrderNum(orderNum);
        orderMember.setPackageType(dto.getPackageType());
        orderMember.setBizUserId(SecurityUtils.getBizUserId());
        orderMember.setCreateUserId(SecurityUtils.getUserId());
        orderMember.setCreateUserName(SecurityUtils.getUsername());
        orderMember.setCreateTime(DateUtil.date());
        orderMember.setUpdateUserId(SecurityUtils.getUserId());
        orderMember.setUpdateUserName(SecurityUtils.getUsername());
        orderMember.setUpdateTime(DateUtil.date());
        self.save(orderMember);

        asyncTaskService.addOrderMemberFlow(AddOrderMemberFlowDTO.builder()
                .orderNum(orderNum)
                .eventName(OrderFlowButtonEnum.CREATE_ORDER.getLabel())
                .targetStatus(OrderMemberStatusEnum.UN_PAY.getCode())
                .build(), SecurityUtils.getLoginUserInfoVo());

        OrderMemberVO orderMemberVO = new OrderMemberVO();
        BeanUtil.copyProperties(orderMember, orderMemberVO);
        BeanUtil.copyProperties(order, orderMemberVO);

        orderMemberVO.setId(orderMember.getId());
        orderMemberVO.setOrderId(order.getId());

        return orderMemberVO;
    }

    @Override
    public OrderMemberVO getOrderMember(String orderNum) {
        if (StringUtils.isEmpty(orderNum)) {
            throw new ServiceException("订单号不能为空");
        }
        OrderMemberVO orderMemberVO = new OrderMemberVO();
        OrderMemberDTO dto = new OrderMemberDTO();
        dto.setOrderNum(orderNum);
        OrderMember orderMember = self.queryOne(dto);

        if (StringUtils.isNull(orderMember)) {
            return null;
        }

        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderNum);
        if (StringUtils.isNull(order)) {
            throw new ServiceException("订单数据不能为空");
        }
        BeanUtil.copyProperties(orderMember, orderMemberVO);
        BeanUtil.copyProperties(order, orderMemberVO);

        orderMemberVO.setId(orderMember.getId());
        orderMemberVO.setOrderId(order.getId());

        return orderMemberVO;
    }

    @Override
    public void checkMemberStatus(OrderMemberVO vo, OrderMemberStatusEnum... statusEnum) {
        List<Integer> status = Arrays.stream(statusEnum).map(OrderMemberStatusEnum::getCode).collect(Collectors.toList());
        if (!status.contains(vo.getStatus())) {
            throw new ServiceException(StrUtil.format("会员订单状态异常，需要是：{}，当前是：[{}]，请刷新页面",
                    Arrays.stream(statusEnum).map(OrderMemberStatusEnum::getLabel).collect(Collectors.toList()),
                    OrderMemberStatusEnum.getLabel(vo.getStatus())));
        }
    }

    @Override
    public OrderMemberDetailVO getOrderMemberDetail(String orderNum) {
        if (StringUtils.isEmpty(orderNum)) {
            throw new ServiceException("订单号不能为空");
        }
        OrderMemberVO orderMember = self.getOrderMember(orderNum);
        if (StringUtils.isNull(orderMember)) {
            return null;
        }
        OrderMemberDetailVO orderMemberDetailVO = BeanUtil.copyProperties(orderMember, OrderMemberDetailVO.class);
        // 获取种草优惠数据
        orderMemberDetailVO.setSurplusAmount(orderMemberDetailVO.getPayAmount().subtract(orderMemberDetailVO.getUseBalance() == null ? BigDecimal.ZERO : orderMemberDetailVO.getUseBalance()));
        orderMemberDetailVO.setPackageAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(orderMemberDetailVO.getPackageType())));
        // 获取开票信息
        orderMemberDetailVO.setOrderInvoiceVO(BeanUtil.copyProperties(orderInvoiceService.getLastInvoiceByOrderNum(orderNum), OrderInvoiceVO.class));
        orderMemberDetailVO.setOrderDiscountDetailVOS(orderPromotionDetailService.getOrderDiscountDetailByOrderNum(orderNum));

        // 加载商家信息
        BusinessAccountVO businessAccountVo = remoteService.getBusinessAccountByAccountId(orderMember.getOrderUserId());
        if (StringUtils.isNull(businessAccountVo)) {
            return orderMemberDetailVO;
        }
        BusinessAccountDetailVO businessAccountDetailVo = new BusinessAccountDetailVO();
        BusinessVO businessVO = businessAccountVo.getBusinessVO();
        BeanUtil.copyProperties(businessVO, businessAccountDetailVo);

        BeanUtil.copyProperties(businessAccountVo, businessAccountDetailVo);
        businessAccountDetailVo.setBusinessName(businessVO.getName());
        businessAccountDetailVo.setBusinessStatus(businessVO.getStatus());
        orderMemberDetailVO.setBusinessAccountDetailVO(businessAccountDetailVo);


        orderMemberDetailVO.setCurrentExchangeRate(orderMemberDetailVO.getIsDefaultExchangeRate() ? null : orderMemberDetailVO.getCurrentExchangeRate());
        return orderMemberDetailVO;
    }

    private void loadInvoiceNumber(List<OrderNoteVO> orderNotes) {
        if (CollUtil.isEmpty(orderNotes)) {
            return;
        }
        List<String> orderNums = orderNotes.stream().filter(item -> StrUtil.isNotBlank(item.getOrderNum())).map(OrderNoteVO::getOrderNum).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderNums)) {
            return;
        }

        Map<String, OrderInvoice> orderInvoiceMap = orderInvoiceService.getInvoiceMap(orderNums);
        if (CollUtil.isEmpty(orderInvoiceMap)) {
            return;
        }

        for (OrderNoteVO item : orderNotes) {
            OrderInvoice orderInvoice = orderInvoiceMap.get(item.getOrderNum());
            if (ObjectUtil.isNull(orderInvoice)) {
                continue;
            }
            item.setNumber(orderInvoice.getNumber());
        }
    }

    @Override
    public List<OrderMemberVO> getOrderMemberList(OrderMemberListDTO dto) {
        if (Boolean.FALSE.equals(haveBusiness(dto))) {
            return new ArrayList<>();
        }
        //支付方式填充
        PayTypeEnum.assemblePayType(dto.getPayTypes());
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("sort", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("ot.update_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("om.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderMemberVO> orderMemberList = baseMapper.getOrderMemberList(dto);
        loadOrderMemberDetail(orderMemberList);
        //填充会员开始时间和结束时间
        return orderMemberList;
    }

    @Override
    public List<OrderMemberVO> workbenchFinanceMemberList() {
        List<OrderMemberVO> orderMemberVOS = baseMapper.workbenchFinanceMemberList();
        if (CollUtil.isEmpty(orderMemberVOS)) {
            return Collections.emptyList();
        }
        for (OrderMemberVO item : orderMemberVOS) {
            item.setPackageAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(item.getPackageType())));
        }

        return orderMemberVOS;
    }

    @Override
    public List<MemberReceivableAuditListExportVO> getOrderMemberExportList(List<OrderMemberVO> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());
        List<MemberReceivableAuditListExportVO> resultList = new ArrayList<>();
        for (OrderMemberVO item : list) {
            MemberReceivableAuditListExportVO memberReceivableAuditListExportVO = BeanUtil.copyProperties(item, MemberReceivableAuditListExportVO.class);
            if (AuditStatusEnum.APPROVE.getCode().equals(item.getAuditStatus()) && ObjectUtil.isNotNull(item.getDifferenceAmount())) {
                memberReceivableAuditListExportVO.setDifferenceAmountString((item.getDifferenceAmount().compareTo(BigDecimal.ZERO) > 0 ? "+" : "") + item.getDifferenceAmount().toString());
            }

            if (ObjectUtil.isNull(item.getPayTime())) {
                memberReceivableAuditListExportVO.setRealPayAmount(null);
                memberReceivableAuditListExportVO.setRealPayAmountCurrency(null);
                memberReceivableAuditListExportVO.setCurrency(null);
            } else {
                memberReceivableAuditListExportVO.setCurrency(dictTypeMap.get(Convert.toStr(item.getCurrency())) == null ? CurrencyEnum.CNY.getLabel() : dictTypeMap.get(Convert.toStr(item.getCurrency())));
            }
            memberReceivableAuditListExportVO.setOrderPayeeAccount(getOrderPayeeAccount(item.getOrderPayeeAccountVO()));
            resultList.add(memberReceivableAuditListExportVO);
        }
        return resultList;
    }

    private void loadOrderMemberDetail(List<OrderMemberVO> orderMemberList) {
        if (StringUtils.isNotEmpty(orderMemberList)) {
            List<Long> bizUserId = orderMemberList.stream().filter(item -> ObjectUtil.isNotNull(item.getBizUserId())).distinct().map(OrderMemberVO::getBizUserId).collect(Collectors.toList());
            List<Long> merchantIds = orderMemberList.stream().filter(item -> ObjectUtil.isNotNull(item.getMerchantId())).distinct().map(OrderMemberVO::getMerchantId).collect(Collectors.toList());
            List<String> orderNums = orderMemberList.stream().map(OrderMemberVO::getOrderNum).collect(Collectors.toList());
            //填充商家信息
            BizUserDetailListDTO bizUserDetailListDto = new BizUserDetailListDTO();
            bizUserDetailListDto.setIds(bizUserId);
            List<BizUserDetailVO> bizUserDetailList = remoteService.getBizUserDetailList(bizUserDetailListDto);
            //查询来源渠道
            BizUserDetailListDTO queryUserChannelDto = new BizUserDetailListDTO();
            queryUserChannelDto.setIds(merchantIds);
            queryUserChannelDto.setBizUserIds(bizUserId);
            List<BizUserDetailVO> userChannelList = remoteService.getUserChannel(queryUserChannelDto);
            Map<Long, BizUserDetailVO> businessChannelMap = userChannelList.stream().filter(item -> ObjectUtil.isNotNull(item.getBusinessId())).collect(Collectors.toMap(BizUserDetailVO::getBusinessId, uc -> uc, (e, r) -> e));
            Map<Long, BizUserDetailVO> userChannelMap = userChannelList.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).collect(Collectors.toMap(BizUserDetailVO::getId, uc -> uc, (e, r) -> e));

            Map<Long, BizUserDetailVO> bizUserDetailMap = new HashMap<>(16);
            if (StringUtils.isNotEmpty(bizUserDetailList)) {
                Map<Long, BizUserDetailVO> collect = bizUserDetailList.stream().collect(Collectors.toMap(BizUserDetailVO::getId, p -> p));
                bizUserDetailMap.putAll(collect);
            }
            //获取发票相关信息
            Map<String, OrderInvoice> orderInvoiceMap = orderInvoiceService.getInvoiceMap(orderNums);
            //获取收款账号信息
            List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(new ArrayList<>(orderNums));
            Map<String, OrderPayeeAccount> orderPayeeAccountMap = new HashMap<>();
            if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
                orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, p -> p));
            }

            List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(merchantIds, null);
            Map<Long, BusinessAccountDetailVO> businessMap = RemoteService.RemoteUtil.getMerchantMap(businessAccountDetailVOS);

            //  获取订单参与活动以及优惠金额明细
            List<OrderDiscountDetailVO> orderDiscountDetailList = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
            Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailMap = orderDiscountDetailList.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

            Date date = new Date();
            for (OrderMemberVO item : orderMemberList) {
                BusinessAccountDetailVO businessAccountDetailVO = businessMap.getOrDefault(item.getMerchantId(), BusinessAccountDetailVO.builder().memberValidity(date).build());
                if (OrderMemberStatusEnum.UN_CHECK.getCode().equals(item.getStatus())) {
                    item.setMemberStartTime(businessAccountDetailVO.getMemberValidity().after(date) ? businessAccountDetailVO.getMemberValidity() : date);
                    item.setMemberEndTime(getResultDate(DateUtils.getEndOfDayIgnoreMillisecond(item.getMemberStartTime()), PackageTypeEnum.getMonthByCode(item.getPackageType())
                            , Optional.ofNullable(item.getPresentedTime()).orElse(0)
                            , Optional.ofNullable(item.getPresentedTimeType()).orElse(PresentedTimeTypeEnum.MONTH.getCode())
                    ));
                }
                if (ObjectUtil.isNotNull(businessAccountDetailVO.getBusinessId())) {
                    item.setBusinessName(businessAccountDetailVO.getBusinessName());
                    item.setMemberCode(businessAccountDetailVO.getMemberCode());
                    item.setBusinessAccount(businessAccountDetailVO.getOwnerAccount());
                } else {
                    item.setBusinessName("");
                    item.setMemberCode("");
                    item.setBusinessAccount("");
                }
                if (item.getIsDefaultExchangeRate()) {
                    item.setCurrentExchangeRate(null);
                }
                item.setSurplusAmount(item.getPayAmount().subtract(item.getUseBalance() == null ? BigDecimal.ZERO : item.getUseBalance()));
                item.setSurplusAmountDollar(item.getSurplusAmount().compareTo(item.getPayAmount()) == 0 ? item.getPayAmountDollar() : item.getSurplusAmount().divide(item.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
                item.setPackageAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(item.getPackageType())));
                BizUserDetailVO bizUserDetailVO = bizUserDetailMap.get(item.getBizUserId());
                if (StringUtils.isNotNull(bizUserDetailVO)) {
//                    item.setBusinessName(bizUserDetailVO.getAccountBusinessName());
                    item.setNickName(bizUserDetailVO.getNickName());
//                    item.setMemberCode(bizUserDetailVO.getMemberCode());
                    // 下单人主账号就是商家账号
//                    item.setBusinessAccount(bizUserDetailVO.getOwnerAccount());
                    item.setPhone(bizUserDetailVO.getPhone());
                    item.setConnectUserName(bizUserDetailVO.getConnectUserName());
                }
                if (StringUtils.isNotEmpty(orderInvoiceMap) && StringUtils.isNotNull(orderInvoiceMap.get(item.getOrderNum()))) {
                    item.setOrderInvoice(BeanUtil.copyProperties(orderInvoiceMap.get(item.getOrderNum()), OrderInvoiceVO.class));
                }

                if (StringUtils.isNotEmpty(orderPayeeAccountMap) && ObjectUtil.isNotNull(orderPayeeAccountMap.get(item.getOrderNum()))) {
                    item.setOrderPayeeAccountVO(BeanUtil.copyProperties(orderPayeeAccountMap.get(item.getOrderNum()), OrderPayeeAccountVO.class));

                }
                BizUserDetailVO businessChannel = businessChannelMap.get(item.getMerchantId());
                if (ObjectUtil.isNotNull(businessChannel)) {
                    loadUserChannel(item, businessChannel);

                } else {
                    BizUserDetailVO bizUserChannel = userChannelMap.get(item.getBizUserId());
                    if (ObjectUtil.isNotNull(bizUserChannel)) {
                        loadUserChannel(item, bizUserChannel);
                    }
                }
                item.setOrderDiscountDetailVOS(orderDiscountDetailMap.get(item.getOrderNum()));
            }
        }
    }

    private void loadUserChannel(OrderMemberVO item, BizUserDetailVO bizUserChannel) {
        item.setRegisterChannelType(StringUtils.isNotNull(bizUserChannel.getRegisterChannelType()) ? bizUserChannel.getRegisterChannelType() : null);
        item.setWechatChannelType(StringUtils.isNotNull(bizUserChannel.getWechatChannelType()) ? bizUserChannel.getWechatChannelType() : null);
        item.setWechatChannelStringType(StrUtil.isNotBlank(bizUserChannel.getChannelName()) ? bizUserChannel.getChannelName() : null);
        item.setAddWechatTime(bizUserChannel.getAddWechatTime() != null ? bizUserChannel.getAddWechatTime() : null);
    }

    @Override
    public List<OrderMemberVO> getBackendOrderMemberList(OrderMemberListDTO dto) {
        if (Boolean.FALSE.equals(haveBusinessNew(dto))) {
            return new ArrayList<>();
        }
        PayTypeEnum.assemblePayType(dto.getPayTypes());
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ot.order_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderMemberVO> orderMemberList = baseMapper.getBackendOrderMemberList(dto);
        loadOrderMemberDetail(orderMemberList);
        return orderMemberList;
    }

    @Override
    public BigDecimal getBackendOrderMemberListByPayTime(OrderMemberListDTO dto) {
        List<OrderMemberVO> orderMemberList = baseMapper.getBackendOrderMemberList(dto);
        return orderMemberList.stream().map(OrderMemberVO::getRealPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<OrderMemberExportVO> getBackendOrderMemberListExport(List<OrderMemberVO> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());
        List<OrderMemberExportVO> orderMemberExportVOs = new ArrayList<>();
        String lf = StrPool.LF;
        for (OrderMemberVO item : list) {
            OrderMemberExportVO orderMemberExportVO = new OrderMemberExportVO();
            BeanUtils.copyProperties(item, orderMemberExportVO);
            if (ObjectUtil.isNull(item.getPayTime())) {
                orderMemberExportVO.setRealPayAmount(null);
                orderMemberExportVO.setRealPayAmountCurrency(null);
                orderMemberExportVO.setCurrency(null);
            } else {
                orderMemberExportVO.setCurrency(dictTypeMap.get(Convert.toStr(item.getCurrency())) == null ? CurrencyEnum.CNY.getLabel() : dictTypeMap.get(Convert.toStr(item.getCurrency())));
            }
            orderMemberExportVO.setOrderPayeeAccount(getOrderPayeeAccount(item.getOrderPayeeAccountVO()));
            orderMemberExportVO.setCurrentExchangeRate(item.getIsDefaultExchangeRate() ? null : item.getCurrentExchangeRate());
            if (CollUtil.isNotEmpty(item.getOrderDiscountDetailVOS())) {
                StringBuilder builder = StrUtil.builder();
                for (OrderDiscountDetailVO orderDiscountDetailVO : item.getOrderDiscountDetailVOS()) {

                    orderMemberExportVO.setOrderDiscountChannelName(StrUtil.isNotBlank(orderDiscountDetailVO.getChannelName()) ? orderDiscountDetailVO.getChannelName() : null);
                    orderMemberExportVO.setOrderDiscountDiscountRatio(orderDiscountDetailVO.getDiscountRatio().toString() + "%");
                    orderMemberExportVO.setOrderDiscountDiscountAmount(orderDiscountDetailVO.getDiscountAmount().toString());
                    builder.append("优惠类型：");
                    if (PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode().equals(orderDiscountDetailVO.getType())) {
                        builder.append("半价续费").append(lf);
                        orderMemberExportVO.setOrderDiscountType("半价续费");
                    } else if (PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode().equals(orderDiscountDetailVO.getType())) {
                        builder.append("满5单减100").append(lf);
                        orderMemberExportVO.setOrderDiscountType("满5单减100");
                    } else if (PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode().equals(orderDiscountDetailVO.getType())) {
                        builder.append("渠道优惠").append(lf);
                        orderMemberExportVO.setOrderDiscountType("渠道优惠");
                        orderMemberExportVO.setMemberDiscount(ChannelDiscountTypeEnum.getLabel(item.getMemberDiscountType()));
                        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(item.getMemberDiscountType())) {
                            orderMemberExportVO.setOrderDiscountDiscountRatio(orderDiscountDetailVO.getDiscountRatio().toString());
                        }
                    } else if (PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode().equals(orderDiscountDetailVO.getType())) {
                        builder.append("裂变优惠").append(lf);
                        orderMemberExportVO.setOrderDiscountType("裂变优惠");
                        orderMemberExportVO.setMemberDiscount(ChannelDiscountTypeEnum.getLabel(item.getMemberDiscountType()));
                        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(item.getMemberDiscountType())) {
                            orderMemberExportVO.setOrderDiscountDiscountRatio(orderDiscountDetailVO.getDiscountRatio().toString());
                        }
                    }
                    if (CharSequenceUtil.isNotBlank(orderDiscountDetailVO.getChannelName())) {
                        if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(orderDiscountDetailVO.getChannelType())) {
                            builder.append("渠道");
                        } else if (ChannelTypeEnum.FISSION.getCode().equals(orderDiscountDetailVO.getChannelType())) {
                            builder.append("裂变");
                        }
                        builder.append("名称：").append(orderDiscountDetailVO.getChannelName()).append(lf);
                    }
                    builder.append("会员折扣：").append(orderDiscountDetailVO.getDiscountRatio()).append("%").append(lf);
                    builder.append("优惠金额：").append("￥").append(orderDiscountDetailVO.getDiscountAmount()).append(lf).append(lf);
                }
                orderMemberExportVO.setOrderDiscountDetail(builder.toString());
            }
            if (ObjectUtil.isNotNull(item.getChannelType()) && ChannelTypeEnum.FISSION.getCode().equals(item.getChannelType())) {
                if (ObjectUtil.isNull(item.getSeedMemberStatus())) {
                    orderMemberExportVO.setSeedMemberStatus(MemberTypeEnum.NO_RECHARGE.getCode());
                }
            }
            orderMemberExportVOs.add(orderMemberExportVO);
        }

        return orderMemberExportVOs;
    }

    private String getOrderPayeeAccount(OrderPayeeAccountVO item) {
        if (ObjectUtil.isNull(item)) {
            return StrPool.DASHED;
        }
        if (ObjectUtil.isNull(item.getAccountType())) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        if (List.of(PayTypeEnum.BANK.getCode(), PayTypeEnum.BANK_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append("银行卡号：");
            sb.append(item.getBankAccount());

            sb.append("\n姓名：");
            sb.append(item.getAccountName());

            sb.append("\n开户行：");
            sb.append(item.getBankName());
        } else if (List.of(PayTypeEnum.PUBLIC.getCode(), PayTypeEnum.PUBLIC_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append(" 收款公司名称：");
            sb.append(item.getAccountName());

            sb.append("\n收款银行账号：");
            sb.append(item.getBankAccount());

            sb.append("\n开户行名称：");
            sb.append(item.getBankName());
        } else if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append("账户名：");
            sb.append(item.getAccountName());

            sb.append("\n收款账户类型：");
            sb.append(item.getCompanyAccountType());

        } else {
            sb.append("收款主体：");
            sb.append(item.getAccountName());

            sb.append("\n商户号：");
            sb.append(item.getBankAccount());
        }
        return sb.toString();
    }

    /**
     * 如果查询商家为空则 则直接返回
     *
     * @param dto
     * @return 返回ture则继续进行  如果返回false则退出
     */
    private Boolean haveBusiness(OrderMemberListDTO dto) {
        if (StrUtil.isNotBlank(dto.getNickName()) || StrUtil.isNotBlank(dto.getBusinessAccount()) || StrUtil.isNotBlank(dto.getMemberCode())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            if (StrUtil.isNotBlank(dto.getBusinessAccount())) {
                businessAccountDetailDTO.setAccount(dto.getBusinessAccount());
            }
            if (StrUtil.isNotBlank(dto.getNickName())) {
                businessAccountDetailDTO.setNickName(dto.getNickName());
            }
            if (StrUtil.isNotBlank(dto.getMemberCode())) {
                businessAccountDetailDTO.setMemberCode(dto.getMemberCode());
            }
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchant(businessAccountDetailDTO);

            if (CollUtil.isNotEmpty(businessList)) {
                Set<Long> merchantIds = businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toSet());
                dto.setBusinessIds(merchantIds);
            } else {
                return false;
            }
        }

        if (StrUtil.isNotBlank(dto.getSearchName())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setSearchNameMemberCodeAccount(dto.getSearchName());
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchant(businessAccountDetailDTO);
            if (CollUtil.isNotEmpty(businessList)) {
                Set<Long> merchantIds = businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toSet());
                dto.setSearchBusinessIds(merchantIds);
            }
        }
        //  如果是商家 查询自己的订单
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE)) {
            if (StatusTypeEnum.YES.getCode().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getIsMock())) {
                Long bizUserId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId();
                dto.setBizUserIds(Arrays.asList(bizUserId));
                dto.setBusinessIds(Collections.singleton(0L));
            } else {
                Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
                dto.setBusinessIds(Collections.singleton(businessId));
            }
        }

        return true;
    }

    /**
     * 新拉一个方法出来（原先方法另一处使用到了，同时另一处方法被其他三处引用到）
     *
     * @param dto
     * @return
     */
    private Boolean haveBusinessNew(OrderMemberListDTO dto) {
        if (StrUtil.isNotBlank(dto.getNickName()) || StrUtil.isNotBlank(dto.getBusinessAccount()) || StrUtil.isNotBlank(dto.getMemberCode())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            if (StrUtil.isNotBlank(dto.getBusinessAccount())) {
                businessAccountDetailDTO.setAccount(dto.getBusinessAccount());
            }
            if (StrUtil.isNotBlank(dto.getNickName())) {
                businessAccountDetailDTO.setNickName(dto.getNickName());
            }
            if (StrUtil.isNotBlank(dto.getMemberCode())) {
                businessAccountDetailDTO.setMemberCode(dto.getMemberCode());
            }
            List<BusinessAccountDetailVO> businessList = remoteService.queryUserInfo(businessAccountDetailDTO);

            if (CollUtil.isNotEmpty(businessList)) {
                List<Long> ids = businessList.stream().map(BusinessAccountDetailVO::getBizUserId).collect(Collectors.toList());
                dto.setBizUserIds(ids);
            } else {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void flowOrderMember(FlowOrderMemberDTO dto) {
        //锁定订单
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MEMBER_FLOW_KEY + dto.getOrderNum(), 60L), "订单流程正在进行，无法再次处理！");
        //获取订单数据
        try {
            OrderMemberVO orderMember = self.getOrderMember(dto.getOrderNum());
            Assert.notNull(orderMember, "订单信息不存在！");

            //检查是否能流转
            this.checkMemberStatusFlow(orderMember, dto.getOrderMemberStatusEnum());
            OrderMemberDTO orderMemberDTO = new OrderMemberDTO();
            orderMemberDTO.setUpdateUserId(SecurityUtils.getUserId());
            orderMemberDTO.setUpdateUserName(SecurityUtils.getUsername());
            orderMemberDTO.setUpdateTime(new Date());
            orderMemberDTO.setOrderNum(dto.getOrderNum());
            orderMemberDTO.setStatus(dto.getOrderMemberStatusEnum().getCode());

            if (OrderMemberStatusEnum.UN_CHECK.getCode().equals(dto.getOrderMemberStatusEnum().getCode())) {
                fillPresentedTime(orderMember, orderMemberDTO);
            }

            Order orderByOrderNum = orderMapper.getOrderByOrderNum(orderMember.getOrderNum());
            if (OrderMemberStatusEnum.UN_CONFIRM.getCode().equals(dto.getOrderMemberStatusEnum().getCode())) {
                //获取商家数据
                if (OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMember.getStatus())) {
                    //填充加赠时间
                    fillPresentedTime(orderMember, orderMemberDTO);
                    fillMemberDate(orderMember, orderMemberDTO, StatusTypeEnum.NO.getCode());
                } else {
                    fillMemberDate(orderMember, orderMemberDTO, StatusTypeEnum.YES.getCode());
                }

                orderMemberDTO.setIsFirstBuy(StrUtil.isNotBlank(orderByOrderNum.getMerchantCode()) ? OtherStatusEnum.UN_FIRST_BUY.getCode() : OtherStatusEnum.FIRST_BUY.getCode());
            }
            self.update(orderMemberDTO);
            if (OrderMemberStatusEnum.UN_CONFIRM.getCode().equals(dto.getOrderMemberStatusEnum().getCode())) {
                FlowMemberDto flowMemberDto = new FlowMemberDto();
                flowMemberDto.setBusinessId(orderMember.getMerchantId());
                flowMemberDto.setBizUserId(orderMember.getBizUserId());
                flowMemberDto.setChoosePackageType(orderMember.getPackageType());
                flowMemberDto.setChooseMemberStatus(MemberTypeEnum.RECHARGE.getCode());
                flowMemberDto.setUseBalance(orderMember.getUseBalance());
                flowMemberDto.setOrderNum(orderMember.getOrderNum());
                flowMemberDto.setPayTime(orderByOrderNum.getPayTime());
                if (OrderMemberStatusEnum.UN_CHECK.getCode().equals(orderMember.getStatus())) {
                    flowMemberDto.setPresentedTimeType(orderMember.getPresentedTimeType());
                    flowMemberDto.setPresentedTime(orderMember.getPresentedTime());
                } else if (OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMember.getStatus())) {
                    flowMemberDto.setPresentedTimeType(orderMemberDTO.getPresentedTimeType());
                    flowMemberDto.setPresentedTime(orderMemberDTO.getPresentedTime());
                }

                if (ObjectUtil.isNotNull(orderByOrderNum)) {
                    // 新增order_member_channel记录
                    flowMemberDto.setChannelType(ObjectUtil.isNotNull(orderByOrderNum.getChannelType()) ? orderByOrderNum.getChannelType() : null);
                    flowMemberDto.setSeedCode(StrUtil.isNotBlank(orderByOrderNum.getSeedCode()) ? orderByOrderNum.getSeedCode() : "");
                    flowMemberDto.setOrderAmount(orderByOrderNum.getOrderAmount());
                    flowMemberDto.setRealPayAmount(orderByOrderNum.getRealPayAmount());
                    flowMemberDto.setRealPayAmountCurrency(orderByOrderNum.getRealPayAmountCurrency());
                    flowMemberDto.setCurrency(orderByOrderNum.getCurrency());
                    flowMemberDto.setPayType(orderByOrderNum.getPayType());
                    flowMemberDto.setTaxPointCost(orderByOrderNum.getTaxPointCost());
                }
                BusinessAccountVO result = SpringUtils.getBean(IOrderService.class).flowMember(flowMemberDto);

                if (orderMember.getMerchantId().compareTo(0L) == 0) {
                    //只有初始化商家才需要处理
                    try {
                        orderMapper.setMerchant(result.getBusinessVO().getMemberCode(), result.getBusinessVO().getId(), orderMember.getBizUserId(), result.getId());
                        baseMapper.setBusinessInfo(orderMember.getBizUserId(), result.getId(), result.getName());
                        List<Order> orderList = orderMapper.selectListByBizUserIds(Arrays.asList(orderMember.getBizUserId()));
                        if (CollUtil.isNotEmpty(orderList)) {
                            orderInvoiceService.setMerchant(orderList.stream().map(Order::getOrderNum).collect(Collectors.toList()), result.getBusinessVO().getId());
                        }
                        orderPayLogService.setBusinessId(orderMember.getOrderNum(), result.getBusinessVO().getId());

                    } catch (Exception e) {
                        log.warn("订单流转成功，回填商家数据失败：{}", e);
                        //删除商家
                        remoteService.deleteBusiness(result.getBusinessId());
                        throw new ServiceException("状态流转异常！");
                    }
                }
            }
            asyncTaskService.addOrderMemberFlow(AddOrderMemberFlowDTO.builder()
                    .orderNum(dto.getOrderNum())
                    .eventName(dto.getOrderFlowButtonEnum().getLabel())
                    .originStatus(orderMember.getStatus())
                    .targetStatus(dto.getOrderMemberStatusEnum().getCode())
                    .build(), SecurityUtils.getLoginUserInfoVo());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MEMBER_FLOW_KEY + dto.getOrderNum());
        }
    }

    private void fillMemberDate(OrderMemberVO orderMember, OrderMemberDTO orderMemberDTO, Integer tableInfo) {
        BusinessAccountVO businessAccountVO = remoteService.getBusinessAccountWithActivityByAccountId(orderMember.getOrderUserId(), orderMember.getPackageType());
        //如果是交易成功 需要设置订单开始时间
        if (StringUtils.isNotNull(businessAccountVO)
                && StringUtils.isNotNull(businessAccountVO.getBusinessVO())
                && StringUtils.isNotNull(businessAccountVO.getBusinessVO().getMemberValidity())
                && businessAccountVO.getBusinessVO().getMemberValidity().after(new Date())
        ) {
            orderMemberDTO.setMemberStartTime(businessAccountVO.getBusinessVO().getMemberValidity());
        } else {
            orderMemberDTO.setMemberStartTime(new Date());
        }
        Integer monthNum = PackageTypeEnum.getMonthByCode(orderMember.getPackageType());
        if (StatusTypeEnum.YES.getCode().equals(tableInfo)) {
            orderMemberDTO.setMemberEndTime(DateUtils.getEndOfDayIgnoreMillisecond(getResultDate(orderMemberDTO.getMemberStartTime(), monthNum
                    , Optional.ofNullable(orderMember.getPresentedTime()).orElse(0)
                    , Optional.ofNullable(orderMember.getPresentedTimeType()).orElse(PresentedTimeTypeEnum.MONTH.getCode())
            )));
        } else {
            orderMemberDTO.setMemberEndTime(DateUtils.getEndOfDayIgnoreMillisecond(getResultDate(orderMemberDTO.getMemberStartTime(), monthNum
                    , Optional.ofNullable(orderMemberDTO.getPresentedTime()).orElse(0)
                    , Optional.ofNullable(orderMemberDTO.getPresentedTimeType()).orElse(PresentedTimeTypeEnum.MONTH.getCode())
            )));
        }

    }

    private void fillPresentedTime(OrderMemberVO orderMember, OrderMemberDTO orderMemberDTO) {
        BusinessMemberActivity businessMemberActivity = remoteService.getBusinessMemberActivity(orderMember.getPackageType());
        if (ObjectUtil.isNotNull(businessMemberActivity) && StatusTypeEnum.YES.getCode().equals(businessMemberActivity.getStatus())) {
            orderMemberDTO.setPresentedTime(businessMemberActivity.getPresentedTime());
            orderMemberDTO.setPresentedTimeType(businessMemberActivity.getPresentedTimeType());
        }
    }


    public Date getResultDate(Date originDate, Integer month, Integer presentedTime, Integer presentedTimeType) {
        Date resultDate = DateUtils.addMonths(originDate, month);
        if (presentedTime.compareTo(0) == 0) {
            return resultDate;
        }
        if (PresentedTimeTypeEnum.DAY.getCode().equals(presentedTimeType)) {
            return DateUtils.addDays(resultDate, presentedTime);
        } else if (PresentedTimeTypeEnum.MONTH.getCode().equals(presentedTimeType)) {
            return DateUtils.addMonths(resultDate, presentedTime);
        } else if (PresentedTimeTypeEnum.YEAR.getCode().equals(presentedTimeType)) {
            return DateUtils.addYears(resultDate, presentedTime);
        } else {
            log.error("加赠时间类型错误：{}", presentedTimeType);
        }

        return resultDate;
    }

    /**
     * 状态流转判断 不存在流转为待支付状态(会员订单状态)
     *
     * @param orderMember           当前订单状态
     * @param orderMemberStatusEnum 选择流转状态（无待支付状态）
     */
    public void checkMemberStatusFlow(OrderMemberVO orderMember, OrderMemberStatusEnum orderMemberStatusEnum) {
        //如果选择待审核
        Assert.isFalse(OrderMemberStatusEnum.UN_CHECK.getCode().equals(orderMember.getStatus()) && OrderMemberStatusEnum.UN_CHECK.getCode().equals(orderMemberStatusEnum.getCode()), "待审核的数据无法流转为待审核状态");
        //交易成功状态 无法流转为其他状态
        Assert.isFalse(OrderMemberStatusEnum.UN_CONFIRM.getCode().equals(orderMember.getStatus()), "交易成功的数据无法流转为" + orderMemberStatusEnum.getLabel() + "，请刷新页面");
        //如果当前是交易关闭状态 无法流转为其他状态
        Assert.isFalse(OrderMemberStatusEnum.UN_MATCH.getCode().equals(orderMember.getStatus()), "交易关闭的数据无法流转为" + orderMemberStatusEnum.getLabel() + "，请刷新页面");

        Assert.isFalse(OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMemberStatusEnum.getCode()), "状态无法转换为待支付，请刷新页面");
    }

    @Override
    public OrderMember queryOne(OrderMemberDTO dto) {
        return self.lambdaQuery()
                .eq(StringUtils.isNotEmpty(dto.getOrderNum()), OrderMember::getOrderNum, dto.getOrderNum())
                .one();
    }

    @Override
    public void update(OrderMemberDTO dto) {
        if (StringUtils.isNull(dto.getId())
                && StringUtils.isEmpty(dto.getOrderNum())) {
            throw new ServiceException("修改会员表数据，修改条件不能都为空");
        }
        self.lambdaUpdate()
                .set(StringUtils.isNotNull(dto.getStatus()), OrderMember::getStatus, dto.getStatus())
                .set(StringUtils.isNotNull(dto.getPackageType()), OrderMember::getPackageType, dto.getPackageType())
                .set(StringUtils.isNotNull(dto.getMemberStartTime()), OrderMember::getMemberStartTime, dto.getMemberStartTime())
                .set(StringUtils.isNotNull(dto.getUpdateTime()), OrderMember::getUpdateTime, dto.getUpdateTime())
                .set(StringUtils.isNotNull(dto.getUpdateUserId()), OrderMember::getUpdateUserId, dto.getUpdateUserId())
                .set(StringUtils.isNotNull(dto.getUpdateUserName()), OrderMember::getUpdateUserName, dto.getUpdateUserName())
                .set(StringUtils.isNotNull(dto.getMemberEndTime()), OrderMember::getMemberEndTime, dto.getMemberEndTime())
                .set(StringUtils.isNotNull(dto.getPresentedTime()), OrderMember::getPresentedTime, dto.getPresentedTime())
                .set(StringUtils.isNotNull(dto.getPresentedTimeType()), OrderMember::getPresentedTimeType, dto.getPresentedTimeType())
                .set(StringUtils.isNotNull(dto.getIsFirstBuy()), OrderMember::getIsFirstBuy, dto.getIsFirstBuy())
                .eq(StringUtils.isNotNull(dto.getId()), OrderMember::getId, dto.getId())
                .eq(StringUtils.isNotNull(dto.getOrderNum()), OrderMember::getOrderNum, dto.getOrderNum())
                .update();
    }

    @Override
    public List<OrderMember> getByOrderNums(List<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return new ArrayList<>();
        }
        return baseMapper.getByOrderNums(orderNums);
    }

    @Override
    public List<OrderMember> queryList(OrderMemberDTO dto) {
        return self.lambdaQuery()
                .eq(StringUtils.isNotNull(dto.getStatus()), OrderMember::getStatus, dto.getStatus())
                .eq(StringUtils.isNotNull(dto.getBizUserId()), OrderMember::getBizUserId, dto.getBizUserId())
                .eq(StringUtils.isNotNull(dto.getCreateUserId()), OrderMember::getCreateUserId, dto.getCreateUserId())
                .list();
    }

    @Override
    public void initUserOrderMemberFlag() {
        baseMapper.updateOrderMemberBuyFlag();
    }

}
