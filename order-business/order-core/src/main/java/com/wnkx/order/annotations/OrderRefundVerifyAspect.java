package com.wnkx.order.annotations;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.exception.PreAuthorizeException;
import com.wnkx.order.service.IOrderVideoRefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderRefundVerifyAspect {

    // 定义一个 SpEL 解析器
    private final ExpressionParser parser = new SpelExpressionParser();
    private final IOrderVideoRefundService orderVideoRefundService;

    @Around("@annotation(com.wnkx.order.annotations.OrderRefundVerify)")
    public Object orderRefundVerify(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的 OrderVideoFlow 注解
        OrderRefundVerify orderRefundVerify = method.getAnnotation(OrderRefundVerify.class);

        // 获取方法入参
        Object[] args = joinPoint.getArgs();
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] params = discoverer.getParameterNames(method);
        if (params == null) {
            return joinPoint.proceed();
        }

        String[] paramNames = signature.getParameterNames();

        // 创建 SpEL 上下文
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 将方法参数加入到 SpEL 上下文中
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }

        // 解析 SpEL 表达式，获取 videoId 的值
        final List<Long> videoIds = parser.parseExpression(orderRefundVerify.videoId()).getValue(context, List.class);

        List<Long> refundVideoIds = orderVideoRefundService.selectVideoIdByRefund(videoIds);
        Assert.isTrue(CollUtil.isEmpty(refundVideoIds), () -> new PreAuthorizeException(ErrorConstants.REFUND_ERROR_TIPS));

        return joinPoint.proceed();
    }
}
