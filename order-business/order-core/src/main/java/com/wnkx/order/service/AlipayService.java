package com.wnkx.order.service;

import com.ruoyi.common.core.enums.PayTranStatusEnum;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28 14:49
 */
public interface AlipayService {

    /**
     * 通过内部订单号关闭支付宝订单
     */
    void closeAlipayOrder(String orderNum, String appId);

    /**
     * 支付宝支付回调
     */
    String alipayCallback(HttpServletRequest request);

    /**
     * 调用支付宝电脑网站支付
     */
    CreatePayVo alipayWebPay(Boolean isMerge, String payNum, List<String> orderNums, String appId, BigDecimal finalPayAmount, Long payUserId);


    /**
     * 支付宝查单
     */
    PayTranStatusEnum checkOrderStatus(String orderNumber, String alipayPayAppId);
}
