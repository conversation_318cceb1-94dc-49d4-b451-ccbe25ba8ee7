package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.UploadCredentialDTO;
import com.ruoyi.system.api.domain.entity.order.OrderDocumentResource;

import java.util.List;

/**
 * 订单支付凭证关联Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IOrderDocumentResourceService extends IService<OrderDocumentResource> {
    /**
     * 查询订单支付凭证关联
     *
     * @param id 订单支付凭证关联主键
     * @return 订单支付凭证关联
     */
    public OrderDocumentResource selectOrderDocumentResourceById(Long id);

    /**
     * 查询订单支付凭证关联列表
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 订单支付凭证关联集合
     */
    public List<OrderDocumentResource> selectOrderDocumentResourceList(OrderDocumentResource orderDocumentResource);

    /**
     * 根据keyword获取支付凭证
     * @param keyword 支付单号、订单号
     * @return
     */
    List<OrderDocumentResource> selectOrderDocumentResourceListByKeyword(String keyword);

    /**
     * 新增订单支付凭证关联
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    public int insertOrderDocumentResource(OrderDocumentResource orderDocumentResource);

    /**
     * 修改订单支付凭证关联
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    public int updateOrderDocumentResource(OrderDocumentResource orderDocumentResource);

    /**
     * 批量删除订单支付凭证关联
     *
     * @param ids 需要删除的订单支付凭证关联主键集合
     * @return 结果
     */
    public int deleteOrderDocumentResourceByIds(Long[] ids);

    /**
     * 删除订单支付凭证关联信息
     *
     * @param id 订单支付凭证关联主键
     * @return 结果
     */
    public int deleteOrderDocumentResourceById(Long id);

    /**
     * 上传凭证信息
     */
    List<OrderDocumentResource> uploadCredential(UploadCredentialDTO uploadCredentialDTO);
}
