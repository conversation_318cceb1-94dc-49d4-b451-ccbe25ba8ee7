package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityAmendmentRecordVO;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-05-21 14:34:23 
 */
public interface PromotionActivityAmendmentRecordService extends IService<PromotionActivityAmendmentRecord> {

    /**
     * 新增活动修改记录
     */
    void savePromotionActivityAmendmentRecord(PromotionActivityAmendmentRecord promotionActivityAmendmentRecord);


    /**
     * 通过活动类型获取修改记录
     */
    List<PromotionActivityAmendmentRecordVO> selectPromotionActivityAmendmentRecordList(Integer type);
}
