package com.wnkx.order.service.delay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.DelayQueueConstant;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack;
import com.wnkx.order.service.IOrderService;
import com.wnkx.order.service.IOrderVideoService;
import com.wnkx.order.service.OrderVideoFeedBackService;
import com.wnkx.order.service.delay.OrderQueueHandleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 订单自动完成处理
 *
 * <AUTHOR>
 * @date 2024/7/9 9:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderAutoFinishedHandle implements OrderQueueHandleService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processTask(String json) {
        Long videoId = Long.parseLong(json);
        log.debug("订单自动完成处理队列{}任务处理，videoId：{}", DelayQueueConstant.ORDER_AUTO_FINISHED_QUEUE_NAME, videoId);

        //  任务处理逻辑。。。
        OrderVideo orderVideo = SpringUtils.getBean(IOrderVideoService.class).getById(videoId);
        if (ObjectUtil.isNull(orderVideo)) {
            log.warn("订单自动完成处理队列{}任务处理失败，视频订单不存在，videoId：{}", DelayQueueConstant.ORDER_AUTO_FINISHED_QUEUE_NAME, videoId);
        }
        if (!OrderStatusEnum.NEED_CONFIRM.getCode().equals(orderVideo.getStatus())) {
            log.debug("当前视频订单无需处理，状态不是需确认，videoId：{}", videoId);
            return;
        }

        List<OrderVideoFeedBack> feedBackList = SpringUtils.getBean(OrderVideoFeedBackService.class).getFeedBackList(videoId, orderVideo.getRollbackId());
        if (feedBackList.stream().allMatch(orderFeedBack -> ObjectUtil.isNull(orderFeedBack.getOverTime()))) {
            log.debug("当前视频订单无需处理，已取消自动确认收货，videoId：{}", videoId);
            return;
        }

        SpringUtils.getBean(IOrderService.class).createOrderFlow(orderVideo, OrderStatusEnum.FINISHED, "系统执行订单自动流转");
    }

    @Override
    public String getQueueName() {
        return DelayQueueConstant.ORDER_AUTO_FINISHED_QUEUE_NAME;
    }
}
