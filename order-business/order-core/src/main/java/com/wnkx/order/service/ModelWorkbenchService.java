package com.wnkx.order.service;

import com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchForMeListVO;

import java.util.List;

public interface ModelWorkbenchService {

    /**
     * 模特端-工作台  给我的
     */
    List<OrderModelWorkbenchForMeListVO> forMe(Long videoId);


    /**
     * 模特端-工作台  模特接单
     */
    void accept(Long videoId, Long preselectModelId);


    /**
     * 模特端-工作台  模特拒绝订单
     */
    void pass(Long videoId, Long preselectModelId, String remark);

    /**
     * 模特端-首页-forMe列表数量统计
     */
    Long getForMeCount();
}
