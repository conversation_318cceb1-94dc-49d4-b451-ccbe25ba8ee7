package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.ModelOrderDTO;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface IOrderVideoModelService extends IService<OrderVideoModel> {

    /**
     * 获取逾期未反馈素材的模特
     * @return 逾期的模特id
     */
    List<Long> checkModelOverdueVideo(Collection<Long> modelId);

    /**
     * 获取订单关联模特map
     */
    Map<Long, List<OrderVideoModel>> getOrderModelByModelId(Collection<Long> modelIds);

    /**
     * 确认模特接单时间
     *
     * @param videoIds  视频订单id
     */
    void acceptModelOrder(List<Long> videoIds);

    /**
     * 通过视频订单ID删除模特关联订单
     */
    void deleteOrderVideoModelByVideoId(Long videoId);

    /**
     * 添加模特关联订单
     */
    void addOrderVideoModel(List<ModelOrderDTO> modelOrderDTOS);

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 有逾期未反馈素材和无法接单的模特id
     */
    List<Long> checkAbnormalModelId(Collection<Long> modelId);

    /**
     * 查询传入的模特是否可以接单
     *
     * @return 无法接单的模特
     */
    List<Long> checkModelAcceptability(Collection<Long> modelId);

    /**
     * 根据视频订单id查询接单模特
     */
    OrderVideoModel getModelIdByVideoId(Long videoId);

    /**
     * 获取模特超时率、售后率
     */
    List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate();

    /**
     * 获取模特已接收的订单 不包括已完成、订单关闭
     */
    Map<Long, List<OrderVideoModel>> getUnfinishedOrderModelByModelId(Collection<Long> modelIds);

    /***
     * *获取订单模特信息
     * @param videoIds
     * @return
     */
    List<OrderVideoModel> getListByVideoIds(Collection<Long> videoIds);
}
