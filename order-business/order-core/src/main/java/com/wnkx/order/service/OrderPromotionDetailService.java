package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_promotion_detail】的数据库操作Service
 * @createDate 2025-03-19 15:50:19
 */
public interface OrderPromotionDetailService extends IService<OrderPromotionDetail> {

    /**
     * 新增订单使用活动明细
     */
    void saveOrderPromotionDetail(OrderPromotionDetail orderPromotionDetail);

    /**
     * 新增订单使用活动明细
     */
    void saveBatchOrderPromotionDetail(List<OrderPromotionDetail> orderPromotionDetails);

    /**
     * 通过订单号获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> getOrderDiscountDetailByOrderNum(String orderNum);

    /**
     * 通过订单号获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> selectOrderDiscountDetailsByOrderNums(List<String> orderNums);

    /**
     * 通过视频订单ID获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> selectOrderDiscountDetailsByVideoId(Long videoId);

    /**
     * 通过视频订单ID获取订单参与活动以及优惠金额明细
     */
    List<OrderDiscountDetailVO> selectOrderDiscountDetailsByVideoIds(List<Long> videoIds);
}
