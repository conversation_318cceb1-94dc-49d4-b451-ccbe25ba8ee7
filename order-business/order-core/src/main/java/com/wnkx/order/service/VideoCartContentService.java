package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.VideoCartContent;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1 14:44
 */
public interface VideoCartContentService extends IService<VideoCartContent> {
    /**
     * 通过购物车id查询关联内容
     */
    List<VideoCartContent> selectListByCartId(Collection<Long> cartIds);

    /**
     * 批量保存视频关联内容（先删后加）
     */
    void saveBatchContents(Long cartId, List<VideoCartContent> videoCartContents);

    /**
     * 清除购物车关联内容
     */
    void deleteByCartIds(Collection<Long> cartIds);
}
