package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.core.enums.ReplyEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.dto.order.ReplyVideoCaseDTO;
import com.ruoyi.system.api.domain.dto.order.SendVideoCaseDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoCase;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoCaseVO;
import com.wnkx.order.mapper.OrderVideoCaseMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.IOrderVideoCaseService;
import com.wnkx.order.service.OrderVideoOperateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单_视频_匹配情况反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
@RequiredArgsConstructor
public class OrderVideoCaseServiceImpl extends ServiceImpl<OrderVideoCaseMapper, OrderVideoCase> implements IOrderVideoCaseService {
    private final RemoteService remoteService;
    private final OrderVideoOperateService orderVideoOperateService;


    /**
     * 通过主键和视频id查询反馈情况
     */
    @Override
    public OrderVideoCase getByIdAndVideoId(Long id, Long videoId) {
        return baseMapper.getByIdAndVideoId(id, videoId);
    }

    /**
     * 运营发起反馈
     */
    @Override
    public void sendOrderVideoCase(SendVideoCaseDTO sendVideoCaseDTO) {
        OrderVideoCase orderVideoCase = BeanUtil.copyProperties(sendVideoCaseDTO, OrderVideoCase.class);
        orderVideoCase.setSendId(SecurityUtils.getUserId());
        orderVideoCase.setSendTime(new Date());

        baseMapper.insert(orderVideoCase);
    }

    /**
     * 商家回复匹配情况反馈
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void replyVideoCase(ReplyVideoCaseDTO replyVideoCaseDTO) {
        OrderVideoCase orderVideoCase = baseMapper.selectById(replyVideoCaseDTO.getId());
        Assert.isTrue(orderVideoCase.getVideoId().equals(replyVideoCaseDTO.getVideoId()), "该条反馈信息与对应视频订单不相同，不允许继续操作！");
        baseMapper.replyVideoCase(replyVideoCaseDTO);
        if (ReplyEnum.AGREE.getCode().equals(replyVideoCaseDTO.getReplyContent())) {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.CONSENT_MATCHING_FEEDBACK.getEventName(), null, OrderVideoOperateTypeEnum.CONSENT_MATCHING_FEEDBACK.getIsPublic(),null, OrderVideoOperateDTO.builder().videoId(replyVideoCaseDTO.getVideoId()).eventContent(OrderVideoOperateTypeEnum.CONSENT_MATCHING_FEEDBACK.getEventContent()).build());
        } else {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REJECT_MATCH_FEEDBACK.getEventName(), null, OrderVideoOperateTypeEnum.REJECT_MATCH_FEEDBACK.getIsPublic(),null, OrderVideoOperateDTO.builder().videoId(replyVideoCaseDTO.getVideoId()).eventContent(OrderVideoOperateTypeEnum.REJECT_MATCH_FEEDBACK.getEventContent()).build());
        }
    }

    /**
     * 通过视频订单id查询视频订单匹配情况反馈
     */
    @Override
    public List<OrderVideoCaseVO> selectListByVideoId(Long videoId) {
        List<OrderVideoCase> videoCases = baseMapper.selectListByVideoId(videoId);

        if (CollUtil.isEmpty(videoCases)) {
            return new ArrayList<>();
        }

        return assembleOrderVideoCaseList(videoCases);
    }

    /**
     * 获取未回复的匹配情况map
     *
     * @return 未回复的匹配情况map  key 视频订单id value 未回复的匹配情况
     */
    @Override
    public Map<Long, List<OrderVideoCase>> getNoReplyMapByVideoId(List<Long> videoIds) {
        List<OrderVideoCase> videoCases = baseMapper.selectNoReplyListByVideoId(videoIds);

        if (CollUtil.isEmpty(videoCases)) {
            return new HashMap<>();
        }

        return videoCases.stream()
                .collect(Collectors.groupingBy(OrderVideoCase::getVideoId));
    }


    /**
     * 组装回显数据
     *
     * @param videoCases 匹配情况反馈
     * @return 返回列表数据
     */
    private List<OrderVideoCaseVO> assembleOrderVideoCaseList(List<OrderVideoCase> videoCases) {
        List<OrderVideoCaseVO> caseVOS = BeanUtil.copyToList(videoCases, OrderVideoCaseVO.class);

        Set<Long> sendIds = caseVOS.stream().map(OrderVideoCaseVO::getSendId).collect(Collectors.toSet());
        Set<Long> replyIds = caseVOS.stream().map(OrderVideoCaseVO::getReplyId).collect(Collectors.toSet());

        //  获取用户信息map
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(sendIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        Map<Long, BusinessAccountDetailVO> accountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(replyIds)) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setIds(replyIds);
            List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(businessAccountDetailDTO);
            accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);
        }

        for (OrderVideoCaseVO caseVO : caseVOS) {
            caseVO.setSendUser(userMap.getOrDefault(caseVO.getSendId(), null));
            caseVO.setReplyUser(accountMap.getOrDefault(caseVO.getReplyId(), null));
        }
        return caseVOS;
    }
}
