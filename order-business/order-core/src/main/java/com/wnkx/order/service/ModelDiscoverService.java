package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.OrderModelListDTO;
import com.ruoyi.system.api.domain.vo.order.*;

import java.util.List;

public interface ModelDiscoverService {

    /**
     * 模特端-首页-订单列表
     */
    List<OrderModelListVO> selectOrderModelListByCondition(OrderModelListDTO dto);

    /**
     * 模特端-首页-订单详情（首页及forme的订单详情）
     */
    OrderModelApplyInfoVO getOrderModelInfo(Long videoId, Long preselectModelId);


    /**
     * 模特端-首页-订单详情（根据视频编码获取首页及forme的订单详情）
     * @param videoCode
     * @param preselectModelId
     * @return
     */
    OrderModelApplyInfoVO getOrderModelInfoByVideoCode(String videoCode, Long preselectModelId);

    /**
     * 模特端-首页-模特报名
     */
    Long apply(Long videoId);

    /**
     * 首页订单数量统计
     */
    ModelHomePageOrderStatisticsVO orderStatistics();

    /**
     * 组装订单基本信息
     *
     * @param infoVO 订单基本信息
     */
    void assembleBaseInfo(OrderModelBaseInfoVO infoVO);

    /**
     * RECOMMEND页面
     */
    List<RecommendModelListVO> selectRecommendList();
}
