package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.domain.ExcelExp;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.TagVO;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.poi.ExcelUtil;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.config.OrderPayProperties;
import com.ruoyi.system.api.domain.dto.*;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.biz.model.CannotAcceptModelDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OrderVideoLogisticFlowDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.OrderPayLog;
import com.ruoyi.system.api.domain.entity.OrderVideoModel;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.entity.order.logistic.OrderVideoLogisticFollow;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.*;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.finace.*;
import com.ruoyi.system.api.domain.vo.order.logistic.LogisticFollowVideoInfoVO;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.wnkx.order.config.AnotherPayProperties;
import com.wnkx.order.config.OrderInvoiceProperties;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.factory.OrderVideoFlowServiceFactory;
import com.wnkx.order.mapper.OrderMapper;
import com.wnkx.order.mapper.OrderVideoTaskMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import com.wnkx.order.service.core.OrderDataScopeService;
import com.wnkx.order.service.core.OrderVideoFlowService;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import com.wnkx.order.utlis.CompletableFutureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {
    /**
     * redis
     */
    private final RedisService redisService;
    /**
     * 订单视频服务
     */
    private final IOrderVideoService orderVideoService;

    /**
     * 会员订单服务
     */
    private final IOrderMemberService orderMemberService;
    /**
     * 订单视频退款服务
     */
    private final IOrderVideoRefundService orderVideoRefundService;
    private final IOrderDocumentResourceService orderDocumentResourceService;
    /**
     * 订单视频物流关联服务
     */
    private final IOrderVideoLogisticService orderVideoLogisticService;
    /**
     * 订单视频反馈情况服务
     */
    private final IOrderVideoCaseService orderVideoCaseService;
    /**
     * 视频_订单关联模特
     */
    private final IOrderVideoModelService orderVideoModelService;
    /**
     * 订单反馈表(商家))
     */
    private final OrderVideoFeedBackService orderVideoFeedBackService;
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    /**
     * 远程服务
     */
    private final RemoteService remoteService;

    private final RemoteBusinessAccountService remoteBusinessAccountService;
    /**
     * 视频订单流转工厂类
     */
    private final OrderVideoFlowServiceFactory orderVideoFlowServiceFactory;

    private final IOrderInvoiceService orderInvoiceService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;
    private final OrderDataScopeService orderDataScopeService;
    private final OrderVideoRoastService orderVideoRoastService;

    private final IOrderService self;
    /**
     * 购物车服务
     */
    private final VideoCartService videoCartService;
    private final OrderVideoReminderRecordService orderVideoReminderRecordService;
    private final CompletableFutureUtil completableFutureUtil;
    private final OrderVideoCommentService orderVideoCommentService;
    private final OrderResourceService orderResourceService;
    private final OrderVideoUploadLinkService orderVideoUploadLinkService;
    private final OrderVideoChangeLogService orderVideoChangeLogService;
    private final OrderVideoModelShippingAddressService orderVideoModelShippingAddressService;

    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoModelChangeService orderVideoModelChangeService;
    private final IOrderVideoContentService orderVideoContentService;
    private final AsyncTaskService asyncTaskService;

    private static final String[] ignoreProperties = new String[]{"id", "createBy", "createTime", "updateBy", "updateTime"};

    private final OrderPayProperties orderPayProperties;

    private final IOrderPayeeAccountService orderPayeeAccountService;
    private final IOrderVideoTagService orderVideoTagService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final OrderVideoTaskService orderVideoTaskService;
    private final OrderAnotherPayService orderAnotherPayService;
    private final AnotherPayProperties anotherPayProperties;
    private final IOrderPayLogService orderPayLogService;
    private final OrderPayeeAccountConfigService orderPayeeAccountConfigService;
    private final IOrderAuditFlowService orderAuditFlowService;
    private final OrderVideoRollbackRecordService orderVideoRollbackRecordService;
    private final OrderInvoiceProperties orderInvoiceProperties;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderVideoTaskMapper orderVideoTaskMapper;
    private final OrderMergeService orderMergeService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final PromotionActivityService promotionActivityService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final OrderPromotionDetailService orderPromotionDetailService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;

    /**
     * 回显前端地址
     */
    @Value(value = "${s3.echoUrl}")
    private String echoUrl;
    @Value("${pay.debugger.enable}")
    private String debuggerEnable;

    private String ORDER_DOES_NOT_EXIST = "订单不存在，请检查后重试！";


    /**
     * 获取用户账号创建订单数量
     */
    @Override
    public Long getUserAccountCreateOrderCount() {
        return baseMapper.getUserAccountCreateOrderCount();
    }

    @Override
    public List<OrderVideoAuditVO> workbenchFinanceVideoList() {
        List<OrderVideoAuditVO> orderVideoAuditVOS = baseMapper.workbenchFinanceVideoList();
        if (CollUtil.isEmpty(orderVideoAuditVOS)) {
            return orderVideoAuditVOS;
        }
        Set<Long> orderUserId = orderVideoAuditVOS.stream().map(OrderVideoAuditVO::getOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> accountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, orderUserId);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(accountDetailVOS);
        for (OrderVideoAuditVO item : orderVideoAuditVOS) {
            if (StatusTypeEnum.NO.getCode().equals(item.getIsMergeOrder())) {
                item.setOrderAmount(item.getMergeOrderAmount());
                item.setOrderAmountDollar(item.getMergeOrderAmountDollar());
            }
            item.setMerchantInfo(accountMap.getOrDefault(item.getOrderUserId(), new BusinessAccountDetailVO()));
        }
        return orderVideoAuditVOS;
    }

    /**
     * 提交支付时设置订单支付号
     */
    @Override
    public void setPayNum(String orderNum) {
        baseMapper.setPayNum(orderNum);
    }

    /**
     * 取消合并时清空订单支付号
     */
    @Override
    public void emptyPayNum(List<String> orderNums) {
        baseMapper.emptyPayNum(orderNums);
    }

    /**
     * 合并时设置订单支付号
     */
    @Override
    public void setPayNum(List<String> orderNums, String payNum) {
        baseMapper.setPayNum(orderNums, payNum);
    }

    /**
     * 释放所使用的余额
     */
    @Override
    public void releaseUseBalance(List<String> orderNums) {
        List<Order> useBalanceList = baseMapper.selectUseBalanceListByOrderNums(orderNums);
        if (CollUtil.isEmpty(useBalanceList)) {
            return;
        }

        rollbackBalance(useBalanceList);
    }

    /**
     * 清除支付类型
     */
    @Override
    public void clearPayType(List<String> orderNums) {
        baseMapper.clearPayType(orderNums);
    }

    /**
     * 通过订单号查询订单
     */
    @Override
    public List<Order> selectListByOrderNums(Collection<String> orderNums) {
        return baseMapper.selectListByOrderNums(orderNums);
    }

    /**
     * 商家端-发票管理-未开票列表
     */
    @Override
    public List<CompanyNotInvoicedListVO> selectCompanyNotInvoicedListByCondition(CompanyNotInvoicedListDTO dto) {
        return baseMapper.selectCompanyNotInvoicedListByCondition(dto, orderInvoiceProperties.getOldDataEndTime());
    }

    /**
     * 订单回退
     */
    @Override
    public void rollbackOrder(RollbackOrderDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_ROLLBACK_LOCK_KEY + dto.getVideoId(), CacheConstants.ORDER_ROLLBACK_LOCK_KEY_SECOND), "请勿频繁操作~");
        try {
            orderVideoRollbackRecordService.rollbackOrder(dto);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_ROLLBACK_LOCK_KEY + dto.getVideoId());
        }
    }

    /**
     * 运营手动获取产品图
     */
    @Override
    public void crawlProductPic(Long videoId) {
        orderVideoService.crawlProductPic(videoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayLog saveOrderPayLog(OrderPayLogDTO dto) {
        return orderPayLogService.saveOrderPayLog(dto);
    }

    @Override
    public void banMemberInvoice(Long businessId) {
        baseMapper.banMemberInvoice(businessId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderPayeeAccount(OrderPayAccountDTO dto) {
        orderPayeeAccountService.saveOrderPayeeAccount(dto);
    }

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @Override
    public void updateIssueId(UpdateIssueIdDTO dto) {
        asyncTaskService.updateIssueId(dto);
    }

    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    @Override
    public List<UnFinishedAndNeedConfirmOrderIssueSelectVO> unFinishedAndNeedConfirmOrderIssueSelect() {
        return orderVideoService.unFinishedAndNeedConfirmOrderIssueSelect();
    }

    /**
     * 汇率异常调整
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBaiduRate(UpdateBaiduRateDTO dto) {
        Order order = baseMapper.getOrderByOrderNum(dto.getOrderNum());
        Assert.notNull(order, ORDER_DOES_NOT_EXIST);
        Assert.isTrue(order.getIsDefaultExchangeRate(), "提交失败！汇率已调整，无需再次提交！");
        if (order.getCurrentExchangeRate().compareTo(dto.getBaiduRate()) == 0) {
            order.setIsDefaultExchangeRate(false);
            baseMapper.updateById(order);
            return;
        }
        order.setIsDefaultExchangeRate(false);
        order.setCurrentExchangeRate(dto.getBaiduRate());
        List<OrderVideo> orderVideos = orderVideoService.selectByOrderNum(dto.getOrderNum());
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType())) {
            Assert.notEmpty(orderVideos, "订单视频不存在，请检查后重试！");
            orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList())).submitter(false).build());
            Assert.isTrue(orderVideos.stream().allMatch(item -> OrderStatusEnum.UN_PAY.getCode().equals(item.getStatus())), "订单交易已关闭，汇率无需调整，请刷新订单");

            PayService payService = SpringUtils.getBean(PayService.class);
            for (OrderVideo orderVideo : orderVideos) {
                OrderVideoDTO orderVideoDTO = BeanUtil.copyProperties(orderVideo, OrderVideoDTO.class);
                orderVideoDTO.setCurrentExchangeRate(dto.getBaiduRate());
                orderVideoDTO = payService.updateTotalPrice(orderVideoDTO, Collections.emptyList()).get(0);

                orderVideo.setAmount(orderVideoDTO.getAmount());
                orderVideo.setPayAmount(orderVideoDTO.getAmount());
                if (ObjectUtil.isNotNull(orderVideo.getVideoPromotionAmount()) && BigDecimal.ZERO.compareTo(orderVideo.getVideoPromotionAmount()) != 0) {
                    orderVideo.setPayAmount(orderVideoDTO.getAmount().subtract(orderVideo.getVideoPromotionAmount()));
                    orderVideo.setPayAmountDollar(orderVideo.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
                }

            }
            final BigDecimal orderAmount = orderVideos.stream().map(OrderVideo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            final BigDecimal payAmount = orderVideos.stream().map(OrderVideo::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            order.setOrderAmount(orderAmount);
            order.setPayAmount(payAmount);
            order.setPayAmountDollar(order.getOrderPromotionAmount().compareTo(BigDecimal.ZERO) == 0 ? order.getPayAmountDollar() : order.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(order.getOrderType())) {
            log.info("'{}'修改会员订单汇率：{}", SecurityUtils.getUsername(), dto.toString());
            //获取会员数据
            OrderMemberVO orderMember = orderMemberService.getOrderMember(dto.getOrderNum());
            Assert.isTrue(OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMember.getStatus()), "订单交易已关闭，汇率无需调整，请刷新订单");
            order.setOrderAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(orderMember.getPackageType())).multiply(dto.getBaiduRate()).setScale(2, RoundingMode.DOWN));
            order.setPayAmount(order.getOrderAmount());
        }
        baseMapper.updateById(order);
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType())) {
            Assert.notEmpty(orderVideos, "订单视频不存在，请检查后重试！");
            orderVideoService.updateBatchById(orderVideos);

            List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> OrderVideoOperateDTO.builder().videoId(item.getId()).eventContent(OrderVideoOperateTypeEnum.MODIFY_THE_EXCHANGE_RATE.getEventContent()).build()).collect(Collectors.toList());
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.MODIFY_THE_EXCHANGE_RATE.getEventName(),
                    null,
                    OrderVideoOperateTypeEnum.MODIFY_THE_EXCHANGE_RATE.getIsPublic(),
                    null,
                    orderVideoOperateDTOS
            );

        }
    }

    @Override
    public List<BalanceLockRecordVO> getBalanceLockRecord() {
        List<BalanceLockRecordVO> result = new ArrayList<>();
        if (UserTypeConstants.USER_TYPE != SecurityUtils.getLoginUserType()) {
            return result;
        }
        List<BalanceLockRecordVO> balanceLockRecord = baseMapper.getBalanceLockRecord(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        if (CollUtil.isNotEmpty(balanceLockRecord)) {
            result.addAll(balanceLockRecord);
        }
        BusinessBalanceAuditFlowValidListDTO dto = new BusinessBalanceAuditFlowValidListDTO();
        dto.setAuditStatus(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode());
        dto.setBusinessIds(Arrays.asList(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()));

        List<BusinessBalanceAuditFlow> businessBalanceAuditFlows = remoteBusinessAccountService.queryValidBalanceAuditFlowList(dto, SecurityConstants.INNER);
        if (CollUtil.isNotEmpty(businessBalanceAuditFlows)) {
            for (BusinessBalanceAuditFlow item : businessBalanceAuditFlows) {
                result.add(BalanceLockRecordVO.builder()
                        .amount(item.getAmount())
                        .orderNum("")
                        .type(2)
                        .build());
            }
        }
        return result;
    }

    @Override
    public List<OrderListVO> getOrderList(OrderListDTO orderListDTO) {
        return baseMapper.selectOrderListByCondition(orderListDTO, OrderTypeEnum.VIDEO_ORDER.getCode());
    }

    /**
     * 获取订单列表视频订单统计数量
     */
    @Override
    public Integer videoStatistics(OrderListDTO orderListDTO) {
        if (CollUtil.isNotEmpty(orderListDTO.getPayType())){
            orderListDTO.setIsFilterClose(StatusTypeEnum.YES.getCode());
        }
        if (Boolean.FALSE.equals(wrapperCondition(orderListDTO))) {
            return 0;
        }

        List<OrderListVO> orderListVOS = baseMapper.selectOrderListByCondition(orderListDTO, OrderTypeEnum.VIDEO_ORDER.getCode());
        if (CollUtil.isEmpty(orderListVOS)) {
            return 0;
        }

        Set<String> orderNums = orderListVOS.stream().map(OrderListVO::getOrderNum).collect(Collectors.toSet());
        orderListDTO.setOrderNums(orderNums);

        List<OrderVideoVO> orderVideoVOS = orderVideoService.selectOrderVideoListByCondition(orderListDTO);
        return orderVideoVOS.size();
    }

    /**
     * 爬取亚马逊图片写入视频订单表
     */
    @Override
    public void crawlTask(AsyncCrawlTask asyncCrawlTask) {
        if (CollUtil.isEmpty(asyncCrawlTask.getAsyncCrawlProductPic())) {
            return;
        }
        remoteService.asyncUpdateOrderVideoImage(asyncCrawlTask);
    }


    /**
     * 检查当前订单是否还需支付
     */
    @Override
    public PayTranStatusEnum checkOrderNeedPay(CheckStatusDTO checkStatusDTO) {
        PayTranStatusEnum success = getOrderMergeForCheckOrderStatus(checkStatusDTO);
        if (success != null) {
            return success;
        }
        OrderAnotherPay orderAnotherPay = orderAnotherPayService.getOrderAnotherPayByOrderNumOrMergeId(checkStatusDTO.getOrderNum(), checkStatusDTO.getMergeId());
        boolean anotherPaying = ObjectUtil.isNotNull(orderAnotherPay) && StatusTypeEnum.YES.getCode().equals(orderAnotherPay.getStatus());

        PayTranStatusEnum notpay = checkOrderNeedPayCommon(checkStatusDTO.getOrderNums(), false, anotherPaying);
        if (notpay != null) {
            return notpay;
        }

        if (anotherPaying) {
            return PayTranStatusEnum.ANOTHER_PAY;
        }

        if (ObjectUtil.isNotNull(orderAnotherPay)) {
            if (StatusTypeEnum.NO.getCode().equals(orderAnotherPay.getStatus())) {
                return PayTranStatusEnum.NOTPAY;
            }
            if (DateUtil.compare(DateUtil.offsetHour(orderAnotherPay.getCreateTime(), anotherPayProperties.getTimeLimit()), DateUtil.date()) <= 0) {
                orderAnotherPayService.cancel(orderAnotherPay.getUuid());
                return PayTranStatusEnum.ANOTHER_PAY_FAILURE;
            }
        }

        return PayTranStatusEnum.NOTPAY;
    }

    @Override
    public PayTranStatusEnum checkOrderAnotherPay(CheckStatusDTO checkStatusDTO) {
        PayTranStatusEnum success = getOrderMergeForCheckOrderStatus(checkStatusDTO);
        if (success != null) {
            return success;
        }
        OrderAnotherPay orderAnotherPay = orderAnotherPayService.getOrderAnotherPayByOrderNumOrMergeId(checkStatusDTO.getOrderNum(), checkStatusDTO.getMergeId());
        if (ObjectUtil.isNull(orderAnotherPay)) {
            return PayTranStatusEnum.CLOSED;
        }

        if (StatusTypeEnum.YES.getCode().equals(orderAnotherPay.getIsPay())) {
            return PayTranStatusEnum.SUCCESS;
        }
        if (StatusTypeEnum.NO.getCode().equals(orderAnotherPay.getStatus())) {
            return PayTranStatusEnum.ANOTHER_PAY_FAILURE;
        }

        if (DateUtil.compare(DateUtil.offsetHour(orderAnotherPay.getCreateTime(), anotherPayProperties.getTimeLimit()), DateUtil.date()) <= 0) {
            orderAnotherPayService.cancel(orderAnotherPay.getUuid());
            return PayTranStatusEnum.ANOTHER_PAY_FAILURE;
        }
        PayTranStatusEnum notpay = checkOrderNeedPayCommon(checkStatusDTO.getOrderNums(), true, true);
        if (notpay != null) {
            return notpay;
        }

        return PayTranStatusEnum.ANOTHER_PAY;
    }

    @Nullable
    private PayTranStatusEnum checkOrderNeedPayCommon(List<String> orderNums, Boolean isAnother, Boolean anotherPaying) {
        if (CollUtil.isEmpty(orderNums)) {
            return PayTranStatusEnum.NOTPAY;
        }
        if (orderNums.get(0).startsWith(OrderConstant.PREPAY_NUM)) {
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(orderNums).build());

            Assert.isTrue(CollUtil.isNotEmpty(businessBalancePrepayVOS) && businessBalancePrepayVOS.size() == orderNums.size(), "订单不存在");
            BusinessBalancePrepayVO businessBalancePrepayVO = businessBalancePrepayVOS.get(0);
            if (!isAnother) {
                Assert.isTrue(businessBalancePrepayVO.getBusinessId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), "查询不到订单，请检查后重试~");
            }
            if (ObjectUtil.isNotNull(businessBalancePrepayVO.getSubmitCredentialTime())) {
                return PayTranStatusEnum.AUDIT;
            }
            if (ObjectUtil.isNotNull(businessBalancePrepayVO.getPayTime())) {
                return PayTranStatusEnum.SUCCESS;
            }
            if (OrderStatusEnum.TRADE_CLOSE.getCode().equals(businessBalancePrepayVO.getStatus())) {
                return PayTranStatusEnum.CLOSED;
            }
            return null;
        }
        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);
        Assert.isTrue(CollUtil.isNotEmpty(orders) && orders.size() == orderNums.size(), "订单不存在");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        if (orders.stream().anyMatch(Order::getIsDefaultExchangeRate)) {
            return PayTranStatusEnum.EXCHANGE_RATE;
        }
        if (orders.stream().anyMatch(order -> ObjectUtil.isNotNull(order.getSubmitCredentialTime()))) {
            return PayTranStatusEnum.AUDIT;
        }

        if (orders.stream().anyMatch(order -> order.getMerchantId().compareTo(0L) == 0) && !anotherPaying) {
            return PayTranStatusEnum.NOTPAY;
        }

        if (!isAnother) {
            if (SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId().compareTo(0L) == 0 && !anotherPaying) {
                remoteService.refreshToken();
                // 刷新token
                return PayTranStatusEnum.NOTPAY;
            } else {
                Assert.isTrue(orders.stream().allMatch(order -> order.getMerchantId().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())), "查询不到订单，请检查后重试~");
            }

        }
        if (orders.stream().anyMatch(order -> ObjectUtil.isNotNull(order.getPayTime()))) {
            return PayTranStatusEnum.SUCCESS;
        }

        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orders.get(0).getOrderType())) {
            List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orderNums);
            if (orderVideos.stream().allMatch(item -> item.getStatus().equals(OrderStatusEnum.TRADE_CLOSE.getCode()))) {
                return PayTranStatusEnum.CLOSED;
            }
        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(orders.get(0).getOrderType())) {
            List<OrderMember> orderMembers = orderMemberService.getByOrderNums(orderNums);
            if (orderMembers.stream().allMatch(item -> item.getStatus().equals(OrderMemberStatusEnum.UN_MATCH.getCode()))) {
                return PayTranStatusEnum.CLOSED;
            }
        }
        return null;
    }

    /**
     * 视频订单导出（商家端）
     */
    @Override
    public void orderVideoCompanyExport(OrderListDTO orderListDTO, HttpServletResponse response) {
        Assert.isTrue(StatusTypeEnum.YES.getCode().equals(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getIsOwnerAccount()), "请使用主账号进行操作~");
        final List<OrderListVO> orderList = selectOrderListByCondition(orderListDTO);

        List<OrderListExportVO> orderListExportVOS = BeanUtil.copyToList(orderList, OrderListExportVO.class);
        List<OrderVideoListExportVO> orderVideoListExportVOS = new ArrayList<>();

        for (OrderListExportVO orderListVO : orderListExportVOS) {
            for (OrderVideoVO orderVideoVO : orderListVO.getOrderVideoVOS()) {
                OrderVideoListExportVO orderVideoListExportVO = BeanUtil.copyProperties(orderVideoVO, OrderVideoListExportVO.class);
                orderVideoListExportVO.setCurrentExchangeRate(orderListVO.getCurrentExchangeRate());
                orderVideoListExportVO.setShootModelName(Optional.ofNullable(orderVideoVO.getShootModel()).orElse(new ModelOrderSimpleVO()).getName());
                if (orderListVO.isDefaultExchangeRate()) {
                    orderVideoListExportVO.setCurrentExchangeRate(null);
                    orderVideoListExportVO.setAmount(null);
                }
                if (CharSequenceUtil.isBlank(orderVideoVO.getCreateOrderUserName())) {
                    orderVideoListExportVO.setCreateOrderUserName(orderVideoVO.getCreateOrderUserNickName());
                }
                orderVideoListExportVOS.add(orderVideoListExportVO);
            }
            if (!orderListVO.isDefaultExchangeRate()) {
                orderListVO.setVideoAmountCount(orderListVO.getOrderVideoVOS().stream().map(OrderVideoVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            orderListVO.setVideoAmountCountUsd(orderListVO.getOrderVideoVOS().stream().map(OrderVideoVO::getAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add));
            orderListVO.setCommissionPaysTaxes(orderListVO.getCommissionPaysTaxes().compareTo(BigDecimal.ZERO) != 0 ? orderListVO.getCommissionPaysTaxes().setScale(2, RoundingMode.DOWN) : BigDecimal.ZERO);
        }

        ExcelExp exp = new ExcelExp("订单汇总", orderListExportVOS, OrderListExportVO.class);
        ExcelExp exp1 = new ExcelExp("视频明细", orderVideoListExportVOS, OrderVideoListExportVO.class);
        List<ExcelExp> mysheet = new ArrayList<ExcelExp>();
        mysheet.add(exp);
        mysheet.add(exp1);
        ExcelUtil.setAttachmentResponseHeader(response, "订单费用明细");
        ExcelUtil<List<ExcelExp>> util = new ExcelUtil<List<ExcelExp>>(mysheet);
        // 设置响应头信息
        util.exportExcelManySheet(response, mysheet);
    }

    /**
     * 视频订单导出（运营端）
     */
    @Override
    public void orderVideoExport(OrderListDTO orderListDTO, HttpServletResponse response) {
        //设置查询订单新状态
        orderListDTO.setStatusTimeSort(OrderByDto.DIRECTION.DESC.value());
        final List<OrderListVO> orderList = selectOrderListByCondition(orderListDTO);
        List<OrderExportDTO> orderExportDTOList = new ArrayList<>();

        if (CollUtil.isNotEmpty(orderList)) {
            try {
                //  查询拍摄要求
                List<Long> videoIds = orderList.stream()
                        .filter(order -> CollUtil.isNotEmpty(order.getOrderVideoVOS()))
                        .flatMap(order -> order.getOrderVideoVOS().stream())
                        .map(OrderVideoVO::getId)
                        .collect(Collectors.toList());

                CompletableFuture<Map<Long, List<OrderVideoContent>>> orderVideoContentMapFuture = CompletableFuture.supplyAsync(() -> {
                    List<OrderVideoContent> orderVideoContents = orderVideoContentService.selectListByVideoIds(videoIds);
                    return orderVideoContents.stream().collect(Collectors.groupingBy(OrderVideoContent::getVideoId));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<OrderFeedBackMaterialVO>>> orderFeedBackMaterialVOMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询模特反馈素材链接
                    List<OrderFeedBackMaterialVO> orderFeedBackMaterialVOS = orderVideoFeedBackMaterialService.modelFeedBackListByVideoIds(videoIds);
                    return orderFeedBackMaterialVOS.stream().collect(Collectors.groupingBy(OrderFeedBackMaterialVO::getVideoId));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<OrderVideoFeedBack>>> orderVideoFeedBackMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询商家反馈素材链接
                    List<OrderVideoFeedBack> feedBackListByVideos = orderVideoFeedBackService.getFeedBackListByVideoIds(videoIds);
                    return feedBackListByVideos.stream().collect(Collectors.groupingBy(OrderVideoFeedBack::getVideoId));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, OrderVideoUploadLinkVO>> orderVideoUploadLinkVOMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询订单上传需求
                    List<OrderVideoUploadLinkVO> orderVideoUploadLinkVOS = orderVideoUploadLinkService.selectListByVideoIds(videoIds);
                    return orderVideoUploadLinkVOS.stream().collect(Collectors.toMap(OrderVideoUploadLinkVO::getVideoId, Function.identity()));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<OrderVideoRefund>>> orderVideoRefundMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询退款数据
                    List<OrderVideoRefund> orderVideoRefunds = orderVideoRefundService.selectValidOrderVideoRefundListByVideoId(videoIds);
                    return orderVideoRefunds.stream().collect(Collectors.groupingBy(OrderVideoRefund::getVideoId));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<OrderLogisticSimpleVO>>> orderLogisticSimpleVOMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询物流信息
                    List<OrderVideoLogistic> videoLogistics = orderVideoLogisticService.selectListByVideoId(videoIds);
                    Map<Long, List<OrderLogisticSimpleVO>> orderLogisticSimpleVOMap = new HashMap<>();
                    if (CollUtil.isNotEmpty(videoLogistics)) {
                        List<OrderLogisticSimpleVO> orderLogisticSimpleVOS = BeanUtil.copyToList(videoLogistics, OrderLogisticSimpleVO.class);
                        List<String> numbers = orderLogisticSimpleVOS.stream().map(OrderLogisticSimpleVO::getNumber).collect(Collectors.toList());
                        Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numbers);

                        for (OrderLogisticSimpleVO orderLogisticSimpleVO : orderLogisticSimpleVOS) {
                            List<LogisticInfoVO> logisticInfo = logisticMap.getOrDefault(orderLogisticSimpleVO.getNumber(), new LogisticVO()).getLogisticInfo();
                            if (CollUtil.isNotEmpty(logisticInfo)) {
                                orderLogisticSimpleVO.setLogisticInfo(logisticInfo);
                                orderLogisticSimpleVO.setMainStatus(logisticInfo.get(0).getMainStatus());
                            }
                        }
                        orderLogisticSimpleVOMap = orderLogisticSimpleVOS.stream().collect(Collectors.groupingBy(OrderLogisticSimpleVO::getVideoId));
                    }
                    return orderLogisticSimpleVOMap;
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<OrderVideoComment>>> orderVideoCommentMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询备注信息
                    List<OrderVideoComment> orderVideoComments = orderVideoCommentService.orderCommentListByVideoIds(videoIds);
                    return orderVideoComments.stream().collect(Collectors.groupingBy(OrderVideoComment::getVideoId));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, OrderVideoModel>> orderVideoModelCommentMapFuture = CompletableFuture.supplyAsync(() -> {
                    //  查询备注信息
                    List<OrderVideoModel> listByVideoIds = orderVideoModelService.getListByVideoIds(videoIds);
                    return listByVideoIds.stream().collect(Collectors.toMap(OrderVideoModel::getVideoId, item -> item));
                }, asyncPoolTaskExecutor);

                CompletableFuture<Map<Long, List<VideoTaskOrderVO>>> orderVideoTaskCommentMapFuture = CompletableFuture.supplyAsync(() -> {
                    //查询是否有创建工单
                    List<VideoTaskOrderVO> taskList = orderVideoTaskMapper.selectVideoTaskOrderVOListByVideoIds(videoIds);
                    return taskList.stream().filter(tlf -> tlf.getTaskType().equals(OrderTaskTypeEnum.AFTER_SALE.getCode())
                                    && (tlf.getStatus().equals(OrderTaskStatusEnum.UN_HANDLE.getCode()) ||
                                    tlf.getStatus().equals(OrderTaskStatusEnum.HANDLE_ING.getCode()) ||
                                    tlf.getStatus().equals(OrderTaskStatusEnum.HANDLE.getCode()))
                                    && (tlf.getAfterSaleVideoType() != null && (tlf.getAfterSaleVideoType().equals(OrderTaskAfterSaleAllTypeEnum.RESHOOT_VIDEO.getCode())
                                    || tlf.getAfterSaleVideoType().equals(OrderTaskAfterSaleAllTypeEnum.RESHOT_VIDEO.getCode()))))
                            .collect(Collectors.groupingBy(VideoTaskOrderVO::getVideoId));
                });


                // 等待所有任务完成
                CompletableFuture.allOf(
                        orderVideoContentMapFuture, orderFeedBackMaterialVOMapFuture, orderVideoUploadLinkVOMapFuture,
                        orderLogisticSimpleVOMapFuture, orderVideoCommentMapFuture, orderVideoFeedBackMapFuture, orderVideoRefundMapFuture, orderVideoModelCommentMapFuture
                        , orderVideoTaskCommentMapFuture
                ).join();

                Map<Long, List<OrderVideoContent>> orderVideoContentMap = orderVideoContentMapFuture.get();
                Map<Long, List<OrderFeedBackMaterialVO>> orderFeedBackMaterialVOMap = orderFeedBackMaterialVOMapFuture.get();
                Map<Long, OrderVideoUploadLinkVO> orderVideoUploadLinkVOMap = orderVideoUploadLinkVOMapFuture.get();
                Map<Long, List<OrderLogisticSimpleVO>> orderLogisticSimpleVOMap = orderLogisticSimpleVOMapFuture.get();
                Map<Long, List<OrderVideoComment>> orderVideoCommentMap = orderVideoCommentMapFuture.get();
                Map<Long, List<OrderVideoFeedBack>> orderVideoFeedBackMap = orderVideoFeedBackMapFuture.get();
                Map<Long, List<OrderVideoRefund>> orderVideoRefundMap = orderVideoRefundMapFuture.get();
                Map<Long, OrderVideoModel> orderVideoModelMap = orderVideoModelCommentMapFuture.get();
                Map<Long, List<VideoTaskOrderVO>> groupOrderVideoTaskMap = orderVideoTaskCommentMapFuture.get();

                String cr = StrPool.LF;
                String amountSymbol = "￥";
                for (OrderListVO orderListVO : orderList) {
                    if (CollUtil.isEmpty(orderListVO.getOrderVideoVOS())) {
                        continue;
                    }
                    List<OrderExportDTO> orderExportDTOS = orderListVO.getOrderVideoVOS().stream().map(orderVideo -> {
                        OrderExportDTO orderExportDTO = new OrderExportDTO();
                        //国家、类型、平台
                        orderExportDTO.setPlatform(orderVideo.getPlatform() != null ? orderVideo.getPlatform() : null);
                        orderExportDTO.setShootingCountry(orderVideo.getShootingCountry() != null ? orderVideo.getShootingCountry() : null);
                        orderExportDTO.setModelType(orderVideo.getModelType() != null ? orderVideo.getModelType() : null);
                        // APP/解说类 设置确认模特
                        if (orderVideo.getPlatform().equals(PlatformEnum.APP.getCode()) && (orderVideo.getStatus().equals(OrderStatusEnum.UN_FINISHED.getCode()) ||
                                orderVideo.getStatus().equals(OrderStatusEnum.NEED_CONFIRM.getCode()) || orderVideo.getStatus().equals(OrderStatusEnum.FINISHED.getCode()))) {
                            orderExportDTO.setFinalReconDate(orderVideo.getStatusTime());
                        }
                        Optional.ofNullable(groupOrderVideoTaskMap.get(orderVideo.getId()))
                                .filter(CollUtil::isNotEmpty)
                                .map(list -> list.stream()
                                        .map(VideoTaskOrderVO::getAfterSaleVideoType)
                                        .collect(Collectors.toSet()))
                                .ifPresent(types -> {
                                    orderExportDTO.setAfterSaleType(types.size() > 1 ? OtherStatusEnum.BOTH_AND.getCode() : types.iterator().next());
                                });

                        orderExportDTO.setPayTime(orderListVO.getPayTime());
                        orderExportDTO.setVideoCode(orderVideo.getVideoCode());
                        orderExportDTO.setProductChinese(StrUtil.builder().append("中文名称：").append(orderVideo.getProductChinese()).toString());
                        orderExportDTO.setProductEnglish(StrUtil.builder().append("英文名称：").append(orderVideo.getProductEnglish()).toString());
                        orderExportDTO.setProductLink(StrUtil.builder().append("产品链接：").append(StrUtil.isNotBlank(orderVideo.getProductLink()) ? orderVideo.getProductLink() : StrUtil.EMPTY).toString());
                        if (ObjectUtil.isNotNull(orderVideo.getShootModel())) {
                            orderExportDTO.setShootModelCountry(StrUtil.builder()
                                    .append(orderVideo.getShootModel().getName()).append(cr)
                                    .append("（").append(ModelTypeEnum.getLabel(orderVideo.getShootModel().getType())).append("）").append(cr)
                                    .append(NationEnum.getLabel(orderVideo.getShootModel().getNation()))
                                    .toString());
                            orderExportDTO.setCooperation(orderVideo.getShootModel().getCooperation());
                        }
                        orderExportDTO.setVideoFormatStyle(StrUtil.builder()
                                .append("视频格式：").append(VideoFormatEnum.getDescription(orderVideo.getVideoFormat())).append(cr)
                                .append("视频风格：").append(PlatformEnum.getStyle(orderVideo.getPlatform()))
                                .toString());
                        orderExportDTO.setPicCount(orderVideo.getPicCount());

                        List<OrderVideoContent> videoContents = orderVideoContentMap.get(orderVideo.getId());
                        String clipRequirements = StrUtil.EMPTY;
                        if (CollUtil.isNotEmpty(videoContents)) {
                            List<OrderVideoContent> shootRequired = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                            List<OrderVideoContent> cautions = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                            List<OrderVideoContent> clipsRequired = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CLIPS_REQUIRED.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                            List<OrderVideoContent> orderSpecificationRequire = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
                            List<OrderVideoContent> particularEmphasis = videoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());

                            if (CollUtil.isNotEmpty(shootRequired)) {
                                orderExportDTO.setShootingRequirement(shootRequired.stream()
                                        .map(vc -> StrUtil.builder().append("·").append(vc.getContent()).append(cr).toString())
                                        .collect(Collectors.joining()));
                            }
                            if (CollUtil.isNotEmpty(cautions)) {
                                orderExportDTO.setCautions(cautions.stream()
                                        .map(vc -> StrUtil.builder().append("·").append(vc.getContent()).append(cr).toString())
                                        .collect(Collectors.joining()));
                            }
                            if (CollUtil.isNotEmpty(clipsRequired)) {
                                clipRequirements = clipsRequired.stream()
                                        .map(vc -> StrUtil.builder().append("·").append(vc.getContent()).append(cr).toString())
                                        .collect(Collectors.joining());
                            }
                            if (CollUtil.isNotEmpty(orderSpecificationRequire)) {
                                orderExportDTO.setOrderSpecificationRequire(orderSpecificationRequire.get(0).getContent());
                            }
                            if (CollUtil.isNotEmpty(particularEmphasis)) {
                                orderExportDTO.setParticularEmphasis(particularEmphasis.get(0).getContent());
                            }
                        }
                        orderExportDTO.setVideoDurationClipRequirements(StrUtil.builder()
                                .append("视频时长：").append(orderVideo.getVideoDuration() == null ? StrUtil.EMPTY : orderVideo.getVideoDuration() + "s").append(cr)
                                .append("剪辑要求：").append(cr).append(clipRequirements)
                                .toString());
                        List<OrderFeedBackMaterialVO> materialVOS = orderFeedBackMaterialVOMap.get(orderVideo.getId());
                        if (CollUtil.isNotEmpty(materialVOS)) {
                            StringBuilder materialLink = StrUtil.builder();
                            materialVOS.sort(Comparator.comparing(OrderFeedBackMaterialVO::getCreateTime).reversed());
                            for (OrderFeedBackMaterialVO materialVO : materialVOS) {
                                for (OrderFeedBackMaterialInfoVO materialInfo : materialVO.getMaterialInfos()) {
                                    materialLink.append("反馈时间：").append(DateUtil.format(materialInfo.getUploadTime(), DatePattern.NORM_DATETIME_PATTERN)).append(cr)
                                            .append("素材链接：").append(materialInfo.getLink()).append(cr)
                                            .append("上传备注：").append(materialInfo.getNote()).append(cr)
                                            .append("下载标记：").append(1 == materialVO.getDownloadStatus() ? "已下载" : StrUtil.EMPTY).append(cr).append(cr);
                                }
                            }
                            orderExportDTO.setMaterialLink(materialLink.toString());
                        }
                        if (CollUtil.isNotEmpty(orderVideoFeedBackMap)) {
                            List<OrderVideoFeedBack> orderVideoFeedBacks = orderVideoFeedBackMap.get(orderVideo.getId());
                            if (CollUtil.isNotEmpty(orderVideoFeedBacks)) {
                                OrderVideoFeedBack orderVideoFeedBack = orderVideoFeedBacks.stream()
                                        .filter(entity -> entity.getCreateTime() != null && (entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO.getCode()) || entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode())))
                                        .max(Comparator.comparing(OrderVideoFeedBack::getCreateTime)).orElse(new OrderVideoFeedBack());
                                orderExportDTO.setFeedbackDate(orderVideoFeedBack.getCreateTime());
                                orderExportDTO.setFeedbackImageDate(
                                        orderVideoFeedBacks.stream()
                                                .filter(entity ->
                                                        entity.getCreateTime() != null && (
                                                                entity.getType().equals(OrderVideoFeedBackTypeEnum.PIC.getCode()) || entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode())
                                                        )
                                                )
                                                .max(Comparator.comparing(OrderVideoFeedBack::getCreateTime))
                                                .orElse(new OrderVideoFeedBack()).getCreateTime()
                                );
                                orderExportDTO.setFirstSendFeedVideoDate(orderVideoFeedBacks.stream()
                                        .filter(entity ->
                                                entity.getCreateTime() != null && (
                                                        entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO.getCode()) || entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode())
                                                )
                                        )
                                        .min(Comparator.comparing(OrderVideoFeedBack::getCreateTime))
                                        .orElse(new OrderVideoFeedBack()).getCreateTime());
                                orderExportDTO.setFirstSendPhotoFeedDate(orderVideoFeedBacks.stream()
                                        .filter(entity ->
                                                entity.getCreateTime() != null && (
                                                        entity.getType().equals(OrderVideoFeedBackTypeEnum.PIC.getCode()) || entity.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO_PIC.getCode())
                                                )
                                        )
                                        .min(Comparator.comparing(OrderVideoFeedBack::getCreateTime))
                                        .orElse(new OrderVideoFeedBack()).getCreateTime());
                            }
                        }
                        if (CollUtil.isNotEmpty(orderVideoRefundMap)) {
                            List<OrderVideoRefund> orderVideoRefunds = orderVideoRefundMap.get(orderVideo.getId());
                            if (CollUtil.isNotEmpty(orderVideoRefunds)) {
                                StringBuilder opentionAmount = new StringBuilder();
                                BigDecimal reparationAmount = BigDecimal.ZERO;
                                for (OrderVideoRefund item : orderVideoRefunds) {
                                    if (RefundTypeEnum.CANCEL_OPENTION.getCode().equals(item.getRefundType())) {
                                        opentionAmount.append(amountSymbol);
                                        opentionAmount.append(item.getRefundAmount()).append(cr);
                                        opentionAmount.append(PicCountEnum.getLabel(item.getPicCount()));
                                    }
                                    if (RefundTypeEnum.REPARATION.getCode().equals(item.getRefundType())) {
                                        reparationAmount = reparationAmount.add(item.getRefundAmount());
                                    }
                                }
                                orderExportDTO.setOpentionAmount(opentionAmount.toString());
                                if (reparationAmount.compareTo(BigDecimal.ZERO) != 0) {
                                    orderExportDTO.setReparationAmount(amountSymbol + reparationAmount);
                                }
                            }
                        }

                        OrderVideoUploadLinkVO orderVideoUploadLinkVO = orderVideoUploadLinkVOMap.get(orderVideo.getId());
                        if (null != orderVideoUploadLinkVO) {
                            orderExportDTO.setSubmitDate(orderVideoUploadLinkVO.getTime());
                            orderExportDTO.setSubmitter(
                                    orderVideoUploadLinkVO.getCompany() == null ||
                                            (StrUtil.isBlank(orderVideoUploadLinkVO.getCompany().getName()) &&
                                                    StrUtil.isBlank(orderVideoUploadLinkVO.getCompany().getNickName())) ?
                                            null :
                                            orderVideoUploadLinkVO.getCompany() == null ?
                                                    orderVideoUploadLinkVO.getBack().getName() :
                                                    StrUtil.isNotBlank(orderVideoUploadLinkVO.getCompany().getName()) ?
                                                            orderVideoUploadLinkVO.getCompany().getName() :
                                                            orderVideoUploadLinkVO.getCompany().getNickName()
                            );
                            orderExportDTO.setUploadLink(orderVideoUploadLinkVO.getNeedUploadLink());
                            orderExportDTO.setVideoTitle(orderVideoUploadLinkVO.getVideoTitle());
                            orderExportDTO.setUploadLinkRemark(orderVideoUploadLinkVO.getRemark());
                        }
                        orderExportDTO.setStatus(orderVideo.getStatus());
                        orderExportDTO.setChineseDepartmentEnglishDepartment(StrUtil.builder()
                                .append("中文客服：").append(orderVideo.getContact() == null ? StrUtil.EMPTY : orderVideo.getContact().getName()).append(cr)
                                .append("英文客服：").append(orderVideo.getIssue() == null ? StrUtil.EMPTY : orderVideo.getIssue().getName())
                                .toString());
                        orderExportDTO.setOrderOperation(StrUtil.builder()
                                .append("姓名:").append(Optional.ofNullable(orderVideo.getCreateOrderUserName()).orElse("")).append(cr)
                                .append("微信名:").append(Optional.ofNullable(orderVideo.getCreateOrderUserNickName()).orElse("")).append(cr)
                                .append("ID:").append(orderVideo.getCreateOrderUserAccount())
                                .toString());
                        if (CollUtil.isNotEmpty(orderLogisticSimpleVOMap)) {
                            List<OrderLogisticSimpleVO> orderLogisticSimpleVOS = orderLogisticSimpleVOMap.get(orderVideo.getId());
                            if (CollUtil.isNotEmpty(orderLogisticSimpleVOS)) {
                                if (orderVideo.getRollbackId() == null || Arrays.asList(OrderStatusEnum.UN_FINISHED.getCode(), OrderStatusEnum.NEED_CONFIRM.getCode(), OrderStatusEnum.FINISHED.getCode()).stream().anyMatch(s -> orderVideo.getStatus() == s)) {
                                    StringBuilder logisticsInfo = StrUtil.builder();
                                    Optional<OrderLogisticSimpleVO> max = orderLogisticSimpleVOS.stream().max(Comparator.comparing(OrderLogisticSimpleVO::getCreateTime));
                                    if (null != max) {
                                        logisticsInfo.append(ReissueEnum.REISSUE.getCode().equals(max.get().getReissue()) ? "补发单号：" : "物流单号：").append(cr)
                                                .append(max.get().getNumber()).append(cr)
                                                .append(LogisticMainStatus.getSketchByLabel(max.get().getMainStatus())).append(cr);
                                        orderExportDTO.setLogisticsInfo(logisticsInfo.toString());
                                    }
                                    //原先未处理，先调整有发货才有发货时间
                                    orderExportDTO.setShippingTime(orderVideo.getShippingTime());
                                    if (ObjectUtil.isNotNull(orderVideo.getLogisticInfo()) && LogisticMainStatus.DELIVERED.getLabel().equals(orderVideo.getLogisticInfo().getMainStatus())) {
                                        orderExportDTO.setReceiptTime(orderVideo.getLogisticInfo().getCurTime());
                                    }
                                    //设置确认收货时间
                                    Optional<OrderLogisticSimpleVO> createMax = orderLogisticSimpleVOS.stream().max(Comparator.comparing(OrderLogisticSimpleVO::getCreateTime));
                                    if (createMax.isPresent() && createMax.get().getReceipt() == 1) {
                                        Optional<OrderLogisticSimpleVO> maxed = orderLogisticSimpleVOS.stream().filter(ols -> ols.getReceiptTime() != null).max(Comparator.comparing(OrderLogisticSimpleVO::getReceiptTime));
                                        orderExportDTO.setFinalReceiptTime(maxed.isPresent() ? maxed.get().getReceiptTime() : null);
                                        if (ObjectUtil.isNull(orderExportDTO.getReceiptTime())) {
                                            orderExportDTO.setReceiptTime(orderExportDTO.getFinalReceiptTime());
                                        }
                                    }
                                }
                            }
                        }

                        List<OrderVideoComment> videoComments = orderVideoCommentMap.get(orderVideo.getId());
                        if (CollUtil.isNotEmpty(videoComments)) {
                            StringBuilder orderNote = StrUtil.builder();
                            videoComments.sort(Comparator.comparing(OrderVideoComment::getCreateTime).reversed());
                            for (OrderVideoComment videoComment : videoComments) {
                                orderNote.append(videoComment.getCreateBy()).append(cr)
                                        .append(DateUtil.format(videoComment.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN)).append(cr)
                                        .append(videoComment.getCommentContent()).append(cr);
                            }
                            orderExportDTO.setOrderNote(orderNote.toString());
                        }
                        OrderVideoModel orderVideoModel = orderVideoModelMap.get(orderVideo.getId());
                        if (ObjectUtil.isNotNull(orderVideoModel)) {
                            orderExportDTO.setSubmitPreselectModelDate(orderVideoModel.getCreateTime());
                        }

                        orderExportDTO.setCarryType(orderVideo.getCarryType());
                        orderExportDTO.setModelCommissionUnit(orderVideo.getCommissionUnit());
                        orderExportDTO.setModelCommission(orderVideo.getCommission());

                        orderExportDTO.setReferenceVideoLink(StrUtil.isNotBlank(orderVideo.getReferenceVideoLink()) ? orderVideo.getReferenceVideoLink() : "-");
                        orderExportDTO.setShippingRemark(orderVideo.getShippingRemark());
                        orderExportDTO.setIsRollback(orderVideo.getRollbackId() == null ? null : StatusTypeEnum.YES.getCode());
                        orderExportDTO.setShippingTime(orderVideo.getShippingTime());

                        orderExportDTO.setAfterSaleTaskStatus(orderVideo.getAfterSaleTaskStatus() == null ? 0 : orderVideo.getAfterSaleTaskStatus());

                        orderExportDTO.setIsCare(orderVideo.getIsCare());
                        return orderExportDTO;
                    }).collect(Collectors.toList());
                    orderExportDTOList.addAll(orderExportDTOS);
                }
            } catch (Exception e) {
                log.error("导出视频订单失败！errorMessage：", e);
                throw new ServiceException("导出视频订单失败！");
            }
        }
        ExcelUtil<OrderExportDTO> util = new ExcelUtil<>(OrderExportDTO.class);

        // 设置响应头信息
        ExcelUtil.setAttachmentResponseHeader(response, "视频订单导出");
        util.exportExcel(response, orderExportDTOList, "视频订单导出");
    }

    /**
     * 查看视频订单_视频详细信息（运营端）
     */
    @Override
    public OrderVideoDetailVO getOrderVideoDetailBackInfo(Long videoId, Boolean isBusiness) {
        OrderVideoDetailVO orderVideoDetailVO = assembleOrderVideoCommonInfo(videoId, isBusiness);
        if (orderVideoDetailVO == null) {
            return new OrderVideoDetailVO();
        }

        List<OrderFeedBackMaterialInfoSimpleVO> orderFeedBackMaterialInfoSimpleVOS = orderVideoFeedBackMaterialService.selectOrderFeedBackMaterialInfoSimpleVOListByVideoId(videoId);
        orderVideoDetailVO.setOrderFeedBackMaterialInfoSimpleVOS(orderFeedBackMaterialInfoSimpleVOS);

        List<OrderVideoModelChangeVO> orderVideoModelChangeVOS = orderVideoModelChangeService.selectOrderVideoModelChangeListByVideoId(videoId);
        if (CollUtil.isNotEmpty(orderVideoModelChangeVOS)) {
            List<OrderVideoModelChangeVO> orderVideoIntentionModelChangeVOS = orderVideoModelChangeVOS.stream().filter(item -> OrderVideoModelChangeSourceEnum.INTENTION_MODEL.getCode().equals(item.getSource())).sorted(Comparator.comparing(OrderVideoModelChangeVO::getCreateTime).reversed()).collect(Collectors.toList());
            List<OrderVideoModelChangeVO> orderVideoShootModelChangeVOS = orderVideoModelChangeVOS.stream().filter(item -> OrderVideoModelChangeSourceEnum.SHOOT_MODEL.getCode().equals(item.getSource())).sorted(Comparator.comparing(OrderVideoModelChangeVO::getCreateTime).reversed()).collect(Collectors.toList());
            orderVideoDetailVO.setOrderVideoIntentionModelChangeVOS(orderVideoIntentionModelChangeVOS);
            orderVideoDetailVO.setOrderVideoShootModelChangeVOS(orderVideoShootModelChangeVOS);
        }

        List<OrderVideoTag> orderVideoTags = orderVideoTagService.selectListByVideoIdAndCategory(videoId, ModelTagEnum.CATEGORY.getCode());
        if (CollUtil.isNotEmpty(orderVideoTags)) {
            Set<Long> tagIds = orderVideoTags.stream().map(OrderVideoTag::getTagId).collect(Collectors.toSet());
            List<TagVO> tagVO = remoteService.getTagVO(tagIds);
            orderVideoDetailVO.getOrderVideoSimpleVO().setProductCategory(tagVO);
        }

        //  任务单状态
        Map<Long, List<VideoTaskOrderVO>> videoTaskOrderVOMap = getVideoTaskOrderVOMap(List.of(videoId));
        List<VideoTaskOrderVO> videoTaskOrderVOS = videoTaskOrderVOMap.get(videoId);
        if (CollUtil.isNotEmpty(videoTaskOrderVOS)) {
            List<VideoTaskOrderVO> afterSaleTaskVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.AFTER_SALE.getCode().equals(item.getTaskType())).collect(Collectors.toList());
            List<VideoTaskOrderVO> workOrderVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.WORK_ORDER.getCode().equals(item.getTaskType())).collect(Collectors.toList());
            //  添加售后单状态
            if (CollUtil.isNotEmpty(afterSaleTaskVOS)) {
                if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()) || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(item.getStatus()))) {
                    orderVideoDetailVO.getOrderVideoSimpleVO().setAfterSaleTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                } else if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                    orderVideoDetailVO.getOrderVideoSimpleVO().setAfterSaleTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                }
            }

            //  添加工单状态
            if (CollUtil.isNotEmpty(workOrderVOS)) {
                if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()))) {
                    orderVideoDetailVO.getOrderVideoSimpleVO().setWorkOrderTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                } else if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                    orderVideoDetailVO.getOrderVideoSimpleVO().setWorkOrderTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                }
            }
        }

        List<OrderVideoRefundVO> orderVideoRefundVOS = orderVideoRefundService.selectOrderVideoRefundListByVideoId(Arrays.asList(videoId));
        orderVideoDetailVO.setOrderVideoRefundVOS(orderVideoRefundVOS);
        orderVideoDetailVO.initRefundAmountTotal();

        return orderVideoDetailVO;
    }

    /**
     * 查看视频订单_视频详细信息（商家端）
     */
    @Override
    public OrderVideoDetailVO getOrderVideoDetailCompanyInfo(Long videoId, Boolean isBusiness) {
        return assembleOrderVideoCommonInfo(videoId, isBusiness);
    }

    private OrderVideoDetailVO assembleOrderVideoCommonInfo(Long videoId, Boolean isBusiness) {
        OrderVideoDetailVO orderVideoDetailVO = new OrderVideoDetailVO();
        //  视频订单详情
        OrderVideoVO orderVideoVO = getOrderVideoInfo(videoId);
        if (ObjectUtil.isNull(orderVideoVO)) {
            return null;
        }
        OrderVideoSimpleVO orderVideoSimpleVO = BeanUtil.copyProperties(orderVideoVO, OrderVideoSimpleVO.class);
        orderVideoSimpleVO.setOrderDiscountDetailVOS(orderPromotionDetailService.selectOrderDiscountDetailsByVideoId(videoId));

        Order order = getOrderByOrderNum(orderVideoVO.getOrderNum());
        OrderSimpleVO orderSimpleVO = BeanUtil.copyProperties(order, OrderSimpleVO.class);
        orderSimpleVO.setPayNum(ObjectUtil.isNotNull(order.getRecordTime()) ? order.getPayNum() : null);
        orderVideoDetailVO.setOrderSimpleVO(orderSimpleVO);
        orderVideoDetailVO.setOrderVideoSimpleVO(orderVideoSimpleVO);

        if (IsObjectEnum.OBJECT.getCode().equals(orderVideoVO.getIsObject()) && ObjectUtil.isNotNull(orderVideoVO.getShootModelId())) {
            //  视频订单所有模特收件地址
            List<OrderVideoModelShippingAddressVO> orderVideoModelShippingAddressVOS = orderVideoModelShippingAddressService.selectListByVideoId(videoId);
            List<ShippingInfoSimpleVO> shippingInfoSimpleVOS = BeanUtil.copyToList(orderVideoModelShippingAddressVOS, ShippingInfoSimpleVO.class);

            //  视频订单所有发货单号
            List<OrderVideoLogistic> videoLogistics = orderVideoLogisticService.selectListByVideoId(Collections.singletonList(orderVideoVO.getId()));
            List<OrderLogisticSimpleVO> videoLogisticVOS = BeanUtil.copyToList(videoLogistics, OrderLogisticSimpleVO.class);
            List<String> numbers = videoLogisticVOS.stream().map(OrderLogisticSimpleVO::getNumber).collect(Collectors.toList());
            //  发货单号明细（具体物流信息）
            Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numbers);

            Map<Long, List<OrderLogisticSimpleVO>> videoLogisticMap = videoLogisticVOS.stream().collect(Collectors.groupingBy(OrderLogisticSimpleVO::getShippingAddressId));

            List<OrderLogisticSimpleVO> orderLogisticSimpleVOS = new ArrayList<>();
            for (int i = 0; i < shippingInfoSimpleVOS.size(); i++) {
                ShippingInfoSimpleVO shippingInfoSimpleVO = shippingInfoSimpleVOS.get(i);
                List<OrderLogisticSimpleVO> logistics = videoLogisticMap.get(shippingInfoSimpleVO.getId());
                if (CollUtil.isEmpty(logistics)) {
                    //  若无该地址下无发货单号 且是首条地址 一般都是需发货状态 此时模特收件地址需要模特库最新的 而不要库里的快照地址
                    if (i == 0 && OrderStatusEnum.NEED_FILLED.getCode().equals(orderVideoVO.getStatus())) {
                        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(Collections.singletonList(orderVideoVO.getShootModelId()));
                        //  模特实时收件地址
                        BeanUtil.copyProperties(modelMap.getOrDefault(orderVideoVO.getShootModelId(), new ModelInfoVO()), shippingInfoSimpleVO, ignoreProperties);
                        //  取orderVideoModelShippingAddressVOS第一条 因为订单流转到需发货 会存储当前拍摄模特的收件地址 以及 发货备注 商家发货标记 之类的
                        OrderVideoModelShippingAddressVO orderVideoModelShippingAddressVO = orderVideoModelShippingAddressVOS.get(0);

                        BusinessVO businessVo = remoteService.getBusinessVo(BusinessDTO.builder().id(orderVideoVO.getCreateOrderBusinessId()).build());
                        if (StatusTypeEnum.NO.getCode().equals(businessVo.getPhoneVisible())) {
                            shippingInfoSimpleVO.setPhone(null);
                            shippingInfoSimpleVO.getShootModel().setPhone(null);
                        }
                        //  设置商家发货标记
                        OrderLogisticSimpleVO orderLogisticSimpleVO = new OrderLogisticSimpleVO();
                        orderLogisticSimpleVO.setLogisticFlag(orderVideoModelShippingAddressVO.getLogisticFlag());
                        orderLogisticSimpleVO.setLogisticFlagRemark(orderVideoModelShippingAddressVO.getLogisticFlagRemark());
                        orderLogisticSimpleVO.setLogisticFlagTime(orderVideoModelShippingAddressVO.getLogisticFlagTime());
                        orderLogisticSimpleVO.setShippingInfoSimpleVO(shippingInfoSimpleVO);
                        orderLogisticSimpleVOS.add(orderLogisticSimpleVO);
                        continue;
                    }
                    if (StatusTypeEnum.NO.getCode().equals(shippingInfoSimpleVO.getPhoneVisible())) {
                        shippingInfoSimpleVO.setPhone(null);
                        shippingInfoSimpleVO.getShootModel().setPhone(null);
                    }
                    //  若不是首条地址 则是历史未发货的收件地址 需要展示
                    OrderVideoModelShippingAddressVO orderVideoModelShippingAddressVO = orderVideoModelShippingAddressVOS.get(i);
                    OrderLogisticSimpleVO orderLogisticSimpleVO = new OrderLogisticSimpleVO();
                    orderLogisticSimpleVO.setLogisticFlag(orderVideoModelShippingAddressVO.getLogisticFlag());
                    orderLogisticSimpleVO.setLogisticFlagRemark(orderVideoModelShippingAddressVO.getLogisticFlagRemark());
                    orderLogisticSimpleVO.setLogisticFlagTime(orderVideoModelShippingAddressVO.getLogisticFlagTime());
                    orderLogisticSimpleVO.setShippingInfoSimpleVO(shippingInfoSimpleVO);
                    orderLogisticSimpleVOS.add(orderLogisticSimpleVO);
                } else {
                    if (StatusTypeEnum.NO.getCode().equals(shippingInfoSimpleVO.getPhoneVisible())) {
                        shippingInfoSimpleVO.setPhone(null);
                        shippingInfoSimpleVO.getShootModel().setPhone(null);
                    }
                    //  设置发货单号的收件地址、物流详情
                    for (OrderLogisticSimpleVO orderLogisticSimpleVO : logistics) {
                        orderLogisticSimpleVO.setShippingInfoSimpleVO(shippingInfoSimpleVO);

                        List<LogisticInfoVO> logisticInfo = logisticMap.getOrDefault(orderLogisticSimpleVO.getNumber(), new LogisticVO()).getLogisticInfo();
                        if (CollUtil.isNotEmpty(logisticInfo)) {
                            orderLogisticSimpleVO.setLogisticInfo(logisticInfo);
                            orderLogisticSimpleVO.setMainStatus(LogisticMainStatus.getSketchByLabel(logisticInfo.get(0).getMainStatus()));
                        }
                    }
                    orderLogisticSimpleVOS.addAll(logistics);
                }
            }

            orderVideoDetailVO.setOrderLogisticSimpleVOS(orderLogisticSimpleVOS);
        }
        OrderVideoUploadLinkSimpleVO orderVideoUploadLinkSimpleVO = orderVideoUploadLinkService.getOrderVideoUploadLinkSimpleVO(videoId);
        orderVideoDetailVO.setOrderVideoUploadLinkSimpleVO(orderVideoUploadLinkSimpleVO);

        if (!isBusiness) {
            List<OrderFeedBackSimpleVO> orderFeedBackSimpleVO = orderVideoFeedBackService.getOrderFeedBackSimpleVO(videoId);
            orderVideoDetailVO.setOrderFeedBackSimpleVOS(orderFeedBackSimpleVO);
        } else {
            List<OrderFeedBackVO> feedBackList = orderVideoFeedBackService.getFeedBackList(ListUtil.of(videoId));
            List<OrderFeedBackSimpleVO> orderFeedBackSimpleVO = new ArrayList<>();
            for (OrderFeedBackVO vo : feedBackList) {
                OrderFeedBackSimpleVO result = new OrderFeedBackSimpleVO();
                BeanUtils.copyProperties(vo, result);
                if (vo.getType().equals(OrderVideoFeedBackTypeEnum.VIDEO.getCode())) {
                    result.setUrl(vo.getVideoUrl());
                } else {
                    result.setUrl(vo.getPicUrl());
                }
                orderFeedBackSimpleVO.add(result);
            }
            orderVideoDetailVO.setOrderFeedBackSimpleVOS(orderFeedBackSimpleVO);
        }

        List<OrderVideoOperateVO> orderVideoOperateVOS = orderVideoOperateService.selectOrderVideoOperateListByVideoId(videoId);
        orderVideoDetailVO.setOrderVideoOperateVOS(orderVideoOperateVOS);

        List<OrderVideoFlowNodeDiagramVO> orderVideoFlowNodeDiagramVOS = orderVideoFlowNodeDiagramService.selectOrderVideoFlowNodeDiagramListByVideoId(videoId);
        orderVideoDetailVO.setOrderVideoFlowNodeDiagramVOS(orderVideoFlowNodeDiagramVOS);
        return orderVideoDetailVO;
    }

    /**
     * 根据订单金额计算公式 获取订单最终金额
     */
    @Override
    public BigDecimal getOrderFinalAmount(Order order) {
        if (order.getOrderAmount() == null || BigDecimal.ZERO.equals(order.getOrderAmount())) {
            return BigDecimal.ZERO;
        }
        return order.getOrderAmount()
                //  + 税点费用
                .add((order.getTaxPointCost() == null || BigDecimal.ZERO.equals(order.getTaxPointCost())) ? BigDecimal.ZERO : order.getTaxPointCost())
                //  - 种草码优惠金额
                .subtract((order.getSeedCodeDiscount() == null || BigDecimal.ZERO.equals(order.getSeedCodeDiscount())) ? BigDecimal.ZERO : order.getSeedCodeDiscount())
                //  - 折扣金额
                .subtract((order.getOrderPromotionAmount() == null || BigDecimal.ZERO.equals(order.getOrderPromotionAmount())) ? BigDecimal.ZERO : order.getOrderPromotionAmount())
                //  + 运营修改的订单金额 order.getBackModifyAmount() 正数为增加金额 反之
                .add((order.getBackModifyAmount() == null || BigDecimal.ZERO.equals(order.getBackModifyAmount())) ? BigDecimal.ZERO : order.getBackModifyAmount());
    }

    /**
     * 根据订单金额计算公式 获取订单最终金额
     */
    public BigDecimal getOrderFinalAmount(Order order, BigDecimal totalDiscountAmount) {
        if (order.getOrderAmount() == null || BigDecimal.ZERO.equals(order.getOrderAmount())) {
            return BigDecimal.ZERO;
        }
        return order.getOrderAmount()
                //  + 税点费用
                .add((order.getTaxPointCost() == null || BigDecimal.ZERO.equals(order.getTaxPointCost())) ? BigDecimal.ZERO : order.getTaxPointCost())
                //  - 优惠金额
                .subtract(totalDiscountAmount)
                //  + 运营修改的订单金额 order.getBackModifyAmount() 正数为增加金额 反之
                .add((order.getBackModifyAmount() == null || BigDecimal.ZERO.equals(order.getBackModifyAmount())) ? BigDecimal.ZERO : order.getBackModifyAmount());
    }

    /**
     * 更改购物车意向模特
     */
    @Override
    public CreateOrderVO updateCartIntentionModel(UpdateCartIntentionModelDTO dto) {
        return videoCartService.updateCartIntentionModel(dto);
    }

    /**
     * 视频订单发货信息
     */
    @Override
    public ShippingVO shippingInfo(Long videoId) {
        return orderVideoService.shippingInfo(videoId);
    }

    /**
     * 重新加入购物车
     */
    @Override
    public void rejoinCart(Long videoId) {
        videoCartService.rejoinCart(videoId);
    }

    /**
     * 订单详情-变更记录
     */
    @Override
    public List<OrderVideoChangeLogVO> getVideoHistoryChangeRecord(Long videoId) {
        return orderVideoChangeLogService.selectVideoChangeListByVideoId(videoId);
    }

    /**
     * 运营帮商家上传素材
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void backHelpUploadMaterial(OrderVideoUploadLinkDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());

        List<OrderVideoFeedBack> feedBackList = orderVideoFeedBackService.getFeedBackList(dto.getVideoId(), orderVideo.getRollbackId());
        Assert.isTrue(CollUtil.isNotEmpty(feedBackList), "当前订单成品未完成，无法上传~");

        checkShootModelType(orderVideo);

        dto.setObject(UploadObjectEnum.BACK.getCode());
        dto.setUserId(SecurityUtils.getUserId());
        orderVideoUploadLinkService.saveOrderVideoUploadLink(dto);

        createOrderFlow(orderVideo, OrderStatusEnum.FINISHED, "运营帮商家上传素材");
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideo.getId(), OrderVideoFlowNodeEnum.MERCHANT_CONFIRMATION);

        orderVideoFeedBackMaterialService.closeMaterialInfoByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId(), EditCloseReasonEnum.LINKAGE_OFF);
    }

    private void checkShootModelType(OrderVideo orderVideo) {
        Assert.notNull(orderVideo, "视频订单数据不能为空！");
        Assert.isTrue(PlatformEnum.AMAZON.getCode().equals(orderVideo.getPlatform()) && (ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(orderVideo.getModelType()) || ModelTypeEnum.ALL.getCode().equals(orderVideo.getModelType())), "亚马逊素人才需要上传至关联区！");
        if (ObjectUtil.isNotNull(orderVideo.getShootModelId())) {
            List<OrderVideoModelChangeVO> orderVideoModelChangeVOS = orderVideoModelChangeService.selectOrderVideoModelChangeListByVideoIdAndModelId(orderVideo.getId(), orderVideo.getShootModelId());
            if (CollUtil.isNotEmpty(orderVideoModelChangeVOS)) {
                //获取最大的数据
                List<OrderVideoModelChangeVO> changeVOS = orderVideoModelChangeVOS.stream().filter(item -> OrderVideoModelChangeSourceEnum.SHOOT_MODEL.getCode().equals(item.getSource())).sorted(Comparator.comparing(OrderVideoModelChangeVO::getSelectedTime).reversed()).collect(Collectors.toList());
                Assert.isTrue(PlatformEnum.AMAZON.getCode().equals(orderVideo.getPlatform()) && ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(changeVOS.get(0).getType()), "亚马逊素人才需要上传至关联区！");
            }
        }
    }

    /**
     * 获取模特超时率、售后率
     */
    @Override
    public List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate() {
        return orderVideoModelService.getModelOvertimeRateAndAfterSaleRate();
    }

    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    @Override
    public List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds) {
        return orderVideoService.getModelOrderCount(modelIds);
    }

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    @Override
    public void updateBatchOrderCartProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto) {
        videoCartService.updateBatchOrderCartProductPic(dto);
    }

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    @Override
    public void updateBatchOrderVideoProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto) {
        orderVideoService.updateBatchOrderVideoProductPic(dto);
    }

    /**
     * 复制购物车
     */
    @Override
    public VideoCartVO copyCart(Long cartId) {
        return videoCartService.copyCart(cartId);
    }

    /**
     * 批量更新视频订单的对接人
     */
    @Override
    public Boolean updateOrderVideoContact(UpdateOrderContactDTO dto) {
        try {
            List<Order> orders = baseMapper.selectListByBusinessId(dto.getBusinessIds());
            if (CollUtil.isEmpty(orders)) {
                return true;
            }

            List<String> orderNums = orders.stream().map(Order::getOrderNum).collect(Collectors.toList());

            orderVideoService.updateOrderVideoContact(orderNums, dto.getContactId());
            return true;
        } catch (Exception e) {
            log.error("批量更新视频订单的对接人失败", e);
            return false;
        }
    }

    /**
     * 当前商户购物车数量统计
     */
    @Override
    public Long getCartCount() {
        return videoCartService.getCartCount();
    }

    /**
     * 运营根据匹配情况修改订单
     */
    @Override
    public void operationVideoCase(OrderOperationVideoCaseDTO dto) {
        orderVideoService.operationVideoCase(dto);
    }

    /**
     * 获取订单总数
     */
    @Override
    public Long getOrderCount() {
        return baseMapper.getOrderCount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
    }

    @Override
    public Long getValidOrderMemberCount(Long bizUserId) {
        return baseMapper.getValidOrderMemberCount(bizUserId);
    }

    @Override
    public Long getUnCancelOrderCount(Long businessId) {
        return baseMapper.getUnCancelOrderCount(businessId);
    }

    /**
     * 根据条件查询视频订单列表
     */
    @Override
    public List<OrderVideo> selectOrderVideoListByCondition(OrderVideoListDTO orderVideoListDTO) {
        PageUtils.startPage();
        return orderVideoService.selectOrderVideoListByConditionV2(orderVideoListDTO);
    }


    @Override
    public void updateBusinessBalance(BusinessBalanceDTO businessBalanceDto) {
        //如果使用余额为0则不需要加锁
        if (ObjectUtil.isNull(businessBalanceDto.getUseBalance()) || BigDecimal.ZERO.compareTo(businessBalanceDto.getUseBalance()) == 0) {
            return;
        }
        LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        for (BusinessBalanceFlowDTO businessBalanceFlowDTO : businessBalanceDto.getBusinessBalanceFlowDTOS()) {
            businessBalanceFlowDTO.setLoginBase(loginUser);
        }
        Assert.notNull(remoteBusinessAccountService.updateBusinessBalance(businessBalanceDto, SecurityConstants.INNER), "修改商家余额失败");
    }

    @Override
    public BusinessAccountVO flowMember(FlowMemberDto dto) {
        BusinessAccountVO businessAccountVO = remoteBusinessAccountService.flowMember(dto);
        Assert.notNull(businessAccountVO, "会员状态流转失败");

        return businessAccountVO;
    }


    /**
     * 上传产品图
     */
    @Override
    public void uploadProductImage(Long videoId, String productPic) {
        orderVideoService.uploadProductImage(videoId, productPic);
    }

    @Override
    public OrderVideoStatisticsVO orderVideoStatistics(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        return orderVideoService.orderVideoStatistics(orderVideoStatisticsDTO);
    }

    @Override
    public List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        return orderVideoService.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
    }

    /**
     * 根据订单号查询订单
     */
    @Override
    public Order getOrderByOrderNum(String orderNum) {
        return baseMapper.getOrderByOrderNum(orderNum);
    }

    /**
     * 提交凭证信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitCredential(SubmitCredentialDTO dto, Boolean isAnother) {
        if (StringUtils.isNotBlank(dto.getSeedCode())) {
            dto.setSeedCode(dto.getSeedCode().toUpperCase());
        }
        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        } else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }
        //  校验支付方式 只能是全币种或者对公
        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);
        Assert.notEmpty(orders, ORDER_DOES_NOT_EXIST);
        Assert.isTrue(orders.stream().noneMatch(Order::getIsDefaultExchangeRate), "订单百度汇率异常，请联系客服处理！");
        if (!isAnother) {
            Assert.isTrue(orders.stream().allMatch(item -> ObjectUtil.isNotNull(item.getPayType())), "订单支付类型不能为空~");
        }
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getPayTime())), "订单已支付~");
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getSubmitCredentialTime())), "订单已提交审核~");
        Assert.isTrue(PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())
                        || PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType())
                        || PayTypeEnum.BANK.getCode().equals(dto.getPayType()),
                "支付方式只能是全币种或者对公");

        Integer orderType = orders.get(0).getOrderType();
        BigDecimal ordersUseBalance = orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())) {
            Assert.isTrue(dto.getPayAmountDollar().compareTo(orderPayProperties.getFullCurrencyUpperLimit()) >= 0, "应支付金额少于" + orderPayProperties.getFullCurrencyUpperLimit() + "USD，不支持使用全币种支付");
        }
        List<OrderVideo> orderVideos = getOrderVideosByOrderType(orderType, orderNums, isAnother);

        OrderPayInfoVO orderPayInfoVO;
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            orderPayInfoVO = SpringUtils.getBean(PayService.class).payInfo(PayInfoDTO.builder().mergeId(dto.getMergeId()).orderNum(dto.getOrderNum()).isPublic(PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()) ? StatusEnum.ENABLED.getCode() : StatusEnum.UN_ENABLED.getCode()).useBalance(ordersUseBalance).isAnother(isAnother).build());
        } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(orderType)) {
            PayMemberInfoDTO build = PayMemberInfoDTO.builder()
                    .orderNum(dto.getOrderNum())
                    .isPublic((PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()) ? StatusEnum.ENABLED.getCode() : StatusEnum.UN_ENABLED.getCode()))
                    .useBalance(ordersUseBalance)
                    .seedCode(dto.getSeedCode())
                    .build();
            orderPayInfoVO = SpringUtils.getBean(PayService.class).payMemberInfo(build, isAnother);
        } else {
            throw new ServiceException("订单类型有误");
        }

        // if (StringUtils.isNull(dto.getTaxPointCost())) {
        //     dto.setTaxPointCost(BigDecimal.ZERO);
        // }
        Assert.isFalse(!orderPayInfoVO.isCanInputSeedCode() && StrUtil.isNotBlank(dto.getSeedCode()), "该订单暂不支持种草码优惠，请联系运营确认");
        // if (orderPayInfoVO.getTaxPointCost() != null && dto.getTaxPointCost() != null) {
        //     Assert.isTrue(orderPayInfoVO.getTaxPointCost().compareTo(dto.getTaxPointCost()) == 0, "税点费用与实际不符，不允许操作");
        // }
        Assert.isTrue(orderPayInfoVO.getPayAmount().compareTo(dto.getPayAmount()) == 0, "应支付金额与实际不符，不允许操作");
        Assert.isTrue(orderPayInfoVO.getPayAmountDollar().compareTo(dto.getPayAmountDollar()) == 0, "应支付美元与实际不符，不允许操作");

        getOrder(dto, orders);
        baseMapper.updateBatchById(orders);

        UploadCredentialDTO uploadCredentialDTO = new UploadCredentialDTO();
        uploadCredentialDTO.setObjectKeys(dto.getObjectKeys());
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            uploadCredentialDTO.setPayNum(orders.get(0).getPayNum());
        } else {
            uploadCredentialDTO.setPayNum(orders.get(0).getPayNum());
            uploadCredentialDTO.setOrderNum(orders.get(0).getOrderNum());
        }
        orderDocumentResourceService.uploadCredential(uploadCredentialDTO);

        if (PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()) || PayTypeEnum.PUBLIC_BALANCE.getCode().equals(dto.getPayType())) {
            orderPayeeAccountService.saveOrderPayeeAccount(orderNums, orders.get(0).getPayeeId(), PayTypeEnum.PUBLIC.getCode());
        } else if (PayTypeEnum.BANK.getCode().equals(dto.getPayType()) || PayTypeEnum.BANK_BALANCE.getCode().equals(dto.getPayType())) {
            orderPayeeAccountService.saveOrderPayeeAccount(orderNums, orders.get(0).getPayeeId(), PayTypeEnum.BANK.getCode());
        } else if (PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType()) || PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(dto.getPayType())) {
            orderPayeeAccountService.saveOrderPayeeAccount(orderNums, orders.get(0).getPayeeId(), PayTypeEnum.FULL_CURRENCY.getCode());
        }

        OrderAnotherPay orderAnotherPay = orderAnotherPayService.getValidLinkByOrderNumOrMergeId(dto.getOrderNum(), dto.getMergeId());
        //流转状态待审核
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            createOrderFlow(orderVideos, OrderStatusEnum.UN_CHECK, "提交凭证信息");
            List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
                OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
                orderVideoOperateDTO.setVideoId(item.getId());
                orderVideoOperateDTO.setEventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.SUBMIT_PAYMENT.getEventContent(),
                        PayTypeEnum.getPayTypeEnumByCode(dto.getPayType() > 10 ? dto.getPayType() - 10 : dto.getPayType()).getLabel(),
                        ObjectUtil.isNotNull(dto.getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(dto.getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY));
                return orderVideoOperateDTO;
            }).collect(Collectors.toList());
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.SUBMIT_PAYMENT.getEventName(), null, OrderVideoOperateTypeEnum.SUBMIT_PAYMENT.getIsPublic(), isAnother && ObjectUtil.isNotNull(orderAnotherPay) ? orderAnotherPay.getCreateBy() : null, orderVideoOperateDTOS);
        } else {
            flowOrderMember(orders.get(0), OrderMemberStatusEnum.UN_CHECK, OrderFlowButtonEnum.SUBMIT_CREDENTIAL);
        }
        SpringUtils.getBean(WeChatService.class).closeAllQrcode(dto.getOrderNum());
        if (ObjectUtil.isNotNull(orderAnotherPay)) {
            orderAnotherPayService.payCancel(orderAnotherPay.getUuid());
        }
    }

    @Override
    public List<OrderPayeeAccount> queryOrderPayeeAccountListByOrderNums(List<String> orderNums) {
        return orderPayeeAccountService.queryListByOrderNums(orderNums);
    }

    private List<OrderVideo> getOrderVideosByOrderType(Integer orderType, List<String> orderNms, Boolean isAnother) {
        List<OrderVideo> orderVideos = new ArrayList<>();
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            orderVideos = orderVideoService.selectValidByOrderNumsAsc(orderNms);
            if (!isAnother) {
                orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList())).businessShare(true).build());
            }
            orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY);
        } else {
            OrderMemberVO orderMember = orderMemberService.getOrderMember(orderNms.get(0));
            orderMemberService.checkMemberStatus(orderMember, OrderMemberStatusEnum.UN_PAY);
        }
        return orderVideos;
    }

    private void getOrder(SubmitCredentialDTO dto, List<Order> orders) {
        if (PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()) || PayTypeEnum.PUBLIC_BALANCE.getCode().equals(dto.getPayType())) {
            dto.setPayAccount(null);
        }
        DateTime date = DateUtil.date();
        OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(orders.get(0).getPayType());
        LoginBusiness loginBusinessUser = SecurityUtils.getLoginBusinessUser();

        DistributionChannel distributionChannelEntityBySeedCode = new DistributionChannel();
        ChannelBrokeRageVO channelBrokeRageVO = new ChannelBrokeRageVO();
        PromotionActivityVO promotionActivityVO = loadChannel(dto.getSeedCode(), distributionChannelEntityBySeedCode, channelBrokeRageVO);

        List<OrderPromotionDetail> orderPromotionDetails = new ArrayList<>();
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        for (Order order : orders) {
            order.setPayAccount(dto.getPayAccount());
            order.setPayUserId(SecurityUtils.getUserId());
            order.setPayUserName(SecurityUtils.getUsername());
            if (ObjectUtil.isNotNull(loginBusinessUser)) {
                order.setPayUserAccount(loginBusinessUser.getBusinessAccountVO().getAccount());
                order.setPayUserNickName(loginBusinessUser.getBusinessAccountVO().getNickName());
            }
            if (OrderTypeEnum.VIP_ORDER.getCode().equals(order.getOrderType())) {
                //  这里原先取前端传入的payAmount + 订单的useBalance 理论上会等于order.payAmount 因为在调动这里之前 会先执行payLock 已经会设置订单的使用余额
                //  所以 这里的前端传入的payAmount  + 订单的useBalance 是一定等于 order.payAmount (针对视频订单而言)
                //  只有在会员订单 可能使用种草码 才可能导致 payAmount + 订单的useBalance 不等于 order.payAmount 从而需要重新设置
                BigDecimal oldPayAmount = order.getPayAmount();
                BigDecimal oldPayAmountDollar = order.getPayAmountDollar();
                order.setPayAmount(dto.getPayAmount().add(order.getUseBalance() == null ? BigDecimal.ZERO : order.getUseBalance()));
                order.setPayAmountDollar(order.getPayAmount().compareTo(oldPayAmount) == 0 ? order.getPayAmountDollar() : order.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));

                //  获取总优惠金额 不包含种草码优惠金额 因为无法在生成支付二维码时 存入进去
                List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(order.getOrderNum(), new ArrayList<>());
                BigDecimal totalDiscountAmount = discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal seedCodeDiscount = SpringUtils.getBean(PayService.class).getSeedCodeDiscountV1(channelBrokeRageVO.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscount(), getOrderFinalAmount(order, totalDiscountAmount));
                order.setSeedCode(dto.getSeedCode());
                order.setMemberDiscountType(channelBrokeRageVO.getMemberDiscountType());
                order.setSettleRage(channelBrokeRageVO.getMemberDiscount());
                order.setSeedCodeDiscount(seedCodeDiscount);
                if (StrUtil.isNotBlank(dto.getSeedCode()) && ObjectUtil.isNotNull(promotionActivityVO)) {
                    Map<String, Integer> seedMemberStatusMap = remoteService.getUserMemberStatusBySeedId(List.of(distributionChannelEntityBySeedCode.getSeedId()));
                    order.setChannelName(distributionChannelEntityBySeedCode.getChannelName());
                    order.setSeedId(distributionChannelEntityBySeedCode.getSeedId());
                    order.setChannelType(distributionChannelEntityBySeedCode.getChannelType());
                    order.setSeedMemberStatus(seedMemberStatusMap.get(order.getSeedId()));

                    OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                    orderPromotionDetail.setActivityId(promotionActivityVO.getId());
                    orderPromotionDetail.setOrderNum(order.getOrderNum());
                    orderPromotionDetail.setDiscountAmount(order.getSeedCodeDiscount());
                    orderPromotionDetail.setDiscountAmountDollar(oldPayAmountDollar.subtract(order.getPayAmountDollar()));
                    orderPromotionDetail.setDiscountType(channelBrokeRageVO.getMemberDiscountType());
                    orderPromotionDetail.setAmount(channelBrokeRageVO.getMemberDiscount());
                    orderPromotionDetails.add(orderPromotionDetail);
                }
            }

            PayTypeEnum payType = PayTypeEnum.PUBLIC;
            if (order.getUseBalance() != null && order.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
                Assert.isFalse(order.getUseBalance().compareTo(order.getPayAmount()) > 0, "余额不能大于订单金额！");
                if (order.getUseBalance().compareTo(order.getPayAmount()) == 0) {
                    payType = PayTypeEnum.BALANCE;
                } else if (PayTypeEnum.BANK.getCode().equals(dto.getPayType())) {
                    payType = PayTypeEnum.BANK_BALANCE;
                } else if (PayTypeEnum.PUBLIC.getCode().equals(dto.getPayType())) {
                    payType = PayTypeEnum.PUBLIC_BALANCE;
                } else if (PayTypeEnum.ALIPAY.getCode().equals(dto.getPayType())) {
                    payType = PayTypeEnum.ALIPAY_BALANCE;
                } else if (PayTypeEnum.WECHAT.getCode().equals(dto.getPayType())) {
                    payType = PayTypeEnum.WECHAT_BALANCE;
                } else if (PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())) {
                    payType = PayTypeEnum.FULL_CURRENCY_BALANCE;
                }
            } else {
                PayTypeEnum byCode = PayTypeEnum.getPayTypeEnumByCode(dto.getPayType());
                payType = byCode == null ? PayTypeEnum.PUBLIC : byCode;
            }
            order.setPayTypeDetail(dto.getPayTypeDetail());

            // BigDecimal taxPoint = orderPayProperties.getTaxPoint();
            // if (payType.getCode().equals(PayTypeEnum.PUBLIC.getCode()) || payType.getCode().equals(PayTypeEnum.PUBLIC_BALANCE.getCode())) {
            // updateOrder.setTaxPoint(taxPoint);
            // updateOrder.setTaxPointCost(dto.getTaxPointCost());
            // } else {
            // updateOrder.setTaxPoint(taxPoint);
            // updateOrder.setTaxPointCost(BigDecimal.ZERO);
            // }
            order.setPayType(payType.getCode());
            order.setSubmitCredentialTime(date);

            //  若payeeId为空 且是12.24之前创建的订单 重新获取主体信息
            if (ObjectUtil.isNull(order.getPayeeId())) {
                if (CompareUtil.compare(order.getCreateTime(), DateUtil.parseDate("2024-12-24 00:00:00")) <= 0) {
                    order.setPayeeId(orderPayeeAccountConfigInfoDTO.getId());
                } else {
                    throw new ServiceException("系统繁忙，请刷新后重试~");
                }
            }
        }
        orderPromotionDetailService.saveBatchOrderPromotionDetail(orderPromotionDetails);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payLock(OrderPayLockDTO dto) {
        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        } else if (dto.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)) {
            List<String> preNums = List.of(dto.getOrderNum());
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(preNums).build());
            Assert.notEmpty(businessBalancePrepayVOS, "订单不存在，请检查后重试！");
            BusinessBalancePrepayVO businessBalancePrepayVO = businessBalancePrepayVOS.get(0);
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getPayTime()), "订单已支付~");
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getSubmitCredentialTime()), "订单已提交审核~");
            Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(businessBalancePrepayVO.getOrderType()), "订单类型需要是线上钱包充值类型~");
            OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(dto.getPayType());

            PrepayUpdateAppIdDTO prepayUpdateAppIdDTO = new PrepayUpdateAppIdDTO();
            prepayUpdateAppIdDTO.setPrepayNums(preNums);
            prepayUpdateAppIdDTO.setPayType(dto.getPayType());
            if (ObjectUtil.isNull(businessBalancePrepayVO.getAccountId()) || ObjectUtil.notEqual(dto.getPayType(), businessBalancePrepayVO.getPayType())) {
                prepayUpdateAppIdDTO.setAccountId(orderPayeeAccountConfigInfoDTO.getDetailId());
            }
            if (CharSequenceUtil.isBlank(businessBalancePrepayVO.getAlipayPayAppId()) && dto.getPayType().equals(PayTypeEnum.ALIPAY.getCode())) {
                prepayUpdateAppIdDTO.setAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
            if (CharSequenceUtil.isBlank(businessBalancePrepayVO.getWechatPayAppId()) && dto.getPayType().equals(PayTypeEnum.WECHAT.getCode())) {
                prepayUpdateAppIdDTO.setAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }

            remoteService.innerBusinessBalancePrepayUpdateAppId(prepayUpdateAppIdDTO);
            return;
        } else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }

        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);

        Assert.notEmpty(orders, ORDER_DOES_NOT_EXIST);
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getPayTime())), "订单已支付~");
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getSubmitCredentialTime())), "订单已提交审核~");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");

        // 收款账号配置
        OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(dto.getPayType());
        Assert.notNull(orderPayeeAccountConfigInfoDTO, "获取收款账号配置信息失败，请联系蜗牛客服处理~");

        Integer realPayType = null;
        if (orders.get(0).getPayType() != null) {
            if (orders.get(0).getPayType() > 10) {
                realPayType = orders.get(0).getPayType() - 10;
            } else {
                realPayType = orders.get(0).getPayType();
            }
        }
        List<Order> updateOrders = new ArrayList<>();
        boolean flag = false;
        for (Order order : orders) {
            Order updateOrder = new Order();
            updateOrder.setId(order.getId());
            updateOrder.setPayAmount(order.getPayAmount());
            updateOrder.setOrderNum(order.getOrderNum());

            if (CharSequenceUtil.isBlank(order.getAlipayPayAppId()) && dto.getPayType().equals(PayTypeEnum.ALIPAY.getCode())) {
                updateOrder.setAlipayPayAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
            if (CharSequenceUtil.isBlank(order.getWechatPayAppId()) && dto.getPayType().equals(PayTypeEnum.WECHAT.getCode())) {
                updateOrder.setWechatPayAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }

            if (ObjectUtil.isNull(order.getPayeeId()) || ObjectUtil.notEqual(realPayType, orderPayeeAccountConfigInfoDTO.getType())) {
                updateOrder.setPayeeId(orderPayeeAccountConfigInfoDTO.getId());
            }

            if (order.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
                // 余额已锁定 修改支付类型就行
                updateOrder.setPayType(dto.getPayType() + 10);
                // self.updateById(updateOrder);
                // return;
                flag = true;
            }

            if (dto.getUseBalance().compareTo(BigDecimal.ZERO) == 0) {
                // 选择支付状态
                updateOrder.setPayType(dto.getPayType());
                // self.updateById(updateOrder);
                // return;
                flag = true;
            }
            updateOrders.add(updateOrder);
        }
        if (flag) {
            self.updateBatchById(updateOrders);
            return;
        }

        businessBalanceLock(orders.get(0).getMerchantId());
        try {
            this.checkBusinessBalance(dto.getUseBalance());
            BigDecimal surplusUseBalance = dto.getUseBalance();
            for (Order updateOrder : updateOrders) {
                if (surplusUseBalance.compareTo(updateOrder.getPayAmount()) >= 0) {
                    updateOrder.setUseBalance(updateOrder.getPayAmount());
                    updateOrder.setPayType(PayTypeEnum.BALANCE.getCode());
                    surplusUseBalance = surplusUseBalance.subtract(updateOrder.getPayAmount());
                } else if (surplusUseBalance.compareTo(BigDecimal.ZERO) > 0) {
                    updateOrder.setUseBalance(surplusUseBalance);
                    updateOrder.setPayType(dto.getPayType() + 10);
                    surplusUseBalance = BigDecimal.ZERO;
                } else {
                    updateOrder.setUseBalance(BigDecimal.ZERO);
                    updateOrder.setPayType(dto.getPayType());
                }
            }
            self.updateBatchById(updateOrders);

            if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orders.get(0).getOrderType())) {
                //分配视频订单使用余额
                assignVideoBalance(updateOrders, dto.getUseBalance());
                //视频订单设置余额
                updateBusinessBalance(dto.getUseBalance(), BalanceSourceTypeEnum.ORDER_SPEND, orders, OrderStatusEnum.UN_CHECK, null);
            } else if (OrderTypeEnum.VIP_ORDER.getCode().equals(orders.get(0).getOrderType())) {
                updateBusinessBalance(dto.getUseBalance(), BalanceSourceTypeEnum.MEMBER_SPEND, orders, OrderStatusEnum.UN_CHECK, null);
            }
        } finally {
            businessBalanceReleaseLock(orders.get(0).getMerchantId());
        }

    }

    /**
     * 分配视频订单使用余额
     *
     * @param orders     大订单
     * @param useBalance 使用余额
     */
    @Override
    public List<OrderVideo> assignVideoBalance(List<Order> orders, BigDecimal useBalance) {
        List<OrderVideo> orderVideoList = orderVideoService.selectByOrderNums(orders.stream().map(Order::getOrderNum).collect(Collectors.toList()));
        if (CollUtil.isEmpty(orderVideoList)) {
            throw new ServiceException("视频订单不能为空~");
        }
        //需要修改的视频订单列表
        List<OrderVideo> updateOrderVideoList = new ArrayList<>();
        //  总使用余额
        BigDecimal surplusBalance = useBalance;
        //  每笔大订单使用的余额
        Map<String, BigDecimal> orderUseBalanceMap = orders.stream().collect(Collectors.toMap(Order::getOrderNum, Order::getUseBalance));

        for (OrderVideo item : orderVideoList) {
            BigDecimal orderUseBalance = orderUseBalanceMap.get(item.getOrderNum());
            if (ObjectUtil.isNull(orderUseBalance) || orderUseBalance.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (surplusBalance.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            OrderVideo updateItem = new OrderVideo();
            updateItem.setId(item.getId());
            updateItem.setOrderNum(item.getOrderNum());
            updateItem.setVideoCode(item.getVideoCode());
            updateItem.setAmount(item.getAmount());
            updateItem.setPayAmount(item.getPayAmount());
            if (orderUseBalance.compareTo(item.getPayAmount()) > 0) {
                updateItem.setUseBalance(item.getPayAmount());
                surplusBalance = surplusBalance.subtract(item.getPayAmount());
                orderUseBalanceMap.put(item.getOrderNum(), orderUseBalance.subtract(item.getPayAmount()));
            } else {
                updateItem.setUseBalance(orderUseBalance);
                surplusBalance = surplusBalance.subtract(orderUseBalance);
                orderUseBalanceMap.put(item.getOrderNum(), BigDecimal.ZERO);
            }
            updateOrderVideoList.add(updateItem);
        }
        if (CollUtil.isNotEmpty(updateOrderVideoList)) {
            orderVideoService.updateBatchById(updateOrderVideoList);
        }

        return updateOrderVideoList;

    }

    /**
     * 代付下单锁定
     */
    @Override
    public void anotherPayLock(OrderPayLockDTO dto) {
        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
        } else if (dto.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)) {
            List<String> preNums = List.of(dto.getOrderNum());
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(preNums).build());
            Assert.notEmpty(businessBalancePrepayVOS, "订单不存在，请检查后重试！");
            BusinessBalancePrepayVO businessBalancePrepayVO = businessBalancePrepayVOS.get(0);
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getPayTime()), "订单已支付~");
            Assert.isTrue(ObjectUtil.isNull(businessBalancePrepayVO.getSubmitCredentialTime()), "订单已提交审核~");
            Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(businessBalancePrepayVO.getOrderType()), "订单类型需要是线上钱包充值类型~");
            OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(dto.getPayType());
            Assert.notNull(orderPayeeAccountConfigInfoDTO, "获取收款账号配置信息失败，请联系蜗牛客服处理~");

            PrepayUpdateAppIdDTO prepayUpdateAppIdDTO = new PrepayUpdateAppIdDTO();
            prepayUpdateAppIdDTO.setPrepayNums(preNums);
            prepayUpdateAppIdDTO.setAccountId(orderPayeeAccountConfigInfoDTO.getDetailId());
            prepayUpdateAppIdDTO.setPayType(dto.getPayType());
            prepayUpdateAppIdDTO.setIsAnother(StatusTypeEnum.YES.getCode());
            if (CharSequenceUtil.isBlank(businessBalancePrepayVO.getAlipayPayAppId()) && dto.getPayType().equals(PayTypeEnum.ALIPAY.getCode())) {
                prepayUpdateAppIdDTO.setAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
            if (CharSequenceUtil.isBlank(businessBalancePrepayVO.getWechatPayAppId()) && dto.getPayType().equals(PayTypeEnum.WECHAT.getCode())) {
                prepayUpdateAppIdDTO.setAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
            remoteService.innerBusinessBalancePrepayUpdateAppId(prepayUpdateAppIdDTO);
            return;
        } else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }
        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);

        Assert.notEmpty(orders, ORDER_DOES_NOT_EXIST);
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getPayTime())), "订单已支付~");
        Assert.isTrue(orders.stream().allMatch(order -> ObjectUtil.isNull(order.getSubmitCredentialTime())), "订单已提交审核~");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");

        OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.typeInfo(dto.getPayType());
        Assert.notNull(orderPayeeAccountConfigInfoDTO, "获取收款账号配置信息失败，请联系蜗牛客服处理~");

        for (Order order : orders) {

            order.setPayeeId(orderPayeeAccountConfigInfoDTO.getId());
            if (StringUtils.isBlank(order.getAlipayPayAppId()) && dto.getPayType().equals(PayTypeEnum.ALIPAY.getCode())) {
                order.setAlipayPayAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
            if (StringUtils.isBlank(order.getWechatPayAppId()) && dto.getPayType().equals(PayTypeEnum.WECHAT.getCode())) {
                order.setWechatPayAppId(orderPayeeAccountConfigInfoDTO.getBankAccount());
            }
        }
        baseMapper.updateBatchById(orders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releasePayLock(List<Order> orders) {
        List<Order> rollbackOrders = BeanUtil.copyToList(orders, Order.class);
        for (Order order : orders) {
            order.setPayType(null);
            order.setUseBalance(BigDecimal.ZERO);
        }
        updateOrderBatchFieldNullToNull(orders);
        orderVideoService.setBalanceZeroByOrderNum(orders.stream().map(Order::getOrderNum).collect(Collectors.toList()));

        self.rollbackBalance(rollbackOrders);
    }

    private void checkBusinessBalance(BigDecimal useBalance) {
        final BusinessAccountVO businessAccount = remoteService.getBusinessAccountByAccountId(SecurityUtils.getUserId());
        Assert.notNull(businessAccount, "商家数据为空，请稍后重试");
        Assert.isTrue(businessAccount.getBusinessVO().getBalance().compareTo(useBalance.add(businessAccount.getBusinessVO().getUseBalance())) >= 0, "商家余额不足");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void balancePay(BalancePayDTO dto) {
        List<String> orderNums;
        if (ObjectUtil.isNotNull(dto.getMergeId())) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(dto.getMergeId(), null);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
            orderMergeService.completeOrderMergeByMergeIdOrPayNum(dto.getMergeId(), null);
        } else {
            orderNums = List.of(dto.getOrderNum());
            orderMergeService.checkOrderMerge(orderNums);
        }
        Assert.notEmpty(orderNums, "查无订单");

        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);
        Assert.notEmpty(orders, ORDER_DOES_NOT_EXIST);
        Assert.isTrue(orders.stream().noneMatch(Order::getIsDefaultExchangeRate), "订单百度汇率异常，请联系客服处理！");
        Assert.isTrue(orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) == 0, "余额已锁定，无法使用余额");
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        businessBalanceLock(orders.get(0).getMerchantId());
        try {
            List<OrderVideo> orderVideos = getOrderVideosByOrderType(orders.get(0).getOrderType(), orderNums, false);

            DateTime date = DateUtil.date();
            BigDecimal orderFinalPriceSum = BigDecimal.ZERO;
            List<OrderPayLog> orderPayLogs = new ArrayList<>();

            DistributionChannel distributionChannelEntityBySeedCode = new DistributionChannel();
            ChannelBrokeRageVO channelBrokeRageVO = new ChannelBrokeRageVO();
            PromotionActivityVO promotionActivityVO = loadChannel(dto.getSeedCode(), distributionChannelEntityBySeedCode, channelBrokeRageVO);

            List<OrderPromotionDetail> orderPromotionDetails = new ArrayList<>();
            List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
            Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

            for (Order order : orders) {
                order.setAuditStatus(AuditStatusEnum.APPROVE.getCode());
                order.setPayType(PayTypeEnum.BALANCE.getCode());
                order.setTaxPoint(orderPayProperties.getTaxPoint());
                order.setTaxPointCost(BigDecimal.ZERO);
                order.setPayUserId(SecurityUtils.getUserId());
                order.setPayUserAccount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount());
                order.setPayUserName(SecurityUtils.getUsername());
                order.setPayUserNickName(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getNickName());
                order.setPayTime(date);
                order.setIsRecord(StatusTypeEnum.YES.getCode());
                order.setRecordTime(date);

                BigDecimal seedCodeDiscount = SpringUtils.getBean(PayService.class).getSeedCodeDiscountV1(channelBrokeRageVO.getMemberDiscountType(), channelBrokeRageVO.getMemberDiscount(), order.getPayAmount());
                order.setSeedCode(dto.getSeedCode());
                order.setMemberDiscountType(channelBrokeRageVO.getMemberDiscountType());
                order.setSeedCodeDiscount(seedCodeDiscount);
                order.setSettleRage(channelBrokeRageVO.getMemberDiscount());
                if (StrUtil.isNotBlank(dto.getSeedCode()) && OrderTypeEnum.VIP_ORDER.getCode().equals(order.getOrderType()) && ObjectUtil.isNotNull(promotionActivityVO)) {
                    order.setChannelName(distributionChannelEntityBySeedCode.getChannelName());
                    order.setChannelType(distributionChannelEntityBySeedCode.getChannelType());
                    order.setSeedId(distributionChannelEntityBySeedCode.getSeedId());
                    Map<String, Integer> seedMemberStatusMap = remoteService.getUserMemberStatusBySeedId(List.of(distributionChannelEntityBySeedCode.getSeedId()));
                    order.setSeedMemberStatus(seedMemberStatusMap.get(order.getSeedId()));

                    OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                    orderPromotionDetail.setActivityId(promotionActivityVO.getId());
                    orderPromotionDetail.setOrderNum(order.getOrderNum());
                    orderPromotionDetail.setDiscountAmount(order.getSeedCodeDiscount());
                    orderPromotionDetail.setDiscountAmountDollar(order.getPayAmountDollar().subtract((order.getPayAmount().subtract(order.getSeedCodeDiscount())).divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN)));
                    orderPromotionDetail.setDiscountType(channelBrokeRageVO.getMemberDiscountType());
                    orderPromotionDetail.setAmount(channelBrokeRageVO.getMemberDiscount());
                    orderPromotionDetails.add(orderPromotionDetail);
                }
                //  获取总优惠金额 不包含种草码优惠金额 因为无法在生成支付二维码时 存入进去
                List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(order.getOrderNum(), new ArrayList<>());
                BigDecimal totalDiscountAmount = discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal orderFinalPrice = getOrderFinalAmount(order, totalDiscountAmount.add(seedCodeDiscount));
                order.setUseBalance(orderFinalPrice);
                order.setRealPayAmount(BigDecimal.ZERO);
                order.setRealPayAmountCurrency(BigDecimal.ZERO);
                order.setCurrency(CurrencyEnum.CNY.getCode());
                order.setPayeeId(null);

                orderFinalPriceSum = orderFinalPriceSum.add(orderFinalPrice);

                fillOrderPayLog(orderPayLogs, order);
            }
            orderPromotionDetailService.saveBatchOrderPromotionDetail(orderPromotionDetails);


            checkBusinessBalance(orderFinalPriceSum);
            updateOrderBatchFieldNullToNull(orders);
            orderPayLogService.saveBatch(orderPayLogs);

            if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orders.get(0).getOrderType())) {
                createOrderFlow(orderVideos, OrderStatusEnum.UN_CONFIRM, "用户支付");
                List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
                    OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
                    orderVideoOperateDTO.setVideoId(item.getId());
                    orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getEventContent(), PayTypeEnum.BALANCE.getLabel()));
                    return orderVideoOperateDTO;
                }).collect(Collectors.toList());
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getEventName(), null, OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getIsPublic(), null, orderVideoOperateDTOS);

                //分配视频订单使用余额
                List<OrderVideo> orderVideoList = assignVideoBalance(orders, orderFinalPriceSum);
                // 余额处理
                updateBusinessBalance(orderFinalPriceSum, BalanceSourceTypeEnum.ORDER_SPEND, orders, OrderStatusEnum.UN_CONFIRM, orderVideoList);

            } else {
                flowOrderMember(orders.get(0), OrderMemberStatusEnum.UN_CONFIRM, OrderFlowButtonEnum.PAY_SUCCESS);
                updateBusinessBalance(orderFinalPriceSum, BalanceSourceTypeEnum.MEMBER_SPEND, orders, OrderStatusEnum.UN_CONFIRM, null);
            }
            try {
                SpringUtils.getBean(WeChatService.class).closeAllQrcode(dto.getOrderNum());
            } catch (Exception e) {
                log.error("关闭订单失败不报错~");
            }
        } finally {
            businessBalanceReleaseLock(orders.get(0).getMerchantId());
        }

    }

    /**
     * 更新大订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderBatchFieldNullToNull(List<Order> orders) {
        for (Order order : orders) {
            baseMapper.updateOrderFieldNullToNull(order);
        }
    }

    /**
     * 余额处理加锁
     *
     * @param businessId
     */
    private void businessBalanceLock(Long businessId) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + businessId, 60L), "订单正在操作中，请稍后重试！");
    }

    /**
     * 余额释放锁
     *
     * @param businessId
     */
    private void businessBalanceReleaseLock(Long businessId) {
        redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + businessId);
    }

    /**
     * 修改余额数据
     *
     * @param useBalance            使用余额
     * @param balanceSourceTypeEnum 余额使用类型（取消订单收入、补偿订单收入....）
     * @param orders                订单数据
     * @param orderStatusEnum       订单状态枚举
     * @param orderVideos           订单锁定数据
     */
    public void updateBusinessBalance(BigDecimal useBalance, BalanceSourceTypeEnum balanceSourceTypeEnum, List<Order> orders, OrderStatusEnum orderStatusEnum, List<OrderVideo> orderVideos) {
        BusinessBalanceDTO businessBalanceDto = new BusinessBalanceDTO();
        businessBalanceDto.setBusinessId(orders.get(0).getMerchantId());
        businessBalanceDto.setUseBalance(useBalance);
        businessBalanceDto.setOrigin(balanceSourceTypeEnum.getCode());
        businessBalanceDto.setIsBalanceLock(StatusTypeEnum.YES.getCode());
        businessBalanceDto.setVideoOperate(CollUtil.isNotEmpty(orderVideos));
        List<BusinessBalanceFlowDTO> businessBalanceFlowDTOS = new ArrayList<>();
        for (Order order : orders) {
            BusinessBalanceFlowDTO businessBalanceFlowDto = new BusinessBalanceFlowDTO();
            businessBalanceFlowDto.setBusinessId(order.getMerchantId());
            businessBalanceFlowDto.setOrderNum(order.getOrderNum());
            businessBalanceFlowDto.setOrderTime(order.getPayTime());
            businessBalanceFlowDTOS.add(businessBalanceFlowDto);
        }

        switch (orderStatusEnum) {
            case UN_CHECK:
                //待审核： 余额加锁（余额不处理）
                businessBalanceDto.setIsBalanceLock(StatusTypeEnum.YES.getCode());
                businessBalanceDto.setAuditStatus(orders.get(0).getAuditStatus());
                break;
            case UN_CONFIRM:
                //交易成功：减余额 余额解锁
                businessBalanceDto.setIsBalanceLock(StatusTypeEnum.NO.getCode());
                //修改为已审核状态 需要扣减余额
                businessBalanceDto.setAuditStatus(AuditStatusEnum.APPROVE.getCode());
                setBusinessBalanceDetailFlowList(orders.get(0).getOrderType(), orderVideos, businessBalanceFlowDTOS, balanceSourceTypeEnum);
                break;
            case TRADE_CLOSE:
                //交易关闭: 解锁（不需要处理余额，未成功数据不会减少余额）
                businessBalanceDto.setIsBalanceLock(StatusTypeEnum.NO.getCode());
                businessBalanceDto.setAuditStatus(orders.get(0).getAuditStatus());
                break;
            default:
                throw new ServiceException("订单状态有误！");
        }

        if (orders.stream().allMatch(item -> PayTypeEnum.BALANCE.getCode().equals(item.getPayType()))) {
            businessBalanceDto.setIsBalancePay(StatusTypeEnum.YES.getCode());
        }
        businessBalanceDto.setBusinessBalanceFlowDTOS(businessBalanceFlowDTOS);
        updateBusinessBalance(businessBalanceDto);
    }

    private void setBusinessBalanceDetailFlowList(Integer orderType, List<OrderVideo> orderVideos, List<BusinessBalanceFlowDTO> businessBalanceFlowDTOS, BalanceSourceTypeEnum balanceSourceTypeEnum) {
        if (!OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderType)) {
            return;
        }
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }
        Map<String, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));

        for (BusinessBalanceFlowDTO businessBalanceFlowDTO : businessBalanceFlowDTOS) {
            List<OrderVideo> videos = orderVideoMap.get(businessBalanceFlowDTO.getOrderNum());
            if (CollUtil.isEmpty(videos)) {
                continue;
            }
            List<BusinessBalanceDetailFlowDTO> list = new ArrayList<>();
            for (OrderVideo item : videos) {
                if (item.getUseBalance().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                BusinessBalanceDetailFlowDTO businessBalanceDetailFlowdto = new BusinessBalanceDetailFlowDTO();
                businessBalanceDetailFlowdto.setNumber(item.getOrderNum());
                businessBalanceDetailFlowdto.setVideoId(item.getId());
                businessBalanceDetailFlowdto.setVideoCode(item.getVideoCode());
                businessBalanceDetailFlowdto.setUseBalance(item.getUseBalance());
                businessBalanceDetailFlowdto.setType(balanceSourceTypeEnum.getType());
                list.add(businessBalanceDetailFlowdto);
            }
            businessBalanceFlowDTO.setBusinessBalanceDetailFlowList(list);
        }
    }

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 有逾期未反馈素材和无法接单的模特id
     */
    @Override
    public List<Long> checkAbnormalModelId(Collection<Long> modelId) {
        return orderVideoModelService.checkAbnormalModelId(modelId);
    }

    /**
     * 订单流转
     */
    private void orderFlow(OrderFlowDTO orderFlowDTO) {
        if (CollUtil.isEmpty(orderFlowDTO.getOrderVideos())) {
            return;
        }
        //  加锁
        List<Long> lockVideoIds = new ArrayList<>();
        try {
            for (OrderVideo orderVideo : orderFlowDTO.getOrderVideos()) {
                Assert.isTrue(redisService.getLock(CacheConstants.ORDER_FLOW_KEY + orderVideo.getId(), 60L), "订单流转中，请稍后重试！");
                lockVideoIds.add(orderVideo.getId());
            }
        } catch (Exception e) {
            //  异常 把加的锁释放掉
            for (Long lockVideoId : lockVideoIds) {
                redisService.releaseLock(CacheConstants.ORDER_FLOW_KEY + lockVideoId);
            }
            throw new ServiceException("订单正在处理中，请稍后重试！");
        }

        try {
            OrderVideoFlowService flowService = orderVideoFlowServiceFactory.getOrderVideoFlowService(orderFlowDTO.getOrderStatus());
            flowService.orderVideoFlow(orderFlowDTO.getOrderVideos(), orderFlowDTO.getEventName());

            //  更新视频订单
            orderVideoService.updateBatchById(orderFlowDTO.getOrderVideos());
        } finally {
            for (OrderVideo orderVideo : orderFlowDTO.getOrderVideos()) {
                redisService.releaseLock(CacheConstants.ORDER_FLOW_KEY + orderVideo.getId());
            }
        }
    }

    /**
     * 审核订单
     */
    @Override
    public CreateOrderVO editOperationOrderVideoInfo(OrderOperationVideoDTO orderOperationVideoDTO) {
        return orderVideoService.editOperationOrderVideoInfo(orderOperationVideoDTO);
    }

    /**
     * 审核订单_获取视频详细信息
     *
     * @param videoId 视频订单的主键
     */
    @Override
    public OrderOperationVideoVO getOperationOrderVideoInfo(Long videoId, OrderListDTO dto) {
        return orderVideoService.getOperationOrderVideoInfo(videoId, dto);
    }

    /**
     * 修改订单费用
     */
    @Override
    public void updateOrderVideoPrice(UpdateOrderVideoPriceDTO dto) {
        orderVideoService.updateOrderVideoPrice(dto);
    }

    /**
     * 运营发起反馈
     */
    @Override
    public void sendOrderVideoCase(SendVideoCaseDTO sendVideoCaseDTO) {
        orderVideoCaseService.sendOrderVideoCase(sendVideoCaseDTO);
    }

    /**
     * 编辑购物车
     */
    @Override
    public CreateOrderVO editCart(OrderVideoDTO orderVideoDTO) {
        if (ObjectUtil.isNotNull(orderVideoDTO.getIntentionModelId())) {
            //  校验意向模特是否符合订单信息
            List<Long> unfulfilledOrderModelIds = checkModelMeetOrder(List.of(orderVideoDTO.getIntentionModelId()), orderVideoDTO);
            if (null != unfulfilledOrderModelIds) {
                return CreateOrderVO.builder().cannotModel(unfulfilledOrderModelIds).build();
            }
            //  校验模特可否接单
            //  意向模特
            Collection<Long> difference = checkModelCanAccept(List.of(orderVideoDTO.getIntentionModelId()), SecurityUtils.getBizUserId());
            if (CollUtil.isNotEmpty(difference)) {
                return CreateOrderVO.builder().cannotModel(difference).build();
            }
        }
        videoCartService.editCart(orderVideoDTO);
        return CreateOrderVO.builder().build();
    }

    /**
     * 确认模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmModel(Long videoId) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        Assert.notNull(orderVideo, "订单不存在，请确认后重试！");
        Assert.notNull(orderVideo.getShootModelId(), "当前订单没有拍摄模特，请确认后重试！");
        Assert.isTrue(IsObjectEnum.NO_OBJECT.getCode().equals(orderVideo.getIsObject()), "当前订单需要发货，请确认后重试！");
        if (IsObjectEnum.NO_OBJECT.getCode().equals(orderVideo.getIsObject())) {
            Order order = baseMapper.getOrderByOrderNum(orderVideo.getOrderNum());
            orderVideoLogisticCore.confirmModel(videoId);
        }
        createOrderFlow(orderVideo, OrderStatusEnum.UN_FINISHED, "确认模特");
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(videoId, OrderVideoFlowNodeEnum.MERCHANT_DELIVERY);
    }

    /**
     * 购物车结算
     */
    @Override
    public CreateOrderVO cartSettleAccounts(List<CartSettleAccountsDTO> cartSettleAccountsDTOS) {
        return videoCartService.cartSettleAccounts(cartSettleAccountsDTOS);
    }

    /**
     * 查看购物车订单
     */
    @Override
    public VideoCartVO getCartInfo(Long cartId) {
        return videoCartService.getCartInfo(cartId);
    }

    /**
     * 删除购物车订单
     */
    @Override
    public void deleteCart(List<Long> cartIds) {
        videoCartService.deleteCart(cartIds);
    }

    /**
     * 购物车列表
     */
    @Override
    public List<VideoCartVO> selectCartList(CartListDTO cartListDTO) {
        return videoCartService.selectCartList(cartListDTO);
    }

    /**
     * 加入购物车
     */
    @Override
    public CreateOrderVO addCart(OrderVideoDTO orderVideoDTO) {
        if (CollUtil.isNotEmpty(orderVideoDTO.getIntentionModelIds())) {
            Assert.isTrue(orderVideoDTO.getShootCount() >= orderVideoDTO.getIntentionModelIds().size(), StrUtil.format("1个视频对应1个模特，请留下{}个模特即可~", orderVideoDTO.getShootCount()));

            //  校验意向模特是否符合订单信息
            List<Long> unfulfilledOrderModelIds = checkModelMeetOrder(orderVideoDTO.getIntentionModelIds(), orderVideoDTO);
            if (null != unfulfilledOrderModelIds) {
                return CreateOrderVO.builder().cannotModel(unfulfilledOrderModelIds).build();
            }
            //  校验模特可否接单
            //  意向模特
            Collection<Long> difference = checkModelCanAccept(orderVideoDTO.getIntentionModelIds(), SecurityUtils.getBizUserId());
            if (CollUtil.isNotEmpty(difference)) {
                return CreateOrderVO.builder().cannotModel(difference).build();
            }
        }

        videoCartService.addCart(orderVideoDTO);
        return CreateOrderVO.builder().build();
    }

    /**
     * 商家端-订单各个状态统计
     */
    @Override
    public OrderStatusVO merchantOrderStatusCount() {
        OrderStatusVO orderStatusVO = orderVideoService.merchantOrderStatusCount();
        if (ObjectUtil.isNull(orderStatusVO)) {
            orderStatusVO = new OrderStatusVO();
            orderStatusVO.setUnPayCount(0);
            orderStatusVO.setUnConfirmCount(0);
            orderStatusVO.setUnCheckCount(0);
            orderStatusVO.setUnMatchCount(0);
            orderStatusVO.setUnFilledCount(0);
            orderStatusVO.setUnFinishedCount(0);
            orderStatusVO.setNeedConfirmCount(0);
            orderStatusVO.setFinishedCount(0);
            orderStatusVO.setUnUploadCount(0);
            orderStatusVO.setUnderwayCount(0);
            return orderStatusVO;
        }
        orderStatusVO.initUnderwayCount();

        return orderStatusVO;
    }

    /**
     * 运营端-订单各个状态统计
     */
    @Override
    public OrderStatusVO backOrderStatusCount() {
        return orderVideoService.backOrderStatusCount();
    }

    @Override
    public OrderStatusVO workbenchOrderStatusCount() {
        List<Order> unPayList = baseMapper.getUnPayListByBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        Integer unPayCount = 0;
        if (CollUtil.isNotEmpty(unPayList)) {
            List<String> nums = new ArrayList<>();
            for (Order item : unPayList) {
                if (StrUtil.isBlank(item.getPayNum())) {
                    if (!nums.contains(item.getOrderNum())) {
                        nums.add(item.getOrderNum());
                        unPayCount++;
                    }
                } else if (!nums.contains(item.getPayNum())) {
                    nums.add(item.getPayNum());
                    unPayCount++;
                }
            }
        }
        OrderStatusVO orderStatusVO = orderVideoService.workbenchOrderStatusCount();
        if (ObjectUtil.isNull(orderStatusVO)) {
            orderStatusVO = new OrderStatusVO();
            orderStatusVO.setUnPayCount(unPayCount);
            orderStatusVO.setUnConfirmCount(0);
            orderStatusVO.setUnCheckCount(0);
            orderStatusVO.setUnMatchCount(0);
            orderStatusVO.setUnFilledCount(0);
            orderStatusVO.setUnFinishedCount(0);
            orderStatusVO.setNeedConfirmCount(0);
            orderStatusVO.setFinishedCount(0);
            orderStatusVO.setUnUploadCount(0);
            orderStatusVO.setUnderwayCount(0);
            return orderStatusVO;
        }
        orderStatusVO.initUnderwayCount();

        orderStatusVO.setUnPayCount(unPayCount);
        return orderStatusVO;
    }

    /**
     * 确认成品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void affirmGoods(OrderAffirmDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());

        List<OrderVideoFeedBack> feedBackList = orderVideoFeedBackService.getFeedBackList(dto.getVideoId(), orderVideo.getRollbackId());
        Assert.isTrue(CollUtil.isNotEmpty(feedBackList), "当前订单成品未完成，无法确认！");

        createOrderFlow(orderVideo, OrderStatusEnum.FINISHED, "确认成品");
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideo.getId(), OrderVideoFlowNodeEnum.MERCHANT_CONFIRMATION);
        if (NeedUploadEnum.NEED.getCode().equals(dto.getNeedUpload())) {
            checkShootModelType(orderVideo);
            OrderVideoUploadLinkDTO orderVideoUploadLinkDTO = BeanUtil.copyProperties(dto, OrderVideoUploadLinkDTO.class);
            orderVideoUploadLinkDTO.setObject(UploadObjectEnum.COMPANY.getCode());
            orderVideoUploadLinkDTO.setUserId(SecurityUtils.getUserId());
            orderVideoUploadLinkService.saveOrderVideoUploadLink(orderVideoUploadLinkDTO);
        } else {
            orderVideoUploadLinkService.saveFinished(orderVideo.getId());
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_COMPLETION.getEventName(), null, OrderVideoOperateTypeEnum.ORDER_COMPLETION.getIsPublic(), null, OrderVideoOperateDTO.builder().videoId(orderVideo.getId()).eventContent(OrderVideoOperateTypeEnum.ORDER_COMPLETION.getEventContent()).build());
            orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideo.getId(), OrderVideoFlowNodeEnum.ORDER_COMPLETION);
            orderVideoFeedBackMaterialInfoTaskDetailService.finishTaskByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId());
        }

        orderVideoFeedBackMaterialService.closeMaterialInfoByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId(), EditCloseReasonEnum.LINKAGE_OFF);
    }

    /**
     * 更换模特
     */
    @Override
    public CreateOrderVO changeModel(Long videoId, Long modelId, String reason) {
        return orderVideoService.changeModel(videoId, modelId, reason);
    }

    /**
     * 发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shipping(ShippingDTO shippingDTO) {
        shippingDTO.setNumber(shippingDTO.getNumber().trim());
        OrderVideo orderVideo = orderVideoService.getById(shippingDTO.getVideoId());
        Assert.notNull(orderVideo, "订单不存在！");
        orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.NEED_FILLED, OrderStatusEnum.UN_FINISHED);
        Assert.isTrue(orderVideo.getIsObject().equals(IsObjectEnum.OBJECT.getCode()), "当前订单无需发货，请确认后重试！");
        Order order = self.getOrderByOrderNum(orderVideo.getOrderNum());
        Assert.notNull(order, "订单不存在！");

        OrderVideoModelShippingAddressVO shippingAddress = orderVideoModelShippingAddressService.getLastOrderVideoModelShippingAddressByVideoId(shippingDTO.getVideoId(), orderVideo.getRollbackId());
        Assert.notNull(shippingAddress, "获取模特收件地址失败，请联系蜗牛客服处理");

        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(Collections.singleton(orderVideo.getShootModelId()));
        ModelInfoVO modelInfoVO = modelMap.getOrDefault(orderVideo.getShootModelId(), new ModelInfoVO());

        Long logisticCount = orderVideoModelShippingAddressService.getLogisticCountByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId());
        OrderVideoLogisticFlowDTO orderVideoLogisticFlowDTO = new OrderVideoLogisticFlowDTO();
        orderVideoLogisticFlowDTO.setBusinessId(order.getMerchantId());
        orderVideoLogisticFlowDTO.setMemberCode(order.getMerchantCode());
        orderVideoLogisticFlowDTO.setVideoCode(orderVideo.getVideoCode());
        orderVideoLogisticFlowDTO.setVideoId(orderVideo.getId());
        orderVideoLogisticFlowDTO.setLogisticNum(shippingDTO.getNumber());
        if (logisticCount > 0) {
            Assert.isTrue(Integer.valueOf(UserTypeConstants.MANAGER_TYPE).equals(SecurityUtils.getLoginUserType()), "您已填写过物流~");
            Assert.isTrue(CharSequenceUtil.isNotBlank(shippingDTO.getReissueCause()), "订单已发货，请刷新页面~");
            shippingDTO.setReissue(ReissueEnum.REISSUE.getCode());

            if (CollUtil.isNotEmpty(shippingDTO.getTaskDetailIds())) {
                orderVideoTaskService.checkRefundPendingTaskThenFinishWorkOrder(shippingDTO.getVideoId(), shippingDTO.getTaskDetailIds());
            }

            shippingDTO.setShippingAddressId(shippingAddress.getId());
            //  添加视频订单与物流关联
            OrderVideoLogistic orderVideoLogistic = orderVideoLogisticService.saveVideoLogistic(shippingDTO);
            //初始化 已发货数据
            orderVideoLogisticFlowDTO.setOrderVideoLogisticId(orderVideoLogistic.getId());
            orderVideoLogisticFlowDTO.setReissue(ReissueEnum.REISSUE.getCode());
            orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.SHIP);
        } else {
            BusinessVO businessVo = remoteService.getBusinessVo(BusinessDTO.builder().id(orderVideo.getCreateOrderBusinessId()).build());

            OrderVideoModelShippingAddress orderVideoModelShippingAddress = new OrderVideoModelShippingAddress();
            orderVideoModelShippingAddress.setVideoId(orderVideo.getId());
            orderVideoModelShippingAddress.setRollbackId(orderVideo.getRollbackId());
            orderVideoModelShippingAddress.setShootModelId(orderVideo.getShootModelId());

            orderVideoModelShippingAddress.setNation(modelInfoVO.getNation());
            orderVideoModelShippingAddress.setRecipient(modelInfoVO.getRecipient());
            orderVideoModelShippingAddress.setCity(modelInfoVO.getCity());
            orderVideoModelShippingAddress.setState(modelInfoVO.getState());
            orderVideoModelShippingAddress.setZipcode(modelInfoVO.getZipcode());
            orderVideoModelShippingAddress.setDetailAddress(modelInfoVO.getDetailAddress());
            orderVideoModelShippingAddress.setPhone(modelInfoVO.getPhone());

            orderVideoModelShippingAddress.setShippingRemark(shippingAddress.getShippingRemark());
            orderVideoModelShippingAddress.setShippingPic(shippingAddress.getShippingPic());
            orderVideoModelShippingAddress.setPhoneVisible(businessVo.getPhoneVisible());
            OrderVideoModelShippingAddress saveOrderVideoModelShippingAddress = orderVideoModelShippingAddressService.saveOrderVideoModelShippingAddress(orderVideoModelShippingAddress);

            shippingDTO.setShippingAddressId(saveOrderVideoModelShippingAddress.getId());
            shippingDTO.setReissueCause(null);
            shippingDTO.setReissue(ReissueEnum.NO_REISSUE.getCode());
            shippingDTO.setLogisticFlag(shippingAddress.getLogisticFlag());
            shippingDTO.setLogisticFlagRemark(shippingAddress.getLogisticFlagRemark());
            shippingDTO.setLogisticFlagTime(shippingAddress.getLogisticFlagTime());
            //  添加视频订单与物流关联
            OrderVideoLogistic orderVideoLogistic = orderVideoLogisticService.saveVideoLogistic(shippingDTO);

            //流转为已发货
            orderVideoLogisticFlowDTO.setOrderVideoLogisticId(orderVideoLogistic.getId());
            orderVideoLogisticFlowDTO.setReissue(ReissueEnum.NO_REISSUE.getCode());
            orderVideoLogisticCore.logisticFlow(orderVideoLogisticFlowDTO, FollowStatusEnum.SHIP);
        }

        checkModelShippingAddressChange(shippingDTO, modelInfoVO, shippingAddress.getPhoneVisible());

        //  注册物流单号
        remoteService.register(Collections.singletonList(shippingDTO.getNumber()));

        if (logisticCount > 0) {
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REDELIVERY.getEventName(), null, OrderVideoOperateTypeEnum.REDELIVERY.getIsPublic(), null, OrderVideoOperateDTO.builder().videoId(shippingDTO.getVideoId()).eventContent(OrderVideoOperateTypeEnum.REDELIVERY.getEventContent()).build());
            return;
        }

        createOrderFlow(orderVideo, OrderStatusEnum.UN_FINISHED, "发货");
        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.MERCHANT_DELIVERY.getEventName(), null, OrderVideoOperateTypeEnum.MERCHANT_DELIVERY.getIsPublic(), null, OrderVideoOperateDTO.builder().videoId(shippingDTO.getVideoId()).eventContent(StrUtil.format(OrderVideoOperateTypeEnum.MERCHANT_DELIVERY.getEventContent(), UserTypeConstants.MANAGER_TYPE == SecurityUtils.getLoginUserType() ? "客服" : "")).build());
        orderVideoFlowNodeDiagramService.setNodeCompleteTime(orderVideo.getId(), OrderVideoFlowNodeEnum.MERCHANT_DELIVERY);
    }

    /**
     * 校验模特地址是否有变更
     */
    private void checkModelShippingAddressChange(ShippingDTO shippingDTO, ModelInfoVO modelInfoVO, Integer phoneVisible) {
        if (UserTypeConstants.MANAGER_TYPE == SecurityUtils.getLoginUserType()) {
            return;
        }
        Assert.isTrue(shippingDTO.getNation() != null && StrUtil.isNotBlank(shippingDTO.getRecipient())
                && StrUtil.isNotBlank(shippingDTO.getCity())
                && StrUtil.isNotBlank(shippingDTO.getZipcode()) && StrUtil.isNotBlank(shippingDTO.getDetailAddress()), "[模特收件信息]不能为空");

        if (StatusTypeEnum.NO.getCode().equals(phoneVisible)) {
            modelInfoVO.setPhone(null);
            shippingDTO.setPhone(null);
        }
        Assert.isFalse(OrderVideoServiceImpl.compareModelShippingAddress(modelInfoVO, shippingDTO.getRecipient(), shippingDTO.getDetailAddress(),
                shippingDTO.getCity(), shippingDTO.getState(), shippingDTO.getZipcode(),
                shippingDTO.getNation(), shippingDTO.getPhone()), () -> new Biz200Exception(2, "模特信息有变更，请刷新后重试！"));
    }

    /**
     * 标记发货
     */
    @Override
    public void shippingFlag(FlagShippingDTO flagShippingDTO) {
        orderVideoModelShippingAddressService.shippingFlag(flagShippingDTO);
    }

    /**
     * 取消订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(String orderNum, boolean isMerge) {
        // 获取订单数据
        final Order order = baseMapper.getOrderByOrderNum(orderNum);
        Assert.notNull(order, "订单数据不能为空");
        Assert.isNull(order.getPayTime(), "订单已支付");
        Assert.isNull(order.getCloseOrderTime(), "订单已关闭");

        List<String> orderNums;
        if (!isMerge) {
            Boolean existMerge = orderMergeService.checkOrderMerge(orderNum);
            if (existMerge) {
                Assert.notNull(order.getSubmitCredentialTime(), "订单已合并");
                orderNums = orderMergeService.getMergeOrderNumsByOrderNum(orderNum);
            } else {
                orderNums = List.of(orderNum);
            }
        } else {
            orderNums = List.of(orderNum);
        }

        List<OrderVideo> orderVideos = orderVideoService.selectByOrderNums(orderNums);
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }

        List<OrderVideo> orderVideoList = orderVideoService.selectValidByOrderNums(orderNums);
        Assert.isTrue(StringUtils.isNotEmpty(orderVideoList), "获取有效视频订单数据为空！");
        Assert.isTrue(orderVideoList.size() == orderVideos.size(), "取消订单数量与有效订单数量不一致！");

        if (ObjectUtil.isNotNull(SecurityUtils.getLoginUserType())) {
            orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder().videoIds(orderVideos.stream().map(OrderVideo::getId).collect(Collectors.toList())).submitter(true).backUserType(BackUserTypeEnum.CHINESE).build());
        }
        if (UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType())) {
            orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY, OrderStatusEnum.UN_CHECK);
        } else {
            orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY);
        }

        createOrderFlow(orderVideos, OrderStatusEnum.TRADE_CLOSE, "取消订单");
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
            OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
            orderVideoOperateDTO.setVideoId(item.getId());
            if (ObjectUtil.isNotNull(SecurityUtils.getLoginUserType())) {
                orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.NON_PAYMENT_CANCEL_ORDER.getEventContent(), UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()) ? "客服" : ""));
            } else {
                orderVideoOperateDTO.setEventContent("订单支付超时自动关闭");
            }
            return orderVideoOperateDTO;
        }).collect(Collectors.toList());
        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.NON_PAYMENT_CANCEL_ORDER.getEventName(), null, OrderVideoOperateTypeEnum.NON_PAYMENT_CANCEL_ORDER.getIsPublic(), null, orderVideoOperateDTOS);

        LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();

        List<Order> closeOrders = baseMapper.selectListByOrderNums(orderNums);
        for (Order closeOrder : closeOrders) {
            closeOrder.setCloseOrderTime(DateUtil.date());
            closeOrder.setIsAutoCancel(EventExecuteObjectEnum.SYSTEM.getCode().equals(loginUserInfoVo.getUserType()) ? StatusTypeEnum.YES.getCode() : StatusTypeEnum.NO.getCode());
            closeOrder.setAuditStatus(AuditStatusEnum.CLOSE.getCode());
        }
        updateOrderBatchFieldNullToNull(closeOrders);
        try {
            asyncTaskService.closeAllOrder(orderNums);
        } catch (Exception e) {
            log.error("取消订单失败：{}", e);
        }

        if (closeOrders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        //视频订单只有未支付之前可以取消
        rollbackBalance(closeOrders);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reopenOrder(String orderNum) {
        List<OrderVideo> orderVideos = orderVideoService.selectByOrderNum(orderNum);
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }
        Order orderByOrderNum = baseMapper.getOrderByOrderNum(orderNum);
        if (ObjectUtil.isNull(orderByOrderNum)) {
            return;
        }
        Assert.isTrue(OrderTypeEnum.VIDEO_ORDER.getCode().equals(orderByOrderNum.getOrderType()), "视频订单才能重新开启订单");
        Assert.isTrue(AuditStatusEnum.CLOSE.getCode().equals(orderByOrderNum.getAuditStatus()), "只有大订单已关闭才能取消订单");
        Assert.isTrue(DateUtils.getDatePoorDay(DateUtils.getNowDate(), orderByOrderNum.getCloseOrderTime()) <= orderVideoProperties.getShowReopenOrder(), "需要订单关闭30天内才能重新打开");
        Assert.isTrue(orderByOrderNum.getReopenCount() == 0, "一个订单仅可开启一次");

        final BigDecimal payOrderAmount = orderVideos.stream().map(OrderVideo::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        final BigDecimal payAmountDollar = orderVideos.stream().map(OrderVideo::getPayAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add);

        baseMapper.clearCloseInfo(orderNum, payOrderAmount, payAmountDollar, orderByOrderNum.getReopenCount());
        orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.TRADE_CLOSE);
        //  ->待支付
        //  a:订单状态修改
        List<OrderVideoFlowDTO> orderVideoFlowDTOS = new ArrayList<>();
        List<OrderVideo> updateOrderVideos = new ArrayList<>();
        List<Long> videoIds = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            orderVideoFlowDTOS.add(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(orderVideo.getStatus()).targetStatus(OrderStatusEnum.UN_PAY.getCode()).build());
            OrderVideo updateOrderVideo = new OrderVideo();
            updateOrderVideo.setId(orderVideo.getId());
            updateOrderVideo.setStatus(OrderStatusEnum.UN_PAY.getCode());
            updateOrderVideo.setStatusTime(DateUtil.date());
            updateOrderVideo.setUseBalance(BigDecimal.ZERO);
            updateOrderVideos.add(updateOrderVideo);
            videoIds.add(orderVideo.getId());
        }
        orderVideoService.updateBatchById(updateOrderVideos);
        orderVideoFlowRecordService.createOrderVideoFlow("开启订单", orderVideoFlowDTOS);
        orderVideoFlowNodeDiagramService.reopenInitOrderVideoFlowNodeDiagram(videoIds);
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
            OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
            orderVideoOperateDTO.setVideoId(item.getId());
            orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.REOPEN_ORDER.getEventContent(), UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()) ? "客服" : ""));
            return orderVideoOperateDTO;
        }).collect(Collectors.toList());
        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.REOPEN_ORDER.getEventName(), null, OrderVideoOperateTypeEnum.REOPEN_ORDER.getIsPublic(), null, orderVideoOperateDTOS);


    }

    @Override
    public void rollbackBalance(List<Order> orders) {
        try {
            businessBalanceLock(orders.get(0).getMerchantId());
            // 回滚锁定余额
            updateBusinessBalance(orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add), BalanceSourceTypeEnum.CANCEL_ORDER_INCOME, orders, OrderStatusEnum.UN_CHECK, null);
        } finally {
            businessBalanceReleaseLock(orders.get(0).getMerchantId());
        }
    }

    /**
     * 取消订单（子账号入驻页）
     */
    @Override
    public CheckWechatVO cancelMemberOrderBySubAccount(String code, List<String> orderNums, Boolean isCancel) {
        BizUser bizUser = redisService.getCacheObject(CacheConstants.SUB_ACCOUNT_WECHAT_AUTH_CODE_CACHE_KEY + code);
        Assert.notNull(bizUser, "页面停留时间过长，请重新扫码重试~");

        //  校验是否已是主账号
        BusinessAccountVO businessAccountVO = remoteService.getBusinessAccountByUnionId(bizUser.getUnionid());
        if (ObjectUtil.isNotNull(businessAccountVO) && MemberTypeEnum.RECHARGE.getCode().equals(businessAccountVO.getBusinessVO().getMemberType())) {
            return CheckWechatVO.builder()
                    .message(CheckWechatEnum.ALREADY_OWNER.getLabel())
                    .status(CheckWechatEnum.ALREADY_OWNER.getCode())
                    .build();
        }

        List<Order> orders = baseMapper.selectListByBizUserId(bizUser.getId());
        Assert.notEmpty(orders, "当前账号未创建订单");

        Assert.isTrue(CollUtil.containsAll(orders.stream().filter(item -> OrderTypeEnum.VIP_ORDER.getCode().equals(item.getOrderType())).map(Order::getOrderNum).collect(Collectors.toList()), orderNums), "当前账号未创建订单");

        if (orders.stream().allMatch(item -> AuditStatusEnum.APPROVE.getCode().equals(item.getAuditStatus()))) {
            return CheckWechatVO.builder()
                    .message(CheckWechatEnum.ALREADY_OWNER.getLabel())
                    .status(CheckWechatEnum.ALREADY_OWNER.getCode())
                    .build();
        }

        boolean phoneBlank = CharSequenceUtil.isBlank(bizUser.getPhone());

        if (orders.stream().allMatch(item -> AuditStatusEnum.CLOSE.getCode().equals(item.getAuditStatus()))) {
            return CheckWechatVO.builder()
                    .message(phoneBlank ? CheckWechatEnum.NO_BIZ_USER_PHONE.getLabel() : CheckWechatEnum.SUCCESS.getLabel())
                    .status(phoneBlank ? CheckWechatEnum.NO_BIZ_USER_PHONE.getCode() : CheckWechatEnum.SUCCESS.getCode())
                    .build();
        }
        if (Boolean.TRUE.equals(isCancel)) {
            for (String orderNum : orderNums) {
                Order order = orders.stream().filter(item -> orderNum.equals(item.getOrderNum())).findFirst().get();
                if (!AuditStatusEnum.APPROVE.getCode().equals(order.getAuditStatus()) && !AuditStatusEnum.CLOSE.getCode().equals(order.getAuditStatus())) {
                    self.cancelMemberOrder(orderNum);
                }
            }
        }
        return CheckWechatVO.builder()
                .message(phoneBlank ? CheckWechatEnum.NO_BIZ_USER_PHONE.getLabel() : CheckWechatEnum.SUCCESS.getLabel())
                .status(phoneBlank ? CheckWechatEnum.NO_BIZ_USER_PHONE.getCode() : CheckWechatEnum.SUCCESS.getCode())
                .build();
    }

    /**
     * 取消会员订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMemberOrders(List<String> orderNums) {
        for (String orderNum : orderNums) {
            cancelMemberOrder(orderNum);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMemberOrder(String orderNum) {
        Order order = getOrderByOrderNum(orderNum);

        businessBalanceLock(order.getMerchantId());
        try {
            flowOrderMember(order, OrderMemberStatusEnum.UN_MATCH, OrderFlowButtonEnum.CANCEL_ORDER);
            this.updateById(Order.builder().id(order.getId()).auditStatus(AuditStatusEnum.CLOSE.getCode()).build());
            updateBusinessBalance(order.getUseBalance(), BalanceSourceTypeEnum.CANCEL_ORDER_INCOME, List.of(order), OrderStatusEnum.TRADE_CLOSE, null);
        } finally {
            businessBalanceReleaseLock(order.getMerchantId());
        }
        try {
            asyncTaskService.closeAllOrder(orderNum);
        } catch (Exception e) {
            log.error("调用关单失败：{}", e);
        }
    }

    @Override
    public void companyApplyCancelMemberOrder(String orderNum) {
        OrderMemberVO orderMember = orderMemberService.getOrderMember(orderNum);
        Assert.isTrue(OrderMemberStatusEnum.UN_PAY.getCode().equals(orderMember.getStatus()), "请刷新后重试~");

        self.cancelMemberOrder(orderNum);
    }

    @Override
    public PromotionActivityVO loadChannel(String seedCode, DistributionChannel distributionChannelEntityBySeedCode, ChannelBrokeRageVO channelBrokeRageVO) {
        PromotionActivityVO promotionActivityVO = null;
        if (StrUtil.isNotBlank(seedCode)) {
            ChannelBrokeRageVO distributionChannelBySeedCodeV1 = remoteService.getDistributionChannelBySeedCodeV1(seedCode);
            if (ObjectUtil.isNotNull(distributionChannelBySeedCodeV1)) {
                //填充数据
                channelBrokeRageVO.setEntity(distributionChannelBySeedCodeV1);
                if (ChannelTypeEnum.FISSION.getCode().equals(channelBrokeRageVO.getChannelType())) {
                    promotionActivityVO = promotionActivityService.getValidPromotionActivityByType(PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode());
                } else if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(channelBrokeRageVO.getChannelType())) {
                    promotionActivityVO = promotionActivityService.getValidPromotionActivityByType(PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode());
                }
            }
            //如果活动在 时间内 则设置对应数据
            if (ObjectUtil.isNotNull(promotionActivityVO)) {
                DistributionChannel distributionChannel = channelBrokeRageVO.getDistributionChannel();
                distributionChannelEntityBySeedCode.setEntity(distributionChannel);
            }
        }
        return promotionActivityVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPayStatus(String orderNum, BigDecimal totalAmount, Integer payType, String mchntOrderNo, String mchid) {
        if (orderNum.startsWith(OrderConstant.PREPAY_NUM)) {
            List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(Arrays.asList(orderNum)).build());
            Assert.notEmpty(businessBalancePrepayVOS, "订单不存在，请检查后重试！");
            BusinessBalancePrepayVO businessBalancePrepayVO = businessBalancePrepayVOS.get(0);
            orderPayeeAccountService.saveOrderPayeeAccount(OrderPayAccountDTO.builder()
                    .orderNum(orderNum)
                    .payeeAccountConfigInfoId(businessBalancePrepayVO.getAccountId())
                    .accountType(payType).build());
            Date date = new Date();
            orderPayLogService.saveOrderPayLog(OrderPayLogDTO.builder()
                    .orderNum(businessBalancePrepayVO.getPrepayNum())
                    .orderType(businessBalancePrepayVO.getOrderType())
                    .businessId(businessBalancePrepayVO.getBusinessId())
                    .payType(payType)
                    .payTypeDetail(businessBalancePrepayVO.getPayTypeDetail())
                    .payTime(date)
                    .payAmount(businessBalancePrepayVO.getPayAmount())
                    .realPayAmount(businessBalancePrepayVO.getPayAmount())
                    .mchid(mchid)
                    .mchntOrderNo(mchntOrderNo)
                    .build());
            PrepayUpdatePayStatusDTO dto = new PrepayUpdatePayStatusDTO();
            dto.setPrepayNum(orderNum);
            dto.setPayType(payType);
            dto.setTotalAmount(totalAmount);
            dto.setDebuggerEnable(debuggerEnable);
            dto.setPayTime(date);

            BusinessBalancePrepay businessBalancePrepay = remoteService.innerUpdateOrderPayStatus(dto);
            return;
        }
        List<String> orderNums;
        OrderMerge orderMerge = new OrderMerge();
        if (orderNum.startsWith(OrderConstant.MERGE_ORDER_PAY_NUM_PREFIX_HBZF)) {
            orderNums = orderMergeService.getOrderNumsByMergeIdOrPayNum(null, orderNum);
            Assert.notEmpty(orderNums, "请刷新页面后重试~");
            orderMerge = orderMergeService.getOrderMergeNormalByIdOrPayNum(null, orderNum);
            orderMergeService.completeOrderMergeByMergeIdOrPayNum(null, orderNum);
        } else {
            orderNums = List.of(orderNum);
        }
        //        更新支付状态
        //        检查当前支付状态是不是支付前
        Assert.isTrue(baseMapper.checkOrderStatus(orderNums), "订单不存在");

        List<Order> orders = baseMapper.selectListByOrderNums(orderNums);
        Assert.isTrue(orders.stream().map(Order::getOrderType).distinct().count() == 1, "订单类型必须相同");
        businessBalanceLock(orders.get(0).getMerchantId());
        DateTime date = DateUtil.date();
        BigDecimal orderFinalAmountSum = BigDecimal.ZERO;
        BigDecimal finalNeedPayAmountSum = BigDecimal.ZERO;
        String seedCode = orders.get(0).getSeedCode();
        DistributionChannel distributionChannelEntityBySeedCode = new DistributionChannel();
        if (StrUtil.isNotBlank(seedCode)) {
            DistributionChannel distributionChannel = remoteService.getDistributionChannelEntityBySeedCode(seedCode);
            if (ObjectUtil.isNotNull(distributionChannel)) {
                //填充数据
                distributionChannelEntityBySeedCode.setEntity(distributionChannel);
            }
        }

        List<OrderPromotionDetail> orderPromotionDetails = new ArrayList<>();
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        for (Order order : orders) {
            order.setPayTime(date);
            order.setRecordTime(date);
            order.setIsRecord(StatusTypeEnum.YES.getCode());
            order.setAuditStatus(AuditStatusEnum.APPROVE.getCode());
            if (ObjectUtil.isNotNull(order.getUseBalance()) && order.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
                if (order.getUseBalance().compareTo(order.getPayAmount()) >= 0) {
                    order.setPayType(PayTypeEnum.BALANCE.getCode());
                } else {
                    order.setPayType(payType + 10);
                }
            } else {
                order.setPayType(payType);
            }
            //  获取总优惠金额 不包含种草码优惠金额 因为无法在生成支付二维码时 存入进去
            List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(order.getOrderNum(), new ArrayList<>());
            BigDecimal totalDiscountAmount = discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            //  种草码优惠金额
            BigDecimal seedCodeDiscount = BigDecimal.ZERO;
            if (StrUtil.isNotBlank(order.getSeedCode())){
                seedCodeDiscount = SpringUtils.getBean(PayService.class).getSeedCodeDiscountV1(order.getMemberDiscountType(), order.getSettleRage(), order.getPayAmount());
                order.setSeedCodeDiscount(seedCodeDiscount);
            }
            BigDecimal oldPayAmount = order.getPayAmount();
            BigDecimal oldPayAmountDollar = order.getPayAmountDollar();
            //  商家应支付金额
            BigDecimal orderFinalAmount = getOrderFinalAmount(order, totalDiscountAmount.add(seedCodeDiscount));
            BigDecimal finalNeedPayAmount = orderFinalAmount.subtract(order.getUseBalance() == null ? BigDecimal.ZERO : order.getUseBalance());
            order.setPayAmount(orderFinalAmount);
            order.setPayAmountDollar(order.getPayAmount().compareTo(oldPayAmount) == 0 ? order.getPayAmountDollar() : order.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
            order.setRealPayAmount(finalNeedPayAmount);
            order.setRealPayAmountCurrency(finalNeedPayAmount);
            order.setCurrency(CurrencyEnum.CNY.getCode());
            if (StrUtil.isNotBlank(order.getSeedCode())) {
                order.setSeedId(distributionChannelEntityBySeedCode.getSeedId());
                order.setChannelName(distributionChannelEntityBySeedCode.getChannelName());
                order.setChannelType(distributionChannelEntityBySeedCode.getChannelType());
                Map<String, Integer> seedMemberStatusMap = remoteService.getUserMemberStatusBySeedId(List.of(distributionChannelEntityBySeedCode.getSeedId()));
                order.setSeedMemberStatus(seedMemberStatusMap.get(distributionChannelEntityBySeedCode.getSeedId()));

                //  在这里存入种草码优惠使用明细
                OrderPromotionDetail orderPromotionDetail = new OrderPromotionDetail();
                if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(distributionChannelEntityBySeedCode.getChannelType())) {
                    orderPromotionDetail.setActivityId(Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode()));
                } else {
                    orderPromotionDetail.setActivityId(Convert.toLong(PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode()));
                }
                orderPromotionDetail.setOrderNum(order.getOrderNum());
                orderPromotionDetail.setDiscountAmount(order.getSeedCodeDiscount());
                orderPromotionDetail.setDiscountAmountDollar(oldPayAmountDollar.subtract(order.getPayAmountDollar()));
                orderPromotionDetail.setDiscountType(order.getMemberDiscountType());
                orderPromotionDetail.setAmount(order.getSettleRage());
                orderPromotionDetails.add(orderPromotionDetail);
            }

            orderFinalAmountSum = orderFinalAmountSum.add(orderFinalAmount);
            finalNeedPayAmountSum = finalNeedPayAmountSum.add(finalNeedPayAmount);
        }
        if (Boolean.TRUE.toString().equals(debuggerEnable)) {
            if (new BigDecimal("0.01").compareTo(totalAmount) != 0) {
                log.error("测试情况 回调金额非0.01错误，订单号：{},回调金额：{}", orderNum, totalAmount);
                throw new ServiceException("测试情况 回调金额非0.01错误！");
            }
        } else if (finalNeedPayAmountSum.compareTo(totalAmount) != 0) {
            log.error("订单应付金额与回调金额不一致，订单号：{},应付金额：{},回调金额：{}", orderNum, orderFinalAmountSum, totalAmount);
            throw new ServiceException("订单应付金额与回调金额不一致！");
        }

        OrderAnotherPay orderAnotherPay = orderAnotherPayService.getValidLinkByOrderNumOrMergeId(ObjectUtil.isNotNull(orderMerge.getId()) ? null : orderNum, orderMerge.getId());
        if (ObjectUtil.isNotNull(orderAnotherPay)) {
            orderAnotherPayService.payCancel(orderAnotherPay.getUuid());
        }
        try {
            orderPromotionDetailService.saveBatchOrderPromotionDetail(orderPromotionDetails);
            self.updateBatchById(orders);
            orderPayeeAccountService.saveOrderPayeeAccount(orders, payType);
            List<OrderPayLog> orderPayLogs = new ArrayList<>();
            for (Order order : orders) {
                OrderPayLog orderPayLog = new OrderPayLog();
                orderPayLog.setOrderNum(order.getOrderNum());
                orderPayLog.setOrderType(order.getOrderType());
                orderPayLog.setBusinessId(order.getMerchantId());
                orderPayLog.setMchntOrderNo(mchntOrderNo);
                orderPayLog.setMchid(mchid);
                orderPayLog.setPayType(order.getPayType());
                orderPayLog.setPayTypeDetail(order.getPayTypeDetail());
                orderPayLog.setPayTime(order.getPayTime());
                orderPayLog.setPayAmount(order.getPayAmount());
                orderPayLog.setRealPayAmount(order.getRealPayAmount());
                orderPayLog.setUseBalance(order.getUseBalance());
                orderPayLogs.add(orderPayLog);
            }
            orderPayLogService.saveBatch(orderPayLogs);
            BigDecimal ordersUseBalace = orders.stream().map(Order::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(orders.get(0).getOrderType())) {
                List<OrderVideo> orderVideos = orderVideoService.selectValidByOrderNumsAsc(orderNums);
                orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_PAY);
                createOrderFlow(orderVideos, OrderStatusEnum.UN_CONFIRM, "用户支付");
                Map<String, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getOrderNum, Function.identity()));
                List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
                    Order order = orderMap.get(item.getOrderNum());
                    OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
                    orderVideoOperateDTO.setVideoId(item.getId());
                    orderVideoOperateDTO.setEventContent(StrUtil.format(OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getEventContent(), PayTypeEnum.getPayTypeEnumByCode(order.getPayType()).getLabel()));
                    return orderVideoOperateDTO;
                }).collect(Collectors.toList());
                orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getEventName(), null, OrderVideoOperateTypeEnum.PAYMENT_SUCCESS.getIsPublic(), null, orderVideoOperateDTOS);
                updateBusinessBalance(ordersUseBalace, BalanceSourceTypeEnum.ORDER_SPEND, orders, OrderStatusEnum.UN_CONFIRM, orderVideos);
            } else {
                flowOrderMember(orders.get(0), OrderMemberStatusEnum.UN_CONFIRM, OrderFlowButtonEnum.PAY_SUCCESS);
                updateBusinessBalance(ordersUseBalace, BalanceSourceTypeEnum.MEMBER_SPEND, orders, OrderStatusEnum.UN_CONFIRM, null);
            }
        } finally {
            businessBalanceReleaseLock(orders.get(0).getMerchantId());
        }
    }

    public void flowOrderMember(Order order, OrderMemberStatusEnum targetStatus, OrderFlowButtonEnum orderFlowButtonEnum) {
        FlowOrderMemberDTO flowOrderMemberDto = new FlowOrderMemberDTO();
        flowOrderMemberDto.setOrderMemberStatusEnum(targetStatus);
        flowOrderMemberDto.setOrderNum(order.getOrderNum());
        flowOrderMemberDto.setUseBalance(order.getUseBalance());
        flowOrderMemberDto.setOrderFlowButtonEnum(orderFlowButtonEnum);
        //获取订单用户id
        flowOrderMemberDto.setBizUserId(order.getBizUserId() != null ? order.getBizUserId() : null);
        orderMemberService.flowOrderMember(flowOrderMemberDto);
    }

    /**
     * 商家回复匹配情况反馈
     */
    @Override
    public void replyVideoCase(ReplyVideoCaseDTO replyVideoCaseDTO) {
        orderVideoCaseService.replyVideoCase(replyVideoCaseDTO);
    }

    /**
     * 查询视频订单匹配情况反馈
     */
    @Override
    public List<OrderVideoCaseVO> selectOrderVideoCaseListByVideoId(Long videoId) {
        return orderVideoCaseService.selectListByVideoId(videoId);
    }


    /**
     * 修改视频订单
     */
    @Override
    public void updateOrderVideo(OrderVideoDTO orderVideoDTO) {
        orderVideoService.updateOrderVideo(orderVideoDTO);
    }

    /**
     * 修改视频订单_获取视频详细信息
     *
     * @param videoId 视频订单的主键
     */
    @Override
    public OrderVideoVO getOrderVideoInfo(Long videoId) {
        return orderVideoService.getOrderVideoInfo(videoId);
    }

    /**
     * 查询订单列表
     *
     * @param orderListDTO 订单
     * @return 订单
     */
    @Override
    public List<OrderListVO> selectOrderListByCondition(OrderListDTO orderListDTO) {
        if (Boolean.FALSE.equals(wrapperCondition(orderListDTO))) {
            return Collections.emptyList();
        }

        // OrderByDto orderByDto = new OrderByDto();
        if (CharSequenceUtil.isNotBlank(orderListDTO.getStatusTimeSort())) {
            orderListDTO.setStatusTimeSort(OrderByDto.DIRECTION.DESC.value().equals(orderListDTO.getStatusTimeSort()) ? OrderByDto.DIRECTION.ASC.value() : OrderByDto.DIRECTION.DESC.value());
            // orderByDto.setField("status_time", OrderByDto.DIRECTION.value(orderListDTO.getStatusTimeSort()));
        }
        // orderByDto.setField("ot.create_time", OrderByDto.DIRECTION.DESC);
        if (ObjectUtil.isNotNull(orderListDTO.getStartPage()) && StatusTypeEnum.YES.getCode().equals(orderListDTO.getStartPage())) {
            PageUtils.startPage();
        }
        return selectOrderListVOS(orderListDTO);
    }

    @Override
    public List<OrderListVO> receivableAuditList(OrderListDTO orderListDTO) {
        // 添加商家id
        wrapperCondition(orderListDTO);
        // 添加支付类型
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("sort", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("ot.update_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        orderListDTO.setStatus(Arrays.stream(OrderStatusEnum.values()).filter(item -> !item.equals(OrderStatusEnum.UN_PAY)).map(OrderStatusEnum::getCode).collect(Collectors.toList()));
        orderListDTO.setIsReceivableAudit(StatusTypeEnum.YES.getCode());
        List<OrderListVO> resultList = baseMapper.selectOrderListV1(orderListDTO);
        if (StringUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        Set<String> orderNums = resultList.stream().map(OrderListVO::getOrderNum).collect(Collectors.toSet());
        orderListDTO.setOrderNums(orderNums);
        List<OrderVideoVO> orderVideoVOS = orderVideoService.selectOrderVideoListByCondition(orderListDTO);
        if (StringUtils.isEmpty(orderVideoVOS)) {
            return new ArrayList<>();
        }

        //  查询下单用户信息
        Set<Long> orderUserId = resultList.stream().map(OrderListVO::getOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> accountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, orderUserId);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(accountDetailVOS);
        Map<String, List<OrderVideoVO>> orderMap = orderVideoVOS.stream()
                .collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
        List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(new ArrayList<>(orderNums));
        Map<String, OrderPayeeAccount> orderPayeeAccountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, p -> p));
        }
        for (OrderListVO vo : resultList) {
            vo.setOrderVideoVOS(orderMap.getOrDefault(vo.getOrderNum(), new ArrayList<>()));
            vo.setOrderUser(accountMap.getOrDefault(vo.getOrderUserId(), new BusinessAccountDetailVO()));
            vo.setSurplusAmount(vo.getPayAmount().subtract(vo.getUseBalance() == null ? BigDecimal.ZERO : vo.getUseBalance()));
            vo.setMerchantInfo(accountMap.getOrDefault(vo.getOrderUserId(), new BusinessAccountDetailVO()));
            vo.setOrderPayeeAccountVO(BeanUtil.copyProperties(Optional.ofNullable(orderPayeeAccountMap.get(vo.getOrderNum())).orElse(new OrderPayeeAccount()), OrderPayeeAccountVO.class));
        }
        return resultList;
    }

    @Override
    public List<OrderVideoAuditVO> receivableAuditListV1(OrderListDTO orderListDTO) {
        // 添加商家id
        wrapperCondition(orderListDTO);
        // 添加支付类型
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("tmp.sort", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("tmp.update_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        orderListDTO.setStatus(Arrays.stream(OrderStatusEnum.values()).filter(item -> !item.equals(OrderStatusEnum.UN_PAY)).map(OrderStatusEnum::getCode).collect(Collectors.toList()));
        orderListDTO.setIsReceivableAudit(StatusTypeEnum.YES.getCode());
        List<OrderVideoAuditVO> orderVideoAuditVOS = baseMapper.selectOrderAuditList(orderListDTO);
        if (CollUtil.isEmpty(orderVideoAuditVOS)) {
            return Collections.emptyList();
        }
        List<String> orderNums = new ArrayList<>();
        List<String> payNums = new ArrayList<>();
        Map<String, List<String>> payNumMap = new HashMap<>();
        for (OrderVideoAuditVO orderVideoAuditVO : orderVideoAuditVOS) {
            if (StrUtil.isNotBlank(orderVideoAuditVO.getPayNum())) {
                payNums.add(orderVideoAuditVO.getPayNum());
            }
            orderNums.add(orderVideoAuditVO.getOrderNum());
        }
        //加载其他数据
        List<Order> resultList = new ArrayList<>();
        if (CollUtil.isNotEmpty(payNums)) {
            List<Order> listByPayNums = baseMapper.getListByPayNums(payNums);
            if (CollUtil.isNotEmpty(listByPayNums)) {
                resultList.addAll(listByPayNums);
            }
        }
        if (CollUtil.isNotEmpty(orderNums)) {
            List<Order> orders = baseMapper.selectListByOrderNums(orderNums);
            if (CollUtil.isNotEmpty(orders)) {
                resultList.addAll(orders);
            }
        }
        if (StringUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        for (Order item : resultList) {
            if (StrUtil.isBlank(item.getPayNum())) {
                continue;
            }
            List<String> orderGroups = payNumMap.get(item.getPayNum());
            if (CollUtil.isEmpty(orderGroups)) {
                orderGroups = new ArrayList<>();
                orderGroups.add(item.getOrderNum());
                payNumMap.put(item.getPayNum(), orderGroups);
            } else if (!orderGroups.contains(item.getOrderNum())) {
                orderGroups.add(item.getOrderNum());
            }
        }
//        Set<String> orderNumSets = new HashSet<>(orderNums);
//        orderListDTO.setOrderNums(orderNumSets);
//        List<OrderVideoVO> orderVideoVOS = orderVideoService.selectOrderVideoListByCondition(orderListDTO);
//        if (StringUtils.isEmpty(orderVideoVOS)) {
//            return new ArrayList<>();
//        }

        //  查询下单用户信息
        Set<Long> orderUserId = resultList.stream().map(Order::getOrderUserId).collect(Collectors.toSet());
        List<BusinessAccountDetailVO> accountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, orderUserId);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(accountDetailVOS);
//        Map<String, List<OrderVideoVO>> orderMap = orderVideoVOS.stream()
//                .collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
        Map<String, Order> orderMap = resultList.stream().collect(Collectors.toMap(Order::getOrderNum, p -> p, (v1, v2) -> v1));
        List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(new ArrayList<>(orderNums));
        Map<String, OrderPayeeAccount> orderPayeeAccountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, p -> p));
        }

        //  获取订单参与活动
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(resultList.stream().map(Order::getOrderNum).collect(Collectors.toList()));
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        for (OrderVideoAuditVO vo : orderVideoAuditVOS) {
            Order orDefault = orderMap.getOrDefault(vo.getOrderNum(), new Order());
            BeanUtil.copyProperties(orDefault, vo);
//            vo.setOrderVideoVOS(orderMap.getOrDefault(vo.getOrderNum(), new ArrayList<>()));
            vo.setOrderUser(accountMap.getOrDefault(vo.getOrderUserId(), new BusinessAccountDetailVO()));
            vo.setSurplusAmount(vo.getPayAmount().subtract(vo.getUseBalance() == null ? BigDecimal.ZERO : vo.getUseBalance()));
            vo.setMerchantInfo(accountMap.getOrDefault(vo.getOrderUserId(), new BusinessAccountDetailVO()));
            vo.setOrderPayeeAccountVO(BeanUtil.copyProperties(Optional.ofNullable(orderPayeeAccountMap.get(vo.getOrderNum())).orElse(new OrderPayeeAccount()), OrderPayeeAccountVO.class));
            vo.setOrderAmountDollar(vo.getMergeOrderAmountDollar());
            List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(vo.getOrderNum(), new ArrayList<>());
            vo.setOrderPromotionAmount(discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

            List<String> payNumOrderNums = payNumMap.getOrDefault(vo.getPayNum(), new ArrayList<>());
            if (CollUtil.isNotEmpty(payNumOrderNums) && payNumOrderNums.size() <= 1) {
                continue;
            }
            vo.setOrderNumGroup(payNumOrderNums);

            //合计美元、人民币、剩余支付、实际支付、支付币种、优惠金额
            BigDecimal orderAmount = BigDecimal.ZERO;
            BigDecimal orderAmountDollar = BigDecimal.ZERO;
            BigDecimal payAmount = BigDecimal.ZERO;
            BigDecimal payAmountDollar = BigDecimal.ZERO;
            BigDecimal useBalance = BigDecimal.ZERO;
            BigDecimal realPayAmount = BigDecimal.ZERO;
            BigDecimal realPayAmountCurrency = BigDecimal.ZERO;
            BigDecimal orderPromotionAmount = BigDecimal.ZERO;

            for (String item : payNumOrderNums) {
                Order order = orderMap.get(item);
                if (ObjectUtil.isNull(order)) {
                    continue;
                }
                if (!PayTypeEnum.BALANCE.getCode().equals(order.getPayType()) && order.getUseBalance().compareTo(BigDecimal.ZERO) > 0) {
                    vo.setPayType(order.getPayType());
                }
                orderAmountDollar = orderAmountDollar.add(Optional.ofNullable(order.getOrderAmountDollar()).orElse(BigDecimal.ZERO));
                orderAmount = orderAmount.add(Optional.ofNullable(order.getOrderAmount()).orElse(BigDecimal.ZERO));
                payAmount = payAmount.add(Optional.ofNullable(order.getPayAmount()).orElse(BigDecimal.ZERO));
                payAmountDollar = payAmountDollar.add(Optional.ofNullable(order.getPayAmountDollar()).orElse(BigDecimal.ZERO));
                useBalance = useBalance.add(Optional.ofNullable(order.getUseBalance()).orElse(BigDecimal.ZERO));
                realPayAmount = realPayAmount.add(Optional.ofNullable(order.getRealPayAmount()).orElse(BigDecimal.ZERO));
                realPayAmountCurrency = realPayAmountCurrency.add(Optional.ofNullable(order.getRealPayAmountCurrency()).orElse(BigDecimal.ZERO));

                List<OrderDiscountDetailVO> otherOrderDiscountDetailVOS = orderDiscountDetailVOMap.getOrDefault(order.getOrderNum(), new ArrayList<>());
                orderPromotionAmount = orderPromotionAmount.add(otherOrderDiscountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            vo.setMergeOrderAmountDollar(orderAmountDollar);
            vo.setMergeOrderAmount(orderAmount);
            vo.setMergeUseBalance(useBalance);
            vo.setMergePayAmount(payAmount);
            vo.setMergePayAmountDollar(payAmountDollar);
            vo.setMergeRealPayAmount(realPayAmount);
            vo.setMergeRealPayAmountCurrency(realPayAmountCurrency);
            vo.setMergeSurplusAmount(vo.getMergePayAmount().subtract(vo.getMergeUseBalance() == null ? BigDecimal.ZERO : vo.getMergeUseBalance()));
            vo.setMergeOrderPromotionAmount(orderPromotionAmount);

            if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(vo.getPayType())) {
                vo.setMergeRealPayAmountCurrency(vo.getRealPayAmountCurrency());
            }

        }
        return orderVideoAuditVOS;
    }

    @Override
    public OrderAuditStatusStatisticsVO orderAuditStatusStatistics() {
        return baseMapper.orderAuditStatusStatistics();
    }

    @Override
    public UnApproveStatusStatistics unApproveStatusStatistics() {
        OrderAuditStatusStatisticsVO orderAuditStatusStatisticsVO = baseMapper.orderAuditStatusStatistics();
        OrderAuditStatusStatisticsVO orderAuditStatusStatisticsVO1 = baseMapper.orderMemberAuditStatusStatistics();
        UnApproveStatusStatistics unApproveStatusStatistics = new UnApproveStatusStatistics();
        if (ObjectUtil.isNotNull(orderAuditStatusStatisticsVO)) {
            Integer unCheckCount = Optional.ofNullable(orderAuditStatusStatisticsVO.getUnCheckNum()).orElse(0);
            Integer exceptionNum = Optional.ofNullable(orderAuditStatusStatisticsVO.getExceptionNum()).orElse(0);
            unApproveStatusStatistics.setVideoCount(unCheckCount + exceptionNum);
        }
        if (ObjectUtil.isNotNull(orderAuditStatusStatisticsVO1)) {
            Integer unCheckCount = Optional.ofNullable(orderAuditStatusStatisticsVO1.getUnCheckNum()).orElse(0);
            Integer exceptionNum = Optional.ofNullable(orderAuditStatusStatisticsVO1.getExceptionNum()).orElse(0);
            unApproveStatusStatistics.setMemberCount(unCheckCount + exceptionNum);
        }

        BusinessBalancePrepayStatisticsVO innerStatistics = remoteService.getInnerStatistics();
        if (ObjectUtil.isNotNull(innerStatistics)) {
            unApproveStatusStatistics.setPrepayCount(Optional.ofNullable(innerStatistics.getPreApproveNum()).orElse(0));
        }
        unApproveStatusStatistics.setVideoCount(Optional.ofNullable(unApproveStatusStatistics.getVideoCount()).orElse(0));
        unApproveStatusStatistics.setMemberCount(Optional.ofNullable(unApproveStatusStatistics.getMemberCount()).orElse(0));
        unApproveStatusStatistics.setPrepayCount(Optional.ofNullable(unApproveStatusStatistics.getPrepayCount()).orElse(0));
        return unApproveStatusStatistics;
    }

    @Override
    public OrderAuditStatusStatisticsVO orderMemberAuditStatusStatistics() {
        return baseMapper.orderMemberAuditStatusStatistics();
    }

    @Override
    public List<ReceivableAuditListExportVO> exportReceivableAuditList(List<OrderListVO> list) {
        List<ReceivableAuditListExportVO> result = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return result;
        }

        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());

        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(list.stream().map(OrderListVO::getOrderNum).collect(Collectors.toList()));
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        for (OrderListVO item : list) {
            ReceivableAuditListExportVO vo = new ReceivableAuditListExportVO();
            if (ObjectUtil.isNotNull(item.getOrderUser())) {
                vo.setBusinessName(Optional.ofNullable(item.getOrderUser().getBusinessName()).orElse(""));
                vo.setMemberCode(Optional.ofNullable(item.getOrderUser().getMemberCode()).orElse(""));
            }
            vo.setOrderUserName(item.getOrderUserName());

            List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(item.getOrderNum(), new ArrayList<>());
            if (CollUtil.isNotEmpty(discountDetailVOS)) {
                vo.setFullConcession(orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                BigDecimal monthFirstOrderDiscount = orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(monthFirstOrderDiscount)) {
                    vo.setMonthFirstOrderDiscount("$" + monthFirstOrderDiscount);
                }
                vo.setMonthFirstOrderDiscountActivity(orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
            }
            vo.setPayNum(item.getPayNum());
            vo.setOrderNum(item.getOrderNum());
            vo.setOrderTime(item.getOrderTime());
            vo.setPayType(item.getPayType());
            vo.setPayTypeDetail(item.getPayTypeDetail());
            vo.setSubmitCredentialTime(item.getSubmitCredentialTime());
            vo.setRemark(item.getOrderRemark());
            vo.setAuditStatus(item.getAuditStatus());
            vo.setPayAmount(item.getPayAmount());
            if (ObjectUtil.isNotNull(item.getPayTime())) {
                vo.setRealPayAmount(item.getRealPayAmount());
                vo.setRealPayAmountCurrency(item.getRealPayAmountCurrency());
                vo.setCurrency(dictTypeMap.get(Convert.toStr(item.getCurrency())) == null ? CurrencyEnum.CNY.getLabel() : dictTypeMap.get(Convert.toStr(item.getCurrency())));
            }
            vo.setUseBalance(item.getUseBalance());
            vo.setSurplusAmount(item.getSurplusAmount());
            vo.setDifferenceAmount(item.getDifferenceAmount());
            if (AuditStatusEnum.APPROVE.getCode().equals(item.getAuditStatus()) && ObjectUtil.isNotNull(item.getDifferenceAmount())) {
                vo.setDifferenceAmountString((item.getDifferenceAmount().compareTo(BigDecimal.ZERO) > 0 ? "+" : "") + item.getDifferenceAmount().toString());
            }
            vo.setPayAccount(item.getPayAccount());
            vo.setPayAmount(item.getPayAmount());
            vo.setPayAmountDollar(item.getPayAmountDollar());
            vo.setCurrentExchangeRate(item.getCurrentExchangeRate());
            vo.setAuditTime(item.getAuditTime());
            vo.setAuditUserName(item.getAuditUserName());
            vo.setOrderPayeeAccount(getOrderPayeeAccount(item.getOrderPayeeAccountVO()));
            if (ObjectUtil.isNotNull(item.getOrderPromotionAmount()) && item.getOrderPromotionAmount().compareTo(BigDecimal.ZERO) > 0) {
                //设置原价
                vo.setPayAmount(item.getOrderAmount());
                vo.setPayAmountDollar(item.getOrderAmountDollar());
            }
            result.add(vo);
        }

        return result;

    }

    private String getOrderPayeeAccount(OrderPayeeAccountVO item) {
        if (ObjectUtil.isNull(item)) {
            return "-";
        }
        if (ObjectUtil.isNull(item.getAccountType())) {
            return "-";
        }
        StringBuilder sb = new StringBuilder();
        if (List.of(PayTypeEnum.BANK.getCode(), PayTypeEnum.BANK_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append("银行卡号：");
            sb.append(item.getBankAccount());

            sb.append("\n姓名：");
            sb.append(item.getAccountName());

            sb.append("\n开户行：");
            sb.append(item.getBankName());
        } else if (List.of(PayTypeEnum.PUBLIC.getCode(), PayTypeEnum.PUBLIC_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append("收款公司名称：");
            sb.append(item.getAccountName());

            sb.append("\n收款银行账号：");
            sb.append(item.getBankAccount());

            sb.append("\n开户行名称：");
            sb.append(item.getBankName());
        } else if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(item.getAccountType())) {
            sb.append("账户名：");
            sb.append(item.getAccountName());
            sb.append("\n收款账户类型：");
            sb.append(item.getCompanyAccountType());

        } else {
            sb.append("收款主体：");
            sb.append(item.getAccountName());

            sb.append("\n商户号：");
            sb.append(item.getBankAccount());
        }
        return sb.toString();
    }

    @Override
    public List<OrderPayDetailVO> orderPayDetailList(OrderPayDetailDTO dto) {
        List<OrderPayDetailVO> basePayDetailVOS = getBasePayDetailVOS(dto);
        fillData(basePayDetailVOS);
        return basePayDetailVOS;
    }

    @Override
    public List<OrderPayDetailVO> getBasePayDetailVOS(OrderPayDetailDTO dto) {
        if (StringUtils.isNotEmpty(dto.getSearchName())) {
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchantBySearchName(dto.getSearchName());
            if (CollUtil.isNotEmpty(businessList)) {
                Set<Long> merchantIds = businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toSet());
                dto.setSearchBusinessIds(merchantIds);
            }
        }
        //  补充支付类型
        PayTypeEnum.assemblePayType(dto.getPayTypes());
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("pay_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("opl.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderPayDetailVO> orderPayDetailVOS = baseMapper.orderPayDetailList(dto);
        if (StringUtils.isEmpty(orderPayDetailVOS)) {
            return new ArrayList<>();
        }
        return orderPayDetailVOS;
    }

    @Override
    public List<OrderPayDetailExportVO> getOrderPayDetailExportVos(OrderPayDetailDTO dto) {
        List<OrderPayDetailVO> orderPayDetailVOS = self.orderPayDetailList(dto);
        List<OrderPayDetailExportVO> orderPayDetailExportVos = new ArrayList<>();
        if (CollUtil.isEmpty(orderPayDetailVOS)) {
            return orderPayDetailExportVos;
        }
        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());
        for (OrderPayDetailVO item : orderPayDetailVOS) {
            OrderPayDetailExportVO orderPayDetailExportVO = BeanUtil.copyProperties(item, OrderPayDetailExportVO.class);
            orderPayDetailExportVO.setSeedCodeDiscount(null);
            if (List.of(OrderTypeEnum.VIDEO_ORDER.getCode(),
                    OrderTypeEnum.ONLINE_RECHARGE.getCode(),
                    OrderTypeEnum.PREPAY_ORDER.getCode()
            ).contains(item.getOrderType())) {
                //设置原价
                orderPayDetailExportVO.setPayAmount(item.getOrderAmount());
            }

            orderPayDetailExportVO.setCurrency(dictTypeMap.get(Convert.toStr(item.getCurrency())));
            if (ObjectUtil.isNull(item.getPayeeAccountConfig())) {
                orderPayDetailExportVos.add(orderPayDetailExportVO);
                continue;
            }
            StringBuilder sb = new StringBuilder();
            if (List.of(PayTypeEnum.BANK.getCode(), PayTypeEnum.BANK_BALANCE.getCode()).contains(item.getPayeeAccountConfig().getAccountType())) {
                sb.append("银行卡号：");
                sb.append(item.getPayeeAccountConfig().getBankAccount());

                sb.append("\n姓名：");
                sb.append(item.getPayeeAccountConfig().getAccountName());

                sb.append("\n开户行：");
                sb.append(item.getPayeeAccountConfig().getBankName());
            } else if (List.of(PayTypeEnum.PUBLIC.getCode(), PayTypeEnum.PUBLIC_BALANCE.getCode()).contains(item.getPayeeAccountConfig().getAccountType())) {
                sb.append("收款公司名称：");
                sb.append(item.getPayeeAccountConfig().getAccountName());

                sb.append("\n收款银行账号：");
                sb.append(item.getPayeeAccountConfig().getBankAccount());

                sb.append("\n开户行名称：");
                sb.append(item.getPayeeAccountConfig().getBankName());
            } else if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(item.getPayeeAccountConfig().getAccountType())) {
                sb.append("账户名：");
                sb.append(item.getPayeeAccountConfig().getAccountName());
                sb.append("\n收款账户类型：");
                sb.append(item.getPayeeAccountConfig().getCompanyAccountType());

            } else {
                sb.append("收款主体：");
                sb.append(item.getPayeeAccountConfig().getAccountName());

                sb.append("\n商户号：");
                sb.append(item.getPayeeAccountConfig().getBankAccount());
            }
            orderPayDetailExportVO.setBankAccount(Optional.ofNullable(sb.toString()).orElse(null));

            List<OrderDiscountDetailVO> orderDiscountDetailVOS = item.getOrderDiscountDetailVOS();
            if (CollUtil.isNotEmpty(orderDiscountDetailVOS)) {
                orderPayDetailExportVO.setRenewAtHalfPrice(orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                orderPayDetailExportVO.setSeedCodeDiscount(orderDiscountDetailVOS.stream().filter(discount -> List.of(PromotionActivityTypeEnum.SEED_CODE_DISTRIBUTION_DISCOUNT.getCode(), PromotionActivityTypeEnum.SEED_CODE_FISSION_DISCOUNT.getCode()).contains(discount.getType())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                orderPayDetailExportVO.setFullConcession(orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                BigDecimal monthFirstOrderDiscount = orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(monthFirstOrderDiscount)) {
                    orderPayDetailExportVO.setMonthFirstOrderDiscount("$" + monthFirstOrderDiscount);
                }
                orderPayDetailExportVO.setMonthFirstOrderDiscountActivity(orderDiscountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                orderPayDetailExportVO.setTotalOrderDiscount(orderDiscountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            orderPayDetailExportVos.add(orderPayDetailExportVO);
        }

        return orderPayDetailExportVos;
    }

    @Override
    public List<OrderPayVideoDetailVO> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto) {
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setName(dto.getKeyword());
            List<ModelInfoVO> modelList = remoteService.queryLikeModelList(modelListDTO);
            if (CollUtil.isNotEmpty(modelList)) {
                List<Long> modelIds = modelList.stream().map(ModelInfoVO::getId).collect(Collectors.toList());
                dto.setKeywordModelIds(modelIds);
            }
        }
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ov.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        PayTypeEnum.assemblePayType(dto.getPayTypes());
        List<OrderPayVideoDetailVO> orderPayVideoDetailVOS = orderVideoService.orderPayDetailVideoList(dto);
        if (CollUtil.isEmpty(orderPayVideoDetailVOS)) {
            return new ArrayList<>();
        }
        List<Long> shootModelIds = orderPayVideoDetailVOS.stream().map(OrderPayVideoDetailVO::getShootModelId).collect(Collectors.toList());
        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelIds);

        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(orderPayVideoDetailVOS.stream().map(OrderPayVideoDetailVO::getId).collect(Collectors.toList()));
        Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));

        for (OrderPayVideoDetailVO item : orderPayVideoDetailVOS) {
            item.setRealPayAmount(item.getPayAmount().subtract(item.getDifferenceAmount()));
            ModelInfoVO modelInfoVO = modelMap.get(item.getShootModelId());
            if (ObjectUtil.isNotNull(modelInfoVO)) {
                item.setShootModelAccount(modelInfoVO.getAccount());
                item.setShootModelName(Optional.ofNullable(modelInfoVO.getName()).orElse(""));
            }
            item.setOrderDiscountDetailVOS(orderDiscountDetailVOMap.get(item.getId()));
        }
        return orderPayVideoDetailVOS;
    }

    private void fillData(List<OrderPayDetailVO> orderPayDetailVOS) {
        if (CollUtil.isEmpty(orderPayDetailVOS)) {
            return;
        }
        List<String> videoOrderNums = new ArrayList<>();
        List<String> memberOrderNums = new ArrayList<>();
        List<String> prepayNums = new ArrayList<>();
        List<String> orderNums = new ArrayList<>();
        Set<Long> businessIds = new HashSet<>();
        for (OrderPayDetailVO item : orderPayDetailVOS) {
            businessIds.add(item.getMerchantId());
            if (OrderTypeEnum.VIP_ORDER.getCode().equals(item.getOrderType())) {
                memberOrderNums.add(item.getOrderNum());
            } else if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(item.getOrderType())) {
                videoOrderNums.add(item.getOrderNum());
            } else if (List.of(OrderTypeEnum.PREPAY_ORDER.getCode(), OrderTypeEnum.ONLINE_RECHARGE.getCode()).contains(item.getOrderType())) {
                prepayNums.add(item.getOrderNum());
            }
            orderNums.add(item.getOrderNum());
        }

        final List<OrderMember> memberList = orderMemberService.getByOrderNums(memberOrderNums);
        final List<OrderVideo> orderVideoList = orderVideoService.selectByOrderNums(videoOrderNums);
        final List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(BusinessAccountDetailDTO.builder().isOwnerAccount(StatusTypeEnum.YES.getCode()).businessIds(businessIds).build());
        List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(orderNums);
        List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(prepayNums).build());
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
        Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

        Map<String, OrderMember> orderMemberMap = new HashMap<>();
        Map<String, List<OrderVideo>> orderVideoMap = new HashMap<>();
        Map<String, BusinessBalancePrepayVO> prePayMap = new HashMap<>();
        Map<String, OrderPayeeAccount> orderPayeeAccountMap = new HashMap<>();
        Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();
        if (CollUtil.isNotEmpty(memberList)) {
            orderMemberMap = memberList.stream().collect(Collectors.toMap(OrderMember::getOrderNum, Function.identity()));
        }
        if (CollUtil.isNotEmpty(orderVideoList)) {
            orderVideoMap = orderVideoList.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));
        }
        if (CollUtil.isNotEmpty(businessBalancePrepayVOS)) {
            prePayMap = businessBalancePrepayVOS.stream().collect(Collectors.toMap(BusinessBalancePrepayVO::getPrepayNum, Function.identity()));
        }
        if (CollUtil.isNotEmpty(businessAccountDetailVOS)) {
            businessMap = businessAccountDetailVOS.stream().collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity()));
        }
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, Function.identity()));
        }

        for (OrderPayDetailVO item : orderPayDetailVOS) {
            item.setSurplusAmount(item.getPayAmount().subtract(item.getUseBalance() == null ? BigDecimal.ZERO : item.getUseBalance()));
            if (OrderTypeEnum.VIP_ORDER.getCode().equals(item.getOrderType())) {
                OrderMember orderMember = orderMemberMap.get(item.getOrderNum());
                OrderDetailMemberAmountVO orderDetailMemberAmountVO = new OrderDetailMemberAmountVO();
                if (ObjectUtil.isNotNull(orderMember)) {
                    orderDetailMemberAmountVO.setMemberPrice(new BigDecimal(PackageTypeEnum.getUSDByCode(orderMember.getPackageType())));
                    orderDetailMemberAmountVO.setPackageType(orderMember.getPackageType());
                }
                orderDetailMemberAmountVO.setTaxPointCost(item.getTaxPointCost());
                orderDetailMemberAmountVO.setUseBalance(Optional.ofNullable(item.getUseBalance()).orElse(BigDecimal.ZERO));
                orderDetailMemberAmountVO.setPayAmount(item.getPayAmount());
                orderDetailMemberAmountVO.setRealPayAmount(item.getRealPayAmount());
                orderDetailMemberAmountVO.setDiscountAmount(item.getSeedCodeDiscount());
                orderDetailMemberAmountVO.setSurplusAmount(item.getSurplusAmount());
                orderDetailMemberAmountVO.setDifferenceAmount(item.getDifferenceAmount());
                orderDetailMemberAmountVO.setOrderAmount(item.getOrderAmount());
                if (ObjectUtil.isNotNull(item.getSeedCodeDiscount()) && item.getSeedCodeDiscount().compareTo(BigDecimal.ZERO) > 0) {
                    //先直接设置为1-种草折扣
                    orderDetailMemberAmountVO.setDiscountType(StatusTypeEnum.YES.getCode());
                }

                item.setMemberAmount(orderDetailMemberAmountVO);
            } else if (List.of(OrderTypeEnum.PREPAY_ORDER.getCode(), OrderTypeEnum.ONLINE_RECHARGE.getCode()).contains(item.getOrderType())) {
                BusinessBalancePrepayVO businessBalancePrepayVO = prePayMap.get(item.getOrderNum());
                item.setSurplusAmount(item.getPayAmount());
                item.setOrderAmount(item.getPayAmount());
                if (ObjectUtil.isNotNull(businessBalancePrepayVO)) {
                    if (OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(item.getOrderType())
                            || OrderTypeEnum.PREPAY_ORDER.getCode().equals(item.getOrderType())) {
                        item.setOrderAmount(businessBalancePrepayVO.getAmount());
                    }
                    item.setAuditTime(businessBalancePrepayVO.getAuditTime());
                    item.setAuditStatus(businessBalancePrepayVO.getAuditStatus());
                    item.setPrepayAmount(BeanUtil.copyProperties(businessBalancePrepayVO, OrderDetailPrepayAmountVO.class));
                    item.setRealPayAmountCurrency(businessBalancePrepayVO.getRealPayAmountCurrency());
                    item.setCurrency(businessBalancePrepayVO.getCurrency());
                    item.setCurrentExchangeRate(businessBalancePrepayVO.getCurrentExchangeRate());
                    item.setPayAccount(businessBalancePrepayVO.getPayAccount());
                }
            } else {
                OrderDetailVideoAmountVO orderDetailVideoAmountVO = new OrderDetailVideoAmountVO();
                List<OrderVideo> orderVideos = orderVideoMap.get(item.getOrderNum());
                if (CollUtil.isNotEmpty(orderVideos)) {
                    BigDecimal videoPrice = orderVideos.stream().map(OrderVideo::getVideoPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal picPrice = orderVideos.stream().map(OrderVideo::getPicPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal exchangePrice = orderVideos.stream().map(OrderVideo::getExchangePrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal servicePrice = orderVideos.stream().map(OrderVideo::getServicePrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal commissionPaysTaxes = orderVideos.stream().map(OrderVideo::getCommissionPaysTaxes).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderDetailVideoAmountVO.setVideoPrice(videoPrice);
                    orderDetailVideoAmountVO.setPicPrice(picPrice);
                    orderDetailVideoAmountVO.setExchangePrice(exchangePrice);
                    orderDetailVideoAmountVO.setServicePrice(servicePrice);
                    orderDetailVideoAmountVO.setCommissionPaysTaxes(commissionPaysTaxes);
                }
                orderDetailVideoAmountVO.setTaxPointCost(item.getTaxPointCost());
                orderDetailVideoAmountVO.setPayAmount(item.getPayAmount());
                orderDetailVideoAmountVO.setUseBalance(Optional.ofNullable(item.getUseBalance()).orElse(BigDecimal.ZERO));
                orderDetailVideoAmountVO.setSurplusAmount(item.getSurplusAmount());
                orderDetailVideoAmountVO.setRealPayAmount(item.getRealPayAmount());
                orderDetailVideoAmountVO.setCurrentExchangeRate(item.getCurrentExchangeRate());
                orderDetailVideoAmountVO.setOrderPromotionAmount(item.getOrderPromotionAmount());
                orderDetailVideoAmountVO.setDifferenceAmount(item.getDifferenceAmount());
                orderDetailVideoAmountVO.setOrderAmount(item.getOrderAmount());
                item.setVideoAmount(orderDetailVideoAmountVO);
            }

            BusinessAccountDetailVO businessAccountDetailVO = businessMap.get(item.getMerchantId());
            if (ObjectUtil.isNotNull(businessAccountDetailVO)) {
                item.setBusinessName(businessAccountDetailVO.getBusinessName());
                item.setBusinessNickName(StrUtil.isNotBlank(businessAccountDetailVO.getName()) ? businessAccountDetailVO.getName() : businessAccountDetailVO.getNickName());
                item.setBusinessAccount(businessAccountDetailVO.getOwnerAccount());
                item.setMerchantCode(businessAccountDetailVO.getMemberCode());

            }
            OrderPayeeAccount orderPayeeAccount = orderPayeeAccountMap.get(item.getOrderNum());
            if (ObjectUtil.isNotNull(orderPayeeAccount)) {
                item.setBankAccount(orderPayeeAccount.getBankAccount());
                item.setPayeeAccountConfig(BeanUtil.copyProperties(orderPayeeAccount, PayeeAccountConfigVO.class));
            }
            item.setOrderDiscountDetailVOS(orderDiscountDetailVOMap.get(item.getOrderNum()));
        }
    }

    @Override
    public OrderVO receivableAuditDetail(Long id) {
        if (StringUtils.isNull(id)) {
            throw new ServiceException("id不能为空");
        }
        Order order = baseMapper.selectOrderById(id);
        OrderVO orderVO = BeanUtil.copyProperties(order, OrderVO.class);
        if (OrderTypeEnum.VIP_ORDER.getCode().equals(order.getOrderType())) {
            OrderMemberVO orderMember = orderMemberService.getOrderMember(order.getOrderNum());
            if (ObjectUtil.isNotNull(orderMember)) {
                orderVO.setPackageAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(orderMember.getPackageType())));
                orderVO.setMemberPackageType(orderMember.getPackageType());
                orderVO.setPresentedTime(orderMember.getPresentedTime());
                orderVO.setPresentedTimeType(orderMember.getPresentedTimeType());
            }

        }
        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType()) && StatusTypeEnum.YES.getCode().equals(order.getIsMergeOrder())) {
            List<Order> orders = baseMapper.getListByPayNum(order.getPayNum());
            List<String> orderNums = orders.stream().map(Order::getOrderNum).collect(Collectors.toList());

            List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByOrderNums(orderNums);
            Map<String, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getOrderNum));

            if (CollUtil.isNotEmpty(orders)) {
                BigDecimal mergePayAmount = BigDecimal.ZERO;
                BigDecimal mergePayAmountDollar = BigDecimal.ZERO;
                BigDecimal mergeUseBalance = BigDecimal.ZERO;
                BigDecimal mergeOrderPromotionAmount = BigDecimal.ZERO;
                for (Order item : orders) {
                    mergePayAmount = mergePayAmount.add(item.getPayAmount());
                    mergePayAmountDollar = mergePayAmountDollar.add(item.getPayAmountDollar());
                    mergeUseBalance = mergeUseBalance.add(item.getUseBalance());

                    List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.getOrDefault(item.getOrderNum(), new ArrayList<>());
                    item.setOrderPromotionAmount(discountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                    mergeOrderPromotionAmount = mergeOrderPromotionAmount.add(item.getOrderPromotionAmount());
                    if (item.getPayAmount().subtract(item.getUseBalance()).compareTo(BigDecimal.ZERO) > 0) {
                        orderVO.setPayType(item.getPayType());
                    }
                }

                orderVO.setMergePayAmount(mergePayAmount);
                orderVO.setMergePayAmountDollar(mergePayAmountDollar);
                orderVO.setMergeUseBalance(mergeUseBalance);
                orderVO.setMergeSurplusAmount(orderVO.getMergePayAmount().subtract(orderVO.getMergeUseBalance() == null ? BigDecimal.ZERO : orderVO.getMergeUseBalance()));
                orderVO.setMergeOrderPromotionAmount(mergeOrderPromotionAmount);
                orderVO.setMergeOrderList(orders);
            }
        }
        loadPayDocument(orderVO);
        //获取种草优惠数据
        orderVO.setSurplusAmount(orderVO.getPayAmount().subtract(orderVO.getUseBalance() == null ? BigDecimal.ZERO : orderVO.getUseBalance()));

        List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(Arrays.asList(order.getOrderNum()));
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            orderVO.setOrderPayeeAccountVO(BeanUtil.copyProperties(orderPayeeAccounts.get(0), OrderPayeeAccountVO.class));
        }
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.getOrderDiscountDetailByOrderNum(orderVO.getOrderNum());
        orderVO.setOrderPromotionAmount(orderDiscountDetailVOS.stream().map(OrderDiscountDetailVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        orderVO.setOrderDiscountDetailVOS(orderDiscountDetailVOS);
        return orderVO;
    }

    @Override
    public OrderAuditInfoVO receivableAuditInfo(Long id) {
        if (StringUtils.isNull(id)) {
            throw new ServiceException("id不能为空");
        }
        Order order = baseMapper.selectOrderById(id);
        OrderAuditInfoVO orderVO = BeanUtil.copyProperties(order, OrderAuditInfoVO.class);
        loadPayDocument(orderVO);
        List<OrderAuditFlowVO> listByOrderNum = new ArrayList<>();
        if (StatusTypeEnum.YES.getCode().equals(order.getIsMergeOrder())) {
            listByOrderNum = orderAuditFlowService.getListByOrderNumOrPayNum(null, order.getPayNum());
        } else {
            listByOrderNum = orderAuditFlowService.getListByOrderNumOrPayNum(order.getOrderNum(), null);
        }

        loadOrderAuditFlowDocument(listByOrderNum, orderVO);
        orderVO.setOrderAuditFlows(listByOrderNum);
        orderVO.setSurplusAmount(orderVO.getPayAmount().subtract(orderVO.getUseBalance() == null ? BigDecimal.ZERO : orderVO.getUseBalance()));
        //获取收款账号
        List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(Arrays.asList(order.getOrderNum()));
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            orderVO.setOrderPayeeAccountVO(BeanUtil.copyProperties(orderPayeeAccounts.get(0), OrderPayeeAccountVO.class));
        }
        //获取下单用户
        List<BusinessAccountDetailVO> accountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, Arrays.asList(order.getOrderUserId()));
        if (CollUtil.isNotEmpty(accountDetailVOS)) {
            orderVO.setOrderUser(accountDetailVOS.get(0));
        }
        if (OrderTypeEnum.VIP_ORDER.getCode().equals(order.getOrderType())) {
            OrderMemberVO orderMember = orderMemberService.getOrderMember(order.getOrderNum());
            if (ObjectUtil.isNotNull(orderMember)) {
                orderVO.setPackageAmount(new BigDecimal(PackageTypeEnum.getUSDByCode(orderMember.getPackageType())));
                orderVO.setMemberPackageType(orderMember.getPackageType());
                orderVO.setPresentedTimeType(orderMember.getPresentedTimeType());
                orderVO.setPresentedTime(orderMember.getPresentedTime());
            }
        }

        if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType())) {
            if (StatusTypeEnum.NO.getCode().equals(order.getIsMergeOrder())) {
                OrderListDTO orderListDTO = new OrderListDTO();
                orderListDTO.setOrderNums(Set.of(order.getOrderNum()));
                List<OrderVideoVO> orderVideoVOS = orderVideoService.selectOrderVideoListByCondition(orderListDTO);
                if (CollUtil.isNotEmpty(orderVideoVOS)) {
                    List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(orderVideoVOS.stream().map(OrderVideoVO::getId).collect(Collectors.toList()));
                    Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));
                    orderVideoVOS.forEach(item -> {
                        item.setCurrentExchangeRate(order.getCurrentExchangeRate());
                        item.setOrderDiscountDetailVOS(orderDiscountDetailVOMap.get(item.getId()));
                    });
                    orderVO.setOrderVideoReceivableAuditDetails(BeanUtil.copyToList(orderVideoVOS, OrderVideoVO.class));
                    orderVO.setOrderAmountDollar(orderVideoVOS.stream().map(OrderVideoVO::getAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            } else {
                OrderMerge orderMerge = orderMergeService.getOrderMergeByPayNum(order.getPayNum());
                if (ObjectUtil.isNull(orderMerge)) {
                    return orderVO;
                }
                orderVO.setMergeNickBy(orderMerge.getMergeNickBy());
                orderVO.setMergeBy(orderMerge.getMergeBy());
                orderVO.setMergeId(orderMerge.getMergeById());
                orderVO.setMergeTime(orderMerge.getMergeTime());
                //加载合并订单列表数据
                List<Order> mergeOrderList = baseMapper.getListByPayNum(order.getPayNum());
                if (CollUtil.isEmpty(mergeOrderList)) {
                    return orderVO;
                }
                BigDecimal payAmount = BigDecimal.ZERO;
                BigDecimal payAmountDollar = BigDecimal.ZERO;
                BigDecimal useBalance = BigDecimal.ZERO;
                BigDecimal realPayAmount = BigDecimal.ZERO;
                BigDecimal realPayAmountCurrency = BigDecimal.ZERO;
                BigDecimal differenceAmount = BigDecimal.ZERO;
                BigDecimal mergeOrderPromotionAmount = BigDecimal.ZERO;
                List<String> orderNum = new ArrayList<>();

                for (Order item : mergeOrderList) {
                    payAmount = payAmount.add(Optional.ofNullable(item.getPayAmount()).orElse(BigDecimal.ZERO));
                    payAmountDollar = payAmountDollar.add(Optional.ofNullable(item.getPayAmountDollar()).orElse(BigDecimal.ZERO));
                    useBalance = useBalance.add(Optional.ofNullable(item.getUseBalance()).orElse(BigDecimal.ZERO));
                    realPayAmount = realPayAmount.add(Optional.ofNullable(item.getRealPayAmount()).orElse(BigDecimal.ZERO));
                    realPayAmountCurrency = realPayAmountCurrency.add(Optional.ofNullable(item.getRealPayAmountCurrency()).orElse(BigDecimal.ZERO));
                    differenceAmount = differenceAmount.add(Optional.ofNullable(item.getDifferenceAmount()).orElse(BigDecimal.ZERO));
                    mergeOrderPromotionAmount = mergeOrderPromotionAmount.add(Optional.ofNullable(item.getOrderPromotionAmount()).orElse(BigDecimal.ZERO));
                    orderNum.add(item.getOrderNum());
                    if (item.getPayAmount().subtract(item.getUseBalance()).compareTo(BigDecimal.ZERO) > 0) {
                        orderVO.setPayType(item.getPayType());
                        orderVO.setCurrency(item.getCurrency());
                    }
                }
                orderVO.setMergeUseBalance(useBalance);
                orderVO.setMergePayAmount(payAmount);
                orderVO.setMergePayAmountDollar(payAmountDollar);
                orderVO.setMergeRealPayAmount(realPayAmount);
                orderVO.setMergeRealPayAmountCurrency(realPayAmountCurrency);
                orderVO.setMergeDifferenceAmount(differenceAmount);
                orderVO.setMergeSurplusAmount(orderVO.getMergePayAmount().subtract(orderVO.getMergeUseBalance() == null ? BigDecimal.ZERO : orderVO.getMergeUseBalance()));
                orderVO.setMergeOrderPromotionAmount(mergeOrderPromotionAmount);
                if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(orderVO.getPayType())) {
                    orderVO.setMergeRealPayAmountCurrency(orderVO.getRealPayAmountCurrency());
                }

                //重置审核流水数据
                if (CollUtil.isNotEmpty(orderVO.getOrderAuditFlows())) {
                    for (OrderAuditFlowVO item : orderVO.getOrderAuditFlows()) {
                        if (AuditStatusEnum.EXCEPTION.getCode().equals(item.getAuditStatus())) {
                            continue;
                        }
                        item.setPayType(orderVO.getPayType());
                        item.setCurrency(orderVO.getCurrency());
                        item.setRealPayAmount(orderVO.getMergeRealPayAmount());
                        item.setRealPayAmountCurrency(orderVO.getMergeRealPayAmountCurrency());
                        item.setDifferenceAmount(orderVO.getMergeDifferenceAmount());
                    }
                }

                List<OrderListVO> orderListVOS = BeanUtil.copyToList(mergeOrderList, OrderListVO.class);
                //加载视频订单列表
                List<OrderVideo> orderVideoList = orderVideoService.selectByOrderNums(orderNum);
                if (CollUtil.isEmpty(orderVideoList)) {
                    orderVO.setOrderListVOList(orderListVOS);
                    orderVO.setMergeOrderAmount(BigDecimal.ZERO);
                    orderVO.setMergeOrderAmountDollar(BigDecimal.ZERO);
                    return orderVO;
                }
                Map<String, List<OrderVideoVO>> orderVideoMap = BeanUtil.copyToList(orderVideoList, OrderVideoVO.class).stream().collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
                BigDecimal mergeOrderAmount = BigDecimal.ZERO;
                BigDecimal mergeOrderAmountDollar = BigDecimal.ZERO;

                List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(orderVideoList.stream().map(OrderVideo::getId).collect(Collectors.toList()));
                Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));
                for (OrderListVO item : orderListVOS) {
                    List<OrderVideoVO> videoList = orderVideoMap.get(item.getOrderNum());
                    if (CollUtil.isEmpty(videoList)) {
                        continue;
                    }
                    for (OrderVideoVO orderVideo : videoList) {
                        orderVideo.setCurrentExchangeRate(item.getCurrentExchangeRate());
                        mergeOrderAmount = mergeOrderAmount.add(orderVideo.getAmount());
                        mergeOrderAmountDollar = mergeOrderAmountDollar.add(orderVideo.getAmountDollar());
                        orderVideo.setOrderDiscountDetailVOS(orderDiscountDetailVOMap.get(orderVideo.getId()));
                    }
                    item.setOrderVideoVOS(videoList);
                }
                orderVO.setOrderListVOList(orderListVOS);
                orderVO.setMergeOrderAmount(mergeOrderAmount);
                orderVO.setMergeOrderAmountDollar(mergeOrderAmountDollar);
            }
        }
        orderVO.setOrderDiscountDetailVOS(orderPromotionDetailService.getOrderDiscountDetailByOrderNum(orderVO.getOrderNum()));

        return orderVO;
    }

    public void loadOrderAuditFlowDocument(List<OrderAuditFlowVO> orderAuditFlowVOS, OrderVO orderVO) {
        if (CollUtil.isEmpty(orderAuditFlowVOS)) {
            return;
        }
        List<Long> resourceIdList = new ArrayList<>();
        for (OrderAuditFlowVO item : orderAuditFlowVOS) {
            if (StrUtil.isNotBlank(item.getOrderDocumentResource())) {
                List<String> split = StrUtil.split(item.getOrderDocumentResource(), StrUtil.COMMA);
                for (String id : split) {
                    resourceIdList.add(Convert.toLong(id));

                }
            }
            if (AuditStatusEnum.EXCEPTION.getCode().equals(item.getAuditStatus())) {
                continue;
            }
            item.setPayTime(orderVO.getPayTime());
            item.setPayType(orderVO.getPayType());
            item.setPayTypeDetail(orderVO.getPayTypeDetail());
            item.setCurrency(orderVO.getCurrency());
            item.setRealPayAmount(orderVO.getRealPayAmount());
            item.setRealPayAmountCurrency(orderVO.getRealPayAmountCurrency());
            item.setDifferenceAmount(orderVO.getDifferenceAmount());
            List<String> resourceStrIdList = StrUtil.split(item.getOrderDocumentResource(), StrUtil.COMMA);
            if (CollUtil.isNotEmpty(resourceStrIdList)) {
                resourceStrIdList.forEach(resourceId -> resourceIdList.add(Convert.toLong(resourceId)));
            }
        }
        if (CollUtil.isEmpty(resourceIdList)) {
            return;
        }
        List<OrderDocumentResource> orderDocumentResources = orderDocumentResourceService.listByIds(resourceIdList.stream().distinct().collect(Collectors.toList()));
        if (CollUtil.isEmpty(orderDocumentResources)) {
            return;
        }
        Map<Long, OrderDocumentResource> orderDocumentResourceMap = orderDocumentResources.stream().collect(Collectors.toMap(OrderDocumentResource::getId, p -> p));

        for (OrderAuditFlowVO item : orderAuditFlowVOS) {
            if (StrUtil.isBlank(item.getOrderDocumentResource())) {
                continue;
            }
            List<String> split = StrUtil.split(item.getOrderDocumentResource(), StrUtil.COMMA);
            List<OrderDocumentResourceVO> orderDocumentResourceVOS = new ArrayList<>();
            for (String id : split) {
                OrderDocumentResource orderDocumentResource = orderDocumentResourceMap.get(Convert.toLong(id));
                if (ObjectUtil.isNull(orderDocumentResource)) {
                    continue;
                }
                orderDocumentResourceVOS.add(BeanUtil.copyProperties(orderDocumentResource, OrderDocumentResourceVO.class));
            }

            item.setResourceVos(orderDocumentResourceVOS);
        }

    }

    @Override
    public OrderVO loadPayDocument(OrderVO vo) {
        if (StringUtils.isNull(vo)) {
            return new OrderVO();
        }
        String keyword = vo.getOrderNum();
        if (StatusTypeEnum.YES.getCode().equals(vo.getIsMergeOrder())) {
            keyword = vo.getPayNum();
        }
        List<OrderDocumentResource> orderDocumentResources = orderDocumentResourceService.selectOrderDocumentResourceListByKeyword(keyword);
        if (CollUtil.isNotEmpty(orderDocumentResources)) {
            List<OrderDocumentResource> collect = orderDocumentResources.stream().filter(item -> UploadTypeEnum.MERCHANT.getCode().equals(item.getUploadType())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                vo.setBusinessResourceVos(BeanUtil.copyToList(collect, OrderDocumentResourceVO.class));
            }
        }
        vo.setResourceVos(BeanUtil.copyToList(orderDocumentResources, OrderDocumentResourceVO.class));
        return vo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditOrder(OrderAuditDto dto) {
        Order order = baseMapper.selectOrderById(dto.getId());
        if (StringUtils.isNull(order)) {
            throw new ServiceException("抱歉,不存在对应订单");
        }
        //检查数据
        auditOrderCheckParameter(dto, order);
        if (StrUtil.isNotBlank(dto.getPayNum()) && StatusTypeEnum.YES.getCode().equals(order.getIsMergeOrder())) {
            self.auditMergeOrder(dto);
            return;
        }

        String orderDocumentResource = "";
        //上传凭证
        if (StringUtils.isNotEmpty(dto.getObjectKeys())) {
            UploadCredentialDTO uploadCredentialDTO = getUploadCredentialDTO(dto);
            uploadCredentialDTO.setOrderNum(order.getOrderNum());
            uploadCredentialDTO.setPayNum(order.getPayNum());
            List<OrderDocumentResource> orderDocumentResources = orderDocumentResourceService.uploadCredential(uploadCredentialDTO);
            List<Long> collect = orderDocumentResources.stream().map(OrderDocumentResource::getId).collect(Collectors.toList());
            orderDocumentResource = StrUtil.join(StrUtil.COMMA, collect);
        }

        Order updateOrder = BeanUtil.copyProperties(dto, Order.class);
        updateOrder.setUpdateBy(SecurityUtils.getUsername());
        updateOrder.setAuditTime(new Date());
        updateOrder.setAuditUserId(SecurityUtils.getUserId());
        updateOrder.setAuditUserName(SecurityUtils.getUsername());
        if (ObjectUtil.isNull(dto.getPayTypeDetail())) {
            updateOrder.setPayTypeDetail(order.getPayTypeDetail());
        }
//        updateOrder.setPayeeId(dto.getPayeeId());

        if (!PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())) {
            updateOrder.setRealPayAmountCurrency(updateOrder.getRealPayAmount());
        }
        OrderVideoOperateTypeEnum orderVideoOperateTypeEnum = null;
        if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            // 添加发票数据
            order.setRealPayAmount(dto.getRealPayAmount());
            order.setPayTime(dto.getPayTime());
            order.setPayType(updateOrder.getPayType());

            if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(dto.getPayType())) {
                updateOrder.setCurrency(dto.getCurrency());
            } else {
                updateOrder.setCurrency(CurrencyEnum.CNY.getCode());
            }
            updateOrder.setDifferenceAmount(dto.getRealPayAmount().subtract(order.getPayAmount().subtract(order.getUseBalance())));
            // 填充修改订单数据
            updateOrder.setIsRecord(StatusTypeEnum.YES.getCode());
            updateOrder.setRecordTime(dto.getPayTime());
            orderVideoOperateTypeEnum = OrderVideoOperateTypeEnum.PAYMENT_APPROVED;

            OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.getPayeeInfoByInfoId(dto.getPayeeId());
            OrderPayeeAccount orderPayeeAccount = BeanUtil.copyProperties(orderPayeeAccountConfigInfoDTO, OrderPayeeAccount.class);
            orderPayeeAccount.setOrderNum(order.getOrderNum());
            orderPayeeAccount.setUpdateTime(new Date());
            orderPayeeAccount.setAccountType(orderPayeeAccountConfigInfoDTO.getType());
            orderPayeeAccountService.updateByOrderNum(orderPayeeAccount);

        } else if (AuditStatusEnum.EXCEPTION.getCode().equals(dto.getAuditStatus())) {
            updateOrder.setPayType(null);
            updateOrder.setCurrency(null);
            updateOrder.setRealPayAmount(null);
            updateOrder.setRealPayAmountCurrency(null);
            updateOrder.setDifferenceAmount(null);
            updateOrder.setPayTime(null);
            orderVideoOperateTypeEnum = OrderVideoOperateTypeEnum.PAYMENT_ANOMALY;
        }
        //修改订单
        if (!PayTypeEnum.FULL_CURRENCY.getCode().equals(updateOrder.getPayType())
                && !PayTypeEnum.FULL_CURRENCY_BALANCE.getCode().equals(updateOrder.getPayType())
                && !AuditStatusEnum.EXCEPTION.getCode().equals(dto.getAuditStatus())) {
            updateOrder.setPayTypeDetail(null);
        }
        baseMapper.updateOrder(updateOrder);
        OrderAuditFlow orderAuditFlow = new OrderAuditFlow();
        orderAuditFlow.setOrderNum(order.getOrderNum());
        orderAuditFlow.setPayNum(order.getPayNum());
        orderAuditFlow.setAuditStatus(updateOrder.getAuditStatus());
        orderAuditFlow.setAuditUserName(updateOrder.getAuditUserName());
        orderAuditFlow.setAuditUserId(updateOrder.getAuditUserId());
        orderAuditFlow.setAuditTime(updateOrder.getAuditTime());
        orderAuditFlow.setOrderDocumentResource(orderDocumentResource);
        orderAuditFlow.setRemark(dto.getOrderRemark());
        orderAuditFlowService.save(orderAuditFlow);

        List<OrderVideo> orderVideos = orderVideoService.selectValidByOrderNum(order.getOrderNum());
        //添加数据埋点
        if (ObjectUtil.isNotNull(orderVideoOperateTypeEnum) && CollUtil.isNotEmpty(orderVideos)) {
            if (dto.getPayType() != null) {
                createOrderVideoOperate(orderVideoOperateTypeEnum,
                        StrUtil.format(orderVideoOperateTypeEnum.getEventContent(),
                                PayTypeEnum.getPayTypeEnumByCode(dto.getPayType() > 10 ? dto.getPayType() - 10 : dto.getPayType()).getLabel(),
                                ObjectUtil.isNotNull(updateOrder.getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(updateOrder.getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY
                        ), orderVideos);
            } else {
                createOrderVideoOperate(orderVideoOperateTypeEnum,
                        StrUtil.format(orderVideoOperateTypeEnum.getEventContent(),
                                PayTypeEnum.getPayTypeEnumByCode(order.getPayType() > 10 ? order.getPayType() - 10 : order.getPayType()).getLabel(),
                                ObjectUtil.isNotNull(updateOrder.getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(updateOrder.getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY
                        ), orderVideos);
            }
        }
        //订单流转、余额处理
        if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            orderPayLogService.saveOrderPayLog(OrderPayLogDTO.builder()
                    .orderNum(order.getOrderNum())
                    .orderType(order.getOrderType())
                    .businessId(order.getMerchantId())
                    .payType(updateOrder.getPayType())
                    .payTypeDetail(updateOrder.getPayTypeDetail())
                    .payTime(updateOrder.getPayTime())
                    .payAmount(order.getPayAmount())
                    .realPayAmount(updateOrder.getRealPayAmount())
                    .useBalance(order.getUseBalance())
                    .build());
            //分配差额
            assignVideoDifferenceAmount(updateOrder.getDifferenceAmount(), orderVideos);
            try {
                businessBalanceLock(order.getMerchantId());
                if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(order.getOrderType())) {
                    orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_CHECK);
                    createOrderFlow(orderVideos, OrderStatusEnum.UN_CONFIRM, "审核通过");
                    updateBusinessBalance(order.getUseBalance(), BalanceSourceTypeEnum.ORDER_SPEND, List.of(order), OrderStatusEnum.UN_CONFIRM, orderVideos);
                } else {
                    flowOrderMember(order, OrderMemberStatusEnum.UN_CONFIRM, OrderFlowButtonEnum.AUDIT_SUCCESS);
                    updateBusinessBalance(order.getUseBalance(), BalanceSourceTypeEnum.MEMBER_SPEND, List.of(order), OrderStatusEnum.UN_CONFIRM, null);
                }
            } finally {
                businessBalanceReleaseLock(order.getMerchantId());
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditMergeOrder(OrderAuditDto dto) {
        List<Order> mergeOrderList = baseMapper.getListByPayNum(dto.getPayNum());
        if (CollUtil.isEmpty(mergeOrderList)) {
            return;
        }
        Assert.isTrue(OrderTypeEnum.VIDEO_ORDER.getCode().equals(mergeOrderList.get(0).getOrderType()), "必须是视频订单才能合并支付~");
        List<Order> updateOrderList = new ArrayList<>();
        //总实付人民币
        BigDecimal realPayAmountTotal = dto.getRealPayAmount();
        BigDecimal useBalanceTotal = BigDecimal.ZERO;
        OrderVideoOperateTypeEnum orderVideoOperateTypeEnum = null;

        List<String> orderNums = new ArrayList<>();
        List<OrderPayLog> orderPayLogs = new ArrayList<>();
        Integer count = 0;
        for (Order order : mergeOrderList) {
            count++;
            Order updateOrder = BeanUtil.copyProperties(dto, Order.class);
            updateOrder.setId(order.getId());
            updateOrder.setOrderNum(order.getOrderNum());
            updateOrder.setUpdateBy(SecurityUtils.getUsername());
            updateOrder.setAuditTime(new Date());
            updateOrder.setAuditUserId(SecurityUtils.getUserId());
            updateOrder.setAuditUserName(SecurityUtils.getUsername());

            orderNums.add(order.getOrderNum());
            if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                useBalanceTotal = useBalanceTotal.add(order.getUseBalance());
                BigDecimal surplusAmount = order.getPayAmount().subtract(order.getUseBalance());
                //计算实际支付金额
                if (realPayAmountTotal.compareTo(BigDecimal.ZERO) == 0) {
                    updateOrder.setRealPayAmount(BigDecimal.ZERO);
                } else if (count == mergeOrderList.size() && realPayAmountTotal.compareTo(surplusAmount) > 0) {
                    updateOrder.setRealPayAmount(realPayAmountTotal);
                    realPayAmountTotal = BigDecimal.ZERO;
                } else if (realPayAmountTotal.compareTo(surplusAmount) > 0) {
                    updateOrder.setRealPayAmount(surplusAmount);
                    realPayAmountTotal = realPayAmountTotal.subtract(surplusAmount);
                } else {
                    updateOrder.setRealPayAmount(realPayAmountTotal);
                    realPayAmountTotal = BigDecimal.ZERO;
                }

                if (order.getPayAmount().compareTo(order.getUseBalance()) == 0) {
                    updateOrder.setPayType(PayTypeEnum.BALANCE.getCode());
                } else if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(dto.getPayType())) {
                    updateOrder.setPayType(dto.getPayType());
                } else {
                    updateOrder.setPayType(dto.getPayType());
                }

                order.setRealPayAmount(updateOrder.getRealPayAmount());
                order.setPayTime(dto.getPayTime());
                order.setPayType(updateOrder.getPayType());
                order.setPayTypeDetail(updateOrder.getPayTypeDetail());
                if (List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(dto.getPayType())) {
                    updateOrder.setCurrency(dto.getCurrency());
                } else {
                    updateOrder.setCurrency(CurrencyEnum.CNY.getCode());
                }

                if (!List.of(PayTypeEnum.FULL_CURRENCY.getCode(), PayTypeEnum.FULL_CURRENCY_BALANCE.getCode()).contains(dto.getPayType())) {
                    updateOrder.setRealPayAmountCurrency(updateOrder.getRealPayAmount());
                }
                updateOrder.setDifferenceAmount(updateOrder.getRealPayAmount().subtract(order.getPayAmount().subtract(order.getUseBalance())));
                // 填充修改订单数据
                updateOrder.setIsRecord(StatusTypeEnum.YES.getCode());
                updateOrder.setRecordTime(dto.getPayTime());
                orderVideoOperateTypeEnum = OrderVideoOperateTypeEnum.PAYMENT_APPROVED;

                fillOrderPayLog(orderPayLogs, order);

            } else if (AuditStatusEnum.EXCEPTION.getCode().equals(dto.getAuditStatus())) {
                updateOrder.setPayType(null);
                updateOrder.setCurrency(null);
                updateOrder.setRealPayAmount(null);
                updateOrder.setRealPayAmountCurrency(null);
                updateOrder.setDifferenceAmount(null);
                updateOrder.setPayTime(null);
                orderVideoOperateTypeEnum = OrderVideoOperateTypeEnum.PAYMENT_ANOMALY;
            }
            updateOrderList.add(updateOrder);
        }

        self.updateBatchById(updateOrderList);
        if (ObjectUtil.isNull(dto.getPayTypeDetail()) && !AuditStatusEnum.EXCEPTION.getCode().equals(dto.getAuditStatus())) {
            baseMapper.updatePayTypeDetailToNull(orderNums);
        }


        String orderDocumentResource = "";
        //上传凭证
        if (StringUtils.isNotEmpty(dto.getObjectKeys())) {
            UploadCredentialDTO uploadCredentialDTO = getUploadCredentialDTO(dto);
            uploadCredentialDTO.setPayNum(dto.getPayNum());
            List<OrderDocumentResource> orderDocumentResources = orderDocumentResourceService.uploadCredential(uploadCredentialDTO);
            List<Long> collect = orderDocumentResources.stream().map(OrderDocumentResource::getId).collect(Collectors.toList());
            orderDocumentResource = StrUtil.join(StrUtil.COMMA, collect);
        }
        //添加流水
        OrderAuditFlow orderAuditFlow = new OrderAuditFlow();
        orderAuditFlow.setPayNum(dto.getPayNum());
        orderAuditFlow.setAuditStatus(updateOrderList.get(0).getAuditStatus());
        orderAuditFlow.setAuditUserName(updateOrderList.get(0).getAuditUserName());
        orderAuditFlow.setAuditUserId(updateOrderList.get(0).getAuditUserId());
        orderAuditFlow.setAuditTime(updateOrderList.get(0).getAuditTime());
        orderAuditFlow.setOrderDocumentResource(orderDocumentResource);
        orderAuditFlow.setRemark(dto.getOrderRemark());
        orderAuditFlowService.save(orderAuditFlow);

        List<OrderVideo> orderVideos = orderVideoService.selectValidByOrderNumsAsc(orderNums);
        //添加数据埋点
        if (ObjectUtil.isNotNull(orderVideoOperateTypeEnum) && CollUtil.isNotEmpty(orderVideos)) {
            if (dto.getPayType() != null) {
                createOrderVideoOperate(orderVideoOperateTypeEnum,
                        StrUtil.format(orderVideoOperateTypeEnum.getEventContent(),
                                PayTypeEnum.getPayTypeEnumByCode(dto.getPayType() > 10 ? dto.getPayType() - 10 : dto.getPayType()).getLabel(),
                                ObjectUtil.isNotNull(dto.getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(dto.getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY
                        ), orderVideos);
            } else {
                if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                    createOrderVideoOperate(orderVideoOperateTypeEnum,
                            StrUtil.format(orderVideoOperateTypeEnum.getEventContent(),
                                    PayTypeEnum.getPayTypeEnumByCode(updateOrderList.get(0).getPayType() > 10 ? updateOrderList.get(0).getPayType() - 10 : updateOrderList.get(0).getPayType()).getLabel(),
                                    ObjectUtil.isNotNull(updateOrderList.get(0).getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(updateOrderList.get(0).getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY
                            ), orderVideos);
                } else {
                    createOrderVideoOperate(orderVideoOperateTypeEnum,
                            StrUtil.format(orderVideoOperateTypeEnum.getEventContent(),
                                    PayTypeEnum.getPayTypeEnumByCode(mergeOrderList.get(0).getPayType() > 10 ? mergeOrderList.get(0).getPayType() - 10 : mergeOrderList.get(0).getPayType()).getLabel(),
                                    ObjectUtil.isNotNull(mergeOrderList.get(0).getPayTypeDetail()) ? StrPool.DASHED + PayTypeEnum.PayTypeDetailEnum.getPayTypeDetailEnumByCode(mergeOrderList.get(0).getPayTypeDetail()).getLabel() : CharSequenceUtil.EMPTY
                            ), orderVideos);
                }

            }
        }

        //订单流转、余额处理
        if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO = orderPayeeAccountConfigService.getPayeeInfoByInfoId(dto.getPayeeId());
            OrderPayeeAccount orderPayeeAccount = BeanUtil.copyProperties(orderPayeeAccountConfigInfoDTO, OrderPayeeAccount.class);
            orderPayeeAccount.setUpdateTime(new Date());
            orderPayeeAccount.setAccountType(orderPayeeAccountConfigInfoDTO.getType());
            orderPayeeAccountService.updateByOrderNums(orderPayeeAccount, orderNums);
            orderPayLogService.saveBatch(orderPayLogs);
            //分配差额
            if (CollUtil.isNotEmpty(orderVideos)) {
                Map<String, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getOrderNum));
                for (Order item : updateOrderList) {
                    List<OrderVideo> orderVideoList = orderVideoMap.get(item.getOrderNum());
                    if (CollUtil.isNotEmpty(orderVideoList)) {
                        assignVideoDifferenceAmount(item.getDifferenceAmount(), orderVideoList);
                    }
                }
            }
            try {
                businessBalanceLock(mergeOrderList.get(0).getMerchantId());
                if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(mergeOrderList.get(0).getOrderType())) {
                    orderMergeService.completeOrderMergeByMergeIdOrPayNum(null, mergeOrderList.get(0).getPayNum());
                    orderVideoService.checkVideoStatus(orderVideos, OrderStatusEnum.UN_CHECK);
                    createOrderFlow(orderVideos, OrderStatusEnum.UN_CONFIRM, "审核通过");
                    updateBusinessBalance(useBalanceTotal, BalanceSourceTypeEnum.ORDER_SPEND, mergeOrderList, OrderStatusEnum.UN_CONFIRM, orderVideos);
                }
            } finally {
                businessBalanceReleaseLock(mergeOrderList.get(0).getMerchantId());
            }
        }
    }

    private void fillOrderPayLog(List<OrderPayLog> orderPayLogs, Order order) {
        OrderPayLog orderPayLog = new OrderPayLog();
        orderPayLog.setOrderNum(order.getOrderNum());
        orderPayLog.setPayNum(order.getPayNum());
        orderPayLog.setOrderType(order.getOrderType());
        orderPayLog.setBusinessId(order.getMerchantId());
        orderPayLog.setPayType(order.getPayType());
        orderPayLog.setPayTypeDetail(order.getPayTypeDetail());
        orderPayLog.setPayTime(order.getPayTime());
        orderPayLog.setPayAmount(order.getPayAmount());
        orderPayLog.setRealPayAmount(order.getRealPayAmount());
        orderPayLog.setUseBalance(order.getUseBalance());
        orderPayLogs.add(orderPayLog);
    }

    /**
     * 分配差额
     *
     * @param differenceAmount 差额
     * @param orderVideos      视频订单列表
     */
    private void assignVideoDifferenceAmount(BigDecimal differenceAmount, List<OrderVideo> orderVideos) {
        if (ObjectUtil.isNotNull(differenceAmount)
                && differenceAmount.compareTo(BigDecimal.ZERO) < 0
                && CollUtil.isNotEmpty(orderVideos)) {
            //需要修改的视频订单列表
            BigDecimal surplusBalance = differenceAmount.abs();
            for (OrderVideo item : orderVideos) {
                if (surplusBalance.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                BigDecimal validAmount = item.getPayAmount().subtract(item.getUseBalance());
                if (validAmount.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (surplusBalance.compareTo(validAmount) > 0) {
                    item.setDifferenceAmount(validAmount);
                    surplusBalance = surplusBalance.subtract(validAmount);
                } else {
                    item.setDifferenceAmount(surplusBalance);
                    surplusBalance = BigDecimal.ZERO;
                }
            }
        }
    }

    public void createOrderVideoOperate(OrderVideoOperateTypeEnum orderVideoOperateTypeEnum,
                                        String evenContent,
                                        List<OrderVideo> orderVideos
    ) {
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }
        Assert.notNull(orderVideoOperateTypeEnum, "[事件]不能为空");
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = orderVideos.stream().map(item -> {
            OrderVideoOperateDTO orderVideoOperateDTO = new OrderVideoOperateDTO();
            orderVideoOperateDTO.setVideoId(item.getId());
            orderVideoOperateDTO.setEventContent(evenContent);
            return orderVideoOperateDTO;
        }).collect(Collectors.toList());
        orderVideoOperateService.createOrderVideoOperate(orderVideoOperateTypeEnum.getEventName(), orderVideoOperateTypeEnum.getIsPublic(), orderVideoOperateDTOS);
    }

    /**
     * 审核订单检查参数
     *
     * @param dto
     * @param order
     */
    private void auditOrderCheckParameter(OrderAuditDto dto, Order order) {
        if (AuditStatusEnum.CLOSE.getCode().equals(order.getAuditStatus())) {
            throw new ServiceException("订单已取消，无需审核");
        }
        if (StrUtil.isNotBlank(order.getPayNum()) && StatusTypeEnum.YES.getCode().equals(order.getIsMergeOrder())) {
            Assert.isTrue(StrUtil.isNotBlank(dto.getPayNum()), "合并订单支付单号不能为空");
        }
        if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            if (AuditStatusEnum.APPROVE.getCode().equals(order.getAuditStatus())) {
                throw new ServiceException("抱歉,订单已审核,无法再次审核");
            }
            if (ObjectUtil.isNull(dto.getPayTime())) {
                throw new ServiceException("抱歉,支付时间不能为空");
            }
            if (ObjectUtil.isNull(dto.getRealPayAmount())) {
                throw new ServiceException("抱歉,订单实付金额不能为空");
            }
            if (ObjectUtil.isNull(dto.getPayType())) {
                throw new ServiceException("抱歉,支付类型不能为空");
            }
            if (ObjectUtil.isNull(dto.getCurrency())) {
                throw new ServiceException("抱歉,币种不能为空");
            }
        } else if (AuditStatusEnum.EXCEPTION.getCode().equals(dto.getAuditStatus())) {
            if (AuditStatusEnum.APPROVE.getCode().equals(order.getAuditStatus())) {
                throw new ServiceException("订单已审核，无法标记为异常");
            }
            if (AuditStatusEnum.EXCEPTION.getCode().equals(order.getAuditStatus())) {
                throw new ServiceException("订单已标记为异常，无法再次标记为异常");
            }

        }
    }


    private UploadCredentialDTO getUploadCredentialDTO(OrderAuditDto dto) {
        UploadCredentialDTO uploadCredentialDTO = new UploadCredentialDTO();

        uploadCredentialDTO.setObjectKeys(dto.getObjectKeys());
        uploadCredentialDTO.setUploadType(UploadTypeEnum.PLATFORM.getCode());
        return uploadCredentialDTO;
    }

    /**
     * 创建订单
     *
     * @param orderVideoDTOS 视频订单
     * @return 结果
     */
    @Override
    public CreateOrderVO createOrder(List<OrderVideoDTO> orderVideoDTOS) {
        //校验意向模特是否可用 不可用直接不执行 返回不符合订单信息、不可接单
        List<Long> intentionModelIds = orderVideoDTOS.stream()
                .map(OrderVideoDTO::getIntentionModelIds)       // 获取每个 DTO 中的列表
                .filter(Objects::nonNull)                        // 过滤掉为 null 的列表
                .filter(list -> !list.isEmpty())                 // 过滤掉空的列表
                .flatMap(List::stream)                           // 将所有子列表打平
                .filter(Objects::nonNull)                        // 如果内部可能存在 null 的 Long，进一步过滤掉
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(intentionModelIds)) {
            //  校验意向模特是否符合订单信息
            Map<Integer, Collection<Long>> unfulfilledOrderModeSerialNumberMap = checkModelMeetOrder(intentionModelIds, orderVideoDTOS);
            if (CollUtil.isNotEmpty(unfulfilledOrderModeSerialNumberMap)) {
                return CreateOrderVO.builder().unfulfilledOrderModeSerialNumberMap(unfulfilledOrderModeSerialNumberMap).build();
            }

            //  校验意向模特可否接单
            Collection<Long> difference = checkModelCanAccept(intentionModelIds, SecurityUtils.getBizUserId());
            if (CollUtil.isNotEmpty(difference)) {
                Map<Integer, Collection<Long>> differenceMap = new HashMap<>();
                for (OrderVideoDTO orderVideoDTO : orderVideoDTOS) {
                    if (CollUtil.isNotEmpty(orderVideoDTO.getIntentionModelIds()) && CollUtil.containsAny(difference, orderVideoDTO.getIntentionModelIds())) {
                        differenceMap.put(orderVideoDTO.getSerialNumber(), CollUtil.intersection(difference, orderVideoDTO.getIntentionModelIds()));
                    }
                }
                return CreateOrderVO.builder().unfulfilledOrderModeSerialNumberMap(differenceMap).build();
            }
        }

        for (OrderVideoDTO orderVideoDTO : orderVideoDTOS) {
            orderVideoDTO.formatVideoLink();
        }

        Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
        if (businessId == 0L) {
            Assert.isTrue(redisService.getLock(CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY + SecurityUtils.getBizUserId(), CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
        } else {
            Assert.isTrue(redisService.getLock(CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY + businessId, CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY_SECOND), "系统繁忙，请稍后重试~");
        }

        try {
            return self.getCreateOrderVO(orderVideoDTOS);
        } finally {
            if (businessId == 0L) {
                redisService.releaseLock(CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY + SecurityUtils.getBizUserId());
            } else {
                redisService.releaseLock(CacheConstants.VIDEO_ORDER_CREATE_LOCK_KEY + businessId);
            }
        }
    }

    /**
     * 创建订单具体操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @MemberAuth
    public CreateOrderVO getCreateOrderVO(List<OrderVideoDTO> orderVideoDTOS) {
        //  生成订单号
        String orderNum = IdUtils.createOrderNum(OrderConstant.ORDER_NUM_PREFIX_DD);
        //  计算视频订单费用
        orderVideoDTOS = SpringUtils.getBean(PayService.class).calculateOrderAmount(orderVideoDTOS);

        BigDecimal orderAmount = BigDecimal.ZERO;
        BigDecimal orderAmountDollar = BigDecimal.ZERO;
        BigDecimal payAmount = BigDecimal.ZERO;
        BigDecimal payAmountDollar = BigDecimal.ZERO;
        //  订单使用满5减100总优惠金额
        BigDecimal orderPromotionAmount = BigDecimal.ZERO;
        for (OrderVideoDTO orderVideoDTO : orderVideoDTOS) {
            orderAmount = orderAmount.add(orderVideoDTO.getAmount());
            orderAmountDollar = orderAmountDollar.add(orderVideoDTO.getAmountDollar());
            payAmount = payAmount.add(orderVideoDTO.getPayAmount());
            payAmountDollar = payAmountDollar.add(orderVideoDTO.getPayAmountDollar());
            orderPromotionAmount = orderPromotionAmount.add(orderVideoDTO.getVideoPromotionAmount() == null ? BigDecimal.ZERO : orderVideoDTO.getVideoPromotionAmount());
        }

        BusinessAccountVO accountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();

        Order order = new Order();
        order.setOrderNum(orderNum);
        order.setOrderType(OrderTypeEnum.VIDEO_ORDER.getCode());
        order.setVideoCount(orderVideoDTOS.size());
        order.setOrderAmount(orderAmount);
        order.setOrderAmountDollar(orderAmountDollar);
        order.setPayAmount(payAmount);
        order.setPayAmountDollar(payAmountDollar);
        order.setOrderPromotionAmount(orderPromotionAmount);
        order.setCurrentExchangeRate(orderVideoDTOS.get(0).getCurrentExchangeRate());
        order.setIsDefaultExchangeRate(orderVideoDTOS.get(0).getIsDefaultExchangeRate());
        order.setOrderUserId(accountVO.getId());
        order.setOrderUserAccount(accountVO.getAccount());
        order.setOrderUserName(accountVO.getName());
        order.setOrderUserNickName(accountVO.getNickName());
        order.setOrderTime(DateUtil.date());
        order.setOrderTimeSign(order.getOrderTime());
        order.setMerchantId(accountVO.getBusinessId());
        order.setMerchantCode(accountVO.getBusinessVO().getMemberCode());
        order.setBizUserId(accountVO.getBizUserId());
        self.save(order);

        //  创建视频订单
        orderVideoService.createOrderVideo(orderVideoDTOS, orderNum);

        return CreateOrderVO.builder().orderNum(orderNum).build();
    }

    @Override
    public void checkBusinessStatus() {
        final BusinessAccountVO businessAccountVO = remoteService.getBusinessAccountByUnionId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getUnionid());
        if (UserStatus.DELETED.getCode().equals(String.valueOf(businessAccountVO.getStatus()))) {
            throw new ServiceException("对不起，您的账号：" + businessAccountVO.getAccount() + " 已被删除，如需恢复，请联系商家~");
        }
        if (UserStatus.DISABLE.getCode().equals(String.valueOf(businessAccountVO.getStatus()))) {
            throw new ServiceException("对不起，您的账号：" + businessAccountVO.getAccount() + " 已停用，如需恢复，请联系商家~");
        }

        BusinessVO businessVO = businessAccountVO.getBusinessVO();
        if (UserStatus.DELETED.getCode().equals(String.valueOf(businessVO.getStatus()))) {
            throw new ServiceException("对不起，您的商家：" + businessVO.getName() + " 已被删除，如需恢复，请联系客服~");
        }
        if (UserStatus.DISABLE.getCode().equals(String.valueOf(businessVO.getStatus()))) {
            throw new ServiceException("对不起，您的商家：" + businessVO.getName() + " 已停用，如需恢复，请联系客服~");
        }
    }

    /**
     * 校验模特是否可以下单 or 接单
     *
     * @param modelIds 模特id
     * @return 无法接单的模特id
     */
    @Override
    public Collection<Long> checkModelCanAccept(Collection<Long> modelIds, Long bizUserId) {
        if (CollUtil.isEmpty(modelIds)) {
            return modelIds;
        }

        //  校验模特状态
        //  a:校验是否有逾期订单
        List<Long> overdueModelIds = orderVideoModelService.checkModelOverdueVideo(modelIds);
        Set<Long> cannotModelIds = new HashSet<>(overdueModelIds);

        //  b:校验是否会超过模特可接单数
//        List<Long> cannotAcceptModel = orderVideoModelService.checkModelAcceptability(modelIds);
//        cannotModelIds.addAll(cannotAcceptModel);

        //  c:校验模特状态 行程时间
        List<Model> modelList = remoteService.queryCannotAcceptList(CannotAcceptModelDTO.builder().modelId(modelIds).bizUserId(bizUserId).build());
        List<Long> modelId = modelList.stream().map(Model::getId).collect(Collectors.toList());
        cannotModelIds.addAll(modelId);

        cannotModelIds.removeIf(Objects::isNull);
        return cannotModelIds;
    }


    @Override
    public void confirmReceipt(Long logisticId, Date signTime) {
        orderVideoLogisticService.confirmReceipt(logisticId, signTime);
    }

    @Override
    public void cancelLatestUnPayOrderNumber() {
        if (ObjectUtil.isNull(SecurityUtils.getBizUserId())) {
            throw new ServiceException("登录人不能为空~");
        }
        MemberUnPayVO memberUnPay = orderMemberService.getMemberUnPay();
        if (ObjectUtil.isNull(memberUnPay)) {
            return;
        }
        if (ObjectUtil.isNotNull(memberUnPay.getUnPayNum()) && memberUnPay.getUnPayNum() > 0) {
            if (memberUnPay.getUnPayNum() > 3) {
                throw new ServiceException("系统繁忙,请前往开通记录手动处理~");
            }
            for (OrderMemberVO item : memberUnPay.getUnPayOrderList()) {
                String orderNum = item.getOrderNum();
                try {
                    self.cancelMemberOrder(orderNum);
                } catch (Exception e) {
                    log.error("取消订单失败，orderNum = {}，错误原因：{}", orderNum, e);
                }
            }
        }
    }

    @Override
    public List<String> selectOrderUserNicknameList(String name) {
        return orderVideoService.selectOrderUserNicknameList(name);
    }

    /**
     * 组装查询列表的条件
     *
     * @param orderListDTO 列表参数
     */
    @Override
    public Boolean wrapperCondition(OrderListDTO orderListDTO) {
        //  输入关键字 远程查询模特信息数据
        if (StrUtil.isNotBlank(orderListDTO.getKeyword())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setName(orderListDTO.getKeyword());
            modelListDTO.setAccount(orderListDTO.getKeyword());
            List<ModelInfoVO> modelList = remoteService.queryLikeModelList(modelListDTO);

            List<Long> modelIds = modelList.stream().map(ModelInfoVO::getId).collect(Collectors.toList());
            orderListDTO.setModelIds(modelIds);

            orderListDTO.getPlatform().addAll(PlatformEnum.getCode(orderListDTO.getKeyword()));
        }

        if (StrUtil.isNotBlank(orderListDTO.getKeyword())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setBusinessName(orderListDTO.getKeyword());
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchant(businessAccountDetailDTO);

            List<Long> merchantIds = businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toList());
            orderListDTO.setSearchBusinessIds(merchantIds);
        }

        if (StrUtil.isNotBlank(orderListDTO.getSearchName())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setSearchNameMemberCodeAccount(orderListDTO.getSearchName());
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchant(businessAccountDetailDTO);

            if (CollUtil.isNotEmpty(businessList)) {
                List<Long> merchantIds = businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toList());
                orderListDTO.setSearchBusinessIds(merchantIds);
            }
        }

        //  补充支付类型
        PayTypeEnum.assemblePayType(orderListDTO.getPayType());

        //  如果是商家 查询自己的订单（商家端）
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE)) {
            orderListDTO.setMerchantIds(Collections.singletonList(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()));

            if (CollUtil.isNotEmpty(orderListDTO.getStatus()) && orderListDTO.getStatus().contains(OrderStatusEnum.UN_MATCH.getCode())) {
                orderListDTO.getStatus().add(OrderStatusEnum.UN_CONFIRM.getCode());
            }
        }

        //  物流主状态不为空 远程获取符合条件的物流单号
        if (CollUtil.isNotEmpty(orderListDTO.getLogisticMainStatus())) {
            Collection<String> logisticNumbers = remoteService.getLogisticNumbersByCondition(null, orderListDTO.getLogisticMainStatus());
            List<OrderVideoModelShippingAddress> modelShippingAddresses = orderVideoModelShippingAddressService.selectModelShippingAddressByLogisticNumber(logisticNumbers);
            if (CollUtil.isEmpty(modelShippingAddresses)) {
                return false;
            }
            orderListDTO.setVideoIds(modelShippingAddresses.stream().map(OrderVideoModelShippingAddress::getVideoId).collect(Collectors.toSet()));
            orderListDTO.setRollbackIds(modelShippingAddresses.stream().map(OrderVideoModelShippingAddress::getRollbackId).collect(Collectors.toSet()));
        }

        //是否确认收货：1:已收货,0:未收货
        if (ObjectUtil.isNotNull(orderListDTO.getReceipt())) {
            Collection<Long> videoIds = orderVideoLogisticService.selectLastVideoIdsByDto(orderListDTO.getVideoIds(), orderListDTO.getReceipt());
            if (CollUtil.isEmpty(videoIds)) {
                return false;
            }
            orderListDTO.setVideoIds(videoIds);
        }
        //上传需求时间
        if (ObjectUtil.isNotNull(orderListDTO.getUploadLinkTimeBegin()) && ObjectUtil.isNotNull(orderListDTO.getUploadLinkTimeEnd())) {
            List<OrderVideoUploadLink> orderVideoUploadLinks = orderVideoUploadLinkService.selectListByDto(UploadLinkListDTO.builder()
                    .videoIds(orderListDTO.getVideoIds())
                    .uploadLinkTimeBegin(orderListDTO.getUploadLinkTimeBegin())
                    .uploadLinkTimeEnd(orderListDTO.getUploadLinkTimeEnd())
                    .build());
            if (CollUtil.isEmpty(orderVideoUploadLinks)) {
                return false;
            }
            orderListDTO.setVideoIds(orderVideoUploadLinks.stream().map(OrderVideoUploadLink::getVideoId).collect(Collectors.toList()));
        }

        if (ObjectUtil.isNotNull(orderListDTO.getDownloadStatus())) {
            List<Long> videoIds = orderVideoFeedBackMaterialService.selectHasFeedBackMaterialByDto(OrderVideoFeedBackMaterialDTO.builder()
                    .videoIds(orderListDTO.getVideoIds())
                    .downloadStatus(orderListDTO.getDownloadStatus()).build());
            if (CollUtil.isEmpty(videoIds)) {
                return false;
            }
            orderListDTO.setVideoIds(videoIds);
        }


        orderListDTO.setLoginUserType(SecurityUtils.getLoginUserType());
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.MANAGER_TYPE)) {
            orderListDTO.setBackUserId(SecurityUtils.getUserId());
        }

        if (CollUtil.isNotEmpty(orderListDTO.getMatchStartTimes())) {
            Map<String, String> searchTimeMap = new HashMap<>();
            orderListDTO.getMatchStartTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    searchTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    searchTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    searchTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    searchTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
            orderListDTO.setSearchMap(searchTimeMap);
        }

        if (ObjectUtil.isNotNull(orderListDTO.getMatchStartTime())) {
            if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(orderListDTO.getMatchStartTime())) {
                orderListDTO.setMatchStartTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setMatchStartTimeEnd(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(orderListDTO.getMatchStartTime())) {
                orderListDTO.setMatchStartTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setMatchStartTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(orderListDTO.getMatchStartTime())) {
                orderListDTO.setMatchStartTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setMatchStartTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(orderListDTO.getMatchStartTime())) {
                orderListDTO.setMatchStartTimeBegin(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setMatchStartTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
            }
        }

        if (ObjectUtil.isNotNull(orderListDTO.getAddPreselectTime())) {
            if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(orderListDTO.getAddPreselectTime())) {
                orderListDTO.setAddPreselectTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setAddPreselectTimeEnd(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(orderListDTO.getAddPreselectTime())) {
                orderListDTO.setAddPreselectTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setAddPreselectTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(orderListDTO.getAddPreselectTime())) {
                orderListDTO.setAddPreselectTimeBegin(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setAddPreselectTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
            } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(orderListDTO.getAddPreselectTime())) {
                orderListDTO.setAddPreselectTimeBegin(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN));
                orderListDTO.setAddPreselectTimeEnd(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
            }

            Set<Long> videoIds = orderVideoMatchService.selectVideoIdByAddPreselectTimeOfOrderVideo(orderListDTO.getAddPreselectTimeBegin(), orderListDTO.getAddPreselectTimeEnd());
            if (CollUtil.isEmpty(videoIds)) {
                return false;
            }
            orderListDTO.getVideoIds().addAll(videoIds);
        }
        if (CollUtil.isNotEmpty(orderListDTO.getShootModelId()) && StatusTypeEnum.YES.getCode().equals(orderListDTO.getIncludeFamily())) {
            ModelListDTO modelListDTO = new ModelListDTO();
            modelListDTO.setFamilyModelIds(orderListDTO.getShootModelId());
            List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(modelListDTO);
            if (CollUtil.isNotEmpty(modelOrderSimpleVOS)) {
                List<Long> modelIds = modelOrderSimpleVOS.stream().map(ModelOrderSimpleVO::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(modelIds)) {
                    orderListDTO.getShootModelId().addAll(modelIds);
                    orderListDTO.setShootModelId(orderListDTO.getShootModelId().stream().distinct().collect(Collectors.toList()));
                }
            }
        }

        if (CollUtil.isNotEmpty(orderListDTO.getConfirmReceiptTimes())) {
            Map<String, String> confirmReceiptTimeMap = new HashMap<>();
            orderListDTO.getConfirmReceiptTimes().forEach(data -> {
                if (OrderPoolPreselectedTimeEnum.WITHIN_24_HOURS.getCode().equals(data)) {
                    confirmReceiptTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.ONE_TO_TWO_DAYS.getCode().equals(data)) {
                    confirmReceiptTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.TWO_TO_THREE_DAYS.getCode().equals(data)) {
                    confirmReceiptTimeMap.put(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -2), DatePattern.NORM_DATETIME_PATTERN));
                } else if (OrderPoolPreselectedTimeEnum.MORE_THAN_THREE_DAYS.getCode().equals(data)) {
                    confirmReceiptTimeMap.put(DateUtil.format(DateUtil.offset(DateUtil.date(), DateField.YEAR, -1), DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -3), DatePattern.NORM_DATETIME_PATTERN));
                }
            });
            orderListDTO.setConfirmReceiptTimeMap(confirmReceiptTimeMap);
        }

        return true;
    }

    /**
     * 获取预选模特信息map
     *
     * @param videoMatchOrderVOS 匹配单
     * @param modelMap           模特信息map
     */
    private Map<Long, VideoMatchOrderVO> getPreselectModelMap(List<VideoMatchOrderVO> videoMatchOrderVOS, Map<Long, ModelOrderSimpleVO> modelMap) {
        for (VideoMatchOrderVO matchOrderVO : videoMatchOrderVOS) {
            if (CollUtil.isEmpty(matchOrderVO.getNormalVideoMatchPreselectModelOrderVOS())) {
                continue;
            }
            for (VideoMatchPreselectModelOrderVO normalVideoMatchPreselectModelOrderVO : matchOrderVO.getNormalVideoMatchPreselectModelOrderVOS()) {
                normalVideoMatchPreselectModelOrderVO.setModel(modelMap.get(normalVideoMatchPreselectModelOrderVO.getModelId()));
            }
        }
        return videoMatchOrderVOS.stream().collect(Collectors.toMap(VideoMatchOrderVO::getVideoId, Function.identity()));
    }

    /**
     * 组装商家端订单列表数据
     *
     * @param orderListVOS  大订单
     * @param orderVideoVOS 小订单
     */
    private void assembleCompanyOrderVideoList(List<OrderListVO> orderListVOS, List<OrderVideoVO> orderVideoVOS) throws ExecutionException, InterruptedException {
        List<Long> videoIds = orderVideoVOS.stream().map(OrderVideoVO::getId).collect(Collectors.toList());
        List<String> orderNums = orderListVOS.stream().map(OrderListVO::getOrderNum).collect(Collectors.toList());

        // 模特ID收集
        CompletableFuture<List<Long>> modelIdsFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> modelIds = new ArrayList<>();
            modelIds.addAll(orderVideoVOS.stream().map(OrderVideoVO::getIntentionModelId).collect(Collectors.toList()));
            modelIds.addAll(orderVideoVOS.stream().map(OrderVideoVO::getShootModelId).collect(Collectors.toList()));
            return modelIds;
        }, asyncPoolTaskExecutor);

//        Set<Long> createOrderUserIds = orderVideoVOS.stream().map(OrderVideoVO::getCreateOrderUserId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());

        // 远程调用和数据库查询
        CompletableFuture<Map<Long, ModelOrderSimpleVO>> modelMapFuture = modelIdsFuture.thenCompose(modelIds ->
                CompletableFuture.supplyAsync(() -> remoteService.getModelSimpleMap(modelIds), asyncPoolTaskExecutor)
        );
        CompletableFuture<Map<Long, List<OrderVideoCase>>> videoCaseFuture = CompletableFuture.supplyAsync(() -> orderVideoCaseService.getNoReplyMapByVideoId(videoIds), asyncPoolTaskExecutor);
        CompletableFuture<Map<Long, OrderResource>> resourceMapFuture = getOrderListResourceMapFuture(orderVideoVOS);

        CompletableFuture<Map<Long, List<OrderFeedBackVO>>> orderFeedBackMapFuture = CompletableFuture.supplyAsync(() -> getOrderFeedBackMap(videoIds), asyncPoolTaskExecutor);
//        CompletableFuture<Map<String, OrderInvoice>> invoiceMapFuture = CompletableFuture.supplyAsync(() -> orderInvoiceService.getInvoiceMap(orderNums), asyncPoolTaskExecutor);

        //  查询视频订单物流信息
        CompletableFuture<Map<Long, OrderVideoLogistic>> lastLogisticMapFuture = getLastLogisticNumberMapFuture(videoIds);
        CompletableFuture<Map<Long, OrderVideoLogisticFollow>> lastLogisticFollowMapFuture = getLastLogisticFollowMapFuture(videoIds);

        // 远程调用和数据库查询
        CompletableFuture<Map<String, LogisticInfoVO>> lastLogisticInfoMapFuture = getLastLogisticInfoMapFuture(lastLogisticMapFuture);

        CompletableFuture<Map<Long, OrderVideoUploadLinkVO>> orderVideoUploadLinkMapFuture = CompletableFuture.supplyAsync(() -> getOrderVideoUploadLinkMap(videoIds), asyncPoolTaskExecutor);

        CompletableFuture<OrderVideoRefundSimpleListVO> orderVideoRefundSimpleListVOCompletableFuture = CompletableFuture.supplyAsync(() -> getVideoRefundMap(videoIds), asyncPoolTaskExecutor);

        CompletableFuture<List<Long>> videoRoastMapFuture = CompletableFuture.supplyAsync(() -> orderVideoRoastService.isRoast(videoIds), asyncPoolTaskExecutor);

        //  获取视频订单最新的模特收件地址
        CompletableFuture<List<OrderVideoModelShippingAddress>> modelShippingAddressFuture = CompletableFuture.supplyAsync(() -> orderVideoModelShippingAddressService.selectLastOrderVideoModelShippingAddressListByVideoId(videoIds), asyncPoolTaskExecutor);

        CompletableFuture<List<OrderMergeListVO>> orderMergeListVOFuture = CompletableFuture.supplyAsync(() -> orderMergeService.selectOrderMergeNormalListByOrderNums(orderNums), asyncPoolTaskExecutor);

        //  查询视频订单最新的反馈给商家素材
        CompletableFuture<List<OrderVideoFeedBack>> latestFeedBackListFuture = CompletableFuture.supplyAsync(() -> orderVideoFeedBackService.selectLatestFeedBackListByVideoIds(videoIds), asyncPoolTaskExecutor);

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                modelMapFuture, videoCaseFuture, resourceMapFuture,
                orderFeedBackMapFuture, lastLogisticInfoMapFuture,
                orderVideoUploadLinkMapFuture, orderVideoRefundSimpleListVOCompletableFuture, videoRoastMapFuture,
                orderMergeListVOFuture, latestFeedBackListFuture
        );

        allFutures.join();

        // 获取结果
        Map<Long, ModelOrderSimpleVO> modelMap = modelMapFuture.get();
        Map<Long, List<OrderVideoCase>> videoCaseMap = videoCaseFuture.get();
        Map<Long, OrderResource> resourceMap = resourceMapFuture.get();
        Map<Long, List<OrderFeedBackVO>> orderFeedBackMap = orderFeedBackMapFuture.get();
        Map<Long, OrderVideoLogistic> firstLogisticMap = lastLogisticMapFuture.get();
        Map<Long, OrderVideoLogisticFollow> lastLogisticFollowMap = lastLogisticFollowMapFuture.get();
        Map<String, LogisticInfoVO> lastLogisticInfoMap = lastLogisticInfoMapFuture.get();
        Map<Long, OrderVideoUploadLinkVO> orderVideoUploadLinkMap = orderVideoUploadLinkMapFuture.get();
        OrderVideoRefundSimpleListVO orderVideoRefundSimpleListVO = orderVideoRefundSimpleListVOCompletableFuture.get();
        List<Long> videoRoastList = videoRoastMapFuture.get();
        List<OrderMergeListVO> orderMergeListVOS = orderMergeListVOFuture.get();
        Map<String, Long> orderMergeListVOMap = orderMergeListVOS.stream().collect(Collectors.toMap(OrderMergeListVO::getOrderNums, OrderMergeListVO::getMergeId));
        Map<String, Long> orderNumMergeMap = new HashMap<>();
        List<OrderVideoFeedBack> latestFeedBackList = latestFeedBackListFuture.get();
        Map<Long, OrderVideoFeedBack> latestFeedBackMap = latestFeedBackList.stream().collect(Collectors.toMap(OrderVideoFeedBack::getVideoId, Function.identity()));

        for (Map.Entry<String, Long> orderMergeListVOEntry : orderMergeListVOMap.entrySet()) {
            String orderNumsStr = orderMergeListVOEntry.getKey();
            List<String> orderNumsSplit = CharSequenceUtil.split(orderNumsStr, StrPool.COMMA);
            for (String orderNum : orderNumsSplit) {
                orderNumMergeMap.put(orderNum, orderMergeListVOEntry.getValue());
            }
        }

        List<OrderVideoModelShippingAddress> shippingAddressesList = modelShippingAddressFuture.get();
        Map<Long, OrderVideoModelShippingAddress> shippingAddressesMap = shippingAddressesList.stream().collect(Collectors.toMap(OrderVideoModelShippingAddress::getVideoId, Function.identity()));
        // 组装 OrderVideoVO
        for (OrderVideoVO videoVO : orderVideoVOS) {
            assembleCommonOrderVideoVO(videoVO, resourceMap, modelMap, firstLogisticMap, lastLogisticInfoMap, lastLogisticFollowMap, orderVideoUploadLinkMap, orderVideoRefundSimpleListVO, shippingAddressesMap);
            List<OrderFeedBackVO> orderFeedBackVOS = orderFeedBackMap.get(videoVO.getId());
            videoVO.setHasFeedBack(CollUtil.isNotEmpty(orderFeedBackVOS));
            videoVO.setHasNewFeedBack(OrderStatusEnum.FINISHED.getCode().equals(videoVO.getStatus()) && orderFeedBackVOS.stream().anyMatch(item -> DateUtil.compare(item.getCreateTime(), videoVO.getStatusTime()) > 0));
            videoVO.setIsRoast(videoRoastList.contains(videoVO.getId()));
            videoVO.setHasCase(videoCaseMap.get(videoVO.getId()) != null);
            videoVO.setLatestFeedBack(latestFeedBackMap.get(videoVO.getId()));
        }

        Map<String, OrderVideoVO> orderVideoVOMap = orderVideoVOS.stream().distinct().collect(Collectors.toMap(OrderVideoVO::getOrderNum, orderVideo -> orderVideo, (e, r) -> e));
        // 组装 OrderListVO
        Map<String, List<OrderVideoVO>> orderMap = orderVideoVOS.stream().collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
        for (OrderListVO orderListVO : orderListVOS) {
            assembleCommonOrderVO(orderListVO, orderMap, orderVideoVOMap);
            if (ObjectUtil.isNotNull(orderListVO.getCloseOrderTime())
                    && DateUtils.getDatePoorDay(DateUtils.getNowDate(), orderListVO.getCloseOrderTime()) <= orderVideoProperties.getShowReopenOrder()
                    && orderListVO.getReopenCount() == 0
            ) {
                orderListVO.setIsShowReopenOrder(StatusTypeEnum.YES.getCode());
            } else {
                orderListVO.setIsShowReopenOrder(StatusTypeEnum.NO.getCode());
            }
            //  用于前端校验取消订单、立即支付 按钮是否展示
            List<OrderVideoVO> videoVOS = orderMap.get(orderListVO.getOrderNum());
            Set<Long> checkCompanyUserIds = videoVOS.stream().map(OrderVideoVO::getCreateOrderUserId).collect(Collectors.toSet());
            checkCompanyUserIds.add(orderListVO.getOrderUserId());
            checkCompanyUserIds.removeIf(ObjectUtil::isNull);
            orderListVO.setCheckCompanyUserIds(checkCompanyUserIds);

            orderListVO.setMergeId(orderNumMergeMap.get(orderListVO.getOrderNum()));
        }
    }

    private CompletableFuture<Map<String, LogisticInfoVO>> getLastLogisticInfoMapFuture(CompletableFuture<Map<Long, OrderVideoLogistic>> lastLogisticNumberMapFuture) {
        return lastLogisticNumberMapFuture.thenCompose(item ->
                CompletableFuture.supplyAsync(() -> {
                    Collection<String> numbers = item.values().stream().map(OrderVideoLogistic::getNumber).collect(Collectors.toSet());
                    return remoteService.getLastLogisticInfoMap(numbers);
                }, asyncPoolTaskExecutor)
        );
    }

    @NotNull
    private CompletableFuture<Map<Long, OrderVideoLogistic>> getLastLogisticNumberMapFuture(List<Long> videoIds) {
        return CompletableFuture.supplyAsync(() -> getLastLogistic(videoIds), asyncPoolTaskExecutor);
    }

    @NotNull
    private CompletableFuture<Map<Long, OrderVideoLogisticFollow>> getLastLogisticFollowMapFuture(List<Long> videoIds) {
        return CompletableFuture.supplyAsync(() -> getLastLogisticFollow(videoIds), asyncPoolTaskExecutor);
    }

    /**
     * 组装运营端订单列表数据
     *
     * @param orderListVOS  大订单
     * @param orderVideoVOS 小订单
     */
    public void assembleBackOrderVideoList(List<OrderListVO> orderListVOS, List<OrderVideoVO> orderVideoVOS) throws ExecutionException, InterruptedException {
        List<Long> videoIds = orderVideoVOS.stream().map(OrderVideoVO::getId).collect(Collectors.toList());
//        List<String> orderNums = orderListVOS.stream().map(OrderListVO::getOrderNum).collect(Collectors.toList());

        List<VideoMatchOrderVO> videoMatchOrderVOS = orderVideoMatchService.selectActiveListByVideoIds(videoIds);
        // 模特ID收集
        CompletableFuture<List<Long>> modelIdsFuture = CompletableFuture.supplyAsync(() -> {
            List<Long> modelIds = new ArrayList<>();
            modelIds.addAll(orderVideoVOS.stream().map(OrderVideoVO::getIntentionModelId).collect(Collectors.toList()));
            modelIds.addAll(orderVideoVOS.stream().map(OrderVideoVO::getShootModelId).collect(Collectors.toList()));
            List<Long> normalMatchPreselectModelIds = videoMatchOrderVOS.stream()
                    .filter(item -> CollUtil.isNotEmpty(item.getNormalVideoMatchPreselectModelOrderVOS()))
                    .flatMap(vo -> vo.getNormalVideoMatchPreselectModelOrderVOS().stream())
                    .map(VideoMatchPreselectModelOrderVO::getModelId)
                    .collect(Collectors.toList());

            modelIds.addAll(normalMatchPreselectModelIds);
            return modelIds;
        }, asyncPoolTaskExecutor);

        // 远程调用和数据库查询
        CompletableFuture<Map<Long, ModelOrderSimpleVO>> modelMapFuture = modelIdsFuture.thenCompose(modelIds ->
                CompletableFuture.supplyAsync(() -> remoteService.getModelSimpleMap(modelIds), asyncPoolTaskExecutor)
        );
        CompletableFuture<Map<Long, UserVO>> userMapFuture = CompletableFuture.supplyAsync(() -> {
            Set<Long> contactIds = orderVideoVOS.stream().map(OrderVideoVO::getContactId).collect(Collectors.toSet());
            Set<Long> issueIds = orderVideoVOS.stream().map(OrderVideoVO::getIssueId).collect(Collectors.toSet());
            contactIds.addAll(issueIds);
            SysUserListDTO dto = new SysUserListDTO();
            dto.setUserId(contactIds);
            return remoteService.getUserMap(dto);
        }, asyncPoolTaskExecutor);
        CompletableFuture<Map<Long, OrderResource>> resourceMapFuture = getOrderListResourceMapFuture(orderVideoVOS);

        Set<Long> merchantIds = orderListVOS.stream().map(OrderListVO::getMerchantId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> createOrderUserIds = orderVideoVOS.stream().map(OrderVideoVO::getCreateOrderUserId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        CompletableFuture<List<BusinessAccountDetailVO>> businessAccountFuture = completableFutureUtil.queryMerchantByBusinessIdsAndAccountIds(merchantIds, createOrderUserIds);

//        CompletableFuture<Map<Long, OrderVideoRefundSimpleVO>> videoRefundMapFuture = CompletableFuture.supplyAsync(() -> getVideoRefundMap(videoIds), asyncPoolTaskExecutor);
        CompletableFuture<OrderVideoRefundSimpleListVO> orderVideoRefundSimpleListVOCompletableFuture = CompletableFuture.supplyAsync(() -> getVideoRefundMap(videoIds), asyncPoolTaskExecutor);
//        CompletableFuture<Map<String, OrderInvoice>> invoiceMapFuture = CompletableFuture.supplyAsync(() -> orderInvoiceService.getInvoiceMap(orderNums), asyncPoolTaskExecutor);

        //  查询视频订单催单记录
        CompletableFuture<Map<Long, List<OrderVideoReminderRecord>>> reminderRecordMapFuture = CompletableFuture.supplyAsync(() -> {
            List<OrderVideoReminderRecord> reminderRecords = orderVideoReminderRecordService.selectListByVideoIds(videoIds);
            return reminderRecords.stream().collect(Collectors.groupingBy(OrderVideoReminderRecord::getVideoId));
        }, asyncPoolTaskExecutor);

        //  查询视频订单物流信息
        CompletableFuture<Map<Long, OrderVideoLogistic>> lastLogisticMapFuture = getLastLogisticNumberMapFuture(videoIds);
        CompletableFuture<Map<Long, OrderVideoLogisticFollow>> lastLogisticFollowMapFuture = getLastLogisticFollowMapFuture(videoIds);

        // 远程调用和数据库查询
        CompletableFuture<Map<String, LogisticInfoVO>> lastLogisticInfoMapFuture = getLastLogisticInfoMapFuture(lastLogisticMapFuture);

        CompletableFuture<List<Long>> videoHasCommentFuture = CompletableFuture.supplyAsync(orderVideoCommentService::getVideoIds, asyncPoolTaskExecutor);

        CompletableFuture<List<Long>> hasMaterialVideoIdsFuture = CompletableFuture.supplyAsync(() -> orderVideoFeedBackMaterialService.selectHasFeedBackMaterialByVideoIds(videoIds), asyncPoolTaskExecutor);

        CompletableFuture<Map<Long, OrderVideoUploadLinkVO>> orderVideoUploadLinkMapFuture = CompletableFuture.supplyAsync(() -> getOrderVideoUploadLinkMap(videoIds), asyncPoolTaskExecutor);

        //  获取任务单信息
        CompletableFuture<Map<Long, List<VideoTaskOrderVO>>> videoTaskOrderVOMapFuture = CompletableFuture.supplyAsync(() -> getVideoTaskOrderVOMap(videoIds), asyncPoolTaskExecutor);
        //  获取视频订单最新的模特收件地址
        CompletableFuture<List<OrderVideoModelShippingAddress>> modelShippingAddressFuture = CompletableFuture.supplyAsync(() -> orderVideoModelShippingAddressService.selectLastOrderVideoModelShippingAddressListByVideoId(videoIds), asyncPoolTaskExecutor);

        //  获取存在剪辑任务为待下载、待反馈、待剪辑、需确认、待上传 中的视频订单ID
        CompletableFuture<List<Long>> inTheEditingVideoIdsFuture = CompletableFuture.supplyAsync(() -> SpringUtils.getBean(IOrderVideoFeedBackMaterialInfoService.class).getInTheEditingVideoIds(videoIds), asyncPoolTaskExecutor);

        //  获取视频订单使用的活动
        CompletableFuture<List<OrderDiscountDetailVO>> orderDiscountDetailsFuture = CompletableFuture.supplyAsync(() -> orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(videoIds), asyncPoolTaskExecutor);

        //  获取视频订单有被商家驳回模特的视频订单
        CompletableFuture<List<Long>> rejectedModelVideoIdsFuture = CompletableFuture.supplyAsync(() -> orderVideoMatchPreselectModelService.getRejectedModelVideoIds(videoIds), asyncPoolTaskExecutor);

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                modelMapFuture, userMapFuture, resourceMapFuture, businessAccountFuture,
                orderVideoRefundSimpleListVOCompletableFuture,
                reminderRecordMapFuture, lastLogisticInfoMapFuture, videoHasCommentFuture,
                hasMaterialVideoIdsFuture, orderVideoUploadLinkMapFuture, modelShippingAddressFuture,
                inTheEditingVideoIdsFuture, orderDiscountDetailsFuture, rejectedModelVideoIdsFuture
        );

        allFutures.join();

        // 获取结果
        Map<Long, ModelOrderSimpleVO> modelMap = modelMapFuture.get();
        Map<Long, UserVO> userMap = userMapFuture.get();
        Map<Long, OrderResource> resourceMap = resourceMapFuture.get();
        OrderVideoRefundSimpleListVO orderVideoRefundSimpleListVO = orderVideoRefundSimpleListVOCompletableFuture.get();
        List<BusinessAccountDetailVO> businessAccountDetailVOS = businessAccountFuture.get();
        Map<Long, List<OrderVideoReminderRecord>> reminderRecordMap = reminderRecordMapFuture.get();
        Map<Long, OrderVideoLogistic> lastLogisticMap = lastLogisticMapFuture.get();
        Map<Long, OrderVideoLogisticFollow> lastLogisticFollowMap = lastLogisticFollowMapFuture.get();
        Map<String, LogisticInfoVO> lastLogisticInfoMap = lastLogisticInfoMapFuture.get();
        List<Long> videoHasComment = videoHasCommentFuture.get();
        List<Long> hasMaterialVideoIds = hasMaterialVideoIdsFuture.get();
        Map<Long, OrderVideoUploadLinkVO> orderVideoUploadLinkMap = orderVideoUploadLinkMapFuture.get();
        Map<Long, List<VideoTaskOrderVO>> videoTaskOrderVOMap = videoTaskOrderVOMapFuture.get();
        List<Long> inTheEditingVideoIds = inTheEditingVideoIdsFuture.get();
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderDiscountDetailsFuture.get();
        List<Long> rejectedModelVideoIds = rejectedModelVideoIdsFuture.get();

        List<OrderVideoModelShippingAddress> shippingAddressesList = modelShippingAddressFuture.get();
        Map<Long, OrderVideoModelShippingAddress> shippingAddressesMap = shippingAddressesList.stream().collect(Collectors.toMap(OrderVideoModelShippingAddress::getVideoId, Function.identity()));

        Map<Long, VideoMatchOrderVO> videoMatchOrderVOMap = getPreselectModelMap(videoMatchOrderVOS, modelMap);
        Map<Long, BusinessAccountDetailVO> businessMap = RemoteService.RemoteUtil.getMerchantMap(businessAccountDetailVOS);

        Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));

        // 组装 OrderVideoVO
        for (OrderVideoVO videoVO : orderVideoVOS) {
            assembleCommonOrderVideoVO(videoVO, resourceMap, modelMap, lastLogisticMap, lastLogisticInfoMap, lastLogisticFollowMap, orderVideoUploadLinkMap, orderVideoRefundSimpleListVO, shippingAddressesMap);
            VideoMatchOrderVO videoMatchOrderVO = videoMatchOrderVOMap.getOrDefault(videoVO.getId(), new VideoMatchOrderVO());
            videoVO.setVideoMatchOrderVO(videoMatchOrderVO);
            videoVO.setCarryType(ObjectUtil.isNotNull(videoMatchOrderVO.getCarryType()) ? videoMatchOrderVO.getCarryType() : videoVO.getCarryType());
            videoVO.setCommissionUnit(ObjectUtil.isNotNull(videoMatchOrderVO.getCommissionUnit()) ? videoMatchOrderVO.getCommissionUnit() : videoVO.getCommissionUnit());
            videoVO.setCommission(ObjectUtil.isNotNull(videoMatchOrderVO.getCommission()) ? videoMatchOrderVO.getCommission() : videoVO.getCommission());
            videoVO.setContact(userMap.get(videoVO.getContactId()));
            videoVO.setIssue(userMap.get(videoVO.getIssueId()));
            videoVO.setHasComment(videoHasComment.contains(videoVO.getId()));
            videoVO.setHasMaterial(hasMaterialVideoIds.contains(videoVO.getId()));
            videoVO.setHasUnfinishedEditTask(inTheEditingVideoIds.contains(videoVO.getId()));
            videoVO.setOrderDiscountDetailVOS(orderDiscountDetailVOMap.get(videoVO.getId()));
            videoVO.setHasRejectedModel(rejectedModelVideoIds.contains(videoVO.getId()));

            if (OrderStatusEnum.UN_FINISHED.getCode().equals(videoVO.getStatus())) {
                //  设置视频订单催单次数
                List<OrderVideoReminderRecord> reminderRecords = reminderRecordMap.get(videoVO.getId());
                if (CollUtil.isNotEmpty(reminderRecords) && reminderRecords.stream().anyMatch(item -> ReminderStatusEnum.UNTREATED.getCode().equals(item.getStatus()))) {
                    videoVO.setReminder(reminderRecords.size());
                }
            }

            List<VideoTaskOrderVO> videoTaskOrderVOS = videoTaskOrderVOMap.get(videoVO.getId());
            if (CollUtil.isNotEmpty(videoTaskOrderVOS)) {
                List<VideoTaskOrderVO> afterSaleTaskVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.AFTER_SALE.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                List<VideoTaskOrderVO> workOrderVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.WORK_ORDER.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                //  添加售后单状态
                if (CollUtil.isNotEmpty(afterSaleTaskVOS)) {
                    if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()) || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(item.getStatus()))) {
                        videoVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        videoVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }

                //  添加工单状态
                if (CollUtil.isNotEmpty(workOrderVOS)) {
                    if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()))) {
                        videoVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        videoVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }
            }
        }

        Map<String, OrderVideoVO> orderVideoVOMap = orderVideoVOS.stream().distinct().collect(Collectors.toMap(OrderVideoVO::getOrderNum, ovv -> ovv, (e, r) -> e));
        // 组装 OrderListVO
        Map<String, List<OrderVideoVO>> orderMap = orderVideoVOS.stream().collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
        for (OrderListVO orderListVO : orderListVOS) {
            assembleCommonOrderVO(orderListVO, orderMap, orderVideoVOMap);
            orderListVO.setMerchantInfo(businessMap.get(orderListVO.getMerchantId()));
            if (ObjectUtil.isNotNull(orderListVO.getCloseOrderTime())
                    && DateUtils.getDatePoorDay(DateUtils.getNowDate(), orderListVO.getCloseOrderTime()) <= orderVideoProperties.getShowReopenOrder()
                    && orderListVO.getReopenCount() == 0
            ) {
                orderListVO.setIsShowReopenOrder(StatusTypeEnum.YES.getCode());
            } else {
                orderListVO.setIsShowReopenOrder(StatusTypeEnum.NO.getCode());
            }
        }
    }

    @Override
    public Map<Long, List<VideoTaskOrderVO>> getVideoTaskOrderVOMap(List<Long> videoIds) {
        List<VideoTaskOrderVO> videoTaskOrderVOS = orderVideoTaskService.selectVideoTaskOrderVOListByVideoIds(videoIds);
        return videoTaskOrderVOS.stream().collect(Collectors.groupingBy(VideoTaskOrderVO::getVideoId));
    }

    /**
     * 获取视频订单的退款状态(优先获取最近的 有效订单（状态 = 退款待审核、退款中、退款成功）)
     *
     * @param videoIds 视频订单id
     */
    @Override
    public OrderVideoRefundSimpleListVO getVideoRefundMap(List<Long> videoIds) {
        OrderVideoRefundSimpleListVO orderVideoRefundSimpleListVO = new OrderVideoRefundSimpleListVO();
        if (CollUtil.isEmpty(videoIds)) {
            return new OrderVideoRefundSimpleListVO();
        }
        List<OrderVideoRefundSimpleVO> refunds = orderVideoRefundService.selectOrderVideoRefundSimpleListByVideoId(videoIds);

        Map<Long, List<OrderVideoRefundSimpleVO>> refundMap = refunds.stream()
                .collect(Collectors.groupingBy(OrderVideoRefundSimpleVO::getVideoId))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().stream()
                                .sorted((a, b) -> b.getApplyTime().compareTo(a.getApplyTime()))
                                .collect(Collectors.toList())
                ));

        Map<Long, OrderVideoRefundSimpleVO> refundStatusMap = new HashMap<>();
        for (Map.Entry<Long, List<OrderVideoRefundSimpleVO>> entry : refundMap.entrySet()) {
            OrderVideoRefundSimpleVO orderVideoRefundSimpleVO = entry.getValue().get(0);
            for (OrderVideoRefundSimpleVO item : entry.getValue()) {
                if (List.of(RefundStatusEnum.AFTER_SALE_UN_CHECK.getCode(), RefundStatusEnum.AFTER_SALE.getCode(), RefundStatusEnum.AFTER_SALE_FINISHED.getCode()).contains(item.getRefundStatus())) {
                    orderVideoRefundSimpleVO = item;
                    break;
                }
            }
            refundStatusMap.put(entry.getKey(), orderVideoRefundSimpleVO);
        }
        Map<Long, List<OrderVideoRefundSimpleVO>> ListRefund = refunds.stream()
                .collect(Collectors.groupingBy(OrderVideoRefundSimpleVO::getVideoId))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().stream().filter(val -> val.getRefundStatus().equals(RefundStatusEnum.AFTER_SALE_FINISHED.getCode()))
                                .sorted((a, b) -> b.getApplyTime().compareTo(a.getApplyTime()))
                                .collect(Collectors.collectingAndThen(
                                        Collectors.toMap(
                                                OrderVideoRefundSimpleVO::getRefundType,
                                                Function.identity(),
                                                (exit, r) -> exit,
                                                LinkedHashMap::new
                                        ),
                                        map -> new ArrayList<>(map.values())
                                ))
                ));

        orderVideoRefundSimpleListVO.setMapVo(refundStatusMap);
        orderVideoRefundSimpleListVO.setListVo(ListRefund);

        return orderVideoRefundSimpleListVO;
    }

    /**
     * 组装凭证信息
     *
     * @param orderNum 订单编号
     * @return 凭证信息
     */
    private GeneratePdfDTO assembleGeneratePdfInfo(String orderNum) {
        Order order = baseMapper.getOrderByOrderNum(orderNum);
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setId(order.getMerchantId());
        BusinessVO businessVo = remoteService.getBusinessVo(businessDTO);


        GeneratePdfDTO generatePdfDTO = new GeneratePdfDTO();
        generatePdfDTO.setCompany(CommonConstant.PDF_COMPANY);
        generatePdfDTO.setUnit(businessVo.getName());
        generatePdfDTO.setDate(DateUtil.format(order.getOrderTime(), DatePattern.CHINESE_DATE_PATTERN));
        generatePdfDTO.setName(CommonConstant.PDF_NAME);
        generatePdfDTO.setAmount(order.getOrderAmount());
        generatePdfDTO.setReceiptCompany(CommonConstant.PDF_RECEIPT_COMPANY);
        generatePdfDTO.setOpeningBank(CommonConstant.PDF_OPENING_BANK);
        generatePdfDTO.setProceedsAccount(CommonConstant.PDF_PROCEEDS_ACCOUNT);
        return generatePdfDTO;
    }


    /**
     * 获取视频订单关联的反馈素材map
     *
     * @param videoIds 视频订单id
     */
    private Map<Long, List<OrderFeedBackVO>> getOrderFeedBackMap(List<Long> videoIds) {
        List<OrderFeedBackVO> orderFeedBacks = orderVideoFeedBackService.getFeedBack(videoIds);
        return orderFeedBacks.stream().collect(Collectors.groupingBy(OrderFeedBackVO::getVideoId));
    }

    /**
     * 获取视频订单关联物流的最新单号
     */
    private Map<Long, OrderVideoLogistic> getLastLogistic(List<Long> videoIds) {
        List<OrderVideoLogistic> orderVideoLogistic = orderVideoLogisticService.selectListByVideoId(videoIds);
        Map<Long, List<OrderVideoLogistic>> map = orderVideoLogistic.stream().collect(Collectors.groupingBy(OrderVideoLogistic::getVideoId));

        Map<Long, OrderVideoLogistic> LastLogisticMap = new HashMap<>();
        for (Map.Entry<Long, List<OrderVideoLogistic>> entry : map.entrySet()) {
            Long videoId = entry.getKey();
            List<OrderVideoLogistic> value = entry.getValue();
            value.sort(Comparator.comparing(OrderVideoLogistic::getShippingTime));

            LastLogisticMap.put(videoId, value.get(value.size() - 1));
        }
        return LastLogisticMap;
    }

    /**
     * 获取视频订单关联物流的最新单号
     */
    private Map<Long, OrderVideoLogisticFollow> getLastLogisticFollow(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return Collections.emptyMap();
        }
        List<OrderVideoLogisticFollow> orderVideoLogistic = orderVideoLogisticCore.getOrderVideoLogisticFollowListByVideoIds(videoIds);
        if (CollUtil.isEmpty(orderVideoLogistic)) {
            return Collections.emptyMap();
        }
        Map<Long, List<OrderVideoLogisticFollow>> map = orderVideoLogistic.stream().collect(Collectors.groupingBy(OrderVideoLogisticFollow::getVideoId));

        Map<Long, OrderVideoLogisticFollow> LastLogisticMap = new HashMap<>();
        for (Map.Entry<Long, List<OrderVideoLogisticFollow>> entry : map.entrySet()) {
            Long videoId = entry.getKey();
            List<OrderVideoLogisticFollow> value = entry.getValue();
            value.sort(Comparator.comparing(OrderVideoLogisticFollow::getCreateTime));
            LastLogisticMap.put(videoId, value.get(value.size() - 1));
        }
        return LastLogisticMap;
    }

    /**
     * 获取组装订单列表的resource   CompletableFuture
     */
    private CompletableFuture<Map<Long, OrderResource>> getOrderListResourceMapFuture(List<OrderVideoVO> orderVideoVOS) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> referencePicIds = orderVideoVOS.stream().map(OrderVideoVO::getReferencePicId).filter(Objects::nonNull).collect(Collectors.toList());

            List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
            return orderResourceService.getResourceMapByIds(resourceIds);
        }, asyncPoolTaskExecutor);
    }


    private void assembleCommonOrderVO(OrderListVO orderListVO, Map<String, List<OrderVideoVO>> orderMap, Map<String, OrderVideoVO> orderVideoVOMap) {
        orderListVO.setOrderVideoVOS(orderMap.get(orderListVO.getOrderNum()));
        if (orderListVO.isDefaultExchangeRate()) {
            orderListVO.setPayAmount(null);
        }
        orderListVO.setStatus(orderVideoVOMap.get(orderListVO.getOrderNum()) != null ? orderVideoVOMap.get(orderListVO.getOrderNum()).getStatus() : null);
    }

    /**
     * 组装OrderVideoVO共用属性
     */
    private void assembleCommonOrderVideoVO(OrderVideoVO videoVO, Map<Long, OrderResource> resourceMap, Map<Long, ModelOrderSimpleVO> modelMap,
                                            Map<Long, OrderVideoLogistic> lastLogisticMap, Map<String, LogisticInfoVO> lastLogisticInfoMap, Map<Long, OrderVideoLogisticFollow> lastLogisticFollowMap,
                                            Map<Long, OrderVideoUploadLinkVO> orderVideoUploadLinkMap, OrderVideoRefundSimpleListVO orderVideoRefundSimpleListVO,
                                            Map<Long, OrderVideoModelShippingAddress> shippingAddressesMap) {
        videoVO.setIntentionModel(modelMap.get(videoVO.getIntentionModelId()));
        videoVO.setShootModel(modelMap.get(videoVO.getShootModelId()));
        OrderVideoUploadLinkVO orderVideoUploadLink = orderVideoUploadLinkMap.get(videoVO.getId());
        videoVO.setUploadStatus(orderVideoUploadLink != null ? orderVideoUploadLink.getStatus() : null);
        videoVO.setUploadLink(orderVideoUploadLink != null ? orderVideoUploadLink.getUploadLink() : null);
        videoVO.setOrderVideoRefund(orderVideoRefundSimpleListVO.getMapVo().get(videoVO.getId()));
        videoVO.setOrderVideoRefundList(orderVideoRefundSimpleListVO.getListVo().get(videoVO.getId()));

        List<Long> referencePicIds = StringUtils.splitToLong(videoVO.getReferencePicId(), StrUtil.COMMA);
        for (Long referencePicId : referencePicIds) {
            OrderResource resource = resourceMap.get(referencePicId);
            if (resource != null) {
                videoVO.getReferencePic().add(resource.getObjectKey());
            }
        }
        //  设置物流最新信息
        OrderVideoLogistic logistic = lastLogisticMap.get(videoVO.getId());
        if (ObjectUtil.isNotNull(logistic)) {
            OrderLogisticInfoVO orderLogisticInfoVO = BeanUtil.copyProperties(lastLogisticInfoMap.getOrDefault(logistic.getNumber(), new LogisticInfoVO()), OrderLogisticInfoVO.class);
            orderLogisticInfoVO.setReceipt(logistic.getReceipt());
            orderLogisticInfoVO.setReceiptTime(logistic.getReceiptTime());
            orderLogisticInfoVO.setLogisticId(logistic.getId());
            videoVO.setLogisticInfo(orderLogisticInfoVO);
            videoVO.setShippingTime(logistic.getShippingTime());
        }
        OrderVideoLogisticFollow orderVideoLogisticFollow = lastLogisticFollowMap.get(videoVO.getId());
        if (ObjectUtil.isNotNull(orderVideoLogisticFollow)) {
            LogisticFollowVideoInfoVO logisticFollowVideoInfoVO = BeanUtil.copyProperties(orderVideoLogisticFollow, LogisticFollowVideoInfoVO.class);
            logisticFollowVideoInfoVO.setMainStatusSketch(LogisticMainStatus.getSketchByLabel(logisticFollowVideoInfoVO.getLatestMainStatus()));
            logisticFollowVideoInfoVO.setMainStatus(logisticFollowVideoInfoVO.getLatestMainStatus());
            videoVO.setLogisticFollowVideoInfoVO(logisticFollowVideoInfoVO);
        }

        //改动需发货，待完成，已完成，需确认
        if (OrderStatusEnum.NEED_FILLED.getCode().equals(videoVO.getStatus())
                || OrderStatusEnum.UN_FINISHED.getCode().equals(videoVO.getStatus())
                || OrderStatusEnum.NEED_CONFIRM.getCode().equals(videoVO.getStatus())
                || OrderStatusEnum.FINISHED.getCode().equals(videoVO.getStatus())) {
            OrderVideoModelShippingAddress orderVideoModelShippingAddress = shippingAddressesMap.getOrDefault(videoVO.getId(), new OrderVideoModelShippingAddress());
            videoVO.setLogisticFlag(orderVideoModelShippingAddress.getLogisticFlag());
            videoVO.setLogisticFlagRemark(orderVideoModelShippingAddress.getLogisticFlagRemark());
            videoVO.setLogisticFlagTime(orderVideoModelShippingAddress.getLogisticFlagTime());
            videoVO.setShippingRemark(orderVideoModelShippingAddress.getShippingRemark());
        }
    }

    @Override
    public void createOrderFlow(List<OrderVideo> orderVideos, OrderStatusEnum orderStatus, String eventName) {
        OrderFlowDTO orderFlowDTO = new OrderFlowDTO();
        orderFlowDTO.setOrderVideos(orderVideos);
        orderFlowDTO.setOrderStatus(orderStatus);
        orderFlowDTO.setEventName(eventName);
        orderFlow(orderFlowDTO);
    }

    @Override
    public void createOrderFlow(OrderVideo orderVideo, OrderStatusEnum orderStatus, String eventName) {
        createOrderFlow(Collections.singletonList(orderVideo), orderStatus, eventName);
    }

    private Map<Long, OrderVideoUploadLinkVO> getOrderVideoUploadLinkMap(List<Long> videoIds) {
        List<OrderVideoUploadLinkVO> orderVideoUploadLinkVOS = orderVideoUploadLinkService.selectListByVideoIds(videoIds);
        return orderVideoUploadLinkVOS.stream().collect(Collectors.toMap(OrderVideoUploadLinkVO::getVideoId, Function.identity()));
    }


    /**
     * 校验意向模特是否符合订单信息
     */
    private Map<Integer, Collection<Long>> checkModelMeetOrder(List<Long> modelIds, List<OrderVideoDTO> orderVideoDTOS) {
        List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().id(modelIds).build());
        Map<Long, ModelOrderSimpleVO> modelOrderSimpleVOMap = modelOrderSimpleVOS.stream().collect(Collectors.toMap(ModelOrderSimpleVO::getId, Function.identity()));

        //  不符合订单信息的模特的订单序号Map
        Map<Integer, Collection<Long>> unfulfilledOrderModeSerialNumberMap = new HashMap<>();

        for (OrderVideoDTO orderVideoDTO : orderVideoDTOS) {
            if (CollUtil.isEmpty(orderVideoDTO.getIntentionModelIds())) {
                continue;
            }

            List<Long> unfulfilledOrderModelIds = new ArrayList<>();

            for (Long intentionModelId : orderVideoDTO.getIntentionModelIds()) {
                ModelOrderSimpleVO modelOrderSimpleVO = modelOrderSimpleVOMap.get(intentionModelId);
                if (ObjectUtil.isNull(modelOrderSimpleVO)) {
                    continue;
                }
                if (!CharSequenceUtil.contains(modelOrderSimpleVO.getPlatform(), Convert.toChar(orderVideoDTO.getPlatform()))) {
                    unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                    continue;
                }

                if (!modelOrderSimpleVO.getNation().equals(orderVideoDTO.getShootingCountry())) {
                    unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                    continue;
                }

                if (ObjectUtil.isNotNull(orderVideoDTO.getPicCount()) && ModelTypeEnum.INFLUENT.getCode().equals(orderVideoDTO.getModelType())) {
                    unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                    continue;
                }

                if (ModelTypeEnum.ALL.getCode().equals(orderVideoDTO.getModelType())) {
                    // 如果模特类型是都可以 就不需要校验模特类型
                    continue;
                }

                if (!modelOrderSimpleVO.getType().equals(orderVideoDTO.getModelType())) {
                    unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                }
            }

            if (CollUtil.isNotEmpty(unfulfilledOrderModelIds)) {
                unfulfilledOrderModeSerialNumberMap.put(orderVideoDTO.getSerialNumber(), unfulfilledOrderModelIds);
            }
        }
        return unfulfilledOrderModeSerialNumberMap;
    }

    /**
     * 校验意向模特是否符合订单信息
     */
    @Override
    public List<Long> checkModelMeetOrder(List<Long> modelIds, OrderVideoDTO orderVideoDTO) {
        List<ModelOrderSimpleVO> modelOrderSimpleVOS = remoteService.queryModelSimpleList(ModelListDTO.builder().id(modelIds).build());

        List<Long> unfulfilledOrderModelIds = new ArrayList<>();

        for (ModelOrderSimpleVO modelOrderSimpleVO : modelOrderSimpleVOS) {
            if (!CharSequenceUtil.contains(modelOrderSimpleVO.getPlatform(), Convert.toChar(orderVideoDTO.getPlatform()))) {
                unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                continue;
            }

            if (!modelOrderSimpleVO.getNation().equals(orderVideoDTO.getShootingCountry())) {
                unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                continue;
            }

            if (ObjectUtil.isNotNull(orderVideoDTO.getPicCount()) && ModelTypeEnum.INFLUENT.getCode().equals(orderVideoDTO.getModelType())) {
                unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                continue;
            }

            if (ModelTypeEnum.ALL.getCode().equals(orderVideoDTO.getModelType())) {
                //如果模特类型是都可以 就不需要校验模特类型
                continue;
            }

            if (!modelOrderSimpleVO.getType().equals(orderVideoDTO.getModelType())) {
                unfulfilledOrderModelIds.add(modelOrderSimpleVO.getId());
                continue;
                // throw new ServiceException(CharSequenceUtil.format("选择的意向模特：{} 不符合订单[模特类型]信息", modelOrderSimpleVO.getName()));
            }
        }
        return CollUtil.isNotEmpty(unfulfilledOrderModelIds) ? unfulfilledOrderModelIds : null;
    }


    @Override
    public void exportFinancialVerificationList(FinancialVerificationExportDTO dto, HttpServletResponse response) {
        if (StatusTypeEnum.YES.getCode().equals(dto.getIsAll())) {
            OrderByDto orderByDto = new OrderByDto();
            orderByDto.setField("ot.pay_time", OrderByDto.DIRECTION.ASC);
            orderByDto.setField("ot.id", OrderByDto.DIRECTION.ASC);
            PageUtils.startPage(orderByDto);
            List<FinancialVerificationAllExportVO> financialVerificationAllExportVOS = orderVideoService.financialVerificationAllExportList(dto);
            if (CollUtil.isEmpty(financialVerificationAllExportVOS)) {
                ExcelUtil<FinancialVerificationAllExportVO> util = new ExcelUtil<>(FinancialVerificationAllExportVO.class);
                // 设置响应头信息
                ExcelUtil.setAttachmentResponseHeader(response, "财务对账全部字段导出");
                util.exportExcel(response, new ArrayList<>(), "财务对账全部字段导出");
                return;
            }
            List<Long> videoIds = new ArrayList<>();
            List<Long> shootModelIds = new ArrayList<>();
            List<String> videoCodes = new ArrayList<>();
            financialVerificationAllExportVOS.stream().map(FinancialVerificationAllExportVO::getShootModelId).collect(Collectors.toList());
            for (FinancialVerificationAllExportVO item : financialVerificationAllExportVOS) {
                if (ObjectUtil.isNotNull(item.getShootModelId())) {
                    shootModelIds.add(item.getShootModelId());
                }
                if (ObjectUtil.isNotNull(item.getPayAmount())) {
                    item.setRealAmount(item.getPayAmount().subtract(Optional.ofNullable(item.getDifferenceAmount()).orElse(BigDecimal.ZERO)));
                } else {
                    item.setRealAmount(BigDecimal.ZERO);
                }
                videoIds.add(item.getVideoId());
                videoCodes.add(item.getVideoCode());
            }
            List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(videoIds);
            Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));

            List<OrderVideoRefund> orderVideoRefunds = orderVideoRefundService.selectValidOrderVideoRefundListByVideoId(videoIds);
            Map<Long, OrderVideoInvoiceVO> orderVideoInvoiceVOMap = orderInvoiceService.getOrderVideoInvoiceVOMap(videoIds);

            Map<Long, BigDecimal> refundAmountMap = new HashMap<>();
            Map<String, BigDecimal> withdrawAmountMap = new HashMap<>();
            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelIds);

            if (CollUtil.isNotEmpty(orderVideoRefunds)) {
                refundAmountMap = orderVideoRefunds.stream()
                        .collect(Collectors.groupingBy(
                                OrderVideoRefund::getVideoId,
                                Collectors.reducing(BigDecimal.ZERO, OrderVideoRefund::getRefundAmount, BigDecimal::add)
                        ));
            }
            //所有所有视频订单退款数据
            List<BusinessBalanceDetailLockInfoVO> lockInfoVOS = remoteService.queryValidLockList(BusinessBalanceDetailLockInfoDTO.builder().videoCodes(videoCodes).build());
            if (CollUtil.isNotEmpty(lockInfoVOS)) {
                withdrawAmountMap = lockInfoVOS.stream()
                        .collect(Collectors.groupingBy(
                                BusinessBalanceDetailLockInfoVO::getVideoCode,
                                Collectors.reducing(BigDecimal.ZERO, BusinessBalanceDetailLockInfoVO::getPayOutAmount, BigDecimal::add)
                        ));
            }
            for (FinancialVerificationAllExportVO item : financialVerificationAllExportVOS) {
                OrderVideoInvoiceVO orderVideoInvoiceVO = orderVideoInvoiceVOMap.get(item.getVideoId());

                if (ObjectUtil.isNotNull(orderVideoInvoiceVO)) {
                    item.setInvoiceNumber(orderVideoInvoiceVO.getNumber());
                }

                ModelInfoVO modelInfoVO = modelMap.get(item.getShootModelId());
                if (ObjectUtil.isNotNull(modelInfoVO)) {
                    item.setShootModelName(Optional.ofNullable(modelInfoVO.getName()).orElse(""));
                }
                item.setRealPayAmount(item.getPayAmount().subtract(item.getUseBalance()));
                BigDecimal videoRefundAmount = refundAmountMap.get(item.getVideoId());
                if (ObjectUtil.isNull(videoRefundAmount)) {
                    item.setBalanceRefund(null);
                    item.setOtherRefund(null);
                } else if (videoRefundAmount.compareTo(item.getRealPayAmount()) <= 0) {
                    //退款金额小于支付现金
                    item.setOtherRefund(videoRefundAmount);
                } else {
                    //退款金额大于现金
                    BigDecimal surplus = videoRefundAmount.subtract(item.getRealPayAmount());
                    if (surplus.compareTo(item.getUseBalance()) >= 0) {
                        //剩余支付金额大于余额
                        item.setOtherRefund(videoRefundAmount.subtract(item.getUseBalance()));
                        item.setBalanceRefund(item.getUseBalance().compareTo(BigDecimal.ZERO) == 0 ? null : item.getUseBalance());
                    } else {
                        item.setBalanceRefund(surplus);
                        item.setOtherRefund(item.getRealPayAmount());
                    }
                }

                BigDecimal withdrawAmount = withdrawAmountMap.get(item.getVideoCode());
                item.setWithdrawAmount(Optional.ofNullable(withdrawAmount).orElse(BigDecimal.ZERO));

                List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.get(item.getVideoId());
                if (CollUtil.isNotEmpty(discountDetailVOS)) {
                    item.setFullConcession(discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                    BigDecimal monthFirstOrderDiscount = discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(monthFirstOrderDiscount)) {
                        item.setMonthFirstOrderDiscount("$" + monthFirstOrderDiscount);
                    }
                    item.setMonthFirstOrderDiscountActivity(discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                }
            }
            financialVerificationAllExportVOS.sort(Comparator.comparing(FinancialVerificationAllExportVO::getPayTime, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(FinancialVerificationAllExportVO::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
            ExcelUtil<FinancialVerificationAllExportVO> util = new ExcelUtil<>(FinancialVerificationAllExportVO.class);
            // 设置响应头信息
            ExcelUtil.setAttachmentResponseHeader(response, "财务对账全部字段导出");
            util.exportExcel(response, financialVerificationAllExportVOS, "财务对账全部字段导出");
        } else {
            OrderByDto orderByDto = new OrderByDto();
            orderByDto.setField("opl.pay_time", OrderByDto.DIRECTION.ASC);
            orderByDto.setField("opl.id", OrderByDto.DIRECTION.ASC);
            //获取预付款、会员订单、视频订单数据
            List<FinancialVerificationExportVO> financialVerificationExports = baseMapper.getFinancialVerificationExports(dto);
            List<BusinessBalanceDetailLockInfoVO> businessBalanceDetailLockInfoVOS = remoteService.queryValidLockList(BusinessBalanceDetailLockInfoDTO.builder().auditEndTime(dto.getEndTime()).auditStartTime(dto.getStartTime()).build());
            if (CollUtil.isEmpty(financialVerificationExports) && CollUtil.isEmpty(businessBalanceDetailLockInfoVOS)) {
                ExcelUtil<FinancialVerificationExportVO> util = new ExcelUtil<>(FinancialVerificationExportVO.class);
                // 设置响应头信息
                ExcelUtil.setAttachmentResponseHeader(response, "财务对账出纳导出");
                util.exportExcel(response, new ArrayList<>(), "财务对账出纳导出");
                return;
            }
            if (CollUtil.isEmpty(financialVerificationExports)) {
                financialVerificationExports = new ArrayList<>();
            }
            Map<String, OrderPayeeAccount> orderPayeeAccountMap = new HashMap<>();
            Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();
            Map<String, BusinessBalancePrepayVO> prePayMap = new HashMap<>();
            List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
            Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
            dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());

            List<Long> videoIds = new ArrayList<>();
            if (CollUtil.isNotEmpty(financialVerificationExports)) {
                List<String> orderNums = new ArrayList<>();
                List<String> prepayNums = new ArrayList<>();
                Set<Long> businessIds = new HashSet<>();
                for (FinancialVerificationExportVO item : financialVerificationExports) {
                    businessIds.add(item.getMerchantId());
                    orderNums.add(item.getOrderNum());
                    if (List.of(OrderTypeEnum.PREPAY_ORDER.getCode(), OrderTypeEnum.ONLINE_RECHARGE.getCode()).contains(item.getOrderType())) {
                        prepayNums.add(item.getOrderNum());
                    }
                    if (ObjectUtil.isNotNull(item.getVideoId())) {
                        videoIds.add(item.getVideoId());
                    }
                }
                List<BusinessBalancePrepayVO> businessBalancePrepayVOS = remoteService.innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO.builder().prepayNums(prepayNums).build());
                if (CollUtil.isNotEmpty(businessBalancePrepayVOS)) {
                    prePayMap = businessBalancePrepayVOS.stream().collect(Collectors.toMap(BusinessBalancePrepayVO::getPrepayNum, Function.identity()));
                }
                final List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(BusinessAccountDetailDTO.builder().isOwnerAccount(StatusTypeEnum.YES.getCode()).businessIds(businessIds).build());
                List<OrderPayeeAccount> orderPayeeAccounts = orderPayeeAccountService.queryListByOrderNums(orderNums);
                if (CollUtil.isNotEmpty(businessAccountDetailVOS)) {
                    businessMap = businessAccountDetailVOS.stream().collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity()));
                }
                if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
                    orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, Function.identity()));
                }
            }
            List<OrderDiscountDetailVO> orderDiscountDetailVOS = orderPromotionDetailService.selectOrderDiscountDetailsByVideoIds(videoIds);
            Map<Long, List<OrderDiscountDetailVO>> orderDiscountDetailVOMap = orderDiscountDetailVOS.stream().collect(Collectors.groupingBy(OrderDiscountDetailVO::getVideoId));

            loadWithdraw(financialVerificationExports, businessBalanceDetailLockInfoVOS);
            financialVerificationExports.sort(Comparator.comparing(FinancialVerificationExportVO::getPayTime, Comparator.nullsLast(Comparator.naturalOrder())).thenComparing(FinancialVerificationExportVO::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
            List<String> validOrderNums = new ArrayList<>();
            for (FinancialVerificationExportVO item : financialVerificationExports) {
                //提现数据不需要剩余支付金额
                if (StrUtil.isBlank(item.getRelevanceNum())) {
                    item.setSurplusAmount(item.getPayAmount().subtract(item.getUseBalance() == null ? BigDecimal.ZERO : item.getUseBalance()));
                }

                //填充预付、会员订单总价
                if (Arrays.asList(OrderTypeEnum.VIP_ORDER.getCode()).contains(item.getOrderType()) && ObjectUtil.isNull(item.getAmount())) {
                    item.setAmount(item.getPayAmount());
                }
                if (OrderTypeEnum.VIDEO_ORDER.getCode().equals(item.getOrderType())) {
                    //设置原价
                    item.setPayAmount(item.getOrderAmount());
                }

                if (List.of(OrderTypeEnum.PREPAY_ORDER.getCode(), OrderTypeEnum.ONLINE_RECHARGE.getCode()).contains(item.getOrderType()) && item.getOrderNum().startsWith(OrderConstant.PREPAY_NUM)) {
                    BusinessBalancePrepayVO businessBalancePrepayVO = prePayMap.getOrDefault(item.getOrderNum(), BusinessBalancePrepayVO.builder().remark("").build());
                    item.setAmount(businessBalancePrepayVO.getAmount());
                    item.setSurplusAmount(item.getPayAmount());
                    item.setPayAmount(businessBalancePrepayVO.getAmount());
                    item.setDifferenceAmount(ObjectUtil.isNotNull(businessBalancePrepayVO.getContainPresentedAmount()) ? businessBalancePrepayVO.getContainPresentedAmount().negate() : BigDecimal.ZERO);
                    item.setAuditRemark(businessBalancePrepayVO.getRemark());
                    item.setRealPayAmountCurrency(businessBalancePrepayVO.getRealPayAmountCurrency());
                    item.setCurrency(businessBalancePrepayVO.getCurrency());
                    item.setPayAccount(businessBalancePrepayVO.getPayAccount());
                }
                if (ObjectUtil.isNotNull(item.getDifferenceAmount())) {
                    item.setDifferenceAmountString((item.getDifferenceAmount().compareTo(BigDecimal.ZERO) > 0 ? "+" : "") + item.getDifferenceAmount().toString());
                }

                //商家信息
                BusinessAccountDetailVO businessAccountDetailVO = businessMap.get(item.getMerchantId());
                if (ObjectUtil.isNotNull(businessAccountDetailVO)) {
                    item.setBusinessName(businessAccountDetailVO.getBusinessName());
                    item.setMerchantCode(businessAccountDetailVO.getMemberCode());
                }
                //收款账号信息
                OrderPayeeAccount orderPayeeAccount = orderPayeeAccountMap.get(item.getOrderNum());
                if (ObjectUtil.isNotNull(orderPayeeAccount)) {
                    item.setBankAccount(getOrderPayeeAccount(BeanUtil.copyProperties(orderPayeeAccount, OrderPayeeAccountVO.class)));
                }
                //只要有一条数据则清楚其他数据
                if (validOrderNums.contains(item.getOrderNum())) {
                    item.setPayAmount(BigDecimal.ZERO);
                    item.setRealPayAmount(BigDecimal.ZERO);
                    item.setSurplusAmount(BigDecimal.ZERO);
                    item.setDifferenceAmountString(null);
                    item.setRealPayAmountCurrency(BigDecimal.ZERO);
                    item.setUseBalance(BigDecimal.ZERO);
                } else {
                    validOrderNums.add(item.getOrderNum());
                }
                if (ObjectUtil.isNotNull(item.getCurrency())) {
                    item.setCurrencyString(dictTypeMap.get(Convert.toStr(item.getCurrency())));
                }

                List<OrderDiscountDetailVO> discountDetailVOS = orderDiscountDetailVOMap.get(item.getVideoId());
                if (CollUtil.isNotEmpty(discountDetailVOS)) {
                    item.setFullConcession(discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                    BigDecimal monthFirstOrderDiscount = discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null);
                    if (ObjectUtil.isNotNull(monthFirstOrderDiscount)) {
                        item.setMonthFirstOrderDiscount("$" + monthFirstOrderDiscount);
                    }
                    item.setMonthFirstOrderDiscountActivity(discountDetailVOS.stream().filter(discount -> discount.getType().equals(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode())).map(OrderDiscountDetailVO::getDiscountAmount).filter(ObjectUtil::isNotNull).findFirst().orElse(null));
                }
            }

            ExcelUtil<FinancialVerificationExportVO> util = new ExcelUtil<>(FinancialVerificationExportVO.class);
            // 设置响应头信息
            ExcelUtil.setAttachmentResponseHeader(response, "财务对账出纳导出");
            util.exportExcel(response, financialVerificationExports, "财务对账出纳导出");
        }
    }

    private void loadWithdraw(List<FinancialVerificationExportVO> financialVerificationExports, List<BusinessBalanceDetailLockInfoVO> businessBalanceDetailLockInfoVOS) {
        if (CollUtil.isNotEmpty(businessBalanceDetailLockInfoVOS)) {
            for (BusinessBalanceDetailLockInfoVO item : businessBalanceDetailLockInfoVOS) {
                FinancialVerificationExportVO vo = new FinancialVerificationExportVO();
                vo.setPayTime(item.getAuditTime());
                vo.setMerchantCode(item.getMerchantCode());
                vo.setOrderNum(item.getNumber());
                vo.setRelevanceNum(item.getBalanceNumber());
                vo.setOrderType(BalanceDetailTypeEnum.VIDEO.getCode().equals(item.getNumberType()) ? OrderTypeEnum.VIDEO_ORDER.getCode() : OrderTypeEnum.PREPAY_ORDER.getCode());
                vo.setVideoCode(item.getVideoCode());
                vo.setBusinessName(item.getBusinessName());

                vo.setPayType(WithdrawTypeEnum.getPayType(item.getWithdrawWay()));

                vo.setPayAccount("");
                vo.setBankAccount("-");


                vo.setAmount(item.getPayOutAmount().negate());
                vo.setPayAmount(item.getAmount().negate());
                vo.setUseBalance(new BigDecimal("0"));
                vo.setRealPayAmount(item.getRealAmount().negate());
                vo.setAuditRemark(item.getAuditRemark());
                vo.setMerchantId(item.getBusinessId());
                financialVerificationExports.add(vo);
            }
        }
    }

    @Override
    public void clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO dto) {
        videoCartService.clearVideoCartIntentionModelId(dto);
    }

    @Override
    public List<OrderVideoRefund> getOrderVideoRefundList(List<String> numbers) {
        return orderVideoRefundService.getOrderVideoRefundListByBusinessId(numbers);
    }

    @Override
    public List<Order> getOrderPayAppIdInfo(List<String> orderNums) {
        return baseMapper.getLogByOrderNumber(orderNums);
    }

    @Override
    public void updateWechatPayAppId(List<String> orderNums, OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        baseMapper.updateWechatPayAppId(orderNums, orderPayeeAccountConfigInfoDTO.getBankAccount());
    }

    @Override
    public void updateAliPayAppId(List<String> orderNums, OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        baseMapper.updateAliPayAppId(orderNums, orderPayeeAccountConfigInfoDTO.getBankAccount());
    }

    @Override
    public List<OrderListVO> selectOrderListVOS(OrderListDTO orderListDTO) {
        List<OrderListVO> orderListVOS = baseMapper.selectOrderListByCondition(orderListDTO, OrderTypeEnum.VIDEO_ORDER.getCode());

        if (CollUtil.isEmpty(orderListVOS)) {
            return new ArrayList<>();
        }

        Set<String> orderNums = orderListVOS.stream().map(OrderListVO::getOrderNum).collect(Collectors.toSet());
        orderListDTO.setOrderNums(orderNums);
        orderListDTO.setNewPreselectStatus(orderListDTO.getPreselectStatus());
        orderListDTO.setPreselectStatus(null);
        List<OrderVideoVO> orderVideoVOS = orderVideoService.selectOrderVideoListByCondition(orderListDTO);
        if (CollUtil.isEmpty(orderVideoVOS)) {
            return new ArrayList<>();
        }

        try {
            if (UserTypeConstants.MANAGER_TYPE == SecurityUtils.getLoginUserType()) {
                assembleBackOrderVideoList(orderListVOS, orderVideoVOS);
            } else {
                assembleCompanyOrderVideoList(orderListVOS, orderVideoVOS);
            }
        } catch (Exception e) {
            log.error("查询订单列表错误！", e);
            throw new ServiceException("查询订单列表错误！");
        }

        return orderListVOS;
    }

    @Nullable
    private PayTranStatusEnum getOrderMergeForCheckOrderStatus(CheckStatusDTO checkStatusDTO) {
        if (ObjectUtil.isNotNull(checkStatusDTO.getMergeId())) {
            //  获取合并单
            OrderMerge orderMerge = orderMergeService.getOrderMergeByIdOrPayNum(checkStatusDTO.getMergeId(), null);
            if (OrderMergeStatusEnum.COMPLETE.getCode().equals(orderMerge.getStatus())) {
                return PayTranStatusEnum.SUCCESS;
            } else if (OrderMergeStatusEnum.CLOSE.getCode().equals(orderMerge.getStatus())) {
                return PayTranStatusEnum.CLOSED;
            }
            checkStatusDTO.setOrderNums(orderMergeService.getOrderNumsByMergeIdOrPayNum(checkStatusDTO.getMergeId(), null));
            checkStatusDTO.setOrderMerge(orderMerge);
        } else {
            if (orderMergeService.checkOrderMerge(checkStatusDTO.getOrderNum())) {
                return PayTranStatusEnum.ORDER_MERGE;
            }
            checkStatusDTO.setOrderNums(List.of(checkStatusDTO.getOrderNum()));
        }
        return null;
    }
}
