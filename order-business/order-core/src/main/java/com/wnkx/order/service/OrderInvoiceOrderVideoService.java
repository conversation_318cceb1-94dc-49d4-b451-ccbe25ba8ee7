package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceOrderVideo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/13 10:47
 */
public interface OrderInvoiceOrderVideoService extends IService<OrderInvoiceOrderVideo> {

    /**
     * 通过发票订单ID获取关联视频订单
     */
    List<OrderInvoiceOrderVideo> selectListByOrderInvoiceOrderIds(List<Long> orderInvoiceOrderIds);

    /**
     * 根据视频编码查询发票关联的视频订单
     */
    List<OrderInvoiceOrderVideo> selectListByVideoCodes(List<String> videoCodes);
}
