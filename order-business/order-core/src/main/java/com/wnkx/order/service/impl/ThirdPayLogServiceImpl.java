package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.ThirdPayLog;
import com.wnkx.order.mapper.ThirdPayLogMapper;
import com.wnkx.order.service.ThirdPayLogService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【third_pay_log(支付回调记录表)】的数据库操作Service实现
 */
@Service
public class ThirdPayLogServiceImpl extends ServiceImpl<ThirdPayLogMapper, ThirdPayLog>
        implements ThirdPayLogService {

    @Override
    public boolean checkOrderNotExist(String mchntOrderNo) {
        return baseMapper.checkOrderNotExist(mchntOrderNo);
    }

    @Override
    public boolean checkOrderNumberNotExist(String orderNumber) {
        return baseMapper.checkOrderNumberNotExist(orderNumber);
    }
}




