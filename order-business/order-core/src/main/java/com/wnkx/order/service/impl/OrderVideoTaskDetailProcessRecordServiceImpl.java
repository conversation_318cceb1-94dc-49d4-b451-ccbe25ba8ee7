package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoTaskDetailProcessRecordDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetailProcessRecord;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailProcessRecordVO;
import com.wnkx.order.mapper.OrderVideoTaskDetailProcessRecordMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoTaskDetailProcessRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/11 18:03
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoTaskDetailProcessRecordServiceImpl extends ServiceImpl<OrderVideoTaskDetailProcessRecordMapper, OrderVideoTaskDetailProcessRecord> implements OrderVideoTaskDetailProcessRecordService {


    private final RemoteService remoteService;
    /**
     * 工单-查询处理记录
     */
    @Override
    public List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordList(String taskNum) {
        List<OrderVideoTaskDetailProcessRecord> orderVideoTaskDetailProcessRecords = baseMapper.selectWorkOrderProcessRecordListByTaskNum(taskNum);
        if (CollUtil.isEmpty(orderVideoTaskDetailProcessRecords)) {
            return Collections.emptyList();
        }

        List<OrderVideoTaskDetailProcessRecordVO> orderVideoTaskDetailProcessRecordVOS = BeanUtil.copyToList(orderVideoTaskDetailProcessRecords, OrderVideoTaskDetailProcessRecordVO.class);

        List<Long> operateByIds = orderVideoTaskDetailProcessRecordVOS.stream().map(OrderVideoTaskDetailProcessRecordVO::getOperateById).collect(Collectors.toList());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(operateByIds).build());

        for (OrderVideoTaskDetailProcessRecordVO orderVideoTaskDetailProcessRecordVO : orderVideoTaskDetailProcessRecordVOS) {
            orderVideoTaskDetailProcessRecordVO.setOperate(userMap.get(orderVideoTaskDetailProcessRecordVO.getOperateById()));
            // 将逗号分隔的字符串转换为List
            if (StrUtil.isNotBlank(orderVideoTaskDetailProcessRecordVO.getObjectKeysStr())) {
                orderVideoTaskDetailProcessRecordVO.setObjectKeys(StrUtil.split(orderVideoTaskDetailProcessRecordVO.getObjectKeysStr(), StrUtil.COMMA));
            }
        }
        return orderVideoTaskDetailProcessRecordVOS;
    }

    /**
     * 批量新增处理记录
     */
    @Override
    public void saveBatchWorkOrderProcessRecord(List<OrderVideoTaskDetailProcessRecordDTO> dtoList) {
        if (CollUtil.isEmpty(dtoList)) {
            return;
        }

        List<OrderVideoTaskDetailProcessRecord> orderVideoTaskDetailProcessRecords = BeanUtil.copyToList(dtoList, OrderVideoTaskDetailProcessRecord.class);
        baseMapper.saveBatch(orderVideoTaskDetailProcessRecords);
    }

    /**
     * 工单-新增处理记录
     */
    @Override
    public void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto) {
        OrderVideoTaskDetailProcessRecord orderVideoTaskDetailProcessRecord = new OrderVideoTaskDetailProcessRecord();
        orderVideoTaskDetailProcessRecord.setTaskNum(dto.getTaskNum());
        orderVideoTaskDetailProcessRecord.setContent(dto.getContent());
        if (CollUtil.isNotEmpty(dto.getObjectKeys())) {
            orderVideoTaskDetailProcessRecord.setObjectKeys(StrUtil.join(StrUtil.COMMA, dto.getObjectKeys()));
        }
        orderVideoTaskDetailProcessRecord.setTime(DateUtil.date());
        orderVideoTaskDetailProcessRecord.setOperateBy(SecurityUtils.getUsername());
        orderVideoTaskDetailProcessRecord.setOperateById(SecurityUtils.getUserId());
        orderVideoTaskDetailProcessRecord.setOperateType(dto.getOperateType());
        orderVideoTaskDetailProcessRecord.setCompletionMode(dto.getCompletionMode());
        baseMapper.insert(orderVideoTaskDetailProcessRecord);
    }

    @Override
    public List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordListByTaskNums(List<String> taskNum) {
        List<OrderVideoTaskDetailProcessRecord> list = baseMapper.selectListByTaskNums(taskNum);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 根据taskNum分组，并根据createTime获取最新的记录
        Map<String, OrderVideoTaskDetailProcessRecord> latestRecords = list.stream()
                .collect(Collectors.toMap(
                        OrderVideoTaskDetailProcessRecord::getTaskNum,
                        record -> record,
                        (existing, replacement) -> existing.getCreateTime().after(replacement.getCreateTime()) ? existing : replacement
                ));

        // 将最新记录转换为VO列表
        List<OrderVideoTaskDetailProcessRecordVO> orderVideoTaskDetailProcessRecordVOS = BeanUtil.copyToList(latestRecords.values(), OrderVideoTaskDetailProcessRecordVO.class);

        List<Long> operateByIds = orderVideoTaskDetailProcessRecordVOS.stream().map(OrderVideoTaskDetailProcessRecordVO::getOperateById).collect(Collectors.toList());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(operateByIds).build());

        for (OrderVideoTaskDetailProcessRecordVO orderVideoTaskDetailProcessRecordVO : orderVideoTaskDetailProcessRecordVOS) {
            if (ObjectUtil.equal(orderVideoTaskDetailProcessRecordVO.getOperateType(), OrderTaskDetailFlowOperateTypeEnum.ROLLBACK_ORDER.getCode())) {
                orderVideoTaskDetailProcessRecordVO.setOperateById(null);
            }
            orderVideoTaskDetailProcessRecordVO.setOperate(userMap.get(orderVideoTaskDetailProcessRecordVO.getOperateById()));
            // 将逗号分隔的字符串转换为List
            if (StrUtil.isNotBlank(orderVideoTaskDetailProcessRecordVO.getObjectKeysStr())) {
                orderVideoTaskDetailProcessRecordVO.setObjectKeys(StrUtil.split(orderVideoTaskDetailProcessRecordVO.getObjectKeysStr(), StrUtil.COMMA));
            }
        }
        return orderVideoTaskDetailProcessRecordVOS;
    }
}
