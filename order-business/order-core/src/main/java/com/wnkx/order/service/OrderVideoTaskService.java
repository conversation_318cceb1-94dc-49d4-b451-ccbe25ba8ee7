package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleFlowDTO;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.dto.order.task.ConfirmAfterOrderDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTask;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleListVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9 15:24
 */
public interface OrderVideoTaskService extends IService<OrderVideoTask> {

    /**
     * 创建任务单
     */
    void createTask(OrderTaskDTO orderTaskDTO);

    /**
     * 校验任务单是否已存在
     */
    Boolean checkTaskExist(OrderTaskDTO orderTaskDTO);

    /**
     * 获取任务单统计数据
     * @param taskType
     * @return
     */
    OrderTaskStatusVO getOrderTaskStatus(Integer taskType);

    /**
     * 工单列表
     */
    List<WorkOrderTaskListVO> selectWorkOrderListByCondition(WorkOrderTaskListDTO dto);

    /**
     * 查看工单详情
     */
    WorkOrderTaskInfoVO getWorkOrderInfo(String taskNum);

    /**
     * 申请补偿退款 OR 补发物流 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getRefundPendingTask(Long videoId, List<Long> taskDetailId);

    /**
     * 校验 申请补偿退款 OR 补发物流 售后单/工单 是否与视频订单关联 然后完结订单
     */
    void checkRefundPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId);

    /**
     * 校验 申请补偿退款 OR 补发物流 售后单/工单 是否与视频订单关联 然后要处理的任务单ID
     */
    void checkRefundPendingTaskThenRecord(Long videoId, List<Long> taskDetailId, OrderVideoRefund orderVideoRefund);

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getBackHelpModelUploadMaterialPendingTask(Long videoId, List<Long> taskDetailIds);

    /**
     * 校验 帮模特反馈素材 售后单/工单 是否与视频订单关联 然后完结订单
     */
    void checkBackHelpModelUploadMaterialPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId);

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getFeedbackMaterialPendingTask(Long videoId, List<Long> taskDetailId, List<Integer> afterSaleClass, Boolean isToBeEdited);

    /**
     * 校验 反馈素材给商家 售后单/工单 是否与视频订单关联 然后完结订单
     */
    void checkFeedbackMaterialPendingTaskThenFinishWorkOrder(Long videoId, List<Long> taskDetailId, List<Integer> afterSaleClass);

    /**
     * 完结工单
     */
    void finishWorkOrder(Long taskDetailId);

    /**
     * 获取售后单列表
     * @param dto
     * @return
     */
    List<AfterSaleListVO> queryAfterSaleList(AfterSaleOrderTaskListDTO dto);

    /**
     * 获取售后单详情
     *
     * @param orderNum
     * @return
     */
    AfterSaleTaskDetailInfoVO getAfterSaleTaskInfo(String orderNum);

    /**
     * 任务单流转
     * @param dto
     */
    void afterSaleFlow(AfterSaleFlowDTO dto);

    /**
     * 确认售后单
     * @param dto
     */
    void confirmAfterOrder(ConfirmAfterOrderDTO dto);

    /**
     * 完成售后单
     * @param taskDetailId
     */
    void finishAfterSale(Long taskDetailId);

    /**
     * 撤销售后
     * @param taskDetailId
     */
    void cancelApplicationAfterOrder(Long taskDetailId);

    /**
     * * 同意取消售后
     * @param taskDetailId
     */
    void agreeCancelAfterOrder(Long taskDetailId);

    /**
     * 拒绝取消
     * @param taskDetailId
     */
    void refuseCancelAfterOrder(Long taskDetailId);

    /**
     * 工单-指派处理人
     */
    void assignHandler(AssignHandlerDTO dto);

    /**
     * 工单-拒绝工单
     */
    void rejectWorkOrder(TaskDetailOperateDTO dto);

    /**
     * 工单-关闭工单
     */
    void closeWorkOrder(Long taskDetailId);

    /**
     * 工单-重新打开
     */
    void reopenWorkOrder(TaskDetailOperateDTO dto);

    /**
     * 工单-新增处理记录
     */
    void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto);

    /**
     * 工单-查询处理记录
     */
    List<OrderVideoTaskDetailProcessRecordVO> selectWorkOrderProcessRecordList(String taskNum);

    /**
     * 通过视频订单ID获取任务单
     */
    List<VideoTaskOrderVO> selectVideoTaskOrderVOListByVideoIds(List<Long> videoIds);

    List<ModelSelectVO> taskModelList(Integer type);

    List<UserVO> getAssigneeUserList(Integer type);

    List<UserVO> getSubmitUserList(Integer type);

    List<UserVO> getRelevanceList(Integer type);

    /**
     * 通过视频订单ID关闭任务单
     */
    void closeTaskByVideoIds(List<Long> videoIds, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum);

    /**
     * 获取订单任务统计
     * @param userId
     * @param assigneeId
     * @return
     */
    OrderTaskStatisticsVO getOrderTaskStatistics(Long userId, Long assigneeId);
}
