package com.wnkx.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.OrderVideoModelSearch;
import com.wnkx.order.mapper.OrderVideoModelSearchMapper;
import com.wnkx.order.service.IOrderVideoModelSearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单_视频_模特搜索记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-02
 */
@Service
@RequiredArgsConstructor
public class OrderVideoModelSearchServiceImpl extends ServiceImpl<OrderVideoModelSearchMapper, OrderVideoModelSearch> implements IOrderVideoModelSearchService {



    /**
     * 添加视频订单模特搜索记录
     *
     * @param keyword 搜索内容
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderVideoModelSearch(String keyword) {
        keyword = StrUtil.trim(keyword);
        OrderVideoModelSearch getOne = baseMapper.getOneByContent(keyword);

        if (ObjectUtil.isNotNull(getOne)) return;

        List<OrderVideoModelSearch> searches = baseMapper.selectOrderVideoModelSearchListByModelId(SecurityUtils.getUserId());

        OrderVideoModelSearch search = new OrderVideoModelSearch();
        search.setModelId(SecurityUtils.getUserId());
        search.setContent(keyword);
        searches.add(0, search);

        List<Long> deleteIds = new ArrayList<>();
        //  搜索内容超过设置的上线 将旧的内容删除（这里是逻辑删除）
        if (searches.size() > OrderConstant.MAX_SEARCH_COUNT) {
            for (int i = OrderConstant.MAX_SEARCH_COUNT; i < searches.size(); i++) {
                deleteIds.add(searches.get(i).getId());
            }
        }

        save(search);
        removeBatchByIds(deleteIds);
    }
}
