package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.order.fy.FyOrderTableDTO;
import com.ruoyi.system.api.domain.entity.order.FyOrderTable;
import com.ruoyi.system.api.domain.vo.order.FyOrderTableVO;
import com.wnkx.order.mapper.FyOrderTableMapper;
import com.wnkx.order.service.IFyOrderTableService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【fy_order_table(富友订单表)】的数据库操作Service实现
* @createDate 2024-08-14 14:01:11
*/
@Service
@RequiredArgsConstructor
public class FyOrderTableServiceImpl extends ServiceImpl<FyOrderTableMapper, FyOrderTable>
implements IFyOrderTableService {

    @Override
    public FyOrderTableVO getValidFyOrderTableVO(FyOrderTableDTO dto) {
        FyOrderTable validFyOrderTable = baseMapper.getValidFyOrderTable(dto);
        if (null == validFyOrderTable){
            return null;
        }
        return BeanUtil.copyProperties(validFyOrderTable, FyOrderTableVO.class);
    }

    @Override
    public List<FyOrderTableVO> getValidFyOrderTableVOList(String orderNum) {
        List<FyOrderTable> validFyOrderTableListByOrderNum = baseMapper.getValidFyOrderTableListByOrderNum(orderNum);
        if (CollUtil.isEmpty(validFyOrderTableListByOrderNum)){
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(validFyOrderTableListByOrderNum, FyOrderTableVO.class);
    }

    @Override
    public Boolean banFyOrderTable(Long id) {
       return this.updateById(FyOrderTable.builder().id(id).status(StatusTypeEnum.NO.getCode()).build());
    }

    @Override
    public Boolean banFyOrderTableByOrderNum(String orderNum) {
        return this.update(new LambdaUpdateWrapper<FyOrderTable>()
                .set(FyOrderTable::getStatus, StatusTypeEnum.NO.getCode())
                .eq(FyOrderTable::getOrderNumber, orderNum)
        );
    }
}
