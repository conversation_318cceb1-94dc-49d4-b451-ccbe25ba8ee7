package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.CheckDataScopeDTO;
import com.ruoyi.system.api.domain.dto.order.ModelSelectRecordListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderModelMyOrderListDTO;
import com.ruoyi.system.api.domain.dto.order.UploadLinkDTO;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.ruoyi.system.api.domain.vo.ModelEndLogisticVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.ModelPersonMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.OrderDataScopeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/7/4 17:10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelPersonServiceImpl implements ModelPersonService {

    private final ModelPersonMapper modelPersonMapper;
    private final RemoteService remoteService;
    /**
     * 模特端-首页（发现）服务
     */
    private final IOrderVideoService orderVideoService;
    /**
     * 订单_视频_物流关联表
     */
    private final IOrderVideoLogisticService orderVideoLogisticService;
    /**
     * 订单反馈表(模特)
     */
    private final OrderVideoFeedBackMaterialService orderVideoFeedBackMaterialService;
    /**
     * 视频内容服务
     */
    private final IOrderVideoContentService videoContentService;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderResourceService orderResourceService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;
    private final OrderVideoModelShippingAddressService orderVideoModelShippingAddressService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final ModelDiscoverService orderModelDiscoverService;
    private final RedisService redisService;
    private final OrderDataScopeService orderDataScopeService;

    /**
     * 模特撤销申请
     */
    @Override
    public void cancelApply(Long videoId) {
        OrderVideoMatch orderVideoMatch = orderVideoMatchService.getActiveByVideoId(videoId);
        Assert.notNull(orderVideoMatch, "Order does not exist, please try again later!");
        Assert.isTrue(orderVideoMatch.getStatus().equals(OrderVideoMatchStatusEnum.NORMAL.getCode()), "The order has been matched, please try again later!");
        Assert.isNull(orderVideoMatch.getEndTime(), "The order has been matched, please try again later!");

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId()), "The system is busy. Please try again later");

            OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = orderVideoMatchPreselectModelService.getActiveByMatchIdAndModelId(orderVideoMatch.getId(), SecurityUtils.getUserId());
            Assert.notNull(orderVideoMatchPreselectModel, "Please refresh the page and try again");
            Assert.isTrue(PreselectModelAddTypeEnum.MODEL_OPTIONAL.getCode().equals(orderVideoMatchPreselectModel.getAddType()), "Please refresh the page and try again");
            Assert.isTrue(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "Order confirmed, please refresh the page");

            OrderVideoMatchPreselectModel selectedModelByMatchId = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(orderVideoMatch.getId());
            Assert.isNull(selectedModelByMatchId, "Cancel failed, please contact customer service");

            orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode());
            orderVideoMatchPreselectModel.setSelectTime(DateUtil.date());
            orderVideoMatchPreselectModelService.updateById(orderVideoMatchPreselectModel);

            redisService.decrementCounter(CacheConstants.MODEL_APPLY_KEY + SecurityUtils.getUserId(), false);
        }finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatch.getId());
        }
    }

    /**
     * 模特选择记录
     */
    @Override
    public List<OrderVideoModelSelectListVO> getModelSelectRecord() {
        ModelSelectRecordListDTO dto = assembleModelSelectRecordCondition();
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovmpm.select_time" , OrderByDto.DIRECTION.DESC);
        orderByDto.setField("ovmpm.id" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderVideoModelSelectListVO> list = modelPersonMapper.getModelSelectRecord(dto);
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        for (OrderVideoModelSelectListVO vo : list) {
            if (PreselectModelAddTypeEnum.INTENTION_MODEL.getCode().equals(vo.getAddType()) || PreselectModelAddTypeEnum.OPERATION.getCode().equals(vo.getAddType())) {
                vo.setSelectTimeout(DateUtil.offsetHour(vo.getAddTime(), orderVideoProperties.getPreselectModelOverTime()));
            }
            vo.setSurplusPicCount(Math.max((PicCountEnum.getValue(vo.getPicCount()) - vo.getRefundPicCount()), 0));
        }

        list.sort(Comparator.comparing(OrderVideoModelSelectListVO::getSelectTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed());

        return list;
    }

    /**
     * 售后订单详情
     */
    @Override
    public OrderModelAfterSalesInfoVO afterSalesDetailInfo(Long id) {
        OrderVideoFeedBackMaterial material = orderVideoFeedBackMaterialService.getById(id);
        if (ObjectUtil.isNull(material)) {
            return null;
        }

        OrderVideo orderVideo = orderVideoService.getById(material.getVideoId());
        Assert.isFalse(checkModelDataScope(List.of(orderVideo.getId()), SecurityUtils.getUserId()), ErrorConstants.ERROR_TIPS_ENGLISH);

        if (ObjectUtil.isNull(orderVideo)) {
            return null;
        }

        OrderModelAfterSalesInfoVO infoVO = new OrderModelAfterSalesInfoVO();
        infoVO.setMaterialId(id);
        BeanUtil.copyProperties(material, infoVO);
        BeanUtil.copyProperties(orderVideo, infoVO);
        orderModelDiscoverService.assembleBaseInfo(infoVO);

        return infoVO;
    }

    /**
     * 售后订单
     */
    @Override
    public List<OrderModelWorkbenchAfterSalesListVO> afterSales(OrderModelMyOrderListDTO dto) {
        Assert.isTrue(dto.getStatus().equals(18), "订单状态错误！");

        assembleCondition(dto);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ofbm.update_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<OrderModelWorkbenchAfterSalesListVO> orderModelWorkbenchAfterSalesListVOS = modelPersonMapper.afterSales(dto);
        for (OrderModelWorkbenchAfterSalesListVO orderModelWorkbenchAfterSalesListVO : orderModelWorkbenchAfterSalesListVOS) {
            orderModelWorkbenchAfterSalesListVO.setSurplusPicCount(Math.max((PicCountEnum.getValue(orderModelWorkbenchAfterSalesListVO.getPicCount()) - orderModelWorkbenchAfterSalesListVO.getRefundPicCount()), 0));
        }
        return orderModelWorkbenchAfterSalesListVOS;
    }

    /**
     * 上传链接记录
     */
    @Override
    public List<OrderFeedBackMaterialVO> getUploadLinkRecord(Long videoId) {
        return orderVideoFeedBackMaterialService.modelFeedBackList(videoId);
    }

    @Override
    public List<OrderFeedBackMaterialVO> getUploadLinkRecordByVideoCode(String videoCode) {
        OrderVideo orderVideo = orderVideoService.selectOneByVideoCode(videoCode);
        if (ObjectUtil.isNull(orderVideo)) {
            return Collections.emptyList();
        }
        orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder()
                .videoIds(Arrays.asList(orderVideo.getId()))
                .build());
        return orderVideoFeedBackMaterialService.modelFeedBackList(orderVideo.getId());
    }

    /**
     * 上传链接记录(售后)
     */
    @Override
    public OrderFeedBackMaterialVO getUploadLinkRecordAfterSale(Long materialId) {
        return orderVideoFeedBackMaterialService.getModelFeedBack(materialId);
    }

    /**
     * 上传链接
     */
    @Override
    public void uploadLink(UploadLinkDTO dto) {
        OrderVideo orderVideo = orderVideoService.getById(dto.getVideoId());
        Assert.notNull(orderVideo, "Video order does not exist!");// 视频订单不存在
        Assert.isTrue(OrderStatusEnum.UN_FINISHED.getCode().equals(orderVideo.getStatus()), "The current order does not allow uploading links!");// 当前订单不允许上传链接
        Assert.notNull(orderVideo.getShootModelId(), "No model has been selected for the current order and cannot be operated!");//  当前订单没有选定模特，无法操作
        Assert.isTrue(SecurityUtils.getUserId().equals(orderVideo.getShootModelId()), "You are not authorized to do this!");// 您无权进行该操作

        orderVideoFeedBackMaterialService.uploadLink(dto);
    }

    /**
     * 订单详情（非售后状态）
     */
    @Override
    public OrderModelNormalInfoVO getOrderModelNormalInfo(Long videoId) {
        OrderVideo orderVideo = orderVideoService.getById(videoId);
        if (ObjectUtil.isNull(orderVideo)) {
            return null;
        }

        OrderModelNormalInfoVO detailInfoVO = BeanUtil.copyProperties(orderVideo, OrderModelNormalInfoVO.class);

        assembleOrderModelNormalInfoVO(detailInfoVO);

        return detailInfoVO;
    }

    @Override
    public OrderModelNormalInfoVO getOrderModelNormalInfoByVideoCode(String videoCode) {
        OrderVideo orderVideo = orderVideoService.selectOneByVideoCode(videoCode);
        if (ObjectUtil.isNull(orderVideo)) {
            return null;
        }
        orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder()
                .videoIds(Arrays.asList(orderVideo.getId()))
                .build());
        OrderModelNormalInfoVO detailInfoVO = BeanUtil.copyProperties(orderVideo, OrderModelNormalInfoVO.class);

        assembleOrderModelNormalInfoVO(detailInfoVO);

        return detailInfoVO;
    }

    /**
     * 组装 订单详情（非售后状态） 数据
     */
    private void assembleOrderModelNormalInfoVO(OrderModelNormalInfoVO info) {
        orderModelDiscoverService.assembleBaseInfo(info);

        boolean exists = orderVideoFeedBackMaterialService.lambdaQuery()
                .eq(OrderVideoFeedBackMaterial::getVideoId, info.getId())
                .ne(OrderVideoFeedBackMaterial::getStatus, MaterialStatusEnum.UN_CONFIRM.getCode())
                .exists();
        info.setInAfterSale(exists ? 1 : 0);

        //  获取模特收件信息
        OrderVideoModelShippingAddressVO lastOrderVideoModelShippingAddressByVideoId = orderVideoModelShippingAddressService.getLastOrderVideoModelShippingAddressByVideoId(info.getId(), info.getRollbackId());
        if (ObjectUtil.isNotNull(lastOrderVideoModelShippingAddressByVideoId)) {
            info.setRecipient(lastOrderVideoModelShippingAddressByVideoId.getRecipient());
            info.setCity(lastOrderVideoModelShippingAddressByVideoId.getCity());
            info.setState(lastOrderVideoModelShippingAddressByVideoId.getState());
            info.setZipcode(lastOrderVideoModelShippingAddressByVideoId.getZipcode());
            info.setDetailAddress(lastOrderVideoModelShippingAddressByVideoId.getDetailAddress());

            //  获取订单关联物流
            List<OrderVideoLogistic> orderVideoLogistics = orderVideoLogisticService.selectListByShippingAddressId(lastOrderVideoModelShippingAddressByVideoId.getId());
            if (CollUtil.isNotEmpty(orderVideoLogistics)) {
                List<String> numbers = orderVideoLogistics.stream().map(OrderVideoLogistic::getNumber).collect(Collectors.toList());
                Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numbers);
                for (OrderVideoLogistic videoLogistic : orderVideoLogistics) {
                    if (ObjectUtil.isNotNull(logisticMap.get(videoLogistic.getNumber()))) {
                        info.getLogistics().add(BeanUtil.copyProperties(logisticMap.get(videoLogistic.getNumber()), ModelEndLogisticVO.class));
                    }
                }
            }
        }

        info.setShootAttention(orderVideoMatchService.getPreselectModelShootAttentionByVideoId(info.getId()));
    }

    /**
     * 我的订单徽标
     */
    @Override
    public OrderModelMyOrderBadgeVO myOrderBadge() {
        OrderModelMyOrderListDTO dto = new OrderModelMyOrderListDTO();
        assembleCondition(dto);

        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();
        return modelPersonMapper.myOrderBadge(dto, codeMap, StatusEnum.UN_ENABLED.getCode());
    }

    /**
     * 我的订单
     */
    @Override
    public List<OrderModelMyOrderListVO> myOrder(OrderModelMyOrderListDTO dto) {
        List<Integer> orderVideoCheckStatus = new ArrayList<>();
        orderVideoCheckStatus.add(OrderStatusEnum.UN_FINISHED.getCode());
        orderVideoCheckStatus.add(OrderStatusEnum.FINISHED.getCode());
        orderVideoCheckStatus.add(OrderStatusEnum.TRADE_CLOSE.getCode());
        Assert.isTrue(orderVideoCheckStatus.contains(dto.getStatus()) || ObjectUtil.isNull(dto.getStatus()), "订单状态错误！");

        assembleCondition(dto);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ov.status_time", OrderByDto.DIRECTION.ASC);
        PageUtils.startPage(orderByDto);
        List<OrderModelMyOrderListVO> list = modelPersonMapper.myOrder(dto);

        assembleMyOrderList(list);

        return list;
    }

    /**
     * 组装我的订单列表回显数据
     */
    private void assembleMyOrderList(List<OrderModelMyOrderListVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        //  查询物流信息
        Set<String> numbers = list.stream()
                .filter(vo -> OrderStatusEnum.UN_FINISHED.getCode().equals(vo.getStatus())
                        || OrderStatusEnum.FINISHED.getCode().equals(vo.getStatus()))
                .map(OrderModelMyOrderListVO::getNumber)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Map<String, LogisticVO> logisticMap = remoteService.getLogisticMap(numbers);

        for (OrderModelMyOrderListVO listVO : list) {
            LogisticVO logisticVO = logisticMap.getOrDefault(listVO.getNumber(), new LogisticVO());
            listVO.setSignTime(logisticVO.getSignTime());
            listVO.setMainStatus(CollUtil.isEmpty(logisticVO.getLogisticInfo()) ? null : logisticVO.getLogisticInfo().get(0).getMainStatus());
            if (CharSequenceUtil.equals(LogisticMainStatus.IN_TRANSIT.getLabel(), listVO.getMainStatus())) {
                listVO.setMainStatus("In Transit");
            }

            listVO.setSurplusPicCount(Math.max((PicCountEnum.getValue(listVO.getPicCount()) - listVO.getRefundPicCount()), 0));
        }
    }

    /**
     * 组装查询我的订单条件
     */
    private void assembleCondition(OrderModelMyOrderListDTO dto) {
        dto.init();
        dto.setCurModelId(SecurityUtils.getUserId());
    }

    /**
     * 组装模特选择记录搜索条件
     * @return  搜索条件
     */
    private ModelSelectRecordListDTO assembleModelSelectRecordCondition() {
        ModelSelectRecordListDTO dto = new ModelSelectRecordListDTO();
        dto.setCurModelId(SecurityUtils.getUserId());
        dto.setOverTime(orderVideoProperties.getPreselectModelOverTime());

        return dto;
    }

    /**
     * 校验模特数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    @Override
    public Boolean checkModelDataScope(List<Long> videoIds, Long userid) {
        return modelPersonMapper.checkModelDataScope(videoIds, userid) > 0;
    }
}
