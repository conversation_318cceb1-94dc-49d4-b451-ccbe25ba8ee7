package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.enums.OrderVideoOperateTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderVideoOperateDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateCartToOrderVideoOperateDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoOperate;
import com.ruoyi.system.api.domain.vo.LoginUserInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoOperateVO;
import com.wnkx.order.mapper.OrderVideoOperateMapper;
import com.wnkx.order.service.OrderVideoOperateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/14 9:35
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoOperateServiceImpl extends ServiceImpl<OrderVideoOperateMapper, OrderVideoOperate> implements OrderVideoOperateService {


    /**
     * 查询视频订单操作记录
     */
    @Override
    public List<OrderVideoOperateVO> selectOrderVideoOperateListByVideoId(Long videoId) {
        List<OrderVideoOperate> list = baseMapper.selectOrderVideoOperateListByVideoId(videoId);
        return BeanUtil.copyToList(list, OrderVideoOperateVO.class);
    }

    /**
     * 将[订单退款]操作记录开放给商家可看
     */
    @Override
    public void updateOrderVideoOperateToPublic(List<Long> videoIds) {
        List<OrderVideoOperate> orderVideoOperates = baseMapper.selectOrderVideoOperateListByVideoIds(videoIds);
        Map<Long, List<OrderVideoOperate>> orderVideoOperateMap = orderVideoOperates.stream()
                .collect(Collectors.groupingBy(
                        OrderVideoOperate::getVideoId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(OrderVideoOperate::getEventExecuteTime).reversed())
                                        .collect(Collectors.toList())
                        )
                ));
        List<OrderVideoOperate> updateOrderVideoOperates = new ArrayList<>();

        for (Map.Entry<Long, List<OrderVideoOperate>> entry : orderVideoOperateMap.entrySet()) {
            for (OrderVideoOperate orderVideoOperate : entry.getValue()) {
                if (OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION.getEventName().equals(orderVideoOperate.getEventName()) ||
                        OrderVideoOperateTypeEnum.ORDER_REFUND_CANCEL_ORDER.getEventName().equals(orderVideoOperate.getEventName()) ||
                        OrderVideoOperateTypeEnum.ORDER_REFUND_CANCEL_OPTION.getEventName().equals(orderVideoOperate.getEventName())||
                        OrderVideoOperateTypeEnum.ORDER_REFUND_REPARATION_AND_CANCEL.getEventName().equals(orderVideoOperate.getEventName())
                ) {
                    orderVideoOperate.setIsPublic(0);
                    updateOrderVideoOperates.add(orderVideoOperate);
                    break;
                }
            }
        }

        baseMapper.updateBatchById(updateOrderVideoOperates);
    }

    /**
     * 将购物车操作记录更新为视频订单操作记录
     */
    @Override
    public void updateCartToOrderVideoOperate(List<UpdateCartToOrderVideoOperateDTO> updateCartToOrderVideoOperateDTOS) {
        for (UpdateCartToOrderVideoOperateDTO updateCartToOrderVideoOperateDTO : updateCartToOrderVideoOperateDTOS) {
            baseMapper.updateCartToOrderVideoOperate(updateCartToOrderVideoOperateDTO);
        }
    }

    /**
     * 新增视频订单操作记录
     */
    @Override
    public void createOrderVideoOperate(String eventName, Integer isCart, Integer isPublic, String eventExecuteUserName, List<OrderVideoOperateDTO> orderVideoOperateDTOS) {
        LoginUserInfoVO loginUserInfoVo = SecurityUtils.getLoginUserInfoVo();

        DateTime date = DateUtil.date();
        List<OrderVideoOperate> orderVideoOperates = orderVideoOperateDTOS.stream().map(item -> {
            OrderVideoOperate orderVideoOperate = new OrderVideoOperate();
            orderVideoOperate.setVideoId(item.getVideoId());
            orderVideoOperate.setEventName(eventName);
            orderVideoOperate.setEventExecuteObject(loginUserInfoVo.getUserType());
            orderVideoOperate.setEventExecuteUserId(loginUserInfoVo.getUserId());
            orderVideoOperate.setEventExecuteUserName(CharSequenceUtil.isNotBlank(eventExecuteUserName) ? eventExecuteUserName : loginUserInfoVo.getName());
            orderVideoOperate.setEventExecuteNickName(loginUserInfoVo.getNickName());
            orderVideoOperate.setEventExecutePhone(loginUserInfoVo.getPhone());
            if (OrderVideoOperateTypeEnum.ORDER_COMPLETION.getEventName().equals(eventName)) {
                orderVideoOperate.setEventExecuteTime(DateUtil.offsetSecond(date, 5));
            } else {
                orderVideoOperate.setEventExecuteTime(ObjectUtil.isNotNull(item.getEventExecuteTime()) ? item.getEventExecuteTime() : date);
            }

            orderVideoOperate.setEventContent(StrUtil.replace(item.getEventContent(), OrderConstant.ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT, CharSequenceUtil.isNotBlank(eventExecuteUserName) ? eventExecuteUserName : StrUtil.isNotBlank(loginUserInfoVo.getName()) ? loginUserInfoVo.getName() : loginUserInfoVo.getNickName()));
            orderVideoOperate.setIsCart(isCart);
            orderVideoOperate.setIsPublic(isPublic);
            return orderVideoOperate;
        }).collect(Collectors.toList());

        baseMapper.saveBatch(orderVideoOperates);
    }

    /**
     * 新增视频订单操作记录
     */
    @Override
    public void createOrderVideoOperate(String eventName, Integer isCart, Integer isPublic, String eventExecuteUserName, OrderVideoOperateDTO orderVideoFlowDTO) {
        createOrderVideoOperate(eventName, isCart, isPublic, eventExecuteUserName, List.of(orderVideoFlowDTO));
    }
}
