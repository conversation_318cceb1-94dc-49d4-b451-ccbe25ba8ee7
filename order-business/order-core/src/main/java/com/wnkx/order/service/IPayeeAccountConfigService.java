package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.PayeeAccountConfig;

/**
* <AUTHOR>
* @description 针对表【payee_account_config(收款人账号配置表)】的数据库操作Service
* @createDate 2024-09-13 11:08:38
*/
public interface IPayeeAccountConfigService extends IService<PayeeAccountConfig> {

    /***
     * * 根据账号类型获取收款人账号配置表
     * @param type
     * @return
     */
    PayeeAccountConfig getValidConfigByAccountType(Integer type);
}
