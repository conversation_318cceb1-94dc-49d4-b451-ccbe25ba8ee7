package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRollbackRecordChange;
import com.wnkx.order.mapper.OrderVideoRollbackRecordChangeMapper;
import com.wnkx.order.service.OrderVideoRollbackRecordChangeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/12/25 17:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoRollbackRecordChangeServiceImpl extends ServiceImpl<OrderVideoRollbackRecordChangeMapper, OrderVideoRollbackRecordChange> implements OrderVideoRollbackRecordChangeService {


    /**
     * 新增回退记录变更数据
     */
    @Override
    public void insertOrderVideoRollbackRecordChange(OrderVideoRollbackRecordChange orderVideoRollbackRecordChange) {
        baseMapper.insert(orderVideoRollbackRecordChange);
    }
}
