package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.OrderMemberStatusEnum;
import com.ruoyi.system.api.domain.dto.order.CreateOrderMemberDTO;
import com.ruoyi.system.api.domain.dto.order.FlowOrderMemberDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.vo.order.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_member(订单_会员表)】的数据库操作Service
 * @createDate 2024-06-24 16:00:58
 */
@Validated
public interface IOrderMemberService extends IService<OrderMember> {

    /**
     * 创建会员订单
     *
     * @param dto
     * @return
     */
    OrderMemberVO createOrder(@Valid CreateOrderMemberDTO dto);


    /**
     * 根据订单号获取会员订单(得到的id为Order的id)
     *
     * @param orderNum
     * @return
     */
    OrderMemberVO getOrderMember(String orderNum);


    /**
     * 检查会员订单状态
     * @param vo
     * @param statusEnum
     */
    void checkMemberStatus(OrderMemberVO vo, OrderMemberStatusEnum... statusEnum);

    /**
     * 根据订单号获取会员订单详情
     * @param orderNum
     * @return
     */
    OrderMemberDetailVO getOrderMemberDetail(String orderNum);

    /**
     * 获取会员订单列表
     *
     * @param dto
     * @return
     */
    List<OrderMemberVO> getOrderMemberList(@Valid OrderMemberListDTO dto);

    /**
     * 获取工作台-财务部-会员待审批列表
     * @return
     */
    List<OrderMemberVO> workbenchFinanceMemberList();

    /**
     * 获取会员应收审批导出
     * @return
     */
    List<MemberReceivableAuditListExportVO> getOrderMemberExportList(List<OrderMemberVO> list);


    /**
     * 获取会员订单列表（运营端）
     * * @param dto
     * @return
     */
    List<OrderMemberVO> getBackendOrderMemberList(@Valid OrderMemberListDTO dto);

    /**
     * 会员订单导出
     * @param list
     * @return
     */
    List<OrderMemberExportVO> getBackendOrderMemberListExport(List<OrderMemberVO> list);


    /**
     * 会员订单状态流转
     *
     * @param dto
     */
    void flowOrderMember(@Valid FlowOrderMemberDTO dto);

    /**
     * 查询订单号
     *
     * @param dto
     * @return
     */
    OrderMember queryOne(OrderMemberDTO dto);

    /**
     * 修改会员表
     *
     * @param dto
     */
    void update(OrderMemberDTO dto);

    /**
     * 根据订单号列表获取会员订单
     * @param orderNums
     * @return
     */
    List<OrderMember> getByOrderNums(List<String> orderNums);

    /**
     * 检查是否是首次购买
     *
     * @return true:首次购买 false:非首次购买
     */
    Boolean checkFirstBuy(Long bizUserId, String orderNum);

    /**
     * 获取未支付会员订单数据
     * @return
     */
    MemberUnPayVO getMemberUnPay();

    /**
     * 通过订单号获取会员订单
     */
    OrderMember getByOrderNum(String orderNum);

    /**
     * 可查询参数：
     *      bizUserId:  登录账号id
     *      status：    订单状态
     * @param dto
     * @return
     */
    List<OrderMember> queryList(OrderMemberDTO dto);

    /**
     * 初始化会员购买标志
     */
    void initUserOrderMemberFlag();

    BigDecimal getBackendOrderMemberListByPayTime(OrderMemberListDTO dto);

    /**
     * 获取会员有效订单
     */
    List<OrderMember> getValidOrderMemberList(Long bizUserId);
}
