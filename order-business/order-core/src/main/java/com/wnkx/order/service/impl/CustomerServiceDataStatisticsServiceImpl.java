package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.enums.CommissionUnitEnum;
import com.ruoyi.common.core.enums.ModelTypeEnum;
import com.ruoyi.common.core.utils.ArithmeticUtil;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsDay;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsMonth;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.CustomerServiceBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.wnkx.order.mapper.CustomerServiceDataStatisticsMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.CustomerServiceDataStatisticsDayService;
import com.wnkx.order.service.CustomerServiceDataStatisticsMonthService;
import com.wnkx.order.service.CustomerServiceDataStatisticsService;
import com.wnkx.order.service.OrderVideoMatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/19 9:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerServiceDataStatisticsServiceImpl implements CustomerServiceDataStatisticsService {

    private final OrderVideoMatchService orderVideoMatchService;
    private final RemoteService remoteService;
    private final CustomerServiceDataStatisticsMapper baseMapper;
    private final CustomerServiceDataStatisticsDayService customerServiceDataStatisticsDayService;
    private final CustomerServiceDataStatisticsMonthService customerServiceDataStatisticsMonthService;

    /**
     * 客服数据-英文部客服数据
     */
    @Override
    public List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData(Date beginTime, Date endTime) {
        List<EnglishCustomerServiceDataVO> englishCustomerServiceDataVOS = remoteService.selectEnglishCustomerServiceData();
        if (CollUtil.isEmpty(englishCustomerServiceDataVOS)) {
            return englishCustomerServiceDataVOS;
        }

        List<EnglishCustomerServiceDataVO> dataList = baseMapper.selectEnglishCustomerServiceData(beginTime, endTime);
        Map<Long, EnglishCustomerServiceDataVO> dataMap = dataList.stream().collect(Collectors.toMap(EnglishCustomerServiceDataVO::getCustomerServiceId, Function.identity()));

        List<EnglishCustomerServiceDataVO> matchDataList = baseMapper.selectEnglishCustomerServiceMatchData(beginTime, endTime);
        Map<Long, EnglishCustomerServiceDataVO> matchDataMap = matchDataList.stream().collect(Collectors.toMap(EnglishCustomerServiceDataVO::getCustomerServiceId, Function.identity()));

        CustomerServiceDataStatisticsDay customerServiceDataStatisticsDay = customerServiceDataStatisticsDayService.getByWriteTime(DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN));
        Map<Long, CustomerServiceAddedCompleteCountInfo> orderCountMap = new HashMap<>();
        if (ObjectUtil.isNotNull(customerServiceDataStatisticsDay)) {
            List<CustomerServiceAddedCompleteCountInfo> orderCounts = JSONUtil.toList(customerServiceDataStatisticsDay.getEnglishCustomerServiceAddedCompleteOrderCountJson(), CustomerServiceAddedCompleteCountInfo.class);
            orderCountMap = orderCounts.stream().collect(Collectors.toMap(CustomerServiceAddedCompleteCountInfo::getCustomerServiceId, Function.identity()));
        }

        CustomerServiceDataStatisticsMonth customerServiceDataStatisticsMonth = customerServiceDataStatisticsMonthService.getByWriteTimeMonth(DateUtil.format(DateUtil.lastMonth(), DatePattern.NORM_MONTH_PATTERN));
        Map<Long, CustomerServiceAddedOustModelCountInfo> modelCountMap = new HashMap<>();
        if (ObjectUtil.isNotNull(customerServiceDataStatisticsMonth)) {
            List<CustomerServiceAddedOustModelCountInfo> modelCounts = JSONUtil.toList(customerServiceDataStatisticsMonth.getEnglishCustomerServiceAddedOustModelCountJson(), CustomerServiceAddedOustModelCountInfo.class);
            modelCountMap = modelCounts.stream().collect(Collectors.toMap(CustomerServiceAddedOustModelCountInfo::getCustomerServiceId, Function.identity()));
        }

        List<OrderVideoMatch> orderVideoMatches = orderVideoMatchService.selectListBySubmitTime(DateUtil.format(beginTime, DatePattern.NORM_DATE_PATTERN), DateUtil.format(endTime, DatePattern.NORM_DATE_PATTERN));
        for (OrderVideoMatch orderVideoMatch : orderVideoMatches) {
            orderVideoMatch.setCommission(orderVideoMatch.getCommission().multiply(CommissionUnitEnum.getByUnit(orderVideoMatch.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));
        }
        Map<Long, List<OrderVideoMatch>> orderVideoMatcheMap = orderVideoMatches.stream().collect(Collectors.groupingBy(OrderVideoMatch::getShootModelPersonId));

        for (EnglishCustomerServiceDataVO englishCustomerServiceDataVO : englishCustomerServiceDataVOS) {
            EnglishCustomerServiceDataVO data = dataMap.getOrDefault(englishCustomerServiceDataVO.getCustomerServiceId(), new EnglishCustomerServiceDataVO());
            englishCustomerServiceDataVO.setOrderCount(data.getOrderCount());
            englishCustomerServiceDataVO.setOrderTotalCount(data.getOrderTotalCount());
            englishCustomerServiceDataVO.setDragOrderCount(data.getDragOrderCount());
            englishCustomerServiceDataVO.setDragOrderRate(data.getDragOrderRate());
            englishCustomerServiceDataVO.setAfterSaleCount(data.getAfterSaleCount());
            englishCustomerServiceDataVO.setAfterSaleRate(data.getAfterSaleRate());
            englishCustomerServiceDataVO.setRightDragOrderCount(data.getRightDragOrderCount());
            englishCustomerServiceDataVO.setRightAfterSaleCount(data.getRightAfterSaleCount());

            EnglishCustomerServiceDataVO matchData = matchDataMap.getOrDefault(englishCustomerServiceDataVO.getCustomerServiceId(), new EnglishCustomerServiceDataVO());
            englishCustomerServiceDataVO.setOrderScheduledCount(matchData.getOrderScheduledCount());
            englishCustomerServiceDataVO.setOrderScheduledSoleCount(matchData.getOrderScheduledSoleCount());
            englishCustomerServiceDataVO.setOrderScheduledInfluencerCount(matchData.getOrderScheduledInfluencerCount());
            englishCustomerServiceDataVO.setRejectOrderCount(matchData.getRejectOrderCount());
            englishCustomerServiceDataVO.setProportionOfQualityModel(matchData.getProportionOfQualityModel());
            // englishCustomerServiceDataVO.setProportionOfMidModel(data.getProportionOfMidModel());
            englishCustomerServiceDataVO.setProportionOfGeneralModel(matchData.getProportionOfGeneralModel());

            List<OrderVideoMatch> orderVideoMatch = orderVideoMatcheMap.get(englishCustomerServiceDataVO.getCustomerServiceId());
            if (CollUtil.isNotEmpty(orderVideoMatch)) {
                englishCustomerServiceDataVO.setAverageCommission(ArithmeticUtil.safeDivide(orderVideoMatch.stream().map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), englishCustomerServiceDataVO.getOrderScheduledCount(), 2, RoundingMode.DOWN));
                englishCustomerServiceDataVO.setAverageCommissionSole(ArithmeticUtil.safeDivide(orderVideoMatch.stream().filter(item -> ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(item.getShootModelType())).map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), englishCustomerServiceDataVO.getOrderScheduledSoleCount(), 2, RoundingMode.DOWN));
                englishCustomerServiceDataVO.setAverageCommissionInfluencer(ArithmeticUtil.safeDivide(orderVideoMatch.stream().filter(item -> ModelTypeEnum.INFLUENT.getCode().equals(item.getShootModelType())).map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), englishCustomerServiceDataVO.getOrderScheduledInfluencerCount(), 2, RoundingMode.DOWN));
            }


            CustomerServiceAddedOustModelCountInfo modelCountData = modelCountMap.getOrDefault(englishCustomerServiceDataVO.getCustomerServiceId(), new CustomerServiceAddedOustModelCountInfo());
            englishCustomerServiceDataVO.setModelNewCount(modelCountData.getAddedCount());
            englishCustomerServiceDataVO.setModelOustCount(modelCountData.getOustCount());

            CustomerServiceAddedCompleteCountInfo orderCountData = orderCountMap.getOrDefault(englishCustomerServiceDataVO.getCustomerServiceId(), new CustomerServiceAddedCompleteCountInfo());
            englishCustomerServiceDataVO.setOrderNewCount(orderCountData.getAddedCount());
            englishCustomerServiceDataVO.setOrderFinishCount(orderCountData.getFinishCount());

            englishCustomerServiceDataVO.echo();
        }
        englishCustomerServiceDataVOS.sort(Comparator.comparingLong(EnglishCustomerServiceDataVO::getOrderCount).reversed());
        return englishCustomerServiceDataVOS;
    }

    /**
     * 客服数据-中文部客服数据
     */
    @Override
    public List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData(Date beginTime, Date endTime) {
        List<ChineseCustomerServiceDataVO> chineseCustomerServiceDataVOS = remoteService.selectChineseCustomerServiceData();
        if (CollUtil.isEmpty(chineseCustomerServiceDataVOS)) {
            return chineseCustomerServiceDataVOS;
        }

        List<ChineseCustomerServiceDataVO> dataList = baseMapper.selectChineseCustomerServiceData(beginTime, endTime);
        Map<Long, ChineseCustomerServiceDataVO> dataMap = dataList.stream().collect(Collectors.toMap(ChineseCustomerServiceDataVO::getCustomerServiceId, Function.identity()));

        //  获取客服关联任务单数量
        List<ChineseCustomerServiceDataVO> taskDataList = baseMapper.selectChineseCustomerServiceTaskData(beginTime, endTime);
        Map<Long, ChineseCustomerServiceDataVO> taskDataMap = taskDataList.stream().collect(Collectors.toMap(ChineseCustomerServiceDataVO::getCustomerServiceId, Function.identity()));

        CustomerServiceDataStatisticsDay customerServiceDataStatisticsDay = customerServiceDataStatisticsDayService.getByWriteTime(DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN));
        Map<Long, CustomerServiceAddedCompleteCountInfo> orderCountMap = new HashMap<>();
        Map<Long, CustomerServiceAddedCompleteCountInfo> taskCountMap = new HashMap<>();
        if (ObjectUtil.isNotNull(customerServiceDataStatisticsDay)) {
            List<CustomerServiceAddedCompleteCountInfo> orderCounts = JSONUtil.toList(customerServiceDataStatisticsDay.getChineseCustomerServiceAddedCompleteOrderCountJson(), CustomerServiceAddedCompleteCountInfo.class);
            List<CustomerServiceAddedCompleteCountInfo> taskCounts = JSONUtil.toList(customerServiceDataStatisticsDay.getChineseCustomerServiceAddedCompleteTaskCountJson(), CustomerServiceAddedCompleteCountInfo.class);
            orderCountMap = orderCounts.stream().collect(Collectors.toMap(CustomerServiceAddedCompleteCountInfo::getCustomerServiceId, Function.identity()));
            taskCountMap = taskCounts.stream().collect(Collectors.toMap(CustomerServiceAddedCompleteCountInfo::getCustomerServiceId, Function.identity()));
        }

        for (ChineseCustomerServiceDataVO chineseCustomerServiceDataVO : chineseCustomerServiceDataVOS) {
            ChineseCustomerServiceDataVO data = dataMap.getOrDefault(chineseCustomerServiceDataVO.getCustomerServiceId(), new ChineseCustomerServiceDataVO());
            chineseCustomerServiceDataVO.setOrderCount(data.getOrderCount());
            chineseCustomerServiceDataVO.setOrderTotalCount(data.getOrderTotalCount());
            chineseCustomerServiceDataVO.setAfterSaleCount(data.getAfterSaleCount());
            chineseCustomerServiceDataVO.setAfterSaleRate(data.getAfterSaleRate());
            chineseCustomerServiceDataVO.setRightAfterSaleCount(data.getRightAfterSaleCount());

            ChineseCustomerServiceDataVO taskData = taskDataMap.getOrDefault(chineseCustomerServiceDataVO.getCustomerServiceId(), new ChineseCustomerServiceDataVO());
            chineseCustomerServiceDataVO.setTaskCount(taskData.getTaskCount());
            chineseCustomerServiceDataVO.setTaskNewCountInTime(taskData.getTaskNewCountInTime());
            chineseCustomerServiceDataVO.setTaskFinishCountInTime(taskData.getTaskFinishCountInTime());

            CustomerServiceAddedCompleteCountInfo orderCountData = orderCountMap.getOrDefault(chineseCustomerServiceDataVO.getCustomerServiceId(), new CustomerServiceAddedCompleteCountInfo());
            chineseCustomerServiceDataVO.setOrderNewCount(orderCountData.getAddedCount());
            chineseCustomerServiceDataVO.setOrderFinishCount(orderCountData.getFinishCount());

            CustomerServiceAddedCompleteCountInfo taskCountData = taskCountMap.getOrDefault(chineseCustomerServiceDataVO.getCustomerServiceId(), new CustomerServiceAddedCompleteCountInfo());
            chineseCustomerServiceDataVO.setTaskNewCount(taskCountData.getAddedCount());
            chineseCustomerServiceDataVO.setTaskFinishCount(taskCountData.getFinishCount());

            chineseCustomerServiceDataVO.echo();
        }
        chineseCustomerServiceDataVOS.sort(Comparator.comparingLong(ChineseCustomerServiceDataVO::getOrderCount).reversed());
        return chineseCustomerServiceDataVOS;
    }

    /**
     * 客服数据-基础看板
     */
    @Override
    public CustomerServiceBaseBoardVO getCustomerServiceBaseBoard() {
        String date = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN);

        CustomerServiceBaseBoardVO customerServiceBaseBoardVO = baseMapper.getCustomerServiceBaseBoardByDate(date);
        List<OrderVideoMatch> orderVideoMatches = orderVideoMatchService.selectLastListBySubmitTime(date, date);
        if (CollUtil.isNotEmpty(orderVideoMatches)) {
            for (OrderVideoMatch orderVideoMatch : orderVideoMatches) {
                orderVideoMatch.setCommission(orderVideoMatch.getCommission().multiply(CommissionUnitEnum.getByUnit(orderVideoMatch.getCommissionUnit()).getToUSDRate()).setScale(2, RoundingMode.DOWN));
            }
            customerServiceBaseBoardVO.setAverageCommission(ArithmeticUtil.safeDivide(orderVideoMatches.stream().map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), customerServiceBaseBoardVO.getOrderScheduledCount(), 2, RoundingMode.DOWN));
            customerServiceBaseBoardVO.setAverageCommissionSole(ArithmeticUtil.safeDivide(orderVideoMatches.stream().filter(item -> ModelTypeEnum.AVERAGE_PEOPLE.getCode().equals(item.getShootModelType())).map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), customerServiceBaseBoardVO.getOrderScheduledSoleCount(), 2, RoundingMode.DOWN));
            customerServiceBaseBoardVO.setAverageCommissionInfluencer(ArithmeticUtil.safeDivide(orderVideoMatches.stream().filter(item -> ModelTypeEnum.INFLUENT.getCode().equals(item.getShootModelType())).map(OrderVideoMatch::getCommission).reduce(BigDecimal.ZERO, BigDecimal::add), customerServiceBaseBoardVO.getOrderScheduledInfluencerCount(), 2, RoundingMode.DOWN));
        }

        return customerServiceBaseBoardVO;
    }
}
