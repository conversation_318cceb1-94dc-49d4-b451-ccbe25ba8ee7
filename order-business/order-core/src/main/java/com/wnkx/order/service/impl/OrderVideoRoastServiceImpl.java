package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.RoastObjectEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastDTO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoRoastListDTO;
import com.ruoyi.system.api.domain.dto.order.task.OrderVideoRoastHandleDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRoast;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRoastVO;
import com.wnkx.order.mapper.OrderVideoMapper;
import com.wnkx.order.mapper.OrderVideoRoastMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.OrderVideoRoastService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/9 14:14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoRoastServiceImpl extends ServiceImpl<OrderVideoRoastMapper, OrderVideoRoast> implements OrderVideoRoastService {

    private final RemoteService remoteService;
    private final OrderVideoMapper orderVideoMapper;

    @Override
    public List<OrderVideoRoastVO> getOrderVideoRoastVO(OrderVideoRoastListDTO dto) {

        if (StrUtil.isNotBlank(dto.getMemberCode())) {
            BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
            businessAccountDetailDTO.setMemberCode(dto.getMemberCode());
            businessAccountDetailDTO.setIsOwnerAccount(StatusTypeEnum.YES.getCode());
            List<BusinessAccountDetailVO> businessList = remoteService.queryMerchant(businessAccountDetailDTO);
            if (CollUtil.isNotEmpty(businessList)) {
                dto.setBusinessIds(businessList.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toList()));
            } else {
                return Collections.emptyList();
            }
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovr.handle_status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("ovr.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<OrderVideoRoastVO> orderVideoRoastVO = baseMapper.getOrderVideoRoastVO(dto);
        if (CollUtil.isEmpty(orderVideoRoastVO)) {
            return List.of();
        }
        List<Long> businessIds = new ArrayList<>();
        List<Long> waiterIds = new ArrayList<>();
        List<Long> shootModelIds = new ArrayList<>();
        for (OrderVideoRoastVO item : orderVideoRoastVO) {
            businessIds.add(item.getBusinessId());
            if (ObjectUtil.isNotNull(item.getContactId())) {
                waiterIds.add(item.getContactId());
            }
            if (ObjectUtil.isNotNull(item.getIssueId())) {
                waiterIds.add(item.getIssueId());
            }
            if (ObjectUtil.isNotNull(item.getShootModelId())) {
                shootModelIds.add(item.getShootModelId());
            }
        }
        final List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchant(BusinessAccountDetailDTO.builder().isOwnerAccount(StatusTypeEnum.YES.getCode())
                .businessIds(businessIds).build());
        Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();
        Map<Long, SysUser> userMap = new HashMap<>();
        if (CollUtil.isNotEmpty(waiterIds)) {
            SysUserListDTO sysUserListDTO = new SysUserListDTO();
            sysUserListDTO.setUserId(waiterIds);
            List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
            if (CollUtil.isNotEmpty(userList)) {
                userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, Function.identity()));
            }
        }
        if (CollUtil.isNotEmpty(businessAccountDetailVOS)) {
            businessMap = businessAccountDetailVOS.stream().collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity()));
        }

        Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(shootModelIds);

        for (OrderVideoRoastVO roastVO : orderVideoRoastVO) {
            BusinessAccountDetailVO businessAccountDetailVO = businessMap.get(roastVO.getBusinessId());
            if (ObjectUtil.isNotNull(businessAccountDetailVO)) {
                roastVO.setBusinessName(businessAccountDetailVO.getBusinessName());
                roastVO.setMemberCode(businessAccountDetailVO.getMemberCode());
            }

            if (ObjectUtil.isNotNull(roastVO.getContactId())) {
                SysUser sysUser = userMap.getOrDefault(roastVO.getContactId(), new SysUser());
                roastVO.setWaiterName(sysUser.getUserName());
            }

            if (ObjectUtil.isNotNull(roastVO.getIssueId())) {
                SysUser issueUser = userMap.getOrDefault(roastVO.getIssueId(), new SysUser());
                roastVO.setIssueName(issueUser.getUserName());
            }
            roastVO.setModelInfoVO(modelMap.getOrDefault(roastVO.getShootModelId(), new ModelInfoVO()));


        }
        return orderVideoRoastVO;
    }

    /**
     * 查询视频订单是否有存在吐槽
     */
    @Override
    public boolean isRoast(Long videoId) {
        return baseMapper.isRoast(videoId);
    }

    @Override
    public List<Long> isRoast(List<Long> videoIds) {
        return baseMapper.isRoast(videoIds);
    }

    @Override
    public void handleRoast(OrderVideoRoastHandleDTO dto) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "当前用户类型不可处理吐槽~");
        OrderVideoRoast orderVideoRoast = BeanUtil.copyProperties(dto, OrderVideoRoast.class);
        orderVideoRoast.setHandleStatus(StatusTypeEnum.YES.getCode());
        orderVideoRoast.setHandleTime(new Date());
        orderVideoRoast.setHandleUserId(SecurityUtils.getUserId());
        orderVideoRoast.setHandleUserName(SecurityUtils.getUsername());
        baseMapper.updateById(orderVideoRoast);
    }

    @Override
    public List<UserVO> roastContactSelect(String keyword) {
        Set<Long> orderIssueId = baseMapper.getRoastContactId();
        if (CollUtil.isEmpty(orderIssueId)) {
            return Collections.emptyList();
        }

        return getUserVOs(orderIssueId, keyword);
    }


    @Override
    public List<UserVO> roastIssueSelect(String keyword) {
        Set<Long> orderIssueId = baseMapper.getRoastIssueId();
        if (CollUtil.isEmpty(orderIssueId)) {
            return Collections.emptyList();
        }

        return getUserVOs(orderIssueId, keyword);
    }

    @Override
    public OrderVideoRoastStatisticsVO orderVideoRoastStatisticsVO() {
        return baseMapper.orderVideoRoastStatisticsVO();
    }

    private List<UserVO> getUserVOs(Set<Long> orderIssueId, String keyword) {
        if (CollUtil.isEmpty(orderIssueId)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(orderIssueId);
        dto.setUserName(keyword);
        return remoteService.customerServiceSelect(dto);
    }

    /**
     * 新增视频吐槽
     */
    @Override
    public void addOrderVideoRoast(OrderVideoRoastDTO dto) {
        Assert.isTrue(UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()), "当前用户类型不可吐槽订单~");
        BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        dto.setRoastUserName(businessAccountVO.getName());
        dto.setRoastUserNickName(businessAccountVO.getNickName());
        dto.setBusinessId(businessAccountVO.getBusinessId());
        dto.setBizUserId(businessAccountVO.getBizUserId());
        if (StrUtil.isNotBlank(dto.getContent())) {
            dto.setContent(dto.getContent().trim());
            if (dto.getObject() == null) {
                throw new IllegalArgumentException("吐槽对象不能为空");
            }
            if (RoastObjectEnum.findByCode(dto.getObject()) == null) {
                throw new IllegalArgumentException("吐槽对象输入错误");
            }
        }
        OrderVideoRoast orderVideoRoast = BeanUtil.copyProperties(dto, OrderVideoRoast.class);
        if (StatusTypeEnum.YES.getCode().equals(dto.getRoastType())) {
            orderVideoRoast.setContactId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessVO().getWaiterId());
        } else {
            OrderVideo orderVideo = orderVideoMapper.selectById(dto.getVideoId());
            Assert.notNull(orderVideo, "视频订单不存在~");
            orderVideoRoast.setIssueId(orderVideo.getIssueId());
            orderVideoRoast.setContactId(orderVideo.getContactId());
        }

        baseMapper.insert(orderVideoRoast);
    }
}

