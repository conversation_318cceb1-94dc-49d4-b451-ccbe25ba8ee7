package com.wnkx.order.service;

import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.vo.order.CheckSeedCodeVO;
import com.ruoyi.system.api.domain.vo.order.CreatePayVo;
import com.ruoyi.system.api.domain.vo.order.Currency2RMBVO;
import com.ruoyi.system.api.domain.vo.order.OrderPayInfoVO;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付相关业务接口
 *
 * <AUTHOR>
 * @date 2024/5/31
 */
public interface PayService {

    /**
     * 微信支付回调
     */
    void weChatCallback();

    /**
     * 计算订单金额
     *
     * @return true 使用的默认汇率 false 使用实时百度汇率
     */
    List<OrderVideoDTO> calculateOrderAmount(List<OrderVideoDTO> orderVideoDTOS);

    /**
     * 更新总价
     * （（视频价格+照片价格+手续费+服务费）*当前汇率） 保留2位小数
     */
    List<OrderVideoDTO> updateTotalPrice(OrderVideoDTO videoDTO, List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS);

    /**
     * 获取二维码
     *
     * @param dto
     * @return
     */
    CreatePayVo generateQrcode(PayCodeDTO dto, Boolean isAnother);

    OrderPayStatusDTO checkOrderStatus(CheckStatusDTO checkStatusDTO);

    OrderPayStatusDTO checkAnotherPayStatus(CheckStatusDTO checkStatusDTO);

    /**
     * 订单支付页信息
     */
    OrderPayInfoVO payInfo(PayInfoDTO payInfoDTO);

    /**
     * 获取开票税点*
     *
     * @return
     */
    BigDecimal getTaxPoint();

    /**
     * 会员订单支付信息
     *
     * @return
     */
    OrderPayInfoVO payMemberInfo(PayMemberInfoDTO dto, Boolean isAnother);

    /**
     * 货币单位转换（美元 -> 人民币）
     */
    Currency2RMBVO dollarToRmb(List<BigDecimal> dollars);

//    /**
//     * 校验种草码
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    Boolean checkSeedCode(String seedCode, String orderNum);

    /**
     * 校验种草码
     * @param seedCode
     * @param orderNum
     * @return
     */
    CheckSeedCodeVO checkSeedCodeV1(String seedCode, String orderNum);

//    /**
//     * 获取种草码优惠的金额
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    BigDecimal getSeedCodeDiscount(String seedCode, BigDecimal payAmount);
//
//    /**
//     * 获取种草码优惠的金额
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    BigDecimal getSeedCodeDiscount(BigDecimal discount, BigDecimal payAmount);

    /**
     *
     * @param type  优惠类型：1-固定金额，2-固定比例
     * @param discount   优惠金额
     * @param payAmount     总金额
     * @return
     */
    BigDecimal getSeedCodeDiscountV1(Integer type,BigDecimal discount, BigDecimal payAmount);

    /**
     * 下载请款清单
     */
    void downloadPayInfo(Long mergeId, String orderNum, boolean isAnotherPay, HttpServletResponse response) throws IOException;
}
