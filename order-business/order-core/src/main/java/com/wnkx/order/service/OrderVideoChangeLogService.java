package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.OrderVideoChangeLogDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoChangeLog;
import com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:15
 */
public interface OrderVideoChangeLogService extends IService<OrderVideoChangeLog> {

    /**
     * 订单详情-变更记录
     */
    List<OrderVideoChangeLogVO> selectVideoChangeListByVideoId(Long videoId);

    /**
     * 添加变更记录
     */
    void addVideoChangeLog(List<OrderVideoChangeLogDTO> dtoList);

    /**
     * 添加变更记录
     */
    void addVideoChangeLog(OrderVideoChangeLogDTO dto);

    /**
     * 查询视频订单是否有变更记录
     */
    boolean videoHasChangeLog(Long videoId);
}
