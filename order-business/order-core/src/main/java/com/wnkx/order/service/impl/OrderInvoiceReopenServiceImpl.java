package com.wnkx.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderInvoiceReopen;
import com.wnkx.order.mapper.OrderInvoiceReopenMapper;
import com.wnkx.order.service.OrderInvoiceReopenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/1/14 19:09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderInvoiceReopenServiceImpl extends ServiceImpl<OrderInvoiceReopenMapper, OrderInvoiceReopen> implements OrderInvoiceReopenService {


    /**
     * 通过新发票ID获取旧发票ID
     */
    @Override
    public Long getOldInvoiceReopenByNewInvoiceId(Long newInvoiceId) {
        OrderInvoiceReopen orderInvoiceReopen = baseMapper.getOldInvoiceReopenByNewInvoiceId(newInvoiceId);
        if (ObjectUtil.isNotNull(orderInvoiceReopen)) {
            return orderInvoiceReopen.getOldInvoiceId();
        }
        return null;
    }

    /**
     * 保存发票重开记录
     */
    @Override
    public void saveOrderInvoiceReopen(OrderInvoiceReopen orderInvoiceRed) {
        baseMapper.insert(orderInvoiceRed);
    }
}
