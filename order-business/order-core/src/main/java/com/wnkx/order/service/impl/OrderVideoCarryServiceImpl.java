package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.OrderVideoCarry;
import com.wnkx.order.mapper.OrderVideoCarryMapper;
import com.wnkx.order.service.IOrderVideoCarryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 订单_视频_携带Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-15
 */
@Service
@RequiredArgsConstructor
public class OrderVideoCarryServiceImpl extends ServiceImpl<OrderVideoCarryMapper, OrderVideoCarry> implements IOrderVideoCarryService {


    /**
     * 新增视频订单携带记录
     */
    @Override
    public void insertOrderVideoCarry(OrderVideoCarry orderVideoCarry) {
        baseMapper.insert(orderVideoCarry);
    }
}
