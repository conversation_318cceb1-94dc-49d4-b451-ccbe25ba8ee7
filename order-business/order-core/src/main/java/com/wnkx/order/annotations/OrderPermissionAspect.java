package com.wnkx.order.annotations;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.CheckDataScopeDTO;
import com.wnkx.order.service.core.OrderDataScopeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户订单权限判断切面
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
@RequiredArgsConstructor
public class OrderPermissionAspect {

    private final OrderDataScopeService orderDataScopeService;


    @Around("@annotation(orderPermissions)")
    public Object loginUserAround(ProceedingJoinPoint pjp, OrderPermissions orderPermissions) throws Throwable {
        int[] include = orderPermissions.include();
        if (include.length > 0) {
            List<Integer> includeList = Arrays.stream(include)
                    .boxed()
                    .collect(Collectors.toList());
            if (!includeList.contains(SecurityUtils.getLoginUserType())) {
                return pjp.proceed();
            }
        }
        ExpressionParser parser = new SpelExpressionParser();
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        Object[] args = pjp.getArgs();
        Method method = ((MethodSignature) pjp.getSignature()).getMethod();
        String[] params = discoverer.getParameterNames(method);
        if (params == null) {
            return pjp.proceed();
        }
        EvaluationContext context = new StandardEvaluationContext();
        for (int len = 0; len < params.length; len++) {
            context.setVariable(params[len], args[len]);
        }
        Expression expression = parser.parseExpression(orderPermissions.orderId());
        final List<Long> orderIds = expression.getValue(context, List.class);
        if (CollectionUtil.isNotEmpty(orderIds)) {
            orderDataScopeService.checkDataScope(CheckDataScopeDTO.builder()
                    .videoIds(orderIds)
                    .businessShare(orderPermissions.businessShare())
                    .submitter(orderPermissions.submitter())
                    .backUserType(orderPermissions.backUserType())
                    .workOrderAssignee(orderPermissions.workOrderAssignee())
                    .build());
        }
        return pjp.proceed();
    }
}
