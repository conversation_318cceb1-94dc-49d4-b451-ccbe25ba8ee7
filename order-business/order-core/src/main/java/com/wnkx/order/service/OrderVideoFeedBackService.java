package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.system.api.domain.dto.order.OrderVideoFeedBackDTO;
import com.ruoyi.system.api.domain.dto.order.VideoScoreListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderFeedBackVO;
import com.ruoyi.system.api.domain.vo.order.VideoScoreListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_video_feed_back(订单反馈表(商家))】的数据库操作Service
 * @createDate 2024-06-18 17:43:06
 */
public interface OrderVideoFeedBackService extends IService<OrderVideoFeedBack> {
    void addFeedBack(OrderVideoFeedBackDTO dto);

    List<OrderVideoFeedBack> getFeedBackList(Long videoId, Long rollbackId);

    /**
     * 获取订单反馈数据
     * @param videoIds
     * @return
     */
    List<OrderVideoFeedBack> getFeedBackListByVideoIds(List<Long> videoIds);

    /**
     * 获取视频订单反馈素材
     *
     * @param videoIds 视频id
     * @return 反馈素材
     */
    List<OrderFeedBackVO> getFeedBack(List<Long> videoIds);
    List<OrderFeedBackVO> getFeedBackList(List<Long> videoIds);

    List<OrderFeedBackSimpleVO> getOrderFeedBackSimpleVO(Long videoId);

    /**
     * 视频评价记录 列表
     */
    List<VideoScoreListVO> selectVideoScoreListByCondition(VideoScoreListDTO dto);

    /**
     * 视频评价记录 拍摄模特下拉框
     */
    List<ModelSelectVO> videoScoreListSelectShootModel();

    /**
     * 视频评价记录 评价人下拉框
     */
    List<UserVO> videoScoreListSelectEvaluatePerson();

    /**
     * 通过模特反馈素材详情ID查询数据
     */
    List<OrderVideoFeedBack> selectListByMaterialInfoIds(List<Long> materialInfoIds);

    /**
     * 通过视频订单ID和回退ID查询反馈给商家的素材
     */
    List<OrderVideoFeedBack> selectListByVideoIdAndRollbackId(Long videoId, Long rollbackId);

    /**
     * 通过视频订单ID查询反馈给商家的素材（部分字段）
     */
    List<OrderVideoFeedBack> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(List<Long> videoIds);

    /**
     * 通过视频订单ID获取每个视频订单最新一条的反馈给商家素材
     */
    List<OrderVideoFeedBack> selectLatestFeedBackListByVideoIds(List<Long> videoIds);
}
