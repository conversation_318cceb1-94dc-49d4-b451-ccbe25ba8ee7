package com.wnkx.order.annotations;

import com.ruoyi.common.core.enums.BackUserTypeEnum;

import java.lang.annotation.*;

/**
 * 检查当前用户是否有权限操作订单
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OrderPermissions {
    /**
     * 视频订单ID
     */
    String orderId() default "";

    /**
     * 只校验指定的用户类型
     */
    int[] include() default {};

    /**
     * 商家端-同个商家内数据共享    true 共享，false 不共享
     */
    boolean businessShare() default false;

    /**
     * 商家端-订单提交人 或者 视频订单创建人 可操作
     */
    boolean submitter() default false;

    /**
     * 校验中文部客服或者英文部客服
     */
    BackUserTypeEnum backUserType() default BackUserTypeEnum.ALL;

    /**
     * 或者是工单处理人
     */
    boolean workOrderAssignee() default false;
}
