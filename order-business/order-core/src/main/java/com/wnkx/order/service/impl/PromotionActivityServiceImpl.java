package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.PromotionActivityTypeEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.promotion.UpdatePromotionActivityDTO;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityDetail;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.domain.vo.order.promotion.BusinessParticipatoryActivityVO;
import com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.wnkx.order.mapper.PromotionActivityMapper;
import com.wnkx.order.service.PromotionActivityAmendmentRecordService;
import com.wnkx.order.service.PromotionActivityDetailService;
import com.wnkx.order.service.PromotionActivityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 针对表【promotion_activity(活动信息表)】的数据库操作Service实现
 * @createDate 2025-03-19 15:50:19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PromotionActivityServiceImpl extends ServiceImpl<PromotionActivityMapper, PromotionActivity> implements PromotionActivityService {

    private final RedisService redisService;
    private final PromotionActivityDetailService promotionActivityDetailService;
    private final PromotionActivityAmendmentRecordService promotionActivityAmendmentRecordService;

    /**
     * 获取进行中的活动列表
     */
    @Override
    public List<PromotionActivityVO> selectValidPromotionActivityList() {
        return baseMapper.selectValidPromotionActivityList();
    }

    /**
     * 根据活动类型获取活动
     */
    @Override
    public PromotionActivityVO getPromotionActivityByType(Integer type) {
        return baseMapper.getPromotionActivityByType(type);
    }

    /**
     * 修改活动配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePromotionActivity(UpdatePromotionActivityDTO dto) {
        Assert.isTrue(PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode().equals(Convert.toInt(dto.getId())), "只允许修改每月首单立减");

        PromotionActivity promotionActivity = baseMapper.selectById(dto.getId());
        Assert.notNull(promotionActivity, "活动不存在");

        promotionActivity.setStartTime(dto.getStartTime());
        promotionActivity.setEndTime(dto.getEndTime());
        baseMapper.updateById(promotionActivity);

        PromotionActivityDetail promotionActivityDetail = promotionActivityDetailService.getByActivityId(dto.getId());
        Assert.notNull(promotionActivityDetail, "活动详情不存在");
        promotionActivityDetail.setAmount(dto.getAmount());
        promotionActivityDetailService.updateById(promotionActivityDetail);

        PromotionActivityAmendmentRecord promotionActivityAmendmentRecord = new PromotionActivityAmendmentRecord();
        promotionActivityAmendmentRecord.setActivityId(dto.getId());
        promotionActivityAmendmentRecord.setType(promotionActivityDetail.getType());
        promotionActivityAmendmentRecord.setAmount(dto.getAmount());
        promotionActivityAmendmentRecord.setCurrency(promotionActivityDetail.getCurrency());
        promotionActivityAmendmentRecord.setStartTime(dto.getStartTime());
        promotionActivityAmendmentRecord.setEndTime(dto.getEndTime());
        promotionActivityAmendmentRecordService.savePromotionActivityAmendmentRecord(promotionActivityAmendmentRecord);
    }

    @Override
    public PromotionActivityVO getValidPromotionActivityByType(Integer type) {
        if (ObjectUtil.isNull(type)) {
            return null;
        }
        return baseMapper.getValidPromotionActivityByType(type);
    }

    /**
     * 获取当前商家可参与的活动
     */
    @Override
    public List<BusinessParticipatoryActivityVO> getBusinessParticipatoryActivityVOS() {
        List<PromotionActivityVO> promotionActivityVOS = baseMapper.selectValidPromotionActivityList();
        if (CollUtil.isEmpty(promotionActivityVOS)) {
            return Collections.emptyList();
        }
        //  商家可参与的活动
        List<BusinessParticipatoryActivityVO> businessParticipatoryActivityVOS = new ArrayList<>();

        //  获取当前登录商家信息
        LoginBusiness loginBusinessUser = SecurityUtils.getLoginBusinessUser();
        Assert.notNull(loginBusinessUser, "获取商家登录信息失败");
        BusinessAccountVO businessAccountVO = loginBusinessUser.getBusinessAccountVO();

        for (PromotionActivityVO promotionActivityVO : promotionActivityVOS) {
            if (PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode().equals(promotionActivityVO.getType())) {
                //  检查当前商家是否满足 会员订单临期续费半价优惠
                if (checkCurrentBusinessSatisfyMemberHalfPriceRenewal(businessAccountVO)) {
                    businessParticipatoryActivityVOS.add(BeanUtil.copyProperties(promotionActivityVO, BusinessParticipatoryActivityVO.class));
                }
            } else if (PromotionActivityTypeEnum.MONTH_FIRST_ORDER_DISCOUNTED.getCode().equals(promotionActivityVO.getType())) {
                //  检查当前商家是否满足 每月首单立减
                if (checkCurrentBusinessSatisfyMonthFirstOrderDiscounted(businessAccountVO)) {
                    businessParticipatoryActivityVOS.add(BeanUtil.copyProperties(promotionActivityVO, BusinessParticipatoryActivityVO.class));
                }
            } else if (PromotionActivityTypeEnum.ORDER_COUNT_FULL_REDUCTION.getCode().equals(promotionActivityVO.getType())) {
                businessParticipatoryActivityVOS.add(BeanUtil.copyProperties(promotionActivityVO, BusinessParticipatoryActivityVO.class));
            }
        }

        return businessParticipatoryActivityVOS;
    }

    /**
     * 检查当前商家是否满足每月首单立减
     */
    private boolean checkCurrentBusinessSatisfyMonthFirstOrderDiscounted(BusinessAccountVO businessAccountVO) {
        return baseMapper.checkCurrentBusinessSatisfyMonthFirstOrderDiscounted(businessAccountVO.getBusinessId(), businessAccountVO.getBizUserId());
    }

    /**
     * 检查当前商家是否满足会员订单临期续费半价优惠
     */
    private Boolean checkCurrentBusinessSatisfyMemberHalfPriceRenewal(BusinessAccountVO businessAccountVO) {
        BusinessVO businessVO = businessAccountVO.getBusinessVO();
        //  非会员 会员到期 不满足活动
        if (!MemberTypeEnum.RECHARGE.getCode().equals(businessVO.getMemberType())) {
            return false;
        }

        //  检查会员到期时间是否是30天内
        if (DateUtil.betweenDay(DateUtil.date(), DateUtil.endOfDay(businessVO.getMemberValidity()), false) >= 30) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否需要提醒当前用户会员半价续费
     */
    @Override
    public Boolean getRemindBusinessMemberHalfPriceRenewal() {
        LoginBusiness loginBusinessUser = SecurityUtils.getLoginBusinessUser();
        Assert.notNull(loginBusinessUser, "获取商家登录信息失败");
        BusinessAccountVO businessAccountVO = loginBusinessUser.getBusinessAccountVO();

        if (!checkCurrentBusinessSatisfyMemberHalfPriceRenewal(businessAccountVO)) {
            return false;
        }

        Date memberValidity = DateUtil.endOfDay(businessAccountVO.getBusinessVO().getMemberValidity());
        DateTime date = DateUtil.date();

        String cacheKey = CacheConstants.REMIND_BUSINESS_MEMBER_HALF_PRICE_RENEWAL_KEY + businessAccountVO.getBusinessId() + StrPool.COLON + Convert.toStr(businessAccountVO.getBizUserId());
        //  已提醒时间
        Long remindTimestamp = redisService.getCacheObject(cacheKey);

        //  未提醒
        if (ObjectUtil.isNull(remindTimestamp)) {
            //  设置提醒时间并返回
            redisService.setCacheObject(
                    cacheKey,
                    date.getTime(),
                    10L,
                    TimeUnit.DAYS);
            return true;
        }

        DateTime remindTime = DateUtil.date(remindTimestamp);

        //  根据提醒时间段判断
        boolean isRemind = false;
        if (DateUtil.betweenDay(date, memberValidity, false) < 30 && DateUtil.betweenDay(date, memberValidity, false) >= 20) {
            //  在30到21天时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -30), DateUtil.offsetDay(memberValidity, -20));
        } else if (DateUtil.betweenDay(date, memberValidity, false) < 20 && DateUtil.betweenDay(date, memberValidity, false) >= 10) {
            //  在20到11天时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -20), DateUtil.offsetDay(memberValidity, -10));
        } else if (DateUtil.betweenDay(date, memberValidity, false) < 10 && DateUtil.betweenDay(date, memberValidity, false) >= 3) {
            //  在10到3天时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -10), DateUtil.offsetDay(memberValidity, -3));
        } else if (DateUtil.betweenDay(date, memberValidity, false) < 3 && DateUtil.betweenDay(date, memberValidity, false) >= 2) {
            //  在3到2天时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -3), DateUtil.offsetDay(memberValidity, -2));
        } else if (DateUtil.betweenDay(date, memberValidity, false) < 2 && DateUtil.betweenDay(date, memberValidity, false) >= 1) {
            //  在2到1天时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -2), DateUtil.offsetDay(memberValidity, -1));
        } else if (DateUtil.betweenDay(date, memberValidity, false) < 1 && DateUtil.betweenDay(date, memberValidity, false) >= 0) {
            //  在1到memberValidity时间区间内
            isRemind = !DateUtil.isIn(remindTime, DateUtil.offsetDay(memberValidity, -1), memberValidity);
        }

        if (isRemind) {
            //  需要提醒 则更新提醒时间
            //  设置提醒时间
            redisService.setCacheObject(
                    cacheKey,
                    date.getTime(),
                    10L,
                    TimeUnit.DAYS);
        }
        return isRemind;
    }
}




