package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundSimpleVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundSuccessVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoRefundVO;
import com.ruoyi.system.api.domain.vo.order.RefundInfoVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 订单_视频_退款Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IOrderVideoRefundService extends IService<OrderVideoRefund> {

    /**
     * 退款订单列表
     *
     * @param orderVideoRefundListDTO 列表条件入参
     * @return 退款订单列表
     */
    List<OrderVideoRefundVO> selectOrderVideoRefundListByCondition(OrderVideoRefundListDTO orderVideoRefundListDTO);

    /**
     * 申请退款-获取退款信息
     */
    RefundInfoVO getRefundInfo(RefundInfoDTO getRefundInfoDTO);

    /**
     * 获取有效退款数据
     * @param videoIds
     * @return
     */
    List<OrderVideoRefund> selectValidOrderVideoRefundListByVideoId(Collection<Long> videoIds);


    /**
     * 申请退款-获取退款信息(特殊处理接口 内部不调用)
     * @param getRefundInfoDTO
     * @return
     */
    RefundInfoVO getRefundInfoVo(RefundInfoDTO getRefundInfoDTO);

    /**
     * 申请退款
     */
    void applyRefund(ApplyRefundDTO applyRefundDTO);

    /**
     * 退款状态流转
     */
    List<OrderVideoRefund> refundFlow(RefundFlowDTO refundFlowDTO);

    /**
     * 导出退款订单
     */
    List<OrderVideoRefundExportDTO> exportExcel(OrderVideoRefundListDTO orderVideoRefundListDTO);

    /**
     * 同意退款
     * @param dto
     */
    void agreeRefund(AgreeRefundDTO dto);

    /**
     * 拒绝退款
     */
    void rejectRefund(RejectRefundDTO rejectRefundDTO);

    /**
     * 商家取消退款
     */
    void cancelRefund(List<Long> id);

    /**
     * 根据视频订单id查询退款列表
     */
    List<OrderVideoRefundVO> selectOrderVideoRefundListByVideoId(Collection<Long> videoId);

    /**
     * 查询有退款或者正在退款的视频订单id
     */
    List<Long> selectVideoIdByRefund(List<Long> videoIds);

    /**
     * 根据视频订单id查询简单的退款列表
     */
    List<OrderVideoRefundSimpleVO> selectOrderVideoRefundSimpleListByVideoId(Collection<Long> videoId);

    /**
     * 财务对账-退款记录列表
     */
    List<OrderVideoRefundSuccessVO> refundSuccessList(OrderVideoRefundSuccessListDTO dto);

    /**
     * 财务对账-退款记录列表-导出
     */
    void refundSuccessListExport(OrderVideoRefundSuccessListDTO dto, HttpServletResponse response);

    /**
     * 根据originNumber获取退款记录
     */
    List<OrderVideoRefund> getOrderVideoRefundListByBusinessId(List<String> numbers);
}
