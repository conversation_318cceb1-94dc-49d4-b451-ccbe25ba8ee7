package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.OrderVideoFlowNodeEnum;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFlowNodeDiagram;
import com.ruoyi.system.api.domain.vo.order.OrderVideoFlowNodeDiagramVO;
import com.wnkx.order.mapper.OrderVideoFlowNodeDiagramMapper;
import com.wnkx.order.service.OrderVideoFlowNodeDiagramService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/14 17:21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoFlowNodeDiagramServiceImpl extends ServiceImpl<OrderVideoFlowNodeDiagramMapper, OrderVideoFlowNodeDiagram> implements OrderVideoFlowNodeDiagramService {


    /**
     * 订单回退设置除下单支付外节点完成时间为null
     */
    @Override
    public void setNodeCompleteTimeNullExceptOrderPay(Long videoId) {
        baseMapper.setNodeCompleteTimeNullExceptOrderPay(videoId);
    }

    /**
     * 查询视频订单节点图
     */
    @Override
    public List<OrderVideoFlowNodeDiagramVO> selectOrderVideoFlowNodeDiagramListByVideoId(Long videoId) {
        List<OrderVideoFlowNodeDiagram> list = baseMapper.selectOrderVideoFlowNodeDiagramListByVideoId(videoId);
        return BeanUtil.copyToList(list, OrderVideoFlowNodeDiagramVO.class);
    }

    /**
     * 设置某一节点的完成时间为null
     */
    @Override
    public void setNodeCompleteTimeNull(Long videoId, OrderVideoFlowNodeEnum nodeEnum) {
        baseMapper.setNodeCompleteTimeNull(videoId, nodeEnum);
    }

    /**
     * 终止视频订单节点
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminateOrderVideoFlowNode(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return;
        }
        baseMapper.deleteNullTime(videoIds);
        List<OrderVideoFlowNodeDiagram> orderVideoFlowNodeDiagrams = videoIds.stream().map(videoId -> {
            OrderVideoFlowNodeDiagram orderVideoFlowNodeDiagram = new OrderVideoFlowNodeDiagram();
            orderVideoFlowNodeDiagram.setVideoId(videoId);
            orderVideoFlowNodeDiagram.setNode(OrderVideoFlowNodeEnum.CANCEL_ORDER.getCode());
            orderVideoFlowNodeDiagram.setTime(DateUtil.date());
            orderVideoFlowNodeDiagram.setSort(OrderVideoFlowNodeEnum.CANCEL_ORDER.getSort());
            return orderVideoFlowNodeDiagram;
        }).collect(Collectors.toList());
        baseMapper.saveBatch(orderVideoFlowNodeDiagrams);
    }

    /**
     * 设置节点完成时间
     */
    @Override
    public void setNodeCompleteTime(Long videoId, OrderVideoFlowNodeEnum... nodeEnum) {
        setNodeCompleteTime(List.of(videoId), nodeEnum);
    }

    /**
     * 设置节点完成时间
     */
    @Override
    public void setNodeCompleteTime(List<Long> videoIds, OrderVideoFlowNodeEnum... nodeEnum) {
        if (CollUtil.isEmpty(videoIds)) {
            return;
        }
        baseMapper.setNodeCompleteTime(videoIds, nodeEnum);
    }

    /**
     * 初始化视频订单节点图
     */
    @Override
    public void initOrderVideoFlowNodeDiagram(List<Long> videoIds) {
        List<OrderVideoFlowNodeDiagram> orderVideoFlowNodeDiagrams = new ArrayList<>();
        for (Long videoId : videoIds) {
            for (OrderVideoFlowNodeEnum nodeEnum : OrderVideoFlowNodeEnum.values()) {
                if (OrderVideoFlowNodeEnum.CANCEL_ORDER.equals(nodeEnum)) {
                    continue;
                }
                OrderVideoFlowNodeDiagram orderVideoFlowNodeDiagram = new OrderVideoFlowNodeDiagram();
                orderVideoFlowNodeDiagram.setVideoId(videoId);
                orderVideoFlowNodeDiagram.setNode(nodeEnum.getCode());
                orderVideoFlowNodeDiagram.setSort(nodeEnum.getSort());
                orderVideoFlowNodeDiagrams.add(orderVideoFlowNodeDiagram);
            }
        }
        baseMapper.saveBatch(orderVideoFlowNodeDiagrams);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reopenInitOrderVideoFlowNodeDiagram(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return;
        }
        baseMapper.deleteClose(videoIds);
        initOrderVideoFlowNodeDiagram(videoIds);
    }
}
