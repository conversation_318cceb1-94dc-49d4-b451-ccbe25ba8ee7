package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account(订单收款账号表)】的数据库操作Service
 * @createDate 2024-10-29 11:12:22
 */
public interface IOrderPayeeAccountService extends IService<OrderPayeeAccount> {
    /**
     * 根据账号类型添加订单收款账号数据
     *
     * @param orderNums
     * @param accountType
     * @return
     */
    void saveOrderPayeeAccount(List<String> orderNums, Long payeeId, Integer accountType);

    void saveOrderPayeeAccount(List<Order> orders, Integer payType);

    /**
     * 添加收款账号列表
     * @param dto
     * @return
     */
    void saveOrderPayeeAccount(OrderPayAccountDTO dto);

    /**
     * 根据订单列表获取  订单收款账号列表
     *
     * @param orderNums
     * @return
     */
    List<OrderPayeeAccount> queryListByOrderNums(List<String> orderNums);

    /**
     * *修改收款账号数据
     * @param orderPayeeAccount
     */
    void updateByOrderNum(OrderPayeeAccount orderPayeeAccount);

    /**
     * 根据订单号列表修改收款账号数据
     * @param orderPayeeAccount
     * @param orderNums
     */
    void updateByOrderNums(OrderPayeeAccount orderPayeeAccount, List<String> orderNums);
}
