package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.PromotionActivityTypeEnum;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO;
import com.wnkx.order.mapper.OrderPromotionDetailMapper;
import com.wnkx.order.service.OrderPromotionDetailService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_promotion_detail】的数据库操作Service实现
 * @createDate 2025-03-19 15:50:19
 */
@Service
public class OrderPromotionDetailServiceImpl extends ServiceImpl<OrderPromotionDetailMapper, OrderPromotionDetail> implements OrderPromotionDetailService {


    /**
     * 通过视频订单ID获取订单参与活动以及优惠金额明细
     */
    @Override
    public List<OrderDiscountDetailVO> selectOrderDiscountDetailsByVideoIds(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectOrderDiscountDetailsByVideoIds(videoIds);
    }

    /**
     * 通过视频订单ID获取订单参与活动以及优惠金额明细
     */
    @Override
    public List<OrderDiscountDetailVO> selectOrderDiscountDetailsByVideoId(Long videoId) {
        return selectOrderDiscountDetailsByVideoIds(List.of(videoId));
    }

    /**
     * 通过订单号获取订单参与活动以及优惠金额明细
     */
    @Override
    public List<OrderDiscountDetailVO> selectOrderDiscountDetailsByOrderNums(List<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return Collections.emptyList();
        }
        List<OrderDiscountDetailVO> orderDiscountDetailVOS = baseMapper.selectOrderDiscountDetailsByOrderNums(orderNums);
        for (OrderDiscountDetailVO orderDiscountDetailVO : orderDiscountDetailVOS) {
            if (PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getCode().equals(orderDiscountDetailVO.getType())) {
                orderDiscountDetailVO.setDiscountRatio(PromotionActivityTypeEnum.MEMBER_ORDER_RENEW_AT_HALF_PRICE.getDiscountRatio().multiply(new BigDecimal("100")));
            }
        }
        return orderDiscountDetailVOS;
    }

    /**
     * 通过订单号获取订单参与活动以及优惠金额明细
     */
    @Override
    public List<OrderDiscountDetailVO> getOrderDiscountDetailByOrderNum(String orderNum) {
        return selectOrderDiscountDetailsByOrderNums(List.of(orderNum));
    }

    /**
     * 新增订单使用活动明细
     */
    @Override
    public void saveBatchOrderPromotionDetail(List<OrderPromotionDetail> orderPromotionDetails) {
        if (CollUtil.isEmpty(orderPromotionDetails)) {
            return;
        }

        Long userId = SecurityUtils.getUserId();
        String username = SecurityUtils.getUsername();
        for (OrderPromotionDetail orderPromotionDetail : orderPromotionDetails) {
            orderPromotionDetail.setCreateUserId(userId);
            orderPromotionDetail.setCreateUserName(username);
            orderPromotionDetail.setUpdateUserId(userId);
            orderPromotionDetail.setUpdateUserName(username);
        }

        baseMapper.saveBatch(orderPromotionDetails);
    }

    /**
     * 新增订单使用活动明细
     */
    @Override
    public void saveOrderPromotionDetail(OrderPromotionDetail orderPromotionDetail) {
        orderPromotionDetail.setCreateUserId(SecurityUtils.getUserId());
        orderPromotionDetail.setCreateUserName(SecurityUtils.getUsername());
        orderPromotionDetail.setUpdateUserId(SecurityUtils.getUserId());
        orderPromotionDetail.setUpdateUserName(SecurityUtils.getUsername());
        baseMapper.insert(orderPromotionDetail);
    }
}




