package com.wnkx.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchForMeListVO;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.ModelWorkbenchMapper;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/3 10:33
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ModelWorkbenchServiceImpl implements ModelWorkbenchService {

    private final ModelWorkbenchMapper modelWorkbenchMapper;
    /**
     * 视频订单预选模特服务
     */
    private final IOrderVideoMatchPreselectModelService orderVideoPreselectModelService;
    /**
     * 订单服务
     */
    private final IOrderService orderService;
    /**
     * 订单服务
     */
    private final IOrderVideoService orderVideoService;
    private final OrderVideoProperties orderVideoProperties;
    private final OrderVideoMatchService orderVideoMatchService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;
    private final RedisService redisService;


    /**
     * 模特端-首页-forMe列表数量统计
     */
    @Override
    public Long getForMeCount() {
        return modelWorkbenchMapper.getForMeCount(SecurityUtils.getUserId(), orderVideoProperties.getPreselectModelOverTime());
    }

    /**
     * 模特端-工作台  模特拒绝订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pass(Long videoId, Long preselectModelId, String remark) {
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = orderVideoMatchPreselectModelService.getById(preselectModelId);
        Assert.notNull(orderVideoMatchPreselectModel, "Order does not exist, please try again later!");// 订单不存在，请稍后重试

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId()), "The system is busy. Please try again later");

            checkModelYesPass(orderVideoMatchPreselectModel, SecurityUtils.getUserId());
            if (!PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType())) {
                checkModelWorkbench(videoId);
            }

            DateTime dateTime = DateUtil.date();
            orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.OUT.getCode());
            orderVideoMatchPreselectModel.setRemark(remark);
            orderVideoMatchPreselectModel.setOustType(PreselectModelOustTypeEnum.WANT_NOT.getCode());
            orderVideoMatchPreselectModel.setOustTime(dateTime);
            orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.WANT_NOT.getCode());
            orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.REJECT.getCode());
            orderVideoMatchPreselectModel.setSelectTime(dateTime);

            if (DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult())) {
                orderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.WANT_NOT.getCode());
                orderVideoMatchPreselectModel.setDistributionResultTime(dateTime);
            }
            orderVideoPreselectModelService.updateById(orderVideoMatchPreselectModel);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId());
        }
    }

    /**
     * 模特端-工作台  模特接单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void accept(Long videoId, Long preselectModelId) {
        Long curModelId = SecurityUtils.getUserId();
        OrderVideoMatchPreselectModel orderVideoMatchPreselectModel = orderVideoMatchPreselectModelService.getById(preselectModelId);
        Assert.notNull(orderVideoMatchPreselectModel, "Order does not exist, please try again later!");//    订单不存在，请稍后重试
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId()), "The system is busy. Please try again later");

            checkModelYesPass(orderVideoMatchPreselectModel, curModelId);
            if (!PreselectModelAddTypeEnum.DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getAddType())) {
                checkModelWorkbench(videoId);
            }

            DateTime dateTime = DateUtil.date();
            OrderVideo orderVideo = orderVideoService.getById(videoId);
            //  校验当前模特能否接单
            Collection<Long> cannotModelIds = orderService.checkModelCanAccept(Collections.singletonList(curModelId), orderVideo.getCreateOrderBizUserId());
            Assert.isTrue(CollUtil.isEmpty(cannotModelIds), "You are currently unable to accept your order!");//    您当前无法接单

            orderVideoMatchPreselectModel.setModelIntention(ModelIntentionEnum.WANT.getCode());
            orderVideoMatchPreselectModel.setSelectStatus(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode());
            orderVideoMatchPreselectModel.setSelectTime(dateTime);
            if (DistributionResultEnum.PENDING.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult())) {
                orderVideoMatchPreselectModel.setStatus(PreselectStatusEnum.UN_JOINTED.getCode());
                orderVideoMatchPreselectModel.setDistributionResult(DistributionResultEnum.WANT.getCode());
                orderVideoMatchPreselectModel.setDistributionResultTime(dateTime);
            }
            orderVideoPreselectModelService.updateById(orderVideoMatchPreselectModel);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_MATCH_LOCK_KEY + orderVideoMatchPreselectModel.getMatchId());
        }
    }

    private void checkModelYesPass(OrderVideoMatchPreselectModel orderVideoMatchPreselectModel, Long curModelId) {
        OrderVideoMatchPreselectModel selectedModelByMatchId = orderVideoMatchPreselectModelService.getSelectedModelByMatchId(orderVideoMatchPreselectModel.getMatchId());
        Assert.isFalse(ObjectUtil.isNotNull(selectedModelByMatchId) && !selectedModelByMatchId.getModelId().equals(curModelId), "Assigned to another creator.");
        Assert.isFalse(OrderVideoModelSelectStatusEnum.IN_REVIEW.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "Under Review");
        Assert.isFalse(OrderVideoModelSelectStatusEnum.REJECT.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "You have skipped this product");
        Assert.isFalse(OrderVideoModelSelectStatusEnum.CANCEL_APPLY.getCode().equals(orderVideoMatchPreselectModel.getSelectStatus()), "You have cancelled the application");
        Assert.isFalse(DistributionResultEnum.CANCEL_DISTRIBUTION.getCode().equals(orderVideoMatchPreselectModel.getDistributionResult()), "This order is unavailable — expired.");
    }

    /**
     * 模特端-工作台  给我的
     */
    @Override
    public List<OrderModelWorkbenchForMeListVO> forMe(Long videoId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovmpm.add_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<OrderModelWorkbenchForMeListVO> orderModelWorkbenchForMeListVOS = modelWorkbenchMapper.forMe(SecurityUtils.getUserId(), orderVideoProperties.getPreselectModelOverTime(), videoId);
        for (OrderModelWorkbenchForMeListVO orderModelWorkbenchForMeListVO : orderModelWorkbenchForMeListVOS) {
            orderModelWorkbenchForMeListVO.setSurplusPicCount(Math.max((PicCountEnum.getValue(orderModelWorkbenchForMeListVO.getPicCount()) - orderModelWorkbenchForMeListVO.getRefundPicCount()), 0));
            orderModelWorkbenchForMeListVO.setSelectTimeout(DateUtil.offsetHour(orderModelWorkbenchForMeListVO.getAddTime(), orderVideoProperties.getPreselectModelOverTime()));
        }
        return orderModelWorkbenchForMeListVOS;
    }

    /**
     * 校验模特能否处理这笔订单（接单、拒绝）
     */
    private void checkModelWorkbench(Long videoId) {
        List<OrderModelWorkbenchForMeListVO> orderModelWorkbenchForMeListVOS = forMe(videoId);
        Assert.notEmpty(orderModelWorkbenchForMeListVOS, "Assigned to another creator.");
    }
}
