package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.UploadCredentialDTO;
import com.ruoyi.system.api.domain.entity.order.OrderDocumentResource;
import com.wnkx.order.mapper.OrderDocumentResourceMapper;
import com.wnkx.order.service.IOrderDocumentResourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单支付凭证关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
@RequiredArgsConstructor
public class OrderDocumentResourceServiceImpl extends ServiceImpl<OrderDocumentResourceMapper, OrderDocumentResource> implements IOrderDocumentResourceService
{
    private final OrderDocumentResourceMapper orderDocumentResourceMapper;


    /**
     * 上传凭证信息
     */
    @Override
    public List<OrderDocumentResource> uploadCredential(UploadCredentialDTO uploadCredentialDTO) {
        List<OrderDocumentResource> documentResources = uploadCredentialDTO.getObjectKeys().stream().map(objectKey -> {
            OrderDocumentResource documentResource = new OrderDocumentResource();
            documentResource.setUploadType(uploadCredentialDTO.getUploadType());
            documentResource.setOrderNum(uploadCredentialDTO.getOrderNum());
            documentResource.setPayNum(uploadCredentialDTO.getPayNum());
            documentResource.setObjectKey(objectKey);
            return documentResource;
        }).collect(Collectors.toList());

        baseMapper.saveBatch(documentResources);
        return documentResources;
    }

    /**
     * 查询订单支付凭证关联
     *
     * @param id 订单支付凭证关联主键
     * @return 订单支付凭证关联
     */
    @Override
    public OrderDocumentResource selectOrderDocumentResourceById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询订单支付凭证关联列表
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 订单支付凭证关联
     */
    @Override
    public List<OrderDocumentResource> selectOrderDocumentResourceList(OrderDocumentResource orderDocumentResource)
    {
        return orderDocumentResourceMapper.selectOrderDocumentResourceList(orderDocumentResource);
    }

    @Override
    public List<OrderDocumentResource> selectOrderDocumentResourceListByKeyword(String keyword) {
        return orderDocumentResourceMapper.selectOrderDocumentResourceListByKeyword(keyword);
    }

    /**
     * 新增订单支付凭证关联
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    @Override
    public int insertOrderDocumentResource(OrderDocumentResource orderDocumentResource)
    {
        return orderDocumentResourceMapper.insertOrderDocumentResource(orderDocumentResource);
    }

    /**
     * 修改订单支付凭证关联
     *
     * @param orderDocumentResource 订单支付凭证关联
     * @return 结果
     */
    @Override
    public int updateOrderDocumentResource(OrderDocumentResource orderDocumentResource)
    {
        return orderDocumentResourceMapper.updateOrderDocumentResource(orderDocumentResource);
    }

    /**
     * 批量删除订单支付凭证关联
     *
     * @param ids 需要删除的订单支付凭证关联主键
     * @return 结果
     */
    @Override
    public int deleteOrderDocumentResourceByIds(Long[] ids)
    {
        return orderDocumentResourceMapper.deleteOrderDocumentResourceByIds(ids);
    }

    /**
     * 删除订单支付凭证关联信息
     *
     * @param id 订单支付凭证关联主键
     * @return 结果
     */
    @Override
    public int deleteOrderDocumentResourceById(Long id)
    {
        return orderDocumentResourceMapper.deleteOrderDocumentResourceById(id);
    }
}
