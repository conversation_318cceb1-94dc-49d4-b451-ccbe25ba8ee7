package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceDataStatisticsMonth;
import com.wnkx.order.mapper.CustomerServiceDataStatisticsMonthMapper;
import com.wnkx.order.service.CustomerServiceDataStatisticsMonthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 
 * @Date 2025-05-16 09:34:44 
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerServiceDataStatisticsMonthServiceImpl extends ServiceImpl<CustomerServiceDataStatisticsMonthMapper, CustomerServiceDataStatisticsMonth> implements CustomerServiceDataStatisticsMonthService {

    /**
     * 通过记录时间 年月格式 获取模特数据统计_每月记录表
     */
    @Override
    public CustomerServiceDataStatisticsMonth getByWriteTimeMonth(String date) {
        return baseMapper.getByWriteTimeMonth(date);
    }
}
