package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowCompletionModeEnum;
import com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum;
import com.ruoyi.common.core.enums.TaskDetailFlowRecordOperateByTypeEnum;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.task.AfterSaleOrderTaskListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfoTaskDetail;
import com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.WorkOrderTaskInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO;
import com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailListVO;
import com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO;

import java.util.List;

/**
 * 订单_工单任务Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOrderVideoTaskDetailService extends IService<OrderVideoTaskDetail>
{
    /**
     * 工单列表
     */
    List<WorkOrderTaskDetailListVO> selectWorkOrderTaskDetailListByCondition(WorkOrderTaskListDTO dto);

    /**
     * 售后单列表
     * @param dto
     * @return
     */
    List<AfterSaleTaskDetailListVO> selectAfterSaleOrderTaskDetailListByCondition(AfterSaleOrderTaskListDTO dto);

    /**
     * 工作台-剪辑部-任务单列表
     * @return
     */
    List<WorkbenchTaskDetailVO> selectWorkbenchTaskDetailList();

    /**
     * 工作台-剪辑部-拒绝任务列表
     * @return
     */
    List<WorkbenchTaskDetailVO> selectWorkbenchRefuseTaskDetailList();

    /**
     * 创建任务单
     */
    void createTaskDetail(OrderVideoTaskDetail orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum flowOperateTypeEnum);

    /**
     * 查看工单详情
     */
    WorkOrderTaskInfoVO getWorkOrderInfo(String taskNum);

    /**
     * 任务单号
     * @param taskNum
     * @return
     */
    OrderVideoTaskDetail getByTaskNum(String taskNum);

    /**
     * 查看售后单详情
     *
     * @param taskNum
     * @return
     */
    AfterSaleTaskDetailInfoVO getAfterSaleTaskInfo(String taskNum);

    /**
     * 获取所有模特的工单（去重、多个视频订单算一个）
     */
    List<OrderVideoTaskDetail> getModelAllDistinctTask();

    /**
     * 获取所有模特的售后单（重拍视频、补拍视频）（去重、多个视频订单算一个）
     */
    List<OrderVideo> getModelAfterSaleDistinctTask();

    /**
     * 校验任务单是否已存在
     */
    Boolean checkTaskExist(OrderTaskDTO dto);

    /**
     * 申请补偿退款_获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getRefundPendingTask(Long taskId, List<Long> taskDetailId);

    /**
     * 帮模特反馈素材 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getBackHelpModelUploadMaterialPendingTask(Long taskId, List<Long> taskDetailIds);

    /**
     * 反馈素材给商家 _获取视频订单相关联的任务单信息
     */
    List<OrderVideoTaskDetailVO> getFeedbackMaterialPendingTask(List<Long> taskIds, List<Long> taskDetailId, List<Integer> afterSaleClass, List<Long> existingTaskDetailIds, Boolean isToBeEdited);

    /**
     * 通过任务单详情ID及任务单详情流转记录新增流转记录
     */
    void addTaskDetailFlow(List<Long> taskDetailIds, OrderVideoTaskDetailFlowRecordDTO dto);

    /**
     * 校验工单处理人操作权限
     */
    Boolean checkWorkOrderAssigneePermission(Long taskDetailId);

    /**
     * 校验工单处理人操作权限
     */
    Boolean checkWorkOrderAssigneePermission(List<Long> taskDetailIds);

    /**
     * 校验工单提交人操作权限
     */
    Boolean checkWorkOrderSubmitPermission(Long taskDetailId);

    /**
     * 完结工单
     */
    void finishWorkOrder(Long taskDetailId);


    /**
     *  完结售后
     * * @param taskDetailId
     */
    void finishAfterSaleOrder(Long taskDetailId);

    /**
     * 批量完结工单
     */
    void finishWorkOrders(List<Long> taskDetailIds, OrderTaskDetailFlowCompletionModeEnum completionMode, TaskDetailFlowRecordOperateByTypeEnum operateByTypeEnum, List<OrderVideoFeedBackMaterialInfoTaskDetail> materialInfoTaskDetails);

    /**
     * 工单-指派处理人
     */
    void assignHandler(AssignHandlerDTO dto);

    /**
     * 工单-拒绝工单
     */
    void rejectWorkOrder(TaskDetailOperateDTO dto);

    /**
     * 工单-关闭工单
     */
    void closeWorkOrder(Long taskDetailId);

    /**
     * 工单-重新打开
     */
    void reopenWorkOrder(TaskDetailOperateDTO dto);

    /**
     * 工单-新增处理记录
     */
    void addWorkOrderProcessRecord(OrderVideoTaskDetailProcessRecordDTO dto);

    /**
     * 添加流水
     * @param dto
     * @param orderVideoTaskDetail
     * @param operateType
     */
    void taskDetailOperate(TaskDetailOperateDTO dto, OrderVideoTaskDetail orderVideoTaskDetail, OrderTaskDetailFlowOperateTypeEnum operateType);

    /**
     * 通过视频订单ID关闭任务单
     */
    void closeTaskByVideoIds(List<Long> taskIds, OrderTaskDetailFlowOperateTypeEnum operateTypeEnum);

    /**
     * 通过视频订单ID查询任务单（部分字段）
     */
    List<OrderVideoTaskDetail> selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart(List<Long> videoIds);

    /**
     * 通过日期 获取中文部客服新增/完成任务单数量
     */
    List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceTaskCountByDate(String date);
}
