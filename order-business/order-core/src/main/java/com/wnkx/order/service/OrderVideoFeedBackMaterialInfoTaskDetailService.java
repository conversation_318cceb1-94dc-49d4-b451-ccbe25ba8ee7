package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBackMaterialInfoTaskDetail;
import com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailSimpleVO;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-20 15:02:23 
 */
public interface OrderVideoFeedBackMaterialInfoTaskDetailService extends IService<OrderVideoFeedBackMaterialInfoTaskDetail> {

    /**
     * 通过模特反馈素材ID和视频订单ID和回退ID查询视频订单关联的模特反馈素材详情任务
     */
    List<OrderVideoFeedBackMaterialInfoTaskDetail> selectListByMaterialInfoIdAndVideoIdAndRollbackId(Long materialInfoId, Long videoId, Long rollbackId);

    /**
     * 通过视频订单ID和回退ID完结任务单
     */
    void finishTaskByVideoIdAndRollbackId(Long videoId, Long rollbackId);

    /**
     * 通过视频订单ID完结任务单
     */
    void finishTaskByVideoIds(List<Long> videoIds);

    /**
     * 通过反馈ID查询视频订单关联的模特反馈素材详情任务
     */
    List<OrderVideoTaskDetailSimpleVO> selectListByFeedBackIds(List<Long> feedBackIds);
}
