package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.order.ShippingDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.OperationVideoLogisticDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 订单_视频_物流关联Service接口
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface IOrderVideoLogisticService extends IService<OrderVideoLogistic> {
    /**
     * 通过视频订单id获取订单关联物流
     *
     * @param videoId 视频订单id
     * @return 订单关联物流
     */
    List<OrderVideoLogistic> selectListByVideoId(Collection<Long> videoId);

    /**
     * 添加视频订单与物流关联信息
     */
    OrderVideoLogistic saveVideoLogistic(ShippingDTO shippingDTO);

    /**
     * 通过物流单号查询订单关联物流的视频id
     */
    List<Long> selectVideoIdsByLogisticNumber(Collection<String> numbers);

    /**
     * 通过视频订单id获取首次发货物流单号
     */
    List<OrderVideoLogistic> selectFirstListByVideoId(List<Long> videoIds);

    /**
     * 根据物流单号获取物流信息列表
     * @param number
     * @return
     */
    List<OrderVideoLogistic> selectListByNumber(String number);

    /**
     * 操作物流单号
     * @param dto
     */
    void operationVideoLogistic(OperationVideoLogisticDTO dto);

    /**
     * 查询模特收件信息有几个发货物流
     */
    Long selectCountByShippingAddressId(Long shippingAddressId);

    /**
     * 确认收货
     *
     * @param logisticId
     */
    void confirmReceipt(Long logisticId, Date signTime);

    /**
     * 通过视频订单id及确认收货状态获取首次发货物流单号
     *
     * @param videoIds
     * @param receipt
     * @return
     */
    @Deprecated
    List<OrderVideoLogistic> selectLastListByDto(List<Long> videoIds, Integer receipt);

    /**
     * 通过条件查询订单关联物流的视频id
     * @param videoIds
     * @param receipt
     * @return
     */
    Collection<Long> selectLastVideoIdsByDto(Collection<Long> videoIds, Integer receipt);

    /**
     * 通过模特收件地址ID获取发货物流
     */
    List<OrderVideoLogistic> selectListByShippingAddressId(Long shippingAddressId);
}
