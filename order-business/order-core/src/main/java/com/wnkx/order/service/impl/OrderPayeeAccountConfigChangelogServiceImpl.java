package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigChangelogDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigChangelog;
import com.wnkx.order.service.OrderPayeeAccountConfigChangelogService;
import com.wnkx.order.mapper.OrderPayeeAccountConfigChangelogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【order_payee_account_config_changelog(收款人账号关联表)】的数据库操作Service实现
 * @createDate 2024-12-17 16:04:06
 */
@Service
public class OrderPayeeAccountConfigChangelogServiceImpl extends ServiceImpl<OrderPayeeAccountConfigChangelogMapper, OrderPayeeAccountConfigChangelog>
        implements OrderPayeeAccountConfigChangelogService {

    @Override
    public void saveNewChangeLog(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO) {
        baseMapper.saveNewChangeLog(orderPayeeAccountConfigInfoDTO);
    }

    @Override
    public List<OrderPayeeAccountConfigChangelogDTO> historyLog(Long type) {
        return baseMapper.historyLog(type);
    }

    @Override
    public void saveUpdateChangeLog(String oldCompanyName, String companyName, Integer type) {
        baseMapper.saveUpdateChangeLog(oldCompanyName, companyName, type);
    }

    @Override
    public void saveModifyChangeLog(String name, Integer type) {
        baseMapper.saveModifyChangeLog(name, type);
    }
}




