package com.wnkx.order.service;

import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.CustomerServiceBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 9:39
 */
public interface CustomerServiceDataStatisticsService {

    /**
     * 客服数据-基础看板
     */
    CustomerServiceBaseBoardVO getCustomerServiceBaseBoard();

    /**
     * 客服数据-中文部客服数据
     */
    List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData(Date beginTime, Date endTime);

    /**
     * 客服数据-英文部客服数据
     */
    List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData(Date beginTime, Date endTime);
}
