package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLinkRecord;
import com.ruoyi.system.api.domain.vo.order.HistoryUploadRecordVO;

import java.util.List;

/**
 * <AUTHOR> 
 * @Date 2025-03-20 15:22:41 
 */
public interface OrderVideoUploadLinkRecordService extends IService<OrderVideoUploadLinkRecord> {

    /**
     * 剪辑管理-历史上传记录
     */
    List<HistoryUploadRecordVO> getHistoryUploadRecord(Long uploadLinkId);

    /**
     * 新增上传记录
     */
    void insertUploadLinkRecord(OrderVideoUploadLinkRecord orderVideoUploadLinkRecord);

    /**
     * 更新最新的上传记录
     */
    void updateLatestUploadLinkRecord(OrderVideoUploadLinkRecord orderVideoUploadLinkRecord);

    /**
     * 通过上传链接ID获取未上传的记录
     */
    OrderVideoUploadLinkRecord getHavenTUploadedByUploadLinkId(Long uploadLinkId);
}
