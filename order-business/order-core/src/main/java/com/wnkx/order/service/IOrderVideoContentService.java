package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.order.OrderVideoContent;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 视频_关联内容Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public interface IOrderVideoContentService extends IService<OrderVideoContent>
{

    /**
     * 新增视频_关联内容
     *
     * @param orderVideoContents 视频_关联内容
     * @return 结果
     */
    public void saveBatchVideoContent(Collection<Long> videoIds, Collection<Integer> types, List<OrderVideoContent> orderVideoContents, Integer isFirstContent);

    /**
     * 根据视频id或内容类型查询关联内容
     *
     * @param id 视频订单id
     * @return 关联内容
     */
    List<OrderVideoContent> selectListByVideoIdOrType(Long id, Integer contentType);

    /**
     * 根据视频id或内容类型查询关联内容
     *
     * @param id 视频订单id
     * @return 关联内容
     */
    List<OrderVideoContent> selectListByVideoIdOrTypes(Long id, List<Integer> contentTypes);

    /**
     * 根据视频id查询关联内容
     *
     * @param videoIds 视频订单id
     * @return 关联内容
     */
    List<OrderVideoContent> selectListByVideoIds(List<Long> videoIds);


    List<OrderVideoContent> selectListByVideoIdsAndTypesAsc(List<Long> videoIds, List<Integer> contentTypes);

    /**
     * 获取视频订单内容对象 Map<类型, entity>
     * @param videoIds
     * @param contentTypes
     * @return
     */
    Map<Integer, OrderVideoContent> selectMapByVideoIdsAndTypesAsc(List<Long> videoIds, List<Integer> contentTypes);

    /**
     * 变更调字内容
     * @param videoId
     */
    void rollbackOrderVideoContent(Long videoId);
}
