package com.wnkx.order.service;

import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfigInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【order_payee_account_config_info(收款人账号配置表)】的数据库操作Service
* @createDate 2024-12-17 16:04:06
*/
public interface OrderPayeeAccountConfigInfoService extends IService<OrderPayeeAccountConfigInfo> {

   default Long saveInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO, Long id){
        OrderPayeeAccountConfigInfo orderPayeeAccountConfigInfo = new OrderPayeeAccountConfigInfo();
        orderPayeeAccountConfigInfo.setAccountName(orderPayeeAccountConfigInfoDTO.getAccountName());
        orderPayeeAccountConfigInfo.setPayeeId(id);
        orderPayeeAccountConfigInfo.setStatus(orderPayeeAccountConfigInfoDTO.getStatus());
        orderPayeeAccountConfigInfo.setType(orderPayeeAccountConfigInfoDTO.getType());
        initData(orderPayeeAccountConfigInfoDTO, orderPayeeAccountConfigInfo);
        orderPayeeAccountConfigInfo.setCreateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigInfo.setCreateById(SecurityUtils.getUserId());
        orderPayeeAccountConfigInfo.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigInfo.setUpdateById(SecurityUtils.getUserId());
        save(orderPayeeAccountConfigInfo);
        return orderPayeeAccountConfigInfo.getId();
    }

    private void initData(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO, OrderPayeeAccountConfigInfo orderPayeeAccountConfigInfo) {
        orderPayeeAccountConfigInfo.setAccountName(orderPayeeAccountConfigInfoDTO.getAccountName());
        orderPayeeAccountConfigInfo.setBankAccount(orderPayeeAccountConfigInfoDTO.getBankAccount());
        orderPayeeAccountConfigInfo.setBankName(orderPayeeAccountConfigInfoDTO.getBankName());
        orderPayeeAccountConfigInfo.setCompanyAccountType(orderPayeeAccountConfigInfoDTO.getCompanyAccountType());
        orderPayeeAccountConfigInfo.setCompanyBankCode(orderPayeeAccountConfigInfoDTO.getCompanyBankCode());
        orderPayeeAccountConfigInfo.setCompanyBankName(orderPayeeAccountConfigInfoDTO.getCompanyBankName());
        orderPayeeAccountConfigInfo.setCompanyBankAddress(orderPayeeAccountConfigInfoDTO.getCompanyBankAddress());
        orderPayeeAccountConfigInfo.setCompanyBankSubCode(orderPayeeAccountConfigInfoDTO.getCompanyBankSubCode());
        orderPayeeAccountConfigInfo.setCompanyBankSwiftCode(orderPayeeAccountConfigInfoDTO.getCompanyBankSwiftCode());
        orderPayeeAccountConfigInfo.setCompanyBankPayeeAddress(orderPayeeAccountConfigInfoDTO.getCompanyBankPayeeAddress());
    }

    default Long updateInfo(OrderPayeeAccountConfigInfoDTO orderPayeeAccountConfigInfoDTO,Long id){
        OrderPayeeAccountConfigInfo orderPayeeAccountConfigInfo = new OrderPayeeAccountConfigInfo();
        orderPayeeAccountConfigInfo.setAccountName(orderPayeeAccountConfigInfoDTO.getAccountName());
        orderPayeeAccountConfigInfo.setPayeeId(id);
        orderPayeeAccountConfigInfo.setType(orderPayeeAccountConfigInfoDTO.getType());
        initData(orderPayeeAccountConfigInfoDTO, orderPayeeAccountConfigInfo);
        orderPayeeAccountConfigInfo.setUpdateBy(SecurityUtils.getUsername());
        orderPayeeAccountConfigInfo.setUpdateById(SecurityUtils.getUserId());
        save(orderPayeeAccountConfigInfo);
        return orderPayeeAccountConfigInfo.getId();
    }

    OrderPayeeAccountConfigInfoDTO getByBankAccount(String appId);
}
