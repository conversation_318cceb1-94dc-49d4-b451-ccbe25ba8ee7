package com.wnkx.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.AlipayOrderTable;
import com.wnkx.order.mapper.AlipayOrderTableMapper;
import com.wnkx.order.service.AlipayOrderTableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AlipayOrderTableServiceImpl extends ServiceImpl<AlipayOrderTableMapper, AlipayOrderTable> implements AlipayOrderTableService {


    /**
     * 根据内部订单号获取订单*
     */
    @Override
    public AlipayOrderTable getAlipayOrderTableByOutTradeNo(String out_trade_no) {
        return baseMapper.getAlipayOrderTableByOutTradeNo(out_trade_no);
    }

    /**
     * 根据订单号无效支付宝订单数据*
     *
     * @param order_num
     */
    @Override
    public void banAlipayOrderTableByOrderNum(String order_num) {
        baseMapper.banAlipayOrderTableByOrderNum(order_num);
    }

    /**
     * 根据orderNUm获取订单列表*
     *
     * @param order_num
     */
    @Override
    public List<AlipayOrderTable> getValidAlipayOrderTableList(String order_num) {
        return baseMapper.getValidAlipayOrderTableList(order_num);
    }

    /**
     * 获取有效订单*
     */
    @Override
    public AlipayOrderTable getValidAlipayOrderTableByOutTradeNo(String out_trade_no) {
        return baseMapper.getValidAlipayOrderTableByOutTradeNo(out_trade_no);
    }

    /**
     * 设置订单为无效*
     */
    @Override
    public void banFyOrderTable(Long id) {
        baseMapper.banFyOrderTable(id);
    }

    /**
     * 获取有效订单*
     */
    @Override
    public AlipayOrderTable getValidAlipayOrderTableByOrderNum(String orderNum) {
        return baseMapper.getValidAlipayOrderTableByOrderNum(orderNum);
    }
}
