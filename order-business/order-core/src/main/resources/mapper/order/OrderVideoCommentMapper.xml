<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoCommentMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderVideoComment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="videoId" column="video_id" jdbcType="VARCHAR"/>
            <result property="commentContent" column="comment_content" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,video_id,comment_content,
        create_by,create_user_id,create_time,
        update_time
    </sql>
    <select id="getVideoIds" resultType="java.lang.Long">
        SELECT DISTINCT
            ( video_id )
        FROM
            order_video_comment
    </select>
</mapper>
