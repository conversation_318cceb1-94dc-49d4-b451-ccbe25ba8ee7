<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.FyOrderTableMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.FyOrderTable">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mchntOrderNo" column="mchnt_order_no" jdbcType="VARCHAR"/>
            <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
            <result property="qrcode" column="qrcode" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="BOOLEAN"/>
            <result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,mchnt_order_no,order_number,
        qrcode,platform,order_amount,
        status,create_time
    </sql>
</mapper>
