<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.PayeeAccountConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.PayeeAccountConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="accountName" column="company_name" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,company_name,bank_name,
        bank_account,status,create_time,
        update_time
    </sql>
</mapper>
