<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoFeedBackMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="videoId" column="video_id" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="videoScore" column="video_score" jdbcType="TINYINT"/>
            <result property="videoScoreContent" column="video_score_content" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,video_id,url,
        video_score,video_score_content,create_by,
        create_user_id,create_time,update_time
    </sql>

    <select id="selectVideoScoreListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.VideoScoreListVO">
        SELECT
            ovfb.id,
            ov.id AS video_id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic AS reference_pic_id,
            ovmc.model_id AS shoot_model_id,
            ovfb.type,
            ovfb.video_url,
            ovfb.pic_url,
            ovfb.video_score,
            ovfb.video_score_content,
            ovfb.video_score_by_id,
            ovfb.video_score_time
        FROM
            order_video_feed_back ovfb
                JOIN order_video ov ON ov.id = ovfb.video_id
                LEFT JOIN (
                    SELECT
                        video_id,
                        rollback_id,
                        model_id
                    FROM
                        (
                        SELECT
                            video_id,
                            rollback_id,
                            model_id,
                            ROW_NUMBER() OVER ( PARTITION BY video_id, IFNULL( rollback_id, 0 ) ORDER BY selected_time DESC ) AS rn
                        FROM
                            order_video_model_change
                        WHERE
                            source = ${@com.ruoyi.common.core.enums.OrderVideoModelChangeSourceEnum@SHOOT_MODEL.getCode}
                        ) t
                    WHERE
                        rn = 1
                ) ovmc ON ovmc.video_id = ovfb.video_id AND IFNULL( ovmc.rollback_id, 0 ) = IFNULL( ovfb.rollback_id, 0 )
        <where>
            ovfb.video_score IS NOT NULL

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                ov.video_code LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_chinese LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_english LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_link LIKE CONCAT('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.shootModelIds != null and dto.shootModelIds.size() > 0 ">
                AND ov.shoot_model_id IN
                <foreach collection="dto.shootModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelType != null">
                AND ov.model_type = #{dto.modelType}
            </if>

            <if test="dto.videoScoreByIds != null and dto.videoScoreByIds.size() > 0 ">
                AND ovfb.video_score_by_id IN
                <foreach collection="dto.videoScoreByIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.videoScores != null and dto.videoScores.size() > 0 ">
                AND
                <foreach collection="dto.videoScores" item="item" open="(" separator=" OR " close=")">
                    ovfb.video_score = #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getShootModelId" resultType="java.lang.Long">
        SELECT
            ov.shoot_model_id
        FROM
            order_video_feed_back ovfb
                JOIN order_video ov ON ov.id = ovfb.video_id
        WHERE
            ovfb.video_score IS NOT NULL
    </select>
    <select id="selectLatestFeedBackListByVideoIds" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoFeedBack">
        WITH LatestVideos AS (
            SELECT
            video_id,
            video_url,
            ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY create_time DESC) AS rn
            FROM
            order_video_feed_back
            WHERE
                video_id IN
                <foreach collection="videoIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            AND video_url IS NOT NULL
        ),
        LatestPics AS (
            SELECT
            video_id,
            pic_url,
            ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY create_time DESC) AS rn
            FROM
            order_video_feed_back
            WHERE
                video_id IN
                <foreach collection="videoIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
                AND pic_url IS NOT NULL
        )
        SELECT
            t.video_id,
            lv.video_url,
            lp.pic_url
        FROM (
            SELECT DISTINCT video_id
            FROM order_video_feed_back
            WHERE video_id IN
            <foreach collection="videoIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        ) AS t
        LEFT JOIN LatestVideos lv ON t.video_id = lv.video_id AND lv.rn = 1
        LEFT JOIN LatestPics lp ON t.video_id = lp.video_id AND lp.rn = 1;
    </select>
</mapper>
