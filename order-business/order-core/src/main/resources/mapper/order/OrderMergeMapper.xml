<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderMergeMapper">

    <sql id="selectNeedPayOrderListCommonCondition">
        AND ot.submit_credential_time IS NULL
        AND ot.pay_time IS NULL
        AND ot.close_order_time IS NULL

        <if test="dto.keyword != null and dto.keyword != ''">
            and (
            ov.order_num like concat('%', #{dto.keyword}, '%')
            OR ov.video_code like concat('%', UCASE(#{dto.keyword}), '%')
            OR ov.product_chinese like concat('%', #{dto.keyword}, '%')
            OR ov.product_english like concat('%', #{dto.keyword}, '%')
            OR ov.product_link like concat('%', #{dto.keyword}, '%')
            OR ov.create_order_user_nick_name like concat('%', #{dto.keyword}, '%')
            OR ov.create_order_user_name like concat('%', #{dto.keyword}, '%')
            <if test="dto.modelIds != null and dto.modelIds.size() != 0">
                OR ov.intention_model_id in
                <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR ov.shoot_model_id in
                <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>

                <if test="dto.loginUserType == 0">
                    OR ov.id in (
                    select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_id in
                    <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>

            <if test="dto.loginUserType == 0">
                <if test="dto.platform != null and dto.platform.size() != 0">
                    OR ov.platform in
                    <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                OR ov.create_order_user_account like concat('%', #{dto.keyword}, '%')
                OR ot.merchant_code like concat('%', UCASE(#{dto.keyword}), '%')
                <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() >0">
                    OR ot.merchant_id in
                    <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            )
        </if>

        <if test="dto.merchantIds != null and dto.merchantIds.size() != 0">
            AND ot.merchant_id IN
            <foreach collection="dto.merchantIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.createOrderUserName != null and dto.createOrderUserName.size() > 0 ">
            AND ov.create_order_user_name IN
            <foreach collection="dto.createOrderUserName" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.platform != null and dto.platform.size() != 0">
            AND ov.platform IN
            <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.orderTimeBegin != null and dto.orderTimeEnd != null">
            and ot.order_time BETWEEN #{dto.orderTimeBegin} AND #{dto.orderTimeEnd}
        </if>

        <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
            and ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
        </if>
    </sql>

    <select id="getOrderNumsByMergeIdOrPayNum" resultType="java.lang.String">
        SELECT
            omd.order_num
        FROM
            order_merge om
                JOIN order_merge_detail omd ON omd.merge_id = om.id
        WHERE
            om.status = ${@<EMAIL>}
            <if test="mergeId != null">
                AND om.id = #{mergeId}
            </if>
            <if test="payNum != null and payNum != ''">
                AND om.pay_num = #{payNum}
            </if>
    </select>

    <select id="checkOrderMerge" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN TRUE ELSE FALSE END
        FROM
            order_merge om
                JOIN order_merge_detail omd ON omd.merge_id = om.id
        WHERE
            om.status != ${@<EMAIL>}
            AND omd.order_num IN
            <foreach collection="orderNums" item="orderNum" open="(" separator="," close=")">
                #{orderNum}
            </foreach>
    </select>

    <select id="selectNeedPayOrderListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderMergeListVO">
        SELECT
            merge_id,
            merge_nick_by,
            merge_by,
            merge_by_id,
            merge_time,
            order_nums
        FROM (
                 SELECT
                     om.id AS merge_id,
                     om.merge_nick_by,
                     om.merge_by,
                     om.merge_by_id,
                     om.merge_time,
                     GROUP_CONCAT( DISTINCT (ot.order_num) ) AS order_nums,
                     NULL AS order_time_sign,
                     om.create_time
                 FROM order_merge om
                          INNER JOIN order_merge_detail omd ON om.id = omd.merge_id
                          INNER JOIN order_table ot ON omd.order_num = ot.order_num
                          INNER JOIN order_video ov ON ov.order_num = ot.order_num
                <where>
                     om.`status` = ${@<EMAIL>}
                     <include refid="selectNeedPayOrderListCommonCondition"/>
                </where>
                 GROUP BY om.id

                 UNION ALL

                 SELECT
                     NULL AS merge_id,
                     NULL AS merge_nick_by,
                     NULL AS merge_by,
                     NULL AS merge_by_id,
                     NULL AS merge_time,
                     ot.order_num AS order_nums,
                     ot.order_time_sign AS order_time_sign,
                     ot.create_time
                 FROM order_table ot
                          INNER JOIN order_video ov ON ov.order_num = ot.order_num
                          LEFT JOIN (
                            SELECT
                                omd.order_num,
                                MIN(om.status) AS min_status
                            FROM
                                order_merge_detail omd
                                    INNER JOIN order_merge om ON omd.merge_id = om.id
                            GROUP BY
                                omd.order_num
                          ) t ON ot.order_num = t.order_num
                <where>
                     (t.min_status IS NULL OR t.min_status = ${@<EMAIL>})
                     AND ot.order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getCode}
                    <include refid="selectNeedPayOrderListCommonCondition"/>
                </where>
                GROUP BY ot.id
             ) need_pay_order
        ORDER BY
            CASE WHEN merge_id IS NOT NULL THEN 1 ELSE 0 END DESC,
            merge_time DESC,
            order_time_sign DESC,
            create_time DESC
    </select>

    <select id="selectOrderMergeNormalListByOrderNums"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderMergeListVO">
        SELECT
            om.id AS merge_id,
            om.merge_by,
            om.merge_time,
            GROUP_CONCAT( DISTINCT ( ot.order_num ) ) AS order_nums,
            NULL AS order_time_sign,
            om.create_time
        FROM
            order_merge om
                INNER JOIN order_merge_detail omd ON om.id = omd.merge_id
                INNER JOIN order_table ot ON omd.order_num = ot.order_num
                INNER JOIN order_video ov ON ov.order_num = ot.order_num
        WHERE
            om.`status` = ${@<EMAIL>}
            AND ot.submit_credential_time IS NULL
            AND ot.pay_time IS NULL
            AND ot.close_order_time IS NULL
            <if test="orderNums != null and orderNums.size() > 0">
                AND omd.order_num IN
                <foreach collection="orderNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        GROUP BY
            om.id
    </select>
    <select id="getMergeOrderNumsByOrderNum" resultType="java.lang.String">
        SELECT
            omd.order_num
        FROM
            order_merge_detail omd
        WHERE
            omd.merge_id = (
                SELECT
                    om.id
                FROM
                    order_merge om
                        JOIN order_merge_detail omd ON omd.merge_id = om.id
                WHERE
                    om.`status` = ${@<EMAIL>}
                  AND omd.order_num = #{orderNum}
                )
    </select>
</mapper>
