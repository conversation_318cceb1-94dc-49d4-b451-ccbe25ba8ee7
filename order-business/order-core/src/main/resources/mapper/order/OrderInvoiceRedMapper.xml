<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderInvoiceRedMapper">

<!--    运营端-发票管理-待红冲列表-->
    <select id="selectToBeRedInvoiceListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.ToBeRedInvoiceListVO">
        SELECT
            oir.id,
            oir.invoice_id,
            oi.ticket_code,
            oi.number,
            oi.merchant_code,
            oi.order_type,
            oi.invoice_type,
            oi.title_type,
            oi.title,
            oi.duty_paragraph,
            oi.company_name,
            oi.company_address,
            oi.company_phone,
            oi.company_contact,
            oi.attachment_object_key,
            oi.content,
            oi.invoice_remark,
            oi.cautions,
            oi.invoice_amount,
            oir.invoice_red_cause,
            oir.apply_time,
            oir.invoice_red_status
        FROM
            order_invoice_red oir
                LEFT JOIN order_invoice oi ON oi.id = oir.invoice_id
        <where>
            oir.invoice_red_status = ${@com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum@WAITING_TO_BE_FLUSHED.getCode}
            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    oi.ticket_code LIKE CONCAT('%',UCASE(#{dto.keyword}),'%')
                    OR oi.number LIKE CONCAT('%',UCASE(#{dto.keyword}),'%')
                    OR oi.merchant_code LIKE CONCAT('%',UCASE(#{dto.keyword}),'%')
                    OR oir.id IN (  SELECT oiro.invoice_red_id
                                    FROM order_invoice_red_order oiro
                                        JOIN order_invoice_red_order_video oirov ON oirov.invoice_red_order_id = oiro.id
                                    WHERE oirov.video_code LIKE CONCAT('%',UCASE(#{dto.keyword}),'%'))
                )
            </if>

            <if test="dto.invoiceRedCause != null ">
                AND oir.invoice_red_cause = #{dto.invoiceRedCause}
            </if>

            <if test="dto.orderType != null ">
                AND oi.order_type = #{dto.orderType}
            </if>

            <if test="dto.applyTimeBegin != null and dto.applyTimeEnd != null">
                AND oir.apply_time BETWEEN #{dto.applyTimeBegin} AND #{dto.applyTimeEnd}
            </if>
        </where>
    </select>
<!--    获取待红冲数量-->
    <select id="getToBeRedInvoiceCount" resultType="java.lang.Long">
        SELECT
            SUM(CASE WHEN invoice_red_status = ${@com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum@WAITING_TO_BE_FLUSHED.getCode} THEN 1 ELSE 0 END) AS quantityToBeFlushed
        FROM
            order_invoice_red
        WHERE
            invoice_red_status = ${@com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum@WAITING_TO_BE_FLUSHED.getCode}
    </select>
<!--    获取待红冲金额-->
    <select id="getToBeRedInvoiceAmount" resultType="java.math.BigDecimal">
        SELECT
            SUM( oi.invoice_amount ) AS quantityToBeFlushed
        FROM
            order_invoice_red oir
                LEFT JOIN order_invoice oi ON oi.id = oir.invoice_id
        WHERE
            oir.invoice_red_status = ${@com.ruoyi.common.core.enums.OrderInvoiceRedStatusEnum@WAITING_TO_BE_FLUSHED.getCode}
    </select>
</mapper>
