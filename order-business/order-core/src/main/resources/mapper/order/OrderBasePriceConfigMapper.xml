<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderBasePriceConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderBasePriceConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="priceType" column="price_type" jdbcType="TINYINT"/>
            <result property="originPrice" column="origin_price" jdbcType="DECIMAL"/>
            <result property="originPriceProxy" column="origin_price_proxy" jdbcType="DECIMAL"/>
            <result property="currentPrice" column="current_price" jdbcType="DECIMAL"/>
            <result property="currentPriceProxy" column="current_price_proxy" jdbcType="DECIMAL"/>
            <result property="sinceTime" column="since_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,price_type,origin_price,
        origin_price_proxy,current_price,current_price_proxy,
        since_time,create_by,create_by_id,
        create_time,update_by,update_by_id,
        update_time
    </sql>
</mapper>
