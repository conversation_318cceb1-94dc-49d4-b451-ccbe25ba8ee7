<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPayeeAccountMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="accountType" column="account_type" jdbcType="BOOLEAN"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_num,account_type,
        account_name,bank_name,bank_account,
        create_time,update_time
    </sql>
</mapper>
