<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderMemberMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderMember">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="BIGINT"/>
            <result property="packageType" column="package_type" jdbcType="BIGINT"/>
            <result property="memberStartTime" column="member_start_time" jdbcType="TIMESTAMP"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_num,status,
        package_type,member_start_time,create_user_id,create_user_name,
        create_time,update_user_id,update_user_name,
        update_time
    </sql>

    <sql id="Base_Order_Member">
        select
        om.id,om.order_num, om.status, om.package_type, om.member_start_time, om.member_end_time,om.biz_user_id,om.presented_time,om.presented_time_type,ot.id as orderId, ot.tax_point,ot.tax_point_cost,
        ot.video_count, ot.order_amount, ot.pay_amount,ot.pay_amount_dollar,ot.currency, ot.real_pay_amount,ot.real_pay_amount_currency, ot.current_exchange_rate, ot.use_balance,ot.difference_amount,ot.submit_credential_time,ot.order_promotion_amount,
        ot.order_user_id, ot.order_time, ot.pay_user_id, ot.pay_time, ot.is_default_exchange_rate, ot.pay_account, ot.pay_type,ot.pay_type_detail, ot.audit_status,ot.audit_time,ot.audit_user_name,
        ot.order_remark, ot.merchant_id, ot.merchant_code,ot.seed_code_discount,ot.seed_code,ot.seed_id,ot.seed_member_status,ot.member_discount_type,ot.settle_rage,ot.channel_name, om.is_first_buy, ot.channel_type,ot.order_user_nick_name
    </sql>

    <sql id="sqlwhereSearch">
        <where>
            ot.order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIP_ORDER.getcode}
            <if test="dto.packageType != null"> and om.package_type = #{dto.packageType}</if>
            <if test="dto.isFilterClose != null"> and om.status != ${@com.ruoyi.common.core.enums.OrderMemberStatusEnum@UN_MATCH.getcode}</if>
            <if test="dto.orderNum != null and dto.orderNum != ''"> and om.order_num like concat('%', #{dto.orderNum}, '%')</if>
            <if test="dto.memberCode != null and dto.memberCode != ''"> and ot.merchant_code like concat('%', #{dto.memberCode}, '%')</if>
            <if test="dto.statusList != null and dto.statusList.size() != 0">
                and om.status in
                <foreach collection="dto.statusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.bizUserIds != null and dto.bizUserIds.size() != 0">
                and ot.biz_user_id in
                <foreach collection="dto.bizUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.auditStatusList != null and dto.auditStatusList.size() != 0">
                and ot.audit_status in
                <foreach collection="dto.auditStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and ot.merchant_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.isReceivableAudit != null and dto.isReceivableAudit == 1">
                and ot.submit_credential_time is not null
            </if>
            <if test="dto.payTypes != null and dto.payTypes.size() != 0">
                and ot.pay_type in
                <foreach collection="dto.payTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.payTypeDetails != null and dto.payTypeDetails.size() != 0">
                and ot.pay_type_detail in
                <foreach collection="dto.payTypeDetails" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.orderTimeBegin != null and dto.orderTimeEnd != null">
                and ot.order_time BETWEEN #{dto.orderTimeBegin} AND #{dto.orderTimeEnd}
            </if>

            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                and ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
            </if>
            <if test="dto.searchName != null and dto.searchName != ''"> and (
                ot.pay_account like concat('%', #{dto.searchName}, '%')
                or ot.order_num like concat('%', #{dto.searchName}, '%')
                <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() != 0">
                    or ot.merchant_id in
                    <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )</if>
            <if test="dto.isFirstBuy != null">
                and om.is_first_buy = #{dto.isFirstBuy}
            </if>

            <if test="dto.promotionActivityTypes != null and dto.promotionActivityTypes.size() > 0">
                AND (
                    EXISTS (
                        SELECT
                            1
                        FROM
                            order_promotion_detail opd
                                JOIN promotion_activity pa ON pa.id = opd.activity_id
                        WHERE
                            opd.order_num = ot.order_num
                            AND pa.type IN
                            <foreach collection="dto.promotionActivityTypes" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                    )
                    <if test="dto.promotionActivityTypes.contains(-1)">
                        OR NOT EXISTS ( SELECT 1 FROM order_promotion_detail opd WHERE opd.order_num = ot.order_num )
                    </if>
                )
            </if>
        </where>
    </sql>
     <select id="getOrderMemberList" resultType = "com.ruoyi.system.api.domain.vo.order.OrderMemberVO">
         <include refid="Base_Order_Member"/>
       , (CASE WHEN ot.audit_status = 2 THEN 1
         WHEN ot.audit_status = 0 THEN 2
         WHEN ot.audit_status = 1 THEN 3 else 100 end) AS sort
        from order_table ot
        left join order_member om on ot.order_num = om.order_num
         <include refid="sqlwhereSearch"/>
    </select>

    <select id="workbenchFinanceMemberList" resultType = "com.ruoyi.system.api.domain.vo.order.OrderMemberVO">
        select
            ot.order_num,
            om.package_type,
            ot.current_exchange_rate,
            ot.order_amount,
            ot.pay_type,
            ot.submit_credential_time,
            om.create_user_name
        from order_table ot
        inner join order_member om on om.order_num = ot.order_num
        <where>
            ot.submit_credential_time is not null and ot.audit_status = ${@com.ruoyi.common.core.enums.AuditStatusEnum@UN_CHECK.getcode}
        </where>
        order by ot.submit_credential_time desc
    </select>

    <select id="getBackendOrderMemberList" resultType = "com.ruoyi.system.api.domain.vo.order.OrderMemberVO">
         <include refid="Base_Order_Member"/>
        from order_table ot
        left join order_member om on ot.order_num = om.order_num
         <include refid="sqlwhereSearch"/>
    </select>

    <select id="getBizUserOrderCount" resultType = "java.lang.Long">
        select count(1)
        from order_table ot
        left join order_member om on ot.order_num = om.order_num
        where om.order_num &lt;&gt; #{orderNum}
        and om.biz_user_id = #{bizUserId}
        and om.status &lt;&gt; ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getcode}
        and ot.merchant_id = 0
    </select>

    <select id="getMemberUnPayOrderNum" resultType = "com.ruoyi.system.api.domain.vo.order.OrderMemberVO">
        select
            om.id,om.order_num, om.status, om.package_type, om.member_start_time, om.member_end_time,om.biz_user_id,ot.id as orderId, ot.tax_point,ot.tax_point_cost,
            ot.video_count, ot.order_amount, ot.pay_amount, ot.real_pay_amount, ot.current_exchange_rate, ot.use_balance,
            ot.order_user_id, ot.order_time, ot.pay_user_id, ot.pay_time, ot.pay_account, ot.pay_type, ot.audit_status,
            ot.order_remark, ot.merchant_id, ot.merchant_code,ot.seed_code_discount
        from order_table ot
                 inner join order_member om on ot.order_num = om.order_num
        where
            om.status = ${@com.ruoyi.common.core.enums.OrderMemberStatusEnum@UN_PAY.getcode}
            <if test="businessId == 0">
                AND ot.merchant_id = #{businessId}
                AND om.biz_user_id = #{bizUserId}
            </if>
            <if test="businessId != 0">
                AND ot.merchant_id = #{businessId}
            </if>
    </select>

    <update id="updateOrderMemberBuyFlag">
        UPDATE order_member
        SET is_first_buy = CASE
                               WHEN id IN (
                                   SELECT id
                                   FROM (
                                            SELECT
                                                id,
                                                ROW_NUMBER() OVER (PARTITION BY biz_user_id ORDER BY create_time ASC) AS rn
                                            FROM
                                                order_member
                                            WHERE
                                                status = 3
                                        ) AS RankedOrders
                                   WHERE rn = 1
                               ) THEN 1
                               ELSE 2
            END
        WHERE status = 3
    </update>

</mapper>
