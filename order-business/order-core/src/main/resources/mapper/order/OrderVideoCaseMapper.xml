<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoCaseMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoCase" id="OrderVideoCaseResult">
        <result property="id"    column="id"    />
        <result property="videoId"    column="video_id"    />
        <result property="sendId"    column="send_id"    />
        <result property="sendContent"    column="send_content"    />
        <result property="sendTime"    column="send_time"    />
        <result property="replyId"    column="reply_id"    />
        <result property="replyTime"    column="reply_time"    />
        <result property="replyContent"    column="reply_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderVideoCaseVo">
        select id, video_id, send_id, send_content, send_time, reply_id, reply_time, reply_content, create_by, create_time, update_by, update_time from order_video_case
    </sql>

    <select id="selectOrderVideoCaseList" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoCase" resultMap="OrderVideoCaseResult">
        <include refid="selectOrderVideoCaseVo"/>
        <where>
            <if test="videoId != null "> and video_id = #{videoId}</if>
            <if test="sendId != null "> and send_id = #{sendId}</if>
            <if test="sendContent != null  and sendContent != ''"> and send_content = #{sendContent}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
            <if test="replyId != null "> and reply_id = #{replyId}</if>
            <if test="replyTime != null "> and reply_time = #{replyTime}</if>
            <if test="replyContent != null "> and reply_content = #{replyContent}</if>
        </where>
    </select>

    <select id="selectOrderVideoCaseById" parameterType="Long" resultMap="OrderVideoCaseResult">
        <include refid="selectOrderVideoCaseVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrderVideoCase" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoCase" useGeneratedKeys="true" keyProperty="id">
        insert into order_video_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="videoId != null">video_id,</if>
            <if test="sendId != null">send_id,</if>
            <if test="sendContent != null and sendContent != ''">send_content,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="replyId != null">reply_id,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="replyContent != null">reply_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="videoId != null">#{videoId},</if>
            <if test="sendId != null">#{sendId},</if>
            <if test="sendContent != null and sendContent != ''">#{sendContent},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="replyId != null">#{replyId},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="replyContent != null">#{replyContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOrderVideoCase" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoCase">
        update order_video_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="videoId != null">video_id = #{videoId},</if>
            <if test="sendId != null">send_id = #{sendId},</if>
            <if test="sendContent != null and sendContent != ''">send_content = #{sendContent},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="replyId != null">reply_id = #{replyId},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="replyContent != null">reply_content = #{replyContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderVideoCaseById" parameterType="Long">
        delete from order_video_case where id = #{id}
    </delete>

    <delete id="deleteOrderVideoCaseByIds" parameterType="String">
        delete from order_video_case where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>