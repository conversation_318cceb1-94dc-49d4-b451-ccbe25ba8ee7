<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.ModelWorkbenchMapper">

    <sql id="FroMeListConditionFilter">
        ovmpm.`status` IN ( ${@com.ruoyi.common.core.enums.PreselectStatusEnum@UN_JOINTED.getcode},${@<EMAIL>} )
        AND ovmpm.model_id = #{modelId}
        AND ovmpm.add_time > NOW() - INTERVAL #{overTime} HOUR
        AND ovmpm.add_type IN ( ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@INTENTION_MODEL.getcode} , ${@<EMAIL>} )
        AND NOT EXISTS (
            SELECT 1
            FROM order_video_match_preselect_model pm2
            WHERE pm2.match_id = ovmpm.match_id
            AND pm2.status = ${@<EMAIL>}
        )
        AND ovmpm.model_intention = ${@com.ruoyi.common.core.enums.ModelIntentionEnum@MT_UN_CONFIRM.getcode}
    </sql>

    <!--    给我的-->
    <select id="forMe" resultType="com.ruoyi.system.api.domain.vo.order.OrderModelWorkbenchForMeListVO">
        SELECT
            ov.id,
            ovmpm.id AS  preselectModelId,
            ov.product_pic,
            ov.video_code,
            ov.video_style,
            ov.product_english,
            ov.video_duration,
            ov.pic_count,
            ov.refund_pic_count,
            ov.platform,
            ovmpm.add_time
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id AND ovm.status = ${@<EMAIL>}
                LEFT JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            <include refid="FroMeListConditionFilter"/>

            <if test="videoId != null ">
                AND ov.id = #{videoId}
            </if>
        </where>
    </select>
    <select id="getForMeCount" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id AND ovm.status = ${@<EMAIL>}
                LEFT JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            <include refid="FroMeListConditionFilter"/>
        </where>
    </select>
</mapper>