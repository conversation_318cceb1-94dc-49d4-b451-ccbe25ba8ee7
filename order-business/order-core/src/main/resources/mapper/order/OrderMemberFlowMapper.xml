<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderMemberFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderMemberFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderId" column="order_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="eventName" column="event_name" jdbcType="VARCHAR"/>
            <result property="eventExecuteObject" column="event_execute_object" jdbcType="INTEGER"/>
            <result property="eventExecuteUser" column="event_execute_user" jdbcType="VARCHAR"/>
            <result property="eventExecuteNickName" column="event_execute_nick_name" jdbcType="VARCHAR"/>
            <result property="eventExecutePhone" column="event_execute_phone" jdbcType="VARCHAR"/>
            <result property="eventExecuteTime" column="event_execute_time" jdbcType="TIMESTAMP"/>
            <result property="originStatus" column="origin_status" jdbcType="INTEGER"/>
            <result property="targetStatus" column="target_status" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_id,user_id,
        event_name,event_execute_object,event_execute_user,
        event_execute_nick_name,event_execute_phone,event_execute_time,
        origin_status,target_status,remark,
        create_time
    </sql>
</mapper>
