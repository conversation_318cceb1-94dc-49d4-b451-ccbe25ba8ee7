<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoModelSearchMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoModelSearch" id="OrderVideoModelSearchResult">
        <result property="id" column="id"/>
        <result property="modelId" column="model_id"/>
        <result property="content" column="content"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectOrderVideoModelSearchVo">
        select id, model_id, content, is_del, create_time, update_time
        from order_video_model_search
    </sql>

</mapper>