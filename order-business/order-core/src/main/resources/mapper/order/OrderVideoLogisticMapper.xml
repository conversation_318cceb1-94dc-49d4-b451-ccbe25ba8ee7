<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoLogisticMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic" id="OrderVideoLogisticResult">
        <result property="id" column="id"/>
        <result property="videoId" column="video_id"/>
        <result property="number" column="number"/>
        <result property="reissue" column="reissue"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectOrderVideoLogisticVo">
        select id,
               video_id,
               number,
               reissue,
               create_by,
               create_time,
               update_by,
               update_time
        from order_video_logistic
    </sql>

    <select id="selectListByVideoIdAndRollbackId" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic">
        select
            ovl.*
        from order_video_logistic ovl
        left join order_video_model_shipping_address ovmsa on ovl.shipping_address_id = ovmsa.id
        <where>
            ovmsa.video_id = #{videoId} and ovl.is_cancel = ${@<EMAIL>}
            <if test="rollbackId != null">
                and ovmsa.rollback_id = #{rollbackId}
            </if>
        </where>

    </select>
    <select id="selectVideoIdsByLogisticNumber"
            resultType="java.lang.Long">
        SELECT ol.video_id
        FROM order_video_logistic ol
        JOIN (
        SELECT video_id, MAX(create_time) AS latest_time
        FROM order_video_logistic  where is_cancel = ${@<EMAIL>}
        GROUP BY video_id
        ) latest ON ol.video_id = latest.video_id AND ol.create_time = latest.latest_time
        where ol.number in
        <foreach collection="numbers" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ol.is_cancel = ${@<EMAIL>}
    </select>
    <select id="selectFirstListByVideoId"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic">
        select video_id,number,create_time from order_video_logistic where video_id in
        <foreach collection="videoIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_cancel = ${@<EMAIL>}
        and reissue = 1
    </select>

    <select id="selectLastListByDto" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoLogistic">
        SELECT
        t1.video_id
        FROM
        order_video_logistic t1
        <where>
            t1.id IN (SELECT MAX(id) FROM order_video_logistic GROUP BY video_id )
            and t1.is_cancel = ${@<EMAIL>}
            <if test="videoIds != null and videoIds.size() != 0">
                and t1.video_id in
                <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="receipt != null">
                and t1.receipt = #{receipt}
            </if>
        </where>
    </select>

</mapper>