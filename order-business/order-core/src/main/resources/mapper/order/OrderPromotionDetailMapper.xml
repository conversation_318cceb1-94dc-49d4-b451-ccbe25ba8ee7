<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPromotionDetailMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="activityId" column="activity_id" jdbcType="BIGINT"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="videoId" column="video_id" jdbcType="BIGINT"/>
        <result property="videoCode" column="video_code" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,activity_id,order_num,
        video_id,video_code,amount,
        create_user_id,create_user_name,create_time,
        update_user_id,update_user_name,update_time
    </sql>
    <select id="selectOrderDiscountDetailsByOrderNums"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO">
        SELECT
            pa.type,
            opd.order_num,
            ot.channel_type,
            ot.channel_name,
            opd.discount_type,
            SUM( opd.amount ) AS amount,
            opd.currency,
            ot.settle_rage AS discount_ratio,
            SUM( opd.discount_amount ) AS discount_amount,
            SUM( opd.discount_amount_dollar ) AS discount_amount_dollar
        FROM
            order_promotion_detail opd
                JOIN promotion_activity pa ON pa.id = opd.activity_id
                JOIN order_table ot ON ot.order_num = opd.order_num
        WHERE
            opd.order_num IN
            <foreach collection="orderNums" item="orderNum" open="(" separator="," close=")">
                #{orderNum}
            </foreach>
        GROUP BY
            pa.type,
            opd.order_num,
            ot.channel_type,
            ot.channel_name,
            opd.discount_type,
            opd.currency,
            ot.settle_rage
    </select>
    <select id="selectOrderDiscountDetailsByVideoIds"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.OrderDiscountDetailVO">
        SELECT
            pa.type,
            opd.video_id,
            opd.discount_type,
            SUM( opd.amount ) AS amount,
            opd.currency,
            SUM( opd.discount_amount ) AS discount_amount,
            SUM( opd.discount_amount_dollar ) AS discount_amount_dollar
        FROM
            order_promotion_detail opd
                JOIN promotion_activity pa ON pa.id = opd.activity_id
        WHERE
            opd.video_id IN
            <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                #{videoId}
            </foreach>
        GROUP BY
            pa.type,
            opd.video_id,
            opd.discount_type,
            opd.currency
    </select>

    <select id="getBusinessMemberDiscountAnalysis" resultType="com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberDiscountVO">
        SELECT
            activity_id,
            discount_type,
            amount,
            count(id) count
        FROM
            `order_promotion_detail`
        <where>
            activity_id in(${@com.ruoyi.common.core.enums.PromotionActivityTypeEnum@SEED_CODE_DISTRIBUTION_DISCOUNT.getcode},
          ${@com.ruoyi.common.core.enums.PromotionActivityTypeEnum@SEED_CODE_FISSION_DISCOUNT.getcode})
            <if test="from != null and to != null">
                and create_time BETWEEN #{from} AND #{to}
            </if>
        </where>
        group by activity_id,discount_type,amount
        order by count desc
    </select>
</mapper>
