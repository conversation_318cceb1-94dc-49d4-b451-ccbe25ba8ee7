<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoRoastMapper">

    <select id="getOrderVideoRoastVO" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoRoastVO">
        select ovr.id,
               ovr.business_id,
               ovr.biz_user_id,
               ovr.roast_user_nick_name,
               ovr.roast_user_name,
               ovr.video_id,
               ov.video_code,
               ov.shoot_model_id,
               ovr.roast_type,
               ovr.object,
               ovr.content,
               ovr.contact_id,
               ovr.issue_id,
               ovr.handle_status,
               ovr.handle_result,
               ovr.handle_time,
               ovr.handle_user_id,
               ovr.handle_user_name,
               ovr.create_time,
               ovr.update_time
        from order_video_roast ovr
                 left join order_video ov on ovr.video_id = ov.id
        <where>
            ovr.content != '商家未吐槽'
            <if test="object != null">
                and ovr.object = #{object}
            </if>
            <if test="handleStatus != null">
                and ovr.handle_status = #{handleStatus}
            </if>
            <if test="roastType != null">
                and ovr.roast_type = #{roastType}
            </if>
            <if test="chineseWaiterIds != null and chineseWaiterIds.size() != 0 ">
                and ovr.contact_id in
                <foreach item="item" index="index" collection="chineseWaiterIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="englishWaiterIds != null and englishWaiterIds.size() != 0 ">
                and ovr.issue_id in
                <foreach item="item" index="index" collection="englishWaiterIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="businessIds != null and businessIds.size() != 0 ">
                and ovr.business_id in
                <foreach item="item" index="index" collection="businessIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="orderVideoRoastStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoRoastStatisticsVO">
        select
            sum( CASE WHEN roast_type = 0 and handle_status = 0 THEN 1 ELSE 0 END ) videoUnHandleCount,
            sum( CASE WHEN roast_type = 0 and handle_status = 1 THEN 1 ELSE 0 END ) videoHandleCount,
            sum( CASE WHEN roast_type = 0 THEN 1 ELSE 0 END ) videoCount,
            sum( CASE WHEN roast_type = 1 and handle_status = 0 THEN 1 ELSE 0 END ) systemUnHandleCount,
            sum( CASE WHEN roast_type = 1 and handle_status = 1 THEN 1 ELSE 0 END ) systemHandleCount,
            sum( CASE WHEN roast_type = 1 THEN 1 ELSE 0 END ) systemCount
        from order_video_roast
        where content != '商家未吐槽'
    </select>
</mapper>
