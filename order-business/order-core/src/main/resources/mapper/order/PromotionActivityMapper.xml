<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.PromotionActivityMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="activityStatus" column="activity_status" jdbcType="BOOLEAN"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,activity_name,start_time,
        end_time,activity_status,remark,
        create_user_id,create_user_name,create_time,
        update_user_id,update_user_name,update_time
    </sql>
    <select id="getValidPromotionActivityByType"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO">
        SELECT
            pa.id,
            pa.activity_name,
            pa.type,
            pad.type AS discount_type,
            pad.amount,
            pad.currency,
            pa.start_time,
            pa.end_time,
            pa.activity_status
        FROM
            promotion_activity pa
                LEFT JOIN promotion_activity_detail pad ON pad.activity_id = pa.id
        WHERE pa.type = #{type}
            AND pa.activity_status = ${@<EMAIL>}
            AND pa.start_time &lt; NOW()
            AND pa.end_time &gt; NOW()
    </select>
    <select id="checkCurrentBusinessSatisfyMonthFirstOrderDiscounted" resultType="java.lang.Boolean">
        SELECT
            CASE WHEN COUNT(1) > 0 THEN FALSE ELSE TRUE END
        FROM
            order_video ov
                JOIN order_promotion_detail opd ON opd.video_id = ov.id
                JOIN promotion_activity pa ON pa.id = opd.activity_id AND pa.type = ${@com.ruoyi.common.core.enums.PromotionActivityTypeEnum@MONTH_FIRST_ORDER_DISCOUNTED.getCode}
        WHERE
            YEAR(ov.create_time) = YEAR(NOW())
            AND  MONTH ( ov.create_time ) = MONTH ( NOW() )
            <if test="businessId == 0 ">
                AND ov.create_order_biz_user_id = #{bizUserId}
            </if>
            <if test="businessId != 0 ">
                AND ov.create_order_business_id = #{businessId}
            </if>
            AND (
            ( ov.un_confirm_time IS NULL AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode} )
                OR ov.un_confirm_time IS NOT NULL
            )
    </select>
    <select id="selectValidPromotionActivityList"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO">
        SELECT
            pa.id,
            pa.activity_name,
            pa.type,
            pad.type AS discount_type,
            pad.amount,
            pad.currency,
            pa.start_time,
            pa.end_time,
            pa.activity_status
        FROM
            promotion_activity pa
                LEFT JOIN promotion_activity_detail pad ON pad.activity_id = pa.id
        WHERE pa.activity_status = ${@<EMAIL>}
          AND pa.start_time &lt; NOW()
          AND pa.end_time &gt; NOW()
    </select>
    <select id="getPromotionActivityByType"
            resultType="com.ruoyi.system.api.domain.vo.order.promotion.PromotionActivityVO">
        SELECT
            pa.id,
            pa.activity_name,
            pa.type,
            pad.type AS discount_type,
            pad.amount,
            pad.currency,
            pa.start_time,
            pa.end_time,
            pa.activity_status
        FROM
            promotion_activity pa
                LEFT JOIN promotion_activity_detail pad ON pad.activity_id = pa.id
        WHERE pa.type = #{type}
    </select>
</mapper>
