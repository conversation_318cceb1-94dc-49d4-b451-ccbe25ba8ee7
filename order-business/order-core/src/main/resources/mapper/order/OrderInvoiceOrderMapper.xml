<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderInvoiceOrderMapper">


<!--    运营端-发票管理-发票关联订单-->
    <select id="getInvoiceVideoBackList" resultType="com.ruoyi.system.api.domain.vo.order.OrderInvoiceVideoBackVO">
        SELECT
            oio.order_num,
            ot.pay_type,
            ot.pay_time,
            SUM( oio.invoice_amount ) AS invoice_amount
        FROM
            order_invoice_order oio
                LEFT JOIN order_table ot ON ot.order_num = oio.order_num
        WHERE oio.invoice_id = #{invoiceId} and  ot.order_type in (${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getcode}, ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIP_ORDER.getcode})
        GROUP BY
            oio.order_num,
            ot.pay_type,
            ot.pay_time
        UNION ALL
        SELECT
            oio.order_num,
            ot.pay_type,
            ot.pay_time,
            SUM( oio.invoice_amount ) AS invoice_amount
        FROM
            order_invoice_order oio
                LEFT JOIN order_pay_log ot ON ot.order_num = oio.order_num
        WHERE oio.invoice_id = #{invoiceId} and ot.order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@ONLINE_RECHARGE.getcode}
        GROUP BY
            oio.order_num,
            ot.pay_type,
            ot.pay_time
    </select>
</mapper>
