<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoModelMapper">

    <sql id="selectOrderVideoModel">
        ovm.id,ovm.model_id,ovm.video_id,ovm.accept_time,ovm.over_time,ovm.create_time,ovm.update_time
    </sql>

    <select id="checkModelOverdueVideo" resultType="java.lang.Long">
        SELECT
            ovm.model_id
        FROM
            order_video_model ovm
            INNER JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            <if test="modelId !=null and modelId.size() != 0">
                ovm.model_id in
                <foreach collection="modelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND NOT EXISTS ( SELECT 1 FROM order_video_feed_back_material WHERE video_id = ovm.video_id )
            AND ovm.accept_time &lt; NOW() - INTERVAL #{orderOverdueMax} HOUR
            AND ov.`status` != #{status.TRADE_CLOSE}
            AND ov.`status` != #{status.FINISHED}
            AND ov.`status` != #{status.NEED_CONFIRM}
        </where>
    </select>

    <select id="getModelAllOrder" resultType="com.ruoyi.system.api.domain.entity.OrderVideoModel">
        SELECT
            <include refid="selectOrderVideoModel"/>
        FROM
            order_video_model ovm
                INNER JOIN order_video ov ON ov.id = ovm.video_id
        WHERE
            ovm.accept_time IS NOT NULL
            AND ov.`status` != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
    </select>
    <select id="getUnfinishedOrderModelByModelId"
            resultType="com.ruoyi.system.api.domain.entity.OrderVideoModel">
        SELECT
            <include refid="selectOrderVideoModel"/>
        FROM
            order_video_model ovm
                INNER JOIN order_video ov ON ov.id = ovm.video_id
        <where>
            <if test="modelId !=null and modelId.size() != 0">
                ovm.model_id in
                <foreach collection="modelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            AND ov.`status` != #{status.TRADE_CLOSE}
            AND ov.`status` != #{status.FINISHED}
        </where>
    </select>
</mapper>
