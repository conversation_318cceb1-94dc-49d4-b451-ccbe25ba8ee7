<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.Order" id="OrderResult">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="payNum" column="pay_num" jdbcType="VARCHAR"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="videoCount" column="video_count" jdbcType="INTEGER"/>
        <result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
        <result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
        <result property="realPayAmount" column="real_pay_amount" jdbcType="DECIMAL"/>
        <result property="currentExchangeRate" column="current_exchange_rate" jdbcType="DECIMAL"/>
        <result property="useBalance" column="use_balance" jdbcType="DECIMAL"/>
        <result property="taxPoint" column="tax_point" jdbcType="DECIMAL"/>
        <result property="taxPointCost" column="tax_point_cost" jdbcType="DECIMAL"/>
        <result property="orderUserId" column="order_user_id" jdbcType="BIGINT"/>
        <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
        <result property="payUserId" column="pay_user_id" jdbcType="BIGINT"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="payAccount" column="pay_account" jdbcType="VARCHAR"/>
        <result property="payType" column="pay_type" jdbcType="TINYINT"/>
        <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
        <result property="isRecord" column="is_record" jdbcType="INTEGER"/>
        <result property="isMergeOrder" column="is_merge_order" jdbcType="INTEGER"/>
        <result property="recordTime" column="record_time" jdbcType="TIMESTAMP"/>
        <result property="orderRemark" column="order_remark" jdbcType="VARCHAR"/>
        <result property="merchantId" column="merchant_id" jdbcType="BIGINT"/>
        <result property="merchantCode" column="merchant_code" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="selectOrderVo">
        SELECT id, order_num, pay_num, biz_user_id, order_type, video_count,
               order_amount, pay_amount, pay_amount_dollar, real_pay_amount,
               real_pay_amount_currency,order_promotion_amount,difference_amount,
               currency, current_exchange_rate, is_default_exchange_rate,
               use_balance, tax_point, tax_point_cost, seed_code, seed_id, settle_rage,
               seed_code_discount,channel_name,channel_type, back_modify_amount,
               order_user_id, order_user_name,order_user_nick_name,order_time, pay_user_id, pay_time, pay_account,
               pay_type,pay_type_detail, payee_id, submit_credential_time, audit_status, audit_time,
               audit_user_id, audit_user_name, is_record,is_merge_order, record_time,
               order_remark, merchant_id, merchant_code, create_by, create_time, update_by, update_time
        FROM order_table
    </sql>

    <select id="selectOrderListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.OrderListVO">
        SELECT
            ot.order_num, ot.order_amount, ot.pay_amount, ot.real_pay_amount,
            ot.use_balance,ot.order_time,ot.order_time_sign,ot.reopen_count, ot.order_user_id,ot.pay_user_id,
            ot.pay_time,ot.close_order_time,ot.pay_type,ot.pay_type_detail,ot.merchant_id,ot.create_time,ot.is_default_exchange_rate,
            ot.current_exchange_rate,ot.tax_point_cost,ot.is_merge_order,
            SUM(ov.commission_pays_taxes) AS commission_pays_taxes,
            ot.order_promotion_amount,
            ot.audit_status,
            ot.audit_time
            <if test="dto.statusTimeSort == 'ASC'">
                , MIN( ov.status_time ) AS status_time
            </if>
            <if test="dto.statusTimeSort == 'DESC'">
                , MAX( ov.status_time ) AS status_time
            </if>
        FROM
            order_table ot
            left join order_video ov ON ot.order_num = ov.order_num
        <where>
            ot.order_type = #{orderType}
            <if test="dto.isFilterClose != null"> and ov.status != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}</if>
            <if test="dto.care != null">
                and ov.is_care = #{dto.care}
            </if>
            <if test="dto.reminderStatus != null and dto.reminderStatus.size() != 0">
              AND (ov.`status` = 6
                    <choose>
                        <when test="dto.reminderStatus.size() == 1 and dto.reminderStatus[0] == 2">
                            AND EXISTS (
                                    SELECT 1
                                    FROM order_video_reminder_record ovrr
                                    WHERE ovrr.video_id = ov.id
                                        AND NOT EXISTS (
                                            SELECT 1
                                            FROM order_video_reminder_record sub_ovrr
                                            WHERE sub_ovrr.video_id = ov.id
                                                AND sub_ovrr.`status` != #{dto.reminderStatus[0]}
                                    )
                            )
                        </when>
                        <otherwise>
                            AND EXISTS (
                                SELECT 1
                                FROM order_video_reminder_record ovrr
                                WHERE ovrr.video_id = ov.id
                                AND ovrr.`status` IN
                                <foreach collection="dto.reminderStatus" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            )
                        </otherwise>
                    </choose>
            )
            </if>
            <if test="dto.keyword != null and dto.keyword != ''">
                and (
                    ov.order_num like concat('%', #{dto.keyword}, '%')
                    OR ov.video_code like concat('%', UCASE(#{dto.keyword}), '%')
                    OR ov.product_chinese like concat('%', #{dto.keyword}, '%')
                    OR ov.product_english like concat('%', #{dto.keyword}, '%')
                    OR ov.product_link like concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_nick_name like concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_name like concat('%', #{dto.keyword}, '%')
                    <if test="dto.modelIds != null and dto.modelIds.size() != 0">
                        OR ov.intention_model_id in
                        <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        OR ov.shoot_model_id in
                        <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>

                        <if test="dto.loginUserType == 0">
                            OR ov.id in (
                            select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_id in
                            <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            )
                        </if>
                    </if>

                    <if test="dto.loginUserType == 0">
                        <if test="dto.platform != null and dto.platform.size() != 0">
                            OR ov.platform in
                            <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        OR ov.create_order_user_account like concat('%', #{dto.keyword}, '%')
                        OR ot.merchant_code like concat('%', UCASE(#{dto.keyword}), '%')
                        <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() >0">
                            OR ot.merchant_id in
                            <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </if>
                )
            </if>
            <if test="dto.merchantIds != null and dto.merchantIds.size() != 0">
                and ot.merchant_id in
                <foreach collection="dto.merchantIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.orderTimeBegin != null and dto.orderTimeEnd != null">
                and ot.order_time BETWEEN #{dto.orderTimeBegin} AND #{dto.orderTimeEnd}
            </if>
            <if test="dto.orderTimeSignBegin != null and dto.orderTimeSignEnd != null">
                and ot.order_time_sign BETWEEN #{dto.orderTimeSignBegin} AND #{dto.orderTimeSignEnd}
            </if>
            <if test="dto.auditStatus != null">
                and ot.audit_status = #{dto.auditStatus}
            </if>
            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                and (
                    (   ot.pay_type in (${@<EMAIL>},
                    ${@<EMAIL>},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@WECHAT_BALANCE.getCode},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@ALIPAY_BALANCE.getCode},
                    ${@<EMAIL>})
                    and ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
                    ) or
                    (   ot.pay_type in (${@<EMAIL>},
                    ${@<EMAIL>},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@FULL_CURRENCY.getCode},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@BANK_BALANCE.getCode},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@PUBLIC_BALANCE.getCode},
                    ${@com.ruoyi.common.core.enums.PayTypeEnum@FULL_CURRENCY_BALANCE.getCode})
                    and ot.audit_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
                    and ot.audit_status = ${@<EMAIL>}
                    )
                )
            </if>

            <if test="dto.payType != null and dto.payType.size() != 0">
                and ot.pay_type in
                <foreach item="item" index="index" collection="dto.payType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderNum != null and dto.orderNum != ''">
                and ot.order_num like concat('%', #{dto.orderNum}, '%')
            </if>

            <if test="dto.videoCode != null and dto.videoCode != ''">
                and ov.video_code like concat('%', UCASE(#{dto.videoCode}), '%')
            </if>

            <if test="dto.productName != null and dto.productName != ''">
                and (ov.product_chinese like concat('%', #{dto.productName}, '%') or ov.product_english like concat('%', #{dto.productName}, '%'))
            </if>

            <if test="dto.productLink != null and dto.productLink != ''">
                and ov.product_link like concat('%', #{dto.productLink}, '%')
            </if>

            <if test="dto.platform != null and dto.platform.size() != 0">
                and ov.platform in
                <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.shootModelId != null and dto.shootModelId.size() != 0">
                and ov.shoot_model_id in
                <foreach collection="dto.shootModelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.contactId != null and dto.contactId.size() != 0">
                and ov.contact_id in
                <foreach collection="dto.contactId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.issueId != null and dto.issueId.size() != 0">
                and ov.issue_id in
                <foreach collection="dto.issueId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size() != 0">
                and ov.`status` in
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.preselectAddUserId != null and dto.preselectAddUserId.size() != 0 ">
                and ov.id in (
                select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.add_user_id in
                <foreach collection="dto.preselectAddUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.preselectAddUserName != null and dto.preselectAddUserName.size() != 0 ">
                and ov.id in (
                select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_person_name in
                <foreach collection="dto.preselectAddUserName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.preselectModelId != null and dto.preselectModelId.size() != 0 ">
                and ov.id in (
                select DISTINCT(ovm.video_id) from order_video_match ovm LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id where ovmpm.model_id in
                <foreach collection="dto.preselectModelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND ovmpm.status != 3
                )
            </if>

            <if test="dto.preselectStatus != null and !dto.preselectStatus.isEmpty()">
                AND ov.id IN (
                SELECT DISTINCT ovm.video_id
                FROM order_video_match ovm
                LEFT JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
                <where>
                    <if test="dto.preselectStatus.contains(0) or dto.preselectStatus.contains(1) or dto.preselectStatus.contains(2)">
                        ovmpm.status IN
                        <foreach collection="dto.preselectStatus" item="item" open="(" separator="," close=")">
                            <if test="item == 0 or item == 1 or item == 2">
                                #{item}
                            </if>
                        </foreach>
                    </if>

                    <if test="dto.preselectStatus.contains(3) or dto.preselectStatus.contains(4) or dto.preselectStatus.contains(5) or dto.preselectStatus.contains(6) or dto.preselectStatus.contains(7) or dto.preselectStatus.contains(8)">
                        <if test="dto.preselectStatus.contains(0) or dto.preselectStatus.contains(1) or dto.preselectStatus.contains(2)">
                            OR
                        </if>
                        ovmpm.oust_type IN (
                        <foreach collection="dto.preselectStatus" item="item" separator="," >
                            <choose>
                                <when test="item == 3">1</when>
                                <when test="item == 4">2</when>
                                <when test="item == 5">3</when>
                                <when test="item == 6">4</when>
                                <when test="item == 7">5</when>
                                <when test="item == 8">6</when>
                            </choose>
                        </foreach>
                        )
                    </if>
                    <if test="dto.preselectStatus.contains(8)">
                        and ov.rollback_id IS NOT NULL
                    </if>
                </where>
                )
            </if>



            <if test="dto.replyContent !=null and dto.replyContent.size() != 0">
                and ov.id in (select DISTINCT(video_id) from order_video_case where reply_content in
                <foreach collection="dto.replyContent" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.videoIds != null and dto.videoIds.size() !=0">
                and ov.id in
                <foreach collection="dto.videoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.rollbackIds != null and dto.rollbackIds.size() !=0">
                and
                <foreach collection="dto.rollbackIds" item="item" open="(" separator="OR" close=")">
                    <if test="item == null ">
                        ov.rollback_id IS NULL
                    </if>
                    <if test="item != null ">
                        ov.rollback_id = #{item}
                    </if>
                </foreach>
            </if>

            <if test="dto.statusDays != null">
                AND DATE(status_time) >= DATE_SUB(CURDATE(), INTERVAL #{dto.statusDays} DAY)
            </if>
            <if test="dto.newStatusDays != null">
                <choose>
                    <when test="dto.newStatusDays == 0">AND DATE(status_time) &gt;= DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)</when>
                    <when test="dto.newStatusDays == 3">
                        AND DATE(status_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                        AND DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                    </when>
                    <when test="dto.newStatusDays == 4">
                        AND DATE(status_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                        AND DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                    </when>
                    <otherwise>
                        AND DATE(status_time) &lt; DATE_SUB(CURDATE(), INTERVAL #{dto.newStatusDays} DAY)
                    </otherwise>
                </choose>
            </if>

            <if test="dto.refundVideoIds != null and dto.refundVideoIds.size() != 0">
                and ov.id not in
                <foreach collection="dto.refundVideoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.modelType != null and dto.modelType.size() != 0">
                and ov.model_type in
                <foreach collection="dto.modelType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.shootingCountry != null and dto.shootingCountry.size() >0">
                and ov.shooting_country in
                <foreach collection="dto.shootingCountry" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.videoId != null">
                and ov.videoId = #{dto.videoId}
            </if>

            <if test="dto.matchStartTimes != null">
                AND (
                <foreach item="value" index="key" collection="dto.searchMap.entrySet()" separator=" or " >
                    date_format(ov.status_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                </foreach>
                )
            </if>

            <if test="dto.createOrderUserName != null and dto.createOrderUserName.size() > 0 ">
                and ov.create_order_user_name in
                <foreach collection="dto.createOrderUserName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.aboutMe == 'true' ">
                and (ov.contact_id = #{dto.backUserId} OR ov.issue_id = #{dto.backUserId})
            </if>

            <if test="dto.statusTimeBegin != null and dto.statusTimeEnd != null">
                AND ov.status_time BETWEEN #{dto.statusTimeBegin} AND #{dto.statusTimeEnd}
            </if>

            <if test="dto.matchStatus != null">
                AND ov.id IN (
                    SELECT
                        ovm.video_id
                    FROM
                        order_video_match ovm
                        JOIN ( SELECT video_id, MAX( start_time ) AS max_start_time FROM order_video_match GROUP BY video_id ) latest ON ovm.video_id = latest.video_id AND ovm.start_time = latest.max_start_time
                    WHERE ovm.`status` = #{dto.matchStatus}
                    <if test="dto.matchStatus == 1">
                        AND ovm.end_time IS NULL
                    </if>
                    <if test="dto.matchStatus == 2">
                        AND ovm.end_time IS NOT NULL
                    </if>
                )
            </if>

            <if test="dto.orderNums != null and dto.orderNums.size() != 0">
                and ot.order_num in
                <foreach collection="dto.orderNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.closeOrderStatus != null and dto.closeOrderStatus == 1">
                and ov.need_confirm_time is null and ov.issue_id is not null and ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getCode}
            </if>

            <if test="dto.modelWaitOrder == true">
                AND (
                    ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_FINISHED.getCode}
                    OR (
                        ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@NEED_CONFIRM.getCode}
                        AND EXISTS (
                                SELECT
                                    1
                                FROM
                                    order_video_task ovt
                                        JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
                                WHERE
                                    ovt.video_id = ov.id
                                        AND ovtd.`status` IN ( ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getCode},
                                                                ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.getCode} )
                        )
                    )
                )
            </if>

            <if test="dto.hasPicCount != null ">
                <if test="dto.hasPicCount == true ">
                    AND ov.pic_count IS NOT NULL
                </if>
                <if test="dto.hasPicCount == false ">
                    AND ov.pic_count IS NULL
                </if>
            </if>

            <if test="dto.beforeStatusTime != null">
                and DATE ( ov.status_time ) &lt;= #{dto.beforeStatusTime}
            </if>

            <if test="dto.beforeStatusTimeStart != null and dto.beforeStatusTimeEnd != null ">
                AND ov.status_time BETWEEN #{dto.beforeStatusTimeStart} AND #{dto.beforeStatusTimeEnd}
            </if>

            <if test="dto.confirmReceiptTimeMap != null or dto.beforeConfirmReceiptTime != null">
                AND ov.id IN (
                            SELECT video_id
                            FROM (
                                SELECT video_id,sign_time as receipt_time,
                                ROW_NUMBER() OVER (PARTITION BY video_id ORDER BY create_time DESC) AS rn
                                FROM order_video_logistic_follow
                            ) AS ranked_logistic
                            WHERE rn = 1
                                 <if test="dto.confirmReceiptTimeMap != null">
                                     AND (
                                     <foreach item="value" index="key" collection="dto.confirmReceiptTimeMap.entrySet()" separator=" or " >
                                         date_format(receipt_time, '%Y-%m-%d %H:%i:%s') BETWEEN #{key} AND #{value}
                                     </foreach>
                                     )
                                 </if>
                                 <if test="dto.beforeConfirmReceiptTime != null">
                                     AND DATE ( receipt_time ) &lt;= #{dto.beforeConfirmReceiptTime}
                                 </if>
                )
            </if>

            <if test="dto.intentionModelIds != null and dto.intentionModelIds.size() > 0 ">
                AND ov.intention_model_id IN
                <foreach collection="dto.intentionModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.isGunds != null and dto.isGunds.size() > 0 ">
                AND ov.is_gund IN
                <foreach collection="dto.isGunds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            ot.order_num,
            ot.order_amount,
            ot.pay_amount,
            ot.real_pay_amount,
            ot.use_balance,
            ot.order_time,
            ot.order_time_sign,
            ot.reopen_count,
            ot.pay_user_id,
            ot.pay_time,
            ot.close_order_time,
            ot.pay_type,
            ot.merchant_id,
            ot.create_time,
            ot.is_merge_order,
            ot.order_promotion_amount,
            ot.audit_time,
            ot.audit_status
        ORDER BY
            <if test="dto.statusTimeSort != null and dto.statusTimeSort != ''">
                status_time ${dto.statusTimeSort} ,
            </if>
            ot.order_time_sign DESC,ot.create_time DESC
    </select>

    <sql id="orderVideoAuditWhere">
            <if test="dto.merchantIds != null and dto.merchantIds.size() != 0">
                ot.merchant_id in
                <foreach collection="dto.merchantIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.isReceivableAudit != null and dto.isReceivableAudit == 1">
                and ot.submit_credential_time is not null
            </if>
            <if test="dto.searchName != null and dto.searchName != ''">
                and (
                ot.order_num like concat('%', #{dto.searchName}, '%')
                or ot.pay_num like concat('%', #{dto.searchName}, '%')
                or ov.product_chinese like concat('%', #{dto.searchName}, '%')
                or ov.product_english like concat('%', #{dto.searchName}, '%')
                or ov.product_link like concat('%', #{dto.searchName}, '%')
                <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() != 0">
                    or ot.merchant_id in
                    <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="dto.orderNum != null and dto.orderNum != ''">and ot.order_num like concat('%', #{dto.orderNum}, '%')</if>
            <if test="dto.orderNums != null and dto.orderNums.size() != 0">
                and ot.order_num in
                <foreach collection="dto.orderNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderUserId != null and dto.orderUserId.size() != 0">
                and ot.order_user_id in
                <foreach collection="dto.orderUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderTimeBegin != null and dto.orderTimeEnd != null">
                and ot.order_time BETWEEN #{dto.orderTimeBegin} AND #{dto.orderTimeEnd}
            </if>
            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                and (
                (   ot.pay_type in (${@<EMAIL>},
                                    ${@<EMAIL>},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@WECHAT_BALANCE.getCode},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@ALIPAY_BALANCE.getCode},
                                    ${@<EMAIL>})
                    and ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
                ) or
                (   ot.pay_type in (${@<EMAIL>},
                                    ${@<EMAIL>},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@FULL_CURRENCY.getCode},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@BANK_BALANCE.getCode},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@PUBLIC_BALANCE.getCode},
                                    ${@com.ruoyi.common.core.enums.PayTypeEnum@FULL_CURRENCY_BALANCE.getCode})
                    and ot.audit_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
                    and ot.audit_status = ${@<EMAIL>}
                )
                )
            </if>
            <if test="dto.payType != null and dto.payType.size() != 0">
                and
                ot.pay_type in
                <foreach collection="dto.payType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.payTypeDetails != null and dto.payTypeDetails.size() != 0">
                and
                ot.pay_type_detail in
                <foreach collection="dto.payTypeDetails" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.auditStatusList != null and dto.auditStatusList.size() != 0">
                and
                ot.audit_status in
                <foreach collection="dto.auditStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


            <if test="dto.videoCode != null and dto.videoCode != ''">
                and ov.video_code like concat('%', UCASE(#{dto.videoCode}), '%')
            </if>
            <if test="dto.productName != null and dto.productName != ''">
                and (ov.product_chinese like concat('%', #{dto.productName}, '%') or ov.product_english like concat('%', #{dto.productName}, '%'))
            </if>
            <if test="dto.productLink != null and dto.productLink != ''">
                and ov.product_link like concat('%', #{dto.productLink}, '%')
            </if>
            <if test="dto.modelIds != null and dto.modelIds.size() != 0">
                and (
                ov.intention_model_id in
                <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or
                ov.shoot_model_id in
                <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.platform != null and dto.platform.size() != 0">
                and ov.platform in
                <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.shootModelId != null and dto.shootModelId.size() != 0">
                and ov.shoot_model_id in
                <foreach collection="dto.shootModelId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.contactId != null and dto.contactId.size() != 0">
                and ov.contact_id in
                <foreach collection="dto.contactId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.issueId != null and dto.issueId.size() != 0">
                and ov.issue_id in
                <foreach collection="dto.issueId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.status != null and dto.status.size() != 0">
                and ov.`status` in
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.userIds != null and dto.userIds.size() !=0">
                and ot.biz_user_id in
                <foreach collection="dto.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </sql>

    <select id="selectOrderAuditList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoAuditVO">
        SELECT
        tmp.merge_id,
        tmp.pay_num,
        tmp.merge_by,
        tmp.merge_time,
        tmp.mergeOrderAmountDollar,
        tmp.order_num_str,
        tmp.order_num,
        tmp.update_time,
        tmp.sort
        FROM (
        SELECT
        m.id AS merge_id,
        m.pay_num AS pay_num,
        m.merge_by,
        m.merge_time,
        sum(ov.amount_dollar) as mergeOrderAmountDollar,
        GROUP_CONCAT( d.order_num ) AS order_num_str,
        max( d.order_num ) AS order_num,
        max(ot.update_time) as update_time,
        max(ot.is_merge_order) as is_merge_order,
        (CASE WHEN max(ot.audit_status) = 2 THEN 1
        WHEN max(ot.audit_status) = 0 THEN 2
        WHEN max(ot.audit_status) = 1 THEN 3 else 100 end) AS sort
        FROM
        order_merge m
        INNER JOIN order_merge_detail d ON m.id = d.merge_id
        INNER JOIN order_table ot on ot.order_num = d.order_num
        INNER JOIN order_video ov on ov.order_num = d.order_num
        WHERE
        ot.is_merge_order = ${@<EMAIL>}
        and m.status in(${@<EMAIL>},${@<EMAIL>})
        <include refid="orderVideoAuditWhere"/>
        GROUP BY
        m.pay_num

        UNION ALL

        SELECT NULL AS merge_id,
        ot.pay_num AS pay_num,
        NULL AS merged_by,
        NULL AS merge_time,
        sum(ov.amount_dollar) as mergeOrderAmountDollar,
        ot.order_num AS order_num_str,
        ot.order_num AS order_num,
        ot.update_time as update_time,
        ot.is_merge_order as is_merge_order,
        (CASE WHEN ot.audit_status = 2 THEN 1
        WHEN ot.audit_status = 0 THEN 2
        WHEN ot.audit_status = 1 THEN 3 else 100 end) AS sort
        FROM
        order_table ot
        left join order_video ov on ot.order_num = ov.order_num
        WHERE
        ot.is_merge_order = ${@<EMAIL>}
        <include refid="orderVideoAuditWhere"/>
        group by ot.order_num
            ) tmp;
    </select>

    <select id="workbenchFinanceVideoList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoAuditVO">
        SELECT
        tmp.merge_id,
        tmp.pay_num,
        tmp.mergeOrderAmount,
        tmp.mergeOrderAmountDollar,
        tmp.order_num_str,
        tmp.order_num,
        tmp.submit_credential_time,
        tmp.create_order_user_name,
        tmp.current_exchange_rate,
        tmp.pay_type,
        tmp.merchant_id,
        tmp.merchant_code,
        tmp.order_user_id,
        tmp.is_merge_order
        FROM (
        SELECT
        m.id AS merge_id,
        m.pay_num AS pay_num,
        sum(ov.amount_dollar) as mergeOrderAmountDollar,
        sum(ov.amount) as mergeOrderAmount,
        GROUP_CONCAT( d.order_num ) AS order_num_str,
        max( d.order_num ) AS order_num,
        max(ot.submit_credential_time) AS submit_credential_time,
        max(ot.current_exchange_rate) AS current_exchange_rate,
        max(ot.pay_type) AS pay_type,
        max(ot.merchant_id) AS merchant_id,
        max(ot.merchant_code) AS merchant_code,
        max(ot.order_user_id) AS order_user_id,
        max(ov.create_order_user_name) AS create_order_user_name,
        max(ot.is_merge_order) as is_merge_order
        FROM
        order_merge m
        INNER JOIN order_merge_detail d ON m.id = d.merge_id
        INNER JOIN order_table ot on ot.order_num = d.order_num
        INNER JOIN order_video ov on ov.order_num = d.order_num
        WHERE
        ot.is_merge_order = ${@<EMAIL>}
        and m.status in(${@<EMAIL>},${@<EMAIL>})
        and ot.submit_credential_time is not null and ot.audit_status = ${@com.ruoyi.common.core.enums.AuditStatusEnum@UN_CHECK.getcode}
        GROUP BY
        m.pay_num

        UNION ALL

        SELECT NULL AS merge_id,
        ot.pay_num AS pay_num,
        sum(ov.amount_dollar) as mergeOrderAmountDollar,
        sum(ov.amount) as mergeOrderAmount,
        ot.order_num AS order_num_str,
        ot.order_num AS order_num,
        ot.submit_credential_time,
        ot.current_exchange_rate,
        ot.pay_type,
        ot.merchant_id,
        ot.merchant_code,
        ot.order_user_id,
        max(ov.create_order_user_name) as create_order_user_name,
        ot.is_merge_order as is_merge_order
        FROM
        order_table ot
        join order_video ov on ot.order_num = ov.order_num
        WHERE
        ot.is_merge_order = ${@<EMAIL>}
        and ot.submit_credential_time is not null and ot.audit_status = ${@com.ruoyi.common.core.enums.AuditStatusEnum@UN_CHECK.getcode}
        group by ot.order_num
        ) tmp
        order by tmp.submit_credential_time desc
    </select>
    <select id="selectOrderListV1" resultType="com.ruoyi.system.api.domain.vo.order.OrderListVO">
        SELECT
        ot.id,
        ot.order_num,
        ot.pay_num,
        ot.order_amount,
        sum(ov.amount_dollar) as order_amount_dollar,
        ot.pay_amount,
        ot.pay_amount_dollar,
        ot.current_exchange_rate,
        ot.currency,
        ot.real_pay_amount,
        ot.real_pay_amount_currency,
        ot.difference_amount,
        ot.order_promotion_amount,
        ot.use_balance,
        ot.audit_time,
        ot.audit_user_name,
        ot.order_user_name,
        ot.order_time,
        ot.pay_time,
        ot.pay_account,
        ot.order_user_id,
        ot.merchant_id,
        ot.pay_type,
        ot.pay_type_detail,
        ot.submit_credential_time,
        ot.audit_status,
        ot.is_merge_order,
        ot.order_remark,
        (CASE WHEN ot.audit_status = 2 THEN 1
        WHEN ot.audit_status = 0 THEN 2
        WHEN ot.audit_status = 1 THEN 3 else 100 end) AS sort
        FROM
        order_table ot
        left join order_video ov on ot.order_num = ov.order_num
        <where>
        <include refid="orderVideoAuditWhere"/>
        </where>
        GROUP BY
        ot.id,
        ot.order_num,
        ot.order_amount,
        ot.pay_amount,
        ot.pay_amount_dollar,
        ot.current_exchange_rate,
        ot.currency,
        ot.real_pay_amount,
        ot.difference_amount,
        ot.order_promotion_amount,
        ot.use_balance,
        ot.order_time,
        ot.pay_time,
        ot.pay_account,
        ot.order_user_id,
        ot.merchant_id,
        ot.pay_type,
        ot.submit_credential_time,
        ot.audit_status,
        ot.is_merge_order,
        ot.order_remark

    </select>

    <select id="orderPayDetailList" resultType="com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO">
        SELECT
        opl.pay_time,
        ot.pay_num,
        ot.record_time,
        opl.order_num,
        opl.mchnt_order_no,
        opl.business_id merchant_id,
        ot.merchant_code,
        opl.order_type,
        opl.pay_type,
        opl.pay_type_detail,
        ot.order_amount,
        ot.difference_amount,
        ot.seed_code_discount,
        ot.back_modify_amount,
        opl.pay_amount,
        ot.pay_amount_dollar,
        ot.order_promotion_amount,
        opl.real_pay_amount,
        ot.real_pay_amount_currency,
        ot.currency,
        opl.use_balance,
        ot.current_exchange_rate,
        ot.audit_status,
        ot.audit_time,
        ot.order_remark,
        ot.is_record,
        ot.tax_point_cost,
        ot.pay_account,
        ot.id orderId,
        ot.order_time,
        ot.order_user_id
        FROM
        order_pay_log opl
        left join order_table ot on ot.order_num = opl.order_num
        <where>
            <if test="dto.orderType != null "> and opl.order_type = #{dto.orderType}</if>
            <if test="dto.orderNum != null and dto.orderNum != ''"> and opl.order_num like concat('%', #{dto.orderNum}, '%')</if>
            <if test="dto.isRecord != null"> and ot.is_record = #{dto.isRecord}</if>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and opl.business_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.payTypes != null and dto.payTypes.size() != 0">
                and opl.pay_type in
                <foreach collection="dto.payTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.payTypeDetails != null and dto.payTypeDetails.size() != 0">
                and opl.pay_type_detail in
                <foreach collection="dto.payTypeDetails" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderTypeList != null and dto.orderTypeList.size() != 0">
                and opl.order_type in
                <foreach collection="dto.orderTypeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.orderNums != null and dto.orderNums.size() != 0">
                and ot.order_num in
                <foreach collection="dto.orderNums" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                and opl.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
            </if>
            <if test="dto.recordTimeBegin != null and dto.recordTimeEnd != null">
                and ot.record_time BETWEEN #{dto.recordTimeBegin} AND #{dto.recordTimeEnd}
            </if>
            <if test="dto.timeBegin != null and dto.timeEnd != null">
                and (ot.record_time BETWEEN #{dto.timeBegin} AND #{dto.timeEnd} or ot.pay_time BETWEEN #{dto.timeBegin} AND #{dto.timeEnd})
            </if>
            <if test="dto.searchName != null and dto.searchName != ''">
                and (
                opl.order_num like concat('%', #{dto.searchName}, '%') or
                ot.pay_num like concat('%', #{dto.searchName}, '%') or
                opl.mchnt_order_no like concat('%', #{dto.searchName}, '%')
                <if test="dto.searchBusinessIds != null and dto.searchBusinessIds.size() != 0">
                    or opl.business_id in
                    <foreach collection="dto.searchBusinessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

        </where>
    </select>

    <select id="getFinancialVerificationExports" resultType="com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationExportVO">
        SELECT
            opl.pay_time,
            opl.order_num,
            ot.merchant_code,
            ot.pay_num,
            opl.order_type,
            ov.id AS video_id,
            ov.video_code,
            opl.business_id merchant_id,
            ot.pay_account,
            ot.real_pay_amount_currency,
            ot.currency,
            opl.pay_type,
            ov.video_price,
            ov.pic_price,
            ov.pic_count,
            ov.exchange_price,
            ov.commission_pays_taxes,
            ov.service_price,
            ot.current_exchange_rate,
            ot.difference_amount,
            ot.order_promotion_amount,
            ot.order_amount,
            ov.amount,
            opl.pay_amount,
            opl.use_balance,
            opl.real_pay_amount,
            ot.order_remark auditRemark
        FROM
            order_pay_log opl
                left join order_table ot on ot.order_num = opl.order_num
                left join order_video ov on ov.order_num = ot.order_num
        where opl.pay_time between #{dto.startTime} and #{dto.endTime}
    </select>

    <select id="getAuditOrderStatistics" resultType="com.ruoyi.system.api.domain.vo.order.WorkbenchVO">
        SELECT
        sum(CASE WHEN a.order_type = 0 THEN 1 ELSE 0 END) videoUnAuditCount,
        sum(CASE WHEN a.order_type = 1 THEN 1 ELSE 0 END) memberUnAuditCount
        from (SELECT
        pay_num groupNum,
        MAX(audit_status) audit_status,
        MAX(order_type) order_type
        FROM
        order_table
        WHERE
        submit_credential_time IS NOT NULL
        AND pay_num IS NOT NULL
        AND order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getCode}
        GROUP BY
        pay_num
        UNION ALL
        SELECT
        order_num groupNum,
        audit_status,
        order_type
        FROM
        order_table
        WHERE
        submit_credential_time IS NOT NULL
        AND pay_num IS NULL) a
        <where>
            a.audit_status = ${@com.ruoyi.common.core.enums.AuditStatusEnum@UN_CHECK.getcode}
        </where>
    </select>

    <select id="selectOrderById" parameterType="Long" resultMap="OrderResult">
        <include refid="selectOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrder" parameterType="com.ruoyi.system.api.domain.entity.order.Order" useGeneratedKeys="true" keyProperty="id">
        insert into order_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num,</if>
            <if test="videoCount != null">video_count,</if>
            <if test="orderAmount != null">order_amount,</if>
            <if test="payAmount != null">pay_amount,</if>
            <if test="useBalance != null">use_balance,</if>
            <if test="orderUserId != null">order_user_id,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="payUserId != null">pay_user_id,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
            <if test="videoCount != null">#{videoCount},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="payAmount != null">#{payAmount},</if>
            <if test="useBalance != null">#{useBalance},</if>
            <if test="orderUserId != null">#{orderUserId},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="payUserId != null">#{payUserId},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="merchantCode != null and merchantCode != ''">#{merchantCode},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOrder" parameterType="com.ruoyi.system.api.domain.entity.order.Order">
        update order_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="videoCount != null">video_count = #{videoCount},</if>
            <if test="orderAmount != null">order_amount = #{orderAmount},</if>
            <if test="payAmount != null">pay_amount = #{payAmount},</if>
            <if test="differenceAmount != null">difference_amount = #{differenceAmount},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            pay_type_detail = #{payTypeDetail},
            <if test="realPayAmount != null">real_pay_amount = #{realPayAmount},</if>
            <if test="realPayAmountCurrency != null">real_pay_amount_currency = #{realPayAmountCurrency},</if>
            <if test="useBalance != null">use_balance = #{useBalance},</if>
            <if test="orderUserId != null">order_user_id = #{orderUserId},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="payUserId != null">pay_user_id = #{payUserId},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="isRecord != null">is_record = #{isRecord},</if>
            <if test="recordTime != null">record_time = #{recordTime},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditUserId != null">audit_user_id = #{auditUserId},</if>
            <if test="auditUserName != null and auditUserName != ''">audit_user_name = #{auditUserName},</if>
            <if test="orderRemark != null and orderRemark != ''">order_remark = #{orderRemark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="setMerchant">
        update order_table
        set merchant_id   = #{merchantId},
            merchant_code = UCASE(#{merchantCode}),
            order_user_id = #{businessAccountId},
            pay_user_id = #{businessAccountId}
        where
            biz_user_id = #{bizUserId}
            and merchant_id = 0
    </update>
    <update id="updateOrderFieldNullToNull">
        UPDATE order_table
        SET
            order_num = #{orderTable.orderNum},
            biz_user_id = #{orderTable.bizUserId},
            order_type = #{orderTable.orderType},
            video_count = #{orderTable.videoCount},
            order_amount = #{orderTable.orderAmount},
            pay_amount = #{orderTable.payAmount},
            pay_amount_dollar = #{orderTable.payAmountDollar},
            real_pay_amount = #{orderTable.realPayAmount},
            real_pay_amount_currency = #{orderTable.realPayAmountCurrency},
            difference_amount = #{orderTable.differenceAmount},
            currency = #{orderTable.currency},
            current_exchange_rate = #{orderTable.currentExchangeRate},
            is_default_exchange_rate = #{orderTable.isDefaultExchangeRate},
            use_balance = #{orderTable.useBalance},
            tax_point = #{orderTable.taxPoint},
            tax_point_cost = #{orderTable.taxPointCost},
            seed_code = #{orderTable.seedCode},
            settle_rage = #{orderTable.settleRage},
            seed_code_discount = #{orderTable.seedCodeDiscount},
            channel_name = #{orderTable.channelName},
            channel_type = #{orderTable.channelType},
            back_modify_amount = #{orderTable.backModifyAmount},
            order_user_id = #{orderTable.orderUserId},
            order_time = #{orderTable.orderTime},
            order_time_sign = #{orderTable.orderTimeSign},
            reopen_count = #{orderTable.reopenCount},
            pay_user_id = #{orderTable.payUserId},
            pay_time = #{orderTable.payTime},
            pay_account = #{orderTable.payAccount},
            pay_type = #{orderTable.payType},
            pay_type_detail = #{orderTable.payTypeDetail},
            payee_id = #{orderTable.payeeId},
            alipay_pay_app_id = #{orderTable.alipayPayAppId},
            wechat_pay_app_id = #{orderTable.wechatPayAppId},
            submit_credential_time = #{orderTable.submitCredentialTime},
            audit_status = #{orderTable.auditStatus},
            audit_time = #{orderTable.auditTime},
            audit_user_id = #{orderTable.auditUserId},
            audit_user_name = #{orderTable.auditUserName},
            close_order_time = #{orderTable.closeOrderTime},
            is_auto_cancel = #{orderTable.isAutoCancel},
            ban_invoice = #{orderTable.banInvoice},
            is_record = #{orderTable.isRecord},
            record_time = #{orderTable.recordTime},
            order_remark = #{orderTable.orderRemark},
            merchant_id = #{orderTable.merchantId},
            merchant_code = #{orderTable.merchantCode},
            create_by = #{orderTable.createBy},
            create_time = #{orderTable.createTime},
            update_by = #{orderTable.updateBy},
            update_time = #{orderTable.updateTime},
            order_promotion_amount = #{orderTable.orderPromotionAmount},
            order_amount_dollar = #{orderTable.orderAmountDollar},
            order_user_name = #{orderTable.orderUserName},
            pay_user_name = #{orderTable.payUserName},
            order_user_nick_name = #{orderTable.orderUserNickName},
            pay_user_nick_name = #{orderTable.payUserNickName}
        WHERE id = #{orderTable.id}
    </update>

    <select id="orderAuditStatusStatistics" resultType="com.ruoyi.system.api.domain.vo.order.finace.OrderAuditStatusStatisticsVO">
        SELECT
            sum(CASE WHEN a.audit_status = 0 THEN 1 ELSE 0 END) unCheckNum,
            sum(CASE WHEN a.audit_status = 1 THEN 1 ELSE 0 END) approveNum,
            sum(CASE WHEN a.audit_status = 2 THEN 1 ELSE 0 END) exceptionNum,
            sum(CASE WHEN a.audit_status = 3 THEN 1 ELSE 0 END) closeNum
           from (SELECT
                     pay_num groupNum,
                     MAX( audit_status ) audit_status
                 FROM
                     order_table
                 WHERE
                     submit_credential_time IS NOT NULL
                   AND pay_num IS NOT NULL
                   AND order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getCode}
                 GROUP BY
                     pay_num
                 UNION ALL
                 SELECT
                     order_num groupNum,
                     audit_status
                 FROM
                     order_table
                 WHERE
                     submit_credential_time IS NOT NULL
                   AND pay_num IS NULL
                   AND order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getCode}) a
    </select>

    <select id="orderMemberAuditStatusStatistics" resultType="com.ruoyi.system.api.domain.vo.order.finace.OrderAuditStatusStatisticsVO">
        select
            sum(CASE WHEN ot.audit_status = 0 THEN 1 ELSE 0 END) unCheckNum,
            sum(CASE WHEN ot.audit_status = 1 THEN 1 ELSE 0 END) approveNum,
            sum(CASE WHEN ot.audit_status = 2 THEN 1 ELSE 0 END) exceptionNum,
            sum(CASE WHEN ot.audit_status = 3 THEN 1 ELSE 0 END) closeNum
        from order_table ot
        inner join order_member om on om.order_num = ot.order_num
        where ot.order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIP_ORDER.getcode}
          and ot.submit_credential_time is not null
          and om.status in (
                            ${@com.ruoyi.common.core.enums.OrderMemberStatusEnum@UN_CHECK.getcode},
                            ${@com.ruoyi.common.core.enums.OrderMemberStatusEnum@UN_CONFIRM.getcode},
                            ${@com.ruoyi.common.core.enums.OrderMemberStatusEnum@UN_MATCH.getcode}
            )

    </select>

    <select id="getBalanceLockRecord" resultType="com.ruoyi.system.api.domain.vo.order.BalanceLockRecordVO">
        select
            order_num,
            use_balance amount,
            order_type type
        from order_table
        where merchant_id = #{businessId} and audit_status = ${@com.ruoyi.common.core.enums.AuditStatusEnum@UN_CHECK.getcode} and use_balance > 0.0
    </select>

    <select id="getUnPayListByBusinessId" resultType="com.ruoyi.system.api.domain.entity.order.Order">
        select
            ot.*
        from order_table ot
        inner join order_video ov on ot.order_num = ov.order_num
        where ot.merchant_id = #{businessId} and ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_PAY.getcode}

        </select>

<!--    商家端-发票管理-未开票列表-->
    <select id="selectCompanyNotInvoicedListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.CompanyNotInvoicedListVO">
        select
        ot.id,
        ot.order_type,
        ot.order_num,
        ot.pay_type,
        ot.pay_type_detail,
        ot.pay_time,
        ot.invoice_amount
        from (
        SELECT
            ot.id,
            ot.order_type,
            ot.order_num,
            ot.pay_type,
            ot.pay_type_detail,
            ot.pay_time,
            (
                ot.real_pay_amount
                <if test="dto.orderPayoutAmountMap != null and dto.orderPayoutAmountMap.size() > 0 ">
                    - CASE
                    <foreach collection="dto.orderPayoutAmountMap" item="value" index="orderNum" separator=" ">
                        WHEN ot.order_num = #{orderNum} THEN #{value}
                    </foreach>
                ELSE 0
                END
                </if>
        ) AS invoice_amount
        FROM
        order_table ot
        <where>
            ot.pay_time IS NOT NULL
            and ot.ban_invoice = 0
            AND ot.merchant_id = #{dto.businessId}
            AND ot.real_pay_amount > 0
            AND ot.pay_time >= #{oldDataEndTime}
            AND (
            ot.order_type != ${@com.ruoyi.common.core.enums.OrderTypeEnum@VIDEO_ORDER.getcode} OR
            EXISTS (
            SELECT 1
            FROM order_video ov
            WHERE ov.order_num = ot.order_num
            AND (
            ov.status BETWEEN ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_CONFIRM.getcode} AND
            ${@<EMAIL>}
            or (ov.status = ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode} and ov.un_confirm_time
            is not null)
            )
            )
            )
            AND NOT EXISTS (
                SELECT
                1
                FROM
                order_invoice oi
                JOIN order_invoice_order oio ON oio.invoice_id = oi.id
                WHERE
                    oio.order_num = ot.order_num
                    AND
                    oi.STATUS IN ( ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getcode},
                    ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getcode},
                    ${@<EMAIL>},
                    ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getcode},
                    ${@<EMAIL>} )
            )

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    ot.order_num LIKE CONCAT('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.orderType != null">
                AND ot.order_type = #{dto.orderType}
            </if>

            <if test="dto.payType != null and dto.payType.size() > 0 ">
                AND ot.pay_type IN
                <foreach collection="dto.payType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                AND ot.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
            </if>

            <if test="dto.orderId != null and dto.orderId.size() > 0 ">
                AND ot.id IN
                <foreach collection="dto.orderId" item="orderId" separator="," open="(" close=")">
                    #{orderId}
                </foreach>
            </if>
            <if test="dto.orderNums != null and dto.orderNums.size() > 0 ">
                AND ot.order_num IN
                <foreach collection="dto.orderNums" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="dto.orderPayoutAmountMap != null and dto.orderPayoutAmountMap.size() > 0 ">
            HAVING invoice_amount > 0
        </if>
        <if test="dto.orderId == null or dto.orderId.size() == 0 ">
            UNION ALL
            <include refid="selectRechargeCompanyNotInvoicedListByCondition"/>
        </if>
                 ) ot
    </select>

    <sql id="selectRechargeCompanyNotInvoicedListByCondition">
        SELECT
        opl.id as id,
        opl.order_type,
        opl.order_num,
        opl.pay_type,
        opl.pay_type_detail,
        opl.pay_time,
        (
        opl.real_pay_amount
        <if test="dto.orderPayoutAmountMap != null and dto.orderPayoutAmountMap.size() > 0 ">
            - CASE
            <foreach collection="dto.orderPayoutAmountMap" item="value" index="orderNum" separator=" ">
                WHEN opl.order_num = #{orderNum} THEN #{value}
            </foreach>
            ELSE 0
            END
        </if>
        ) AS invoice_amount
        FROM
        order_pay_log opl
        <where>
            opl.business_id = #{dto.businessId}
            AND opl.real_pay_amount > 0
            and opl.order_type = ${@com.ruoyi.common.core.enums.OrderTypeEnum@ONLINE_RECHARGE.getcode}
            AND NOT EXISTS (
            SELECT
            1
            FROM
            order_invoice oi
            JOIN order_invoice_order oio ON oio.invoice_id = oi.id
            WHERE
            oio.order_num = opl.order_num
            AND oi.STATUS IN ( ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getcode},
            ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getcode},
            ${@<EMAIL>},
            ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getcode},
            ${@<EMAIL>} )
            )

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                opl.order_num LIKE CONCAT('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.orderType != null">
                AND opl.order_type = #{dto.orderType}
            </if>

            <if test="dto.payType != null and dto.payType.size() > 0 ">
                AND opl.pay_type IN
                <foreach collection="dto.payType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.payTimeBegin != null and dto.payTimeEnd != null">
                AND opl.pay_time BETWEEN #{dto.payTimeBegin} AND #{dto.payTimeEnd}
            </if>
            <if test="dto.orderNums != null and dto.orderNums.size() > 0 ">
                AND opl.order_num IN
                <foreach collection="dto.orderNums" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="dto.orderPayoutAmountMap != null and dto.orderPayoutAmountMap.size() > 0 ">
            HAVING invoice_amount > 0
        </if>


    </sql>
</mapper>