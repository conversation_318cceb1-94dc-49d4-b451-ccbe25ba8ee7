<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderInvoiceMapper">

    <sql id="selectOrderInvoice">
            oi.id,
            oi.ticket_code,
            oi.source,
            oi.order_type,
            oi.pay_time,
            oi.merchant_id,
            oi.merchant_code,
            oi.invoice_amount,
            oi.STATUS,
            oi.invoicing_time,
            oi.number,
            oi.object_key,
            oi.invoice_type,
            oi.title_type,
            oi.title,
            oi.duty_paragraph,
            oi.company_name,
            oi.company_address,
            oi.company_phone,
            oi.company_contact,
            oi.attachment_object_key,
            oi.content,
            oi.invoice_remark,
            oi.submit_by,
            oi.submit_by_id,
            oi.submit_time,
            oi.is_new,
            oi.operator_by_type,
            oi.operator_by_id,
            oi.operator_time,
            oi.audit_by_id,
            oi.cautions,
            oi.is_apply_reopen,
            oi.create_time,
            oi.update_time
    </sql>

<!--    商家/运营端-发票管理-待开票列表-->
    <select id="selectToBeInvoicedListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.ToBeInvoicedListVO">
        SELECT
            <include refid="selectOrderInvoice"/>
        FROM
            order_invoice oi
        <where>
            oi.status NOT IN (${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_PERFECT.getCode()} , ${@<EMAIL>()})
            AND oi.status IN (${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getCode()} , ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getCode()}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getCode()})
            AND oi.invoice_amount != 0
            <if test="dto.currentUserType == 1">
                AND oi.merchant_id = #{dto.currentBusinessId}
            </if>
            <if test="dto.keyword != null and dto.keyword != '' ">
                <if test="dto.currentUserType == 0">
                    AND (
                        oi.ticket_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.id IN (SELECT invoice_id FROM order_invoice_order WHERE order_num LIKE concat('%', UCASE(#{dto.keyword}), '%'))
                        OR oi.merchant_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.title LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_name LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_address LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_phone LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_contact LIKE concat('%', UCASE(#{dto.keyword}), '%')
                    )
                </if>
                <if test="dto.currentUserType == 1">
                    AND (
                        oi.title LIKE concat('%', #{dto.keyword}, '%')
                        OR oi.duty_paragraph LIKE concat('%', #{dto.keyword}, '%')
                        OR oi.company_name LIKE concat('%', #{dto.keyword}, '%')
                        <if test="dto.keywordCompanyUserIds != null and dto.keywordCompanyUserIds.size() > 0 ">
                            OR oi.submit_by_id IN
                            <foreach collection="dto.keywordCompanyUserIds" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                    )
                </if>
            </if>

            <if test="dto.status != null and dto.status.size() > 0 ">
                AND oi.status IN
                <foreach collection="dto.status" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="dto.orderType != null">
                AND oi.order_type = #{dto.orderType}
            </if>

            <if test="dto.invoiceType != null">
                AND oi.invoice_type = #{dto.invoiceType}
            </if>

            <if test="dto.source != null">
                AND oi.source = #{dto.source}
            </if>

            <if test="dto.submitTimeBegin != null and dto.submitTimeEnd != null ">
                AND oi.submit_time BETWEEN #{dto.submitTimeBegin} AND #{dto.submitTimeEnd}
            </if>
        </where>
    </select>
<!--    商家/运营端-发票管理-已完成列表-->
    <select id="selectInvoiceFinishListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.InvoiceFinishListVO">
        SELECT
            <include refid="selectOrderInvoice"/>
        FROM
            order_invoice oi
        <where>
                oi.status != ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_PERFECT.getCode()}
                AND oi.status IN (${@<EMAIL>()} , ${@<EMAIL>()}, ${@<EMAIL>()}, ${@<EMAIL>()})
            <if test="dto.currentUserType == 1 ">
                AND oi.merchant_id = #{dto.currentBusinessId}
                AND oi.status != ${@<EMAIL>()}
            </if>

            <if test="dto.keyword != null and dto.keyword != '' ">
                <if test="dto.currentUserType == 0 ">
                    AND (
                        oi.ticket_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.number LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.id IN (SELECT invoice_id FROM order_invoice_order WHERE order_num LIKE concat('%', UCASE(#{dto.keyword}), '%'))
                        OR oi.merchant_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.title LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.duty_paragraph LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_name LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_address LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_phone LIKE concat('%', UCASE(#{dto.keyword}), '%')
                        OR oi.company_contact LIKE concat('%', UCASE(#{dto.keyword}), '%')
                    )
                </if>

                <if test="dto.currentUserType == 1 ">
                    AND (
                        oi.number LIKE concat('%', #{dto.keyword}, '%')
                        OR oi.title LIKE concat('%', #{dto.keyword}, '%')
                        OR oi.duty_paragraph LIKE concat('%', #{dto.keyword}, '%')
                        OR oi.company_name LIKE concat('%', #{dto.keyword}, '%')
                        <if test="dto.keywordCompanyUserIds != null and dto.keywordCompanyUserIds.size() > 0 ">
                            OR oi.submit_by_id IN
                            <foreach collection="dto.keywordCompanyUserIds" item="item" open="(" close=")" separator=",">
                                #{item}
                            </foreach>
                        </if>
                    )
                </if>
            </if>

            <if test="dto.status != null and dto.status.size() > 0 ">
                AND oi.status IN
                <foreach collection="dto.status" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="dto.orderType != null ">
                AND oi.order_type = #{dto.orderType}
            </if>

            <if test="dto.invoiceType != null ">
                AND oi.invoice_type = #{dto.invoiceType}
            </if>

            <if test="dto.source != null ">
                AND oi.source = #{dto.source}
            </if>

            <if test="dto.operatorTimeBegin != null and dto.operatorTimeEnd != null ">
                AND oi.operator_time BETWEEN #{dto.operatorTimeBegin} AND #{dto.operatorTimeEnd}
            </if>

            <if test="dto.submitTimeBegin != null and dto.submitTimeEnd != null ">
                AND oi.submit_time BETWEEN #{dto.submitTimeBegin} AND #{dto.submitTimeEnd}
            </if>
        </where>
    </select>
<!--    商家端-发票管理-数量统计-->
    <select id="companyInvoiceStatistics"
            resultType="com.ruoyi.system.api.domain.vo.order.CompanyInvoiceStatisticsVO">
        SELECT
            SUM(CASE WHEN status IN (${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getCode}) AND invoice_amount != 0 THEN 1 ELSE 0 END) AS quantityToBeInvoiced,
            SUM(CASE WHEN status IN (${@<EMAIL>}, ${@<EMAIL>}, ${@<EMAIL>}) THEN 1 ELSE 0 END) AS invoicedQuantity
        FROM
            order_invoice
        WHERE
            merchant_id = #{currentBusinessId}
    </select>
<!--    运营端-发票管理-数量统计-->
    <select id="backInvoiceStatistics"
            resultType="com.ruoyi.system.api.domain.vo.order.BackInvoiceStatisticsVO">
        SELECT
            SUM(CASE WHEN status IN (${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getCode}) AND invoice_amount != 0 THEN 1 ELSE 0 END) AS quantityToBeInvoiced
        FROM
            order_invoice
    </select>
<!--    运营端-发票管理-开票金额统计-->
    <select id="invoiceAmountStatistics"
            resultType="com.ruoyi.system.api.domain.vo.order.InvoiceAmountStatisticsVO">
        SELECT
            SUM(CASE WHEN status IN (${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_INVOICE.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_CONFIRM.getCode}, ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@TO_BE_REVIEWED.getCode}) THEN invoice_amount ELSE 0 END) AS amountToBeBilled,
            SUM(CASE WHEN status IN (${@<EMAIL>}, ${@<EMAIL>}, ${@<EMAIL>}) THEN invoice_amount ELSE 0 END) AS invoicedAmount
        FROM
            order_invoice
    </select>
<!--    通过订单号获取已投递或者已红冲的发票 一个订单号只获取一条-->
    <select id="selectDeliverOrRedPushListByOrderNums"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoInvoiceVO">
        SELECT
            oi.id,
            oi.ticket_code,
            oi.source,
            oio.invoice_id,
            oio.order_num,
            oiov.video_id,
            oiov.video_code,
            oio.invoice_amount,
            oi.order_type,
            oi.pay_time,
            oi.merchant_id,
            oi.merchant_code,
            oi.invoice_amount,
            oi.STATUS,
            oi.invoicing_time,
            oi.number,
            oi.object_key,
            oi.invoice_type,
            oi.title_type,
            oi.title,
            oi.duty_paragraph,
            oi.company_name,
            oi.company_address,
            oi.company_phone,
            oi.company_contact,
            oi.attachment_object_key,
            oi.content,
            oi.invoice_remark,
            oi.submit_by,
            oi.submit_by_id,
            oi.submit_time,
            oi.is_new,
            oi.operator_by_type,
            oi.operator_by_id,
            oi.operator_time,
            oi.audit_by_id,
            oi.cautions,
            oi.create_time,
            oi.update_time
        FROM
            order_invoice_order oio
                JOIN order_invoice oi ON oi.id = oio.invoice_id
                LEFT JOIN order_invoice_order_video oiov ON oiov.invoice_order_id = oio.id
        <where>
            oi.`status` IN ( ${@<EMAIL>}, ${@<EMAIL>} )
          AND oi.invoicing_time = (
            SELECT
                MAX( sub_oi.invoicing_time )
            FROM
                order_invoice_order sub_oio
                    JOIN order_invoice sub_oi ON sub_oi.id = sub_oio.invoice_id
            WHERE
                sub_oio.order_num = oio.order_num
              AND sub_oi.STATUS IN ( ${@<EMAIL>}, ${@<EMAIL>} )
              group by sub_oio.order_num
        )

        <if test="orderNums != null and orderNums.size() > 0">
            AND oio.order_num IN
            <foreach collection="orderNums" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        <if test="videoIds != null and videoIds.size() > 0">
            AND oiov.video_id IN
            <foreach collection="videoIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>

        </where>
    </select>
<!--    通过订单号获取开票信息-->
    <select id="getLastInvoiceByOrderNum" resultType="com.ruoyi.system.api.domain.entity.order.OrderInvoice">
        SELECT
            <include refid="selectOrderInvoice"/>
        FROM
            order_invoice oi
                JOIN order_invoice_order oio ON oio.invoice_id = oi.id
        WHERE
            oio.order_num = #{orderNum} AND oi.`status` NOT IN ( ${@com.ruoyi.common.core.enums.OrderInvoiceStatusEnum@UN_PERFECT.getCode}, ${@<EMAIL>} )
        ORDER BY oi.submit_time DESC
        LIMIT 1
    </select>
</mapper>