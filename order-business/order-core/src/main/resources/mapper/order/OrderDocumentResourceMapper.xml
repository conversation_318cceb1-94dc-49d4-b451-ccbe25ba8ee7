<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderDocumentResourceMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderDocumentResource" id="OrderDocumentResourceResult">
        <result property="id"    column="id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="payNum"    column="pay_num"    />
        <result property="objectKey"    column="object_key"    />
        <result property="uploadType"    column="upload_type"    />
    </resultMap>

    <sql id="selectOrderDocumentResourceVo">
        select id, order_num, pay_num, object_key, upload_type from order_document_resource
    </sql>

    <select id="selectOrderDocumentResourceList" parameterType="com.ruoyi.system.api.domain.entity.order.OrderDocumentResource" resultMap="OrderDocumentResourceResult">
        <include refid="selectOrderDocumentResourceVo"/>
        <where>
            <if test="orderNum != null  and orderNum != ''"> and order_num = #{orderNum}</if>
            <if test="keyword != null  and keyword != ''"> and (order_num = #{keyword} or pay_num = #{keyword})</if>
            <if test="objectKey != null "> and object_key = #{objectKey}</if>
            <if test="uploadType != null "> and upload_type = #{uploadType}</if>
        </where>
    </select>
    <select id="selectOrderDocumentResourceListByKeyword" resultMap="OrderDocumentResourceResult">
        <include refid="selectOrderDocumentResourceVo"/>
        where order_num = #{keyword} or pay_num = #{keyword}
    </select>

    <select id="selectOrderDocumentResourceById" parameterType="Long" resultMap="OrderDocumentResourceResult">
        <include refid="selectOrderDocumentResourceVo"/>
        where id = #{id}
    </select>

    <insert id="insertOrderDocumentResource" parameterType="com.ruoyi.system.api.domain.entity.order.OrderDocumentResource" useGeneratedKeys="true" keyProperty="id">
        insert into order_document_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num,</if>
            <if test="objectKey != null">object_key,</if>
            <if test="uploadType != null">upload_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
            <if test="objectKey != null">#{objectKey},</if>
            <if test="uploadType != null">#{uploadType},</if>
        </trim>
    </insert>

    <update id="updateOrderDocumentResource" parameterType="com.ruoyi.system.api.domain.entity.order.OrderDocumentResource">
        update order_document_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="objectKey != null">object_key = #{objectKey},</if>
            <if test="uploadType != null">upload_type = #{uploadType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderDocumentResourceById" parameterType="Long">
        delete from order_document_resource where id = #{id}
    </delete>

    <delete id="deleteOrderDocumentResourceByIds" parameterType="String">
        delete from order_document_resource where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>