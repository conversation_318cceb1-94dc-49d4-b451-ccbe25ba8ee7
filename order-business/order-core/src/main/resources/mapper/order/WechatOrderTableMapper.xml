<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.WechatOrderTableMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.WechatOrderTable">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="mchntOrderNo" column="mchnt_order_no" jdbcType="VARCHAR"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="qrcode" column="qrcode" jdbcType="VARCHAR"/>
            <result property="orderAmount" column="order_amount" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,mchnt_order_no,order_num,
        qrcode,order_amount,status,
        create_time
    </sql>
    <select id="unPayAlipayOrderList" resultType="com.ruoyi.system.api.domain.entity.order.WechatOrderTable">
        SELECT <include refid="Base_Column_List"></include>
        FROM wechat_order_table wot
        WHERE wot.order_num in (SELECT ot.order_num
                                FROM order_table ot
                                         left join order_video ov ON ot.order_num = ov.order_num
                                WHERE ov.status = 1
                                GROUP BY ot.order_num,
                                         ot.order_amount,
                                         ot.pay_amount,
                                         ot.real_pay_amount,
                                         ot.use_balance,
                                         ot.order_time,
                                         ot.pay_user_id,
                                         ot.pay_time,
                                         ot.pay_type,
                                         ot.merchant_id,
                                         ot.create_time)
        union All
        SELECT <include refid="Base_Column_List"></include>
        FROM wechat_order_table wot
        WHERE wot.order_num in (SELECT ot.order_num
                                from order_table ot
                                         left join order_member om on ot.order_num = om.order_num
                                where om.status = 1)

    </select>
</mapper>
