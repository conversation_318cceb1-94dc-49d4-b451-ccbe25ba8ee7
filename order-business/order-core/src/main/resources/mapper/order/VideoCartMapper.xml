<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.VideoCartMapper">

    <sql id="selectOrderVo">
        select id,
               platform,
               video_format,
               shooting_country,
               model_type,
               product_chinese,
               product_english,
               product_link,
               product_pic,
               intention_model_id,
               reference_video_link,
               pic_count,
               reference_pic as reference_pic_id,
               amount,
               video_price,
               pic_price,
               commission_pays_taxes,
               exchange_price,
               service_price,
               create_order_business_id,
               create_order_biz_user_id,
               create_order_user_id,
               create_order_user_account,
               create_order_user_name,
               create_order_user_nick_name,
               create_time,
               update_time
        from video_cart
    </sql>
    <update id="updateCart">
        update video_cart
        <trim prefix="SET" suffixOverrides=",">
            <if test="platform != null">platform = #{platform},</if>
            <if test="videoFormat != null">video_format = #{videoFormat},</if>
            <if test="shootingCountry != null">shooting_country = #{shootingCountry},</if>
            <if test="modelType != null">model_type = #{modelType},</if>
            <if test="productChinese != null and productChinese != ''">product_chinese = #{productChinese},</if>
            <if test="productEnglish != null and productEnglish != ''">product_english = #{productEnglish},</if>
            product_link = #{productLink},
            product_pic = #{productPic},
            intention_model_id = #{intentionModelId},
            reference_video_link = #{referenceVideoLink},
            pic_count = #{picCount},
            reference_pic = #{referencePicId},
            <if test="amount != null">amount = #{amount},</if>
            <if test="videoPrice != null">video_price = #{videoPrice},</if>
            <if test="picPrice != null">pic_price = #{picPrice},</if>
            <if test="commissionPaysTaxes != null">commission_pays_taxes = #{commissionPaysTaxes},</if>
            <if test="exchangePrice != null">exchange_price = #{exchangePrice},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCartIntentionModel">
        update video_cart
        set intention_model_id = #{modelId}
        where id = #{id}
    </update>

    <select id="selectCartList" resultType="com.ruoyi.system.api.domain.vo.order.VideoCartVO">
        <include refid="selectOrderVo"/>
        <where>
            create_order_business_id = #{businessId}
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                    product_chinese LIKE concat('%', #{dto.keyword}, '%')
                    OR product_english LIKE concat('%', #{dto.keyword}, '%')
                    OR product_link LIKE concat('%', #{dto.keyword}, '%')
                    OR create_order_user_nick_name LIKE concat('%', #{dto.keyword}, '%')
                    <if test="dto.modelIds != null and dto.modelIds.size() != 0 ">
                        OR intention_model_id IN
                        <foreach collection="dto.modelIds" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            </if>

            <if test="dto.platforms != null and dto.platforms.size() != 0">
                AND platform IN
                    <foreach collection="dto.platforms" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>

            <if test="dto.userName != null and dto.userName.size() != 0">
                AND create_order_user_id IN
                <foreach collection="dto.userName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectListByProductPicIsNullAndProductLink"
            resultType="com.ruoyi.system.api.domain.entity.order.VideoCart">
        SELECT
            id,product_link
        FROM
            video_cart
        WHERE
            product_link in
            <foreach collection="productLinks" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND product_pic IS NULL
    </select>
</mapper>
