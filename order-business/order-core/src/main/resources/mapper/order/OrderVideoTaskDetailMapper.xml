<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoTaskDetailMapper">

    <sql id="selectOrderTaskVo">
        select id, video_id, task_num, type, content,issue_pic as issue_pic_id, submit_by, submit_time, video_code, product_chinese, product_english, shoot_model_id, order_user_id, priority, assignee_id, status, end_time, create_time, update_time from order_video_task
    </sql>

    <select id="selectWorkOrderTaskDetailListByCondition" parameterType="com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail" resultType="com.ruoyi.system.api.domain.vo.order.WorkOrderTaskDetailListVO">
        SELECT
            ovtd.id,
            ovtd.task_id,
            ovtd.task_num,
            ovtd.submit_time,
            ovtd.work_order_type,
            ovtd.content,
            ovtd.issue_pic AS issue_pic_id,
            ovtd.priority,
            ovtd.submit_by_id,
            ovtd.assignee_id,
            ovtd.last_reply_time,
            ovtd.`status`
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        <where>
            ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode}
            <if test="dto.priority != null and dto.priority.size() > 0 ">
                AND ovtd.priority IN
                <foreach collection="dto.priority" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.taskIds != null and dto.taskIds.size() > 0 ">
                AND ovtd.task_id IN
                <foreach collection="dto.taskIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.workOrderType != null and dto.workOrderType.size() > 0 ">
                AND ovtd.work_order_type IN
                <foreach collection="dto.workOrderType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size() > 0 ">
                AND ovtd.status IN
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.assigneeId != null and dto.assigneeId.size() > 0 ">
                AND ovtd.assignee_id IN
                <foreach collection="dto.assigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.lastReplyTime and dto.lastReplyTimeLogicalSymbol != null and dto.lastReplyTimeLogicalSymbol != '' ">
                AND date_format(ovtd.last_reply_time,'%Y-%m-%d') ${dto.lastReplyTimeLogicalSymbol} date_format(#{dto.lastReplyTime},'%Y-%m-%d')
            </if>

            <if test="dto.historyAssigneeId != null and dto.historyAssigneeId.size() > 0 ">
                AND ovtd.task_num IN ( SELECT task_num FROM order_video_task_work_assignee_history WHERE original_assignee_id IN
                <foreach collection="dto.historyAssigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.submitById != null and dto.submitById.size() > 0 ">
                AND ovtd.submit_by_id IN
                <foreach collection="dto.submitById" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.submitTimeBegin != null and dto.submitTimeEnd != null">
                AND ovtd.submit_time BETWEEN #{dto.submitTimeBegin} AND #{dto.submitTimeEnd}
            </if>

        </where>
        ORDER BY
            FIELD(ovtd.status, 1, 4, 5, 6) ASC,
            ovtd.create_time DESC
    </select>

    <select id="selectAfterSaleOrderTaskDetailListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailListVO">
        SELECT
            ovtd.id,
            ovtd.task_id,
            ovtd.task_num,
            ovtd.submit_time,
            ovtd.after_sale_class,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.content,
            ovtd.issue_pic AS issue_pic_id,
            ovtd.priority,
            ovtd.submit_time,
            ovtd.submit_by_id,
            ovtd.submit_by,
            ovtd.assignee_id,
            ovtd.`status`
        FROM
            order_video_task_detail ovtd
                inner JOIN order_video_task ovt ON ovt.id = ovtd.task_id
<!--        <if test="dto.afterSaleTimeEnd != null">-->
<!--            inner join order_video_task_detail_flow_record ovtdfr on ovtdfr.task_num = ovtd.task_num and ovtdfr.operate_type = ${@com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum@CONFIRM_AFTER_SALE.getcode}-->
<!--        </if>-->
        <where>
            ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}
            <if test="dto.priority != null and dto.priority.size() > 0 ">
                AND ovtd.priority IN
                <foreach collection="dto.priority" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.taskIds != null and dto.taskIds.size() > 0 ">
                AND ovtd.task_id IN
                <foreach collection="dto.taskIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


            <choose>
                <when test="dto.afterSaleVideoTypes != null and dto.afterSaleVideoTypes.size() > 0 and dto.afterSalePicTypes != null and dto.afterSalePicTypes.size() > 0">
                    AND (
                    ovtd.after_sale_video_type IN
                    <foreach collection="dto.afterSaleVideoTypes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    ovtd.after_sale_pic_type IN
                    <foreach collection="dto.afterSalePicTypes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <otherwise>
                    <if test="dto.afterSaleVideoTypes != null and dto.afterSaleVideoTypes.size() > 0">
                        AND ovtd.after_sale_video_type IN
                        <foreach collection="dto.afterSaleVideoTypes" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="dto.afterSalePicTypes != null and dto.afterSalePicTypes.size() > 0 ">
                        AND ovtd.after_sale_pic_type IN
                        <foreach collection="dto.afterSalePicTypes" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>

            <if test="dto.status != null and dto.status.size() > 0 ">
                AND ovtd.status IN
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.assigneeId != null and dto.assigneeId.size() > 0 ">
                AND ovtd.assignee_id IN
                <foreach collection="dto.assigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.submitById != null and dto.submitById.size() > 0 ">
                AND ovtd.submit_by_id IN
                <foreach collection="dto.submitById" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.afterSaleTimeEnd != null">
                AND ovtd.confirm_time &lt;= #{dto.afterSaleTimeEnd}
                <if test="dto.afterSaleTimeBegin != null">
                    AND ovtd.confirm_time &gt;= #{dto.afterSaleTimeBegin}
                </if>
            </if>
        </where>
    </select>

    <select id="selectWorkbenchTaskDetailList" resultType="com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO">

        SELECT
            ovt.task_type,
            ovtd.id taskDetailId,
            ovtd.task_id,
            ovtd.task_num,
            ovtd.after_sale_class,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.content,
            ovtd.issue_pic AS issue_pic_id,
            ovtd.priority,
            ovtd.submit_time,
            ovtd.submit_by_id,
            ovtd.submit_by,
            ovtd.`status`
        FROM
            order_video_task_detail ovtd
                inner JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        <where>
            ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode}
            <if test="userId  != null">
                AND ovtd.assignee_id = #{userId}
            </if>

        </where>
        order by ovtd.submit_time desc
    </select>

    <select id="selectWorkbenchRefuseTaskDetailList" resultType="com.ruoyi.system.api.domain.vo.order.workbench.WorkbenchTaskDetailVO">

        SELECT
            ovt.task_type,
            ovtd.id taskDetailId,
            ovtd.task_id,
            ovtd.task_num,
            ovtd.after_sale_class,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.content,
            ovtdfr.operate_by as refuseOperateBy,
            ovtdfr.remark as refuseRemark,
            ovtdfr.time as refuseTime,
            ovtd.`status`
        FROM
            order_video_task_detail ovtd
                inner JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                inner JOIN (
                SELECT *,
                ROW_NUMBER() OVER (PARTITION BY task_num ORDER BY id DESC) AS rn
                FROM order_video_task_detail_flow_record
                where operate_type in (${@com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum@REFUSE_AFTER_SALE.getcode},${@com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum@REJECT_WORK_ORDER.getcode})
                ) ovtdfr ON ovtdfr.task_num = ovtd.task_num AND ovtdfr.rn = 1
        <where>
            ovtd.status = ${@<EMAIL>}
            <if test="userId  != null">
                AND ovtd.submit_by_id = #{userId}
            </if>

        </where>
        order by ovtdfr.time desc
    </select>
    <select id="getModelAllDistinctTask" resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail">
        SELECT
            video_id,
            shoot_model_id
        FROM
            order_video_task
        GROUP BY
            video_id,
            shoot_model_id
    </select>
    <select id="getModelAfterSaleDistinctTask"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideo">
        SELECT
            ov.id,
            ov.shoot_model_id
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                LEFT JOIN order_video ov ON ov.id = ovt.video_id
        WHERE
              ov.shoot_model_id IS NOT NULL
              AND ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}
              AND ovtd.after_sale_video_type IN (${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getcode},${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getcode})
        GROUP BY
            ov.id,
            ov.shoot_model_id
    </select>
    <select id="getRefundPendingTask" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO">
        SELECT
            ovtd.id,
            ovt.task_type,
            ovtd.submit_time,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.content
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        WHERE ovtd.task_id = #{taskId}
            AND ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode}
            AND ovtd.work_order_type = ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@MODEL_DIDNT_GET_IT.getcode}
            <if test="assigneeId != null ">
                AND ovtd.assignee_id = #{assigneeId}
            </if>
            <if test="taskDetailIds != null and taskDetailIds.size() > 0">
                AND ovtd.id IN
                <foreach collection="taskDetailIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getBackHelpModelUploadMaterialPendingTask"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO">
        SELECT
            ovtd.id,
            ovt.task_type,
            ovtd.submit_time,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.content
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        WHERE ovtd.task_id = #{taskId}
          AND ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode}
          AND ovtd.work_order_type = ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@ACCELERATOR_MATERIAL.getcode}
            <if test="assigneeId != null ">
                AND ovtd.assignee_id = #{assigneeId}
            </if>
            <if test="taskDetailIds != null and taskDetailIds.size() > 0">
                AND ovtd.id IN
                <foreach collection="taskDetailIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getFeedbackMaterialPendingTask"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoTaskDetailVO">
        SELECT
            ovtd.id,
            ovt.task_type,
            ovtd.submit_time,
            ovtd.after_sale_video_type,
            ovtd.after_sale_pic_type,
            ovtd.work_order_type,
            ovtd.content
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        <where>
            (
                (ovtd.STATUS = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode} AND ovtd.work_order_type IN (
                                                                                                                                  ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@NEED_CLIPS.getcode},
                                                                                                                                  ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@ACCELERATOR_MATERIAL.getcode} ,
                                                                                                                                  ${@com.ruoyi.common.core.enums.OrderTaskWorkOrderTypeEnum@PROBLEMS_RELATED_TO_MATERIAL_LINKS.getcode}
                                                                                                                                 )
                    )
                OR
                    (ovtd.STATUS = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.getcode} AND ovtd.after_sale_class IS NOT NULL
                     AND (
                         ovtd.after_sale_video_type IN (${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOOT_VIDEO.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RESHOT_VIDEO.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@NEED_HD_VIDEO.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@SOURCE_MATERIAL.getcode},
                                                        ${@com.ruoyi.common.core.enums.OrderTaskAfterSaleVideoTypeEnum@RE_UPLOAD.getcode}
                                                       )
                             OR ovtd.after_sale_pic_type IN (${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOOT_PIC.getcode},
                                                             ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@RESHOT_PIC.getcode},
                                                             ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@NEED_HD_PIC.getcode},
                                                             ${@com.ruoyi.common.core.enums.OrderTaskAfterSalePicTypeEnum@SOURCE_MATERIAL.getcode}
                                                            )
                        )

                    <if test="afterSaleClass != null and afterSaleClass.size() > 0 ">
                        AND ovtd.after_sale_class IN
                        <foreach collection="afterSaleClass" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                )
            )
            <if test="taskIds != null and taskIds.size() > 0">
                AND ovtd.task_id IN
                <foreach collection="taskIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="taskDetailIds != null and taskDetailIds.size() > 0">
                AND ovtd.id IN
                <foreach collection="taskDetailIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="existingTaskDetailIds != null and existingTaskDetailIds.size() > 0">
                AND ovtd.id NOT IN
                <foreach collection="existingTaskDetailIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getAfterSaleTaskDetailInfoVO" resultType="com.ruoyi.system.api.domain.vo.order.task.AfterSaleTaskDetailInfoVO">
        SELECT
            ovtd.*,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link
        FROM
            order_video_task_detail ovtd
                inner JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                left join order_video ov on ov.id = ovt.video_id
        where ovtd.task_num = #{taskNum}
    </select>
    <select id="selectPartialFieldListByVideoIdsForSetAutoCompleteTimeStart"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderVideoTaskDetail">
        SELECT
            ovt.video_id AS task_id,
            ovtd.status,
            ovtd.end_time,
            ovtd.update_time
        FROM
            order_video_task_detail ovtd
                JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        WHERE ovt.video_id IN
        <foreach collection="videoIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getChineseCustomerServiceTaskCountByDate"
            resultType="com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo">
        SELECT
            user_id AS customerServiceId,
            COUNT(DISTINCT CASE WHEN DATE ( submit_time ) = #{date} THEN id END ) AS addedCount,
            COUNT(DISTINCT CASE WHEN DATE ( end_time ) = #{date} AND `STATUS` = ${@<EMAIL>} THEN id END ) AS finishCount
        FROM
            (
                SELECT
                    ovtd.id,
                    ovtd.submit_by_id AS user_id,
                    ovtd.submit_time,
                    ovtd.end_time,
                    ovtd.STATUS
                FROM
                    order_video_task_detail ovtd
                        JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                        JOIN order_video ov ON ov.id = ovt.video_id
                WHERE ov.rollback_id IS NULL
                  AND ov.STATUS != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}

                UNION ALL

                SELECT
                    ovtd.id,
                    ovtd.assignee_id AS user_id,
                    ovtd.submit_time,
                    ovtd.end_time,
                    ovtd.STATUS
                FROM
                    order_video_task_detail ovtd
                        JOIN order_video_task ovt ON ovt.id = ovtd.task_id
                        JOIN order_video ov ON ov.id = ovt.video_id
                WHERE
                    ov.rollback_id IS NULL
                  AND ov.STATUS != ${@com.ruoyi.common.core.enums.OrderStatusEnum@TRADE_CLOSE.getcode}
            ) AS ovtd
        WHERE
            user_id IS NOT NULL
        GROUP BY
            user_id
    </select>
</mapper>