<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoUploadLinkMapper">


    <select id="selectUploadLinkListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.UploadLinkListVO">
        SELECT
            ovul.id,
            ov.id AS video_id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.pic_count,
            ov.reference_pic AS reference_pic_id,
            ov.refund_pic_count,
            ov.shoot_model_id,
            ov.contact_id,
            ov.issue_id,
            ovul.need_upload_link,
            ovul.asin,
            ovul.video_title_first,
            ovul.video_title,
            ovul.video_cover_first,
            ovul.video_cover,
            ovul.remark,
            ovul.upload_account,
            ovul.upload_user_id,
            ovul.upload_time,
            ovul.`status`,
            IFNULL(ovulr.count, 0 ) AS count,
            IFNULL( hcr.history_clip_record, 0 ) AS history_clip_record
        FROM
            order_video_upload_link ovul
                JOIN order_video ov ON ov.id = ovul.video_id
                LEFT JOIN (
                    select upload_link_id,
                           max(count) count
                    from order_video_upload_link_record
                    group by upload_link_id
                ) ovulr ON ovulr.upload_link_id = ovul.id
                LEFT JOIN (
                    SELECT
                        m.video_id,
                        COUNT(*) AS history_clip_record
                    FROM
                        order_video_feed_back_material_info t
                            JOIN order_video_feed_back_material m ON m.id = t.material_id
                    GROUP BY
                        m.video_id
                ) hcr ON hcr.video_id = ovul.video_id
        <where>
            <if test="dto.uploadLinkCount != null">
                <choose>
                    <when test="dto.uploadLinkCount &lt;= 4 ">
                        and ovulr.count = #{dto.uploadLinkCount}
                    </when>
                    <otherwise>
                        and ovulr.count &gt; 4
                    </otherwise>
                </choose>
            </if>
            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                ov.video_code LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_chinese LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_english LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ov.product_link LIKE CONCAT('%', #{dto.keyword}, '%')
                OR ovul.video_title LIKE CONCAT('%', #{dto.keyword}, '%')
                <if test="dto.backUserIds != null and dto.backUserIds.size() > 0 ">
                    OR ov.contact_id IN
                    <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    OR ov.issue_id IN
                    <foreach collection="dto.backUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>

            <if test="dto.shootModelIds != null and dto.shootModelIds.size() > 0 ">
                AND ov.shoot_model_id IN
                <foreach collection="dto.shootModelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <choose>
                <when test="dto.status != null">
                    <if test="dto.status == 0">
                        AND ( ovul.`status` = #{dto.status} OR ovul.`status` = ${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@NO_UPLOAD_REQUIRED.getcode} )
                    </if>
                    <if test="dto.status != 0">
                        AND ovul.`status` = #{dto.status}
                    </if>
                </when>
                <otherwise>
                    AND ovul.`status` IN (${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@HAVEN_T_UPLOADED.getcode},${@com.ruoyi.common.core.enums.UploadLinkStatusEnum@UPLOAD_TO_BE_CONFIRMED.getcode})
                </otherwise>
            </choose>
        
            <if test="dto.existCover != null">
                <choose>
                    <when test="dto.existCover == 1">
                        AND ( ovul.video_cover IS NOT NULL AND ovul.video_cover != '' )
                    </when>
                    <when test="dto.existCover == 0">
                        AND ( ovul.video_cover IS NULL OR ovul.video_cover = '' )
                    </when>
                </choose>
            </if>

            <if test="dto.uploadAccounts != null and dto.uploadAccounts.size() > 0">
                AND ovul.upload_account IN
                <foreach collection="dto.uploadAccounts" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.uploadLinkTimeBegin != null and dto.uploadLinkTimeEnd != null">
                AND ovul.time BETWEEN #{dto.uploadLinkTimeBegin} AND #{dto.uploadLinkTimeEnd}
            </if>

        </where>
        GROUP BY
            ovul.id,
            ov.id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.platform,
            ov.model_type,
            ov.shooting_country,
            ov.video_format,
            ov.shoot_model_id,
            ov.contact_id,
            ov.issue_id,
            ovul.need_upload_link,
            ovul.asin,
            ovul.video_title_first,
            ovul.video_title,
            ovul.video_cover_first,
            ovul.video_cover,
            ovul.remark,
            ovul.upload_account,
            ovul.upload_user_id,
            ovul.upload_time,
            ovul.`status`
    </select>
    <select id="getShootModelId" resultType="java.lang.Long">
        SELECT
            ov.shoot_model_id
        FROM
            order_video_upload_link ovul
                JOIN order_video ov ON ov.id = ovul.video_id
        WHERE ovul.`status` IN
                <foreach collection="status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
    </select>
</mapper>