<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPayLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.OrderPayLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="orderType" column="order_type" jdbcType="BOOLEAN"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="mchntOrderNo" column="mchnt_order_no" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="BOOLEAN"/>
            <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
            <result property="payAmount" column="pay_amount" jdbcType="DECIMAL"/>
            <result property="realPayAmount" column="real_pay_amount" jdbcType="DECIMAL"/>
            <result property="useBalance" column="use_balance" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_num,order_type,
        business_id,mchnt_order_no,pay_type,
        pay_time,pay_amount,real_pay_amount,
        use_balance
    </sql>
</mapper>
