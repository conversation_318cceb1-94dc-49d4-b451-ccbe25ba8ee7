<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoRefundMapper">

    <sql id="selectOrderVideoRefundVo">
        select id, video_id,apply_time,order_num, refund_num, video_code,product_chinese, product_english,
               product_link,platform,shooting_country,pic_count,refund_pic_count,status,  shoot_model_id, contact_id,issue_id,
               amount, real_amount, refund_amount,refund_amount_total, refund_type, initiator_source, initiator_name, refund_cause,remark, refund_status,
               operate_time, reject_cause, business_id,is_cancel_order,is_full_refund,is_full_refund_pic,create_time,remark, update_time from order_video_refund
    </sql>

    <select id="selectOrderVideoRefundListByCondition" parameterType="com.ruoyi.system.api.domain.dto.order.OrderVideoRefundListDTO" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoRefundVO">
        <include refid="selectOrderVideoRefundVo"/>
        <where>
            <if test="loginBusinessId != null"> and business_id = #{loginBusinessId}</if>
            <if test="orderNum != null  and orderNum != ''"> and order_num like concat('%', #{orderNum}, '%')</if>
            <if test="refundNum != null  and refundNum != ''"> and refund_num like concat('%', #{refundNum}, '%')</if>
            <if test="videoCode != null  and videoCode != ''"> and video_code like concat('%', UCASE(#{videoCode}), '%')</if>
            <if test="productName != null  and productName != ''"> and (product_chinese like concat('%', #{productName}, '%') or product_english like concat('%', #{productName}, '%') )</if>

            <if test="refundStatus != null and refundStatus.size() != 0 ">
                and refund_status in
                <foreach collection="refundStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
             </if>

            <if test="refundType != null and refundType.size() != 0 ">
                and refund_type in
                <foreach collection="refundType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
             </if>

            <if test="timeBegin != null and timeEnd != null">
                and (
                <choose>
                    <when test="loginBusinessId == null">
                        apply_time between #{timeBegin} and #{timeEnd}
                        or operate_time between #{timeBegin} and #{timeEnd}
                    </when>
                    <otherwise>
                        apply_time between #{timeBegin} and #{timeEnd}
                    </otherwise>
                </choose>
                )
            </if>

            <if test="modelIds != null and modelIds.size() != 0">
                    and shoot_model_id in
                <foreach collection="modelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
             </if>

             <if test="keyword != null and keyword != ''">
                 and (
                     order_num like concat('%', #{keyword}, '%')
                     OR refund_num like concat('%', #{keyword}, '%')
                     OR video_code like concat('%', UCASE(#{keyword}), '%')
                     OR product_chinese like concat('%', #{keyword}, '%')
                     OR product_english like concat('%', #{keyword}, '%')
                     OR product_link like concat('%', #{keyword}, '%')
                     <if test="backUserIds != null and backUserIds.size() != 0">
                         OR contact_id IN
                         <foreach collection="backUserIds" item="item" open="(" separator="," close=")">
                             #{item}
                         </foreach>
                         OR issue_id IN
                         <foreach collection="backUserIds" item="item" open="(" separator="," close=")">
                             #{item}
                         </foreach>
                     </if>
                 )
             </if>

        </where>
    </select>

    <select id="selectVideoIdByRefund" resultType="java.lang.Long">
        SELECT DISTINCT(video_id)
        FROM order_video_refund
        WHERE
        refund_status IN (#{refundStatus.AFTER_SALE_UN_CHECK}, #{refundStatus.AFTER_SALE}, #{refundStatus.AFTER_SALE_FINISHED})
        and refund_type = ${@com.ruoyi.common.core.enums.RefundTypeEnum@CANCEL_ORDER.getcode}
        <if test="videoIds != null and videoIds.size() != 0">
            AND video_id IN
            <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
                #{videoId}
            </foreach>
        </if>
    </select>

    <select id="getRefundTotalAmount" resultType="java.math.BigDecimal">
        select sum(refund_amount)
        from order_video_refund
        where
            video_id = #{videoId}
            and refund_status = ${@com.ruoyi.common.core.enums.RefundStatusEnum@AFTER_SALE_FINISHED.getcode}
    </select>
    <select id="refundSuccessList" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoRefundSuccessVO">
        SELECT
            ovr.id,
            ovr.apply_time,
            ovr.order_num,
            ovr.refund_num,
            ovr.video_code,
            ovr.product_chinese,
            ovr.product_english,
            ot.merchant_code,
            ovr.refund_type,
            ovr.initiator_source,
            ovr.initiator_name,
            ovr.pic_count,
            ovr.refund_pic_count,
            ovr.refund_cause,
            ovr.operate_by,
            ovr.operate_time,
            ovr.refund_amount
        FROM
            order_video_refund ovr
                LEFT JOIN order_table ot ON ot.order_num = ovr.order_num
        WHERE ovr.refund_status  = ${@com.ruoyi.common.core.enums.RefundStatusEnum@AFTER_SALE_FINISHED.getCode}
                <if test="dto.keyword != null and dto.keyword != ''">
                    AND (
                        ovr.order_num like concat('%', #{dto.keyword}, '%')
                        OR ovr.refund_num like concat('%', #{dto.keyword}, '%')
                        OR ovr.video_code like concat('%', #{dto.keyword}, '%')
                        OR ovr.product_chinese like concat('%', #{dto.keyword}, '%')
                        OR ovr.product_english like concat('%', #{dto.keyword}, '%')
                        OR ovr.initiator_name like concat('%', #{dto.keyword}, '%')
                        OR ovr.operate_by like concat('%', #{dto.keyword}, '%')
                    )
                </if>

                <if test="dto.refundType != null and dto.refundType.size() >0">
                    AND ovr.refund_type in
                    <foreach collection="dto.refundType" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="dto.timeBegin != null and dto.timeEnd != null">
                    AND (
                        ovr.apply_time between #{dto.timeBegin} and #{dto.timeEnd}
                        OR ovr.operate_time between #{dto.timeBegin} and #{dto.timeEnd}
                    )
                </if>
    </select>

    <select id="refundStatistics" resultType="com.ruoyi.system.api.domain.vo.order.OrderVideoRefundStatisticsVO">
        select
            video_id,
            SUM(CASE WHEN refund_type = ${@<EMAIL>} THEN refund_amount ELSE 0.0 END) AS reparationAmount,
            SUM(CASE WHEN refund_type = ${@com.ruoyi.common.core.enums.RefundTypeEnum@CANCEL_ORDER.getcode} THEN refund_amount ELSE 0.0 END) AS cancelOrderAmount,
            SUM(CASE WHEN refund_type = ${@com.ruoyi.common.core.enums.RefundTypeEnum@CANCEL_OPENTION.getcode} THEN refund_amount ELSE 0.0 END) AS cancelOpentionAmount
        from order_video_refund
        where
        refund_status = ${@com.ruoyi.common.core.enums.RefundStatusEnum@AFTER_SALE_FINISHED.getcode} and
        video_id in
        <foreach collection="videoIds" item="videoId" open="(" separator="," close=")">
            #{videoId}
        </foreach>
        group by video_id
    </select>
</mapper>