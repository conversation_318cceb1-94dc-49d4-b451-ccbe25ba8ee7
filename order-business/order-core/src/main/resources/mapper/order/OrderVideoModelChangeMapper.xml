<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoModelChangeMapper">


    <update id="updateOrderVideoModelChangeFieldNullToNull">
        UPDATE order_video_model_change
        SET
            video_id = #{orderVideoModelChange.videoId},
            rollback_id = #{orderVideoModelChange.rollbackId},
            model_id = #{orderVideoModelChange.modelId},
            source = #{orderVideoModelChange.source},
            model_pic = #{orderVideoModelChange.modelPic},
            name = #{orderVideoModelChange.name},
            nation = #{orderVideoModelChange.nation},
            sex = #{orderVideoModelChange.sex},
            type = #{orderVideoModelChange.type},
            age_group = #{orderVideoModelChange.ageGroup},
            platform = #{orderVideoModelChange.platform},
            cooperation = #{orderVideoModelChange.cooperation},
            cooperation_score = #{orderVideoModelChange.cooperationScore},
            issue_user_name = #{orderVideoModelChange.issueUserName},
            selected_time = #{orderVideoModelChange.selectedTime},
            schedule_type = #{orderVideoModelChange.scheduleType},
            carry_type = #{orderVideoModelChange.carryType},
            commission_unit = #{orderVideoModelChange.commissionUnit},
            commission = #{orderVideoModelChange.commission},
            overstatement = #{orderVideoModelChange.overstatement},
            specialty_category = #{orderVideoModelChange.specialtyCategory},
            model_tag = #{orderVideoModelChange.modelTag},
            create_time = #{orderVideoModelChange.createTime}
        WHERE id = #{orderVideoModelChange.id}
    </update>
</mapper>