<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.WechatPayLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.WechatPayLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="appid" column="appid" jdbcType="VARCHAR"/>
            <result property="mchid" column="mchid" jdbcType="VARCHAR"/>
            <result property="outTradeNo" column="out_trade_no" jdbcType="VARCHAR"/>
            <result property="transactionId" column="transaction_id" jdbcType="VARCHAR"/>
            <result property="tradeType" column="trade_type" jdbcType="VARCHAR"/>
            <result property="tradeState" column="trade_state" jdbcType="VARCHAR"/>
            <result property="tradeStateDesc" column="trade_state_desc" jdbcType="VARCHAR"/>
            <result property="bankType" column="bank_type" jdbcType="VARCHAR"/>
            <result property="attach" column="attach" jdbcType="VARCHAR"/>
            <result property="successTime" column="success_time" jdbcType="TIMESTAMP"/>
            <result property="openid" column="openid" jdbcType="VARCHAR"/>
            <result property="total" column="total" jdbcType="INTEGER"/>
            <result property="payerTotal" column="payer_total" jdbcType="INTEGER"/>
            <result property="currency" column="currency" jdbcType="VARCHAR"/>
            <result property="payerCurrency" column="payer_currency" jdbcType="VARCHAR"/>
            <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,appid,mchid,
        out_trade_no,transaction_id,trade_type,
        trade_state,trade_state_desc,bank_type,
        attach,success_time,openid,
        total,payer_total,currency,
        payer_currency,device_id,promotion_detail
    </sql>
</mapper>
