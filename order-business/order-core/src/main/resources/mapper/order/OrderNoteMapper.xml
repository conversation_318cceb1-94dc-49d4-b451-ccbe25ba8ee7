<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderNoteMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.order.OrderNote" id="OrderNoteResult">
        <result property="id"    column="id"    />
        <result property="orderNum"    column="order_num"    />
        <result property="title"    column="title"    />
        <result property="dutyParagraph"    column="duty_paragraph"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectOrderNoteVo">
        select id, order_num, title, duty_paragraph, content, create_time, update_time from order_note
    </sql>

    <select id="selectOrderNoteList" parameterType="com.ruoyi.system.api.domain.entity.order.OrderNote" resultMap="OrderNoteResult">
        <include refid="selectOrderNoteVo"/>
        <where>
            <if test="orderNum != null  and orderNum != ''"> and order_num = #{orderNum}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="dutyParagraph != null  and dutyParagraph != ''"> and duty_paragraph = #{dutyParagraph}</if>
        </where>
    </select>

    <select id="selectOrderNoteById" parameterType="Long" resultMap="OrderNoteResult">
        <include refid="selectOrderNoteVo"/>
        where id = #{id}
    </select>
    <select id="selectOrderNoteByOrderNum" resultMap="OrderNoteResult">
        <include refid="selectOrderNoteVo"/>
        where order_num = #{orderNum}
    </select>

    <insert id="insertOrderNote" parameterType="com.ruoyi.system.api.domain.entity.order.OrderNote" useGeneratedKeys="true" keyProperty="id">
        insert into order_note
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="dutyParagraph != null and dutyParagraph != ''">duty_paragraph,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">#{orderNum},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="dutyParagraph != null and dutyParagraph != ''">#{dutyParagraph},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateOrderNote" parameterType="com.ruoyi.system.api.domain.entity.order.OrderNote">
        update order_note
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNum != null and orderNum != ''">order_num = #{orderNum},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="dutyParagraph != null and dutyParagraph != ''">duty_paragraph = #{dutyParagraph},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOrderNoteById" parameterType="Long">
        delete from order_note where id = #{id}
    </delete>

    <delete id="deleteOrderNoteByIds" parameterType="String">
        delete from order_note where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>