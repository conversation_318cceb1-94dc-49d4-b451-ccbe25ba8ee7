<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.ThirdPayLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.ThirdPayLog">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="curr_type" column="curr_type" jdbcType="VARCHAR"/>
        <result property="mchnt_order_no" column="mchnt_order_no" jdbcType="VARCHAR"/>
        <result property="orderNumber" column="order_number" jdbcType="VARCHAR"/>
        <result property="order_amt" column="order_amt" jdbcType="VARCHAR"/>
        <result property="order_type" column="order_type" jdbcType="VARCHAR"/>
        <result property="reserved_addn_inf" column="reserved_addn_inf" jdbcType="VARCHAR"/>
        <result property="reserved_bank_type" column="reserved_bank_type" jdbcType="VARCHAR"/>
        <result property="reserved_buyer_logon_id" column="reserved_buyer_logon_id" jdbcType="VARCHAR"/>
        <result property="reserved_channel_order_id" column="reserved_channel_order_id" jdbcType="VARCHAR"/>
        <result property="reserved_coupon_fee" column="reserved_coupon_fee" jdbcType="VARCHAR"/>
        <result property="reserved_fund_bill_list" column="reserved_fund_bill_list" jdbcType="VARCHAR"/>
        <result property="reserved_fy_settle_dt" column="reserved_fy_settle_dt" jdbcType="VARCHAR"/>
        <result property="reserved_fy_trace_no" column="reserved_fy_trace_no" jdbcType="VARCHAR"/>
        <result property="reserved_is_credit" column="reserved_is_credit" jdbcType="VARCHAR"/>
        <result property="reserved_promotion_detail" column="reserved_promotion_detail" jdbcType="VARCHAR"/>
        <result property="reserved_settlement_amt" column="reserved_settlement_amt" jdbcType="VARCHAR"/>
        <result property="settle_order_amt" column="settle_order_amt" jdbcType="VARCHAR"/>
        <result property="term_id" column="term_id" jdbcType="VARCHAR"/>
        <result property="transaction_id" column="transaction_id" jdbcType="VARCHAR"/>
        <result property="txn_fin_ts" column="txn_fin_ts" jdbcType="VARCHAR"/>
        <result property="user_id" column="user_id" jdbcType="VARCHAR"/>
        <result property="create_time" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="update_time" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,curr_type,mchnt_order_no,order_number
        order_amt,order_type,reserved_addn_inf,
        reserved_bank_type,reserved_buyer_logon_id,reserved_channel_order_id,
        reserved_coupon_fee,reserved_fund_bill_list,reserved_fy_settle_dt,
        reserved_fy_trace_no,reserved_is_credit,reserved_promotion_detail,
        reserved_settlement_amt,settle_order_amt,term_id,
        transaction_id,txn_fin_ts,user_id,
        create_time,update_time
    </sql>
</mapper>
