<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderVideoTaskMapper">

    <sql id="selectOrderTaskList">
        SELECT
            ovt.id,
            ov.id AS video_id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.pic_count,
            ov.create_order_user_id,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            ov.shoot_model_id,
            ov.contact_id,
            ov.status
        FROM
            order_video_task ovt
                LEFT JOIN order_video ov ON ov.id = ovt.video_id
                LEFT JOIN order_video_task_detail ovtd ON ovtd.task_id = ovt.id
    </sql>

    <sql id="selectOrderTaskListGroupBy">
        GROUP BY
            ovt.id,
            ov.id,
            ov.product_pic,
            ov.video_code,
            ov.product_chinese,
            ov.product_english,
            ov.product_link,
            ov.pic_count,
            ov.create_order_user_name,
            ov.create_order_user_nick_name,
            ov.shoot_model_id,
            ov.contact_id,
            ov.status
    </sql>

    <!--    任务单-工单列表-->
    <select id="selectWorkOrderListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.WorkOrderTaskListVO">
        <include refid="selectOrderTaskList"/>
        <where>
            ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode}
            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    ov.video_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                    OR ov.product_chinese LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.product_english LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.product_link LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_name LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_nick_name LIKE concat('%', #{dto.keyword}, '%')
                    OR ovtd.task_num LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.shootModelId != null and dto.shootModelId.size() > 0 ">
                AND ov.shoot_model_id IN
                    <foreach item="item" index="index" collection="dto.shootModelId" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            </if>

            <if test="dto.priority != null and dto.priority.size() > 0 ">
                AND ovtd.priority IN
                <foreach collection="dto.priority" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.workOrderType != null and dto.workOrderType.size() > 0 ">
                AND ovtd.work_order_type IN
                <foreach collection="dto.workOrderType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size() > 0 ">
                AND ovtd.status IN
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.assigneeId != null and dto.assigneeId.size() > 0 ">
                AND ovtd.assignee_id IN
                <foreach collection="dto.assigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.lastReplyTime and dto.lastReplyTimeLogicalSymbol != null and dto.lastReplyTimeLogicalSymbol != '' ">
                AND date_format(ovtd.last_reply_time,'%Y-%m-%d') ${dto.lastReplyTimeLogicalSymbol} date_format(#{dto.lastReplyTime},'%Y-%m-%d')
            </if>

            <if test="dto.historyAssigneeId != null and dto.historyAssigneeId.size() > 0 ">
                AND ovtd.task_num IN ( SELECT task_num FROM order_video_task_work_assignee_history WHERE original_assignee_id IN
                <foreach collection="dto.historyAssigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.submitById != null and dto.submitById.size() > 0 ">
                AND ovtd.submit_by_id IN
                <foreach collection="dto.submitById" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.submitTimeBegin != null and dto.submitTimeEnd != null">
                AND ovtd.submit_time BETWEEN #{dto.submitTimeBegin} AND #{dto.submitTimeEnd}
            </if>

            <if test="dto.videoCode != null and dto.videoCode != '' ">
                AND ov.video_code = #{dto.videoCode}
            </if>

            <if test="dto.relevanceUserId != null and dto.relevanceUserId.size() > 0 ">
                AND( ovtd.submit_by_id IN
                <foreach collection="dto.relevanceUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or ovtd.assignee_id IN
                <foreach collection="dto.relevanceUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>


        </where>
        <include refid="selectOrderTaskListGroupBy"/>
    </select>

    <select id="selectAfterSaleListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.task.AfterSaleListVO">
        <include refid="selectOrderTaskList"/>
<!--        <if test="dto.afterSaleTimeEnd != null">-->
<!--            inner join order_video_task_detail_flow_record ovtdfr on ovtdfr.task_num = ovtd.task_num and ovtdfr.operate_type = ${@com.ruoyi.common.core.enums.OrderTaskDetailFlowOperateTypeEnum@CONFIRM_AFTER_SALE.getcode}-->
<!--        </if>-->
        <where>
            ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode}
            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    ov.video_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                    OR ov.product_chinese LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.product_english LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.product_link LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_name LIKE concat('%', #{dto.keyword}, '%')
                    OR ov.create_order_user_nick_name LIKE concat('%', #{dto.keyword}, '%')
                    OR ovtd.task_num LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.priority != null and dto.priority.size() > 0 ">
                AND ovtd.priority IN
                <foreach collection="dto.priority" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.shootModelId != null and dto.shootModelId.size() > 0 ">
                AND ov.shoot_model_id IN
                    <foreach item="item" index="index" collection="dto.shootModelId" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            </if>
            <choose>
                <when test="dto.afterSaleVideoTypes != null and dto.afterSaleVideoTypes.size() > 0 and dto.afterSalePicTypes != null and dto.afterSalePicTypes.size() > 0">
                    AND (
                    ovtd.after_sale_video_type IN
                    <foreach collection="dto.afterSaleVideoTypes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    ovtd.after_sale_pic_type IN
                    <foreach collection="dto.afterSalePicTypes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <otherwise>
                    <if test="dto.afterSaleVideoTypes != null and dto.afterSaleVideoTypes.size() > 0">
                        AND ovtd.after_sale_video_type IN
                        <foreach collection="dto.afterSaleVideoTypes" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>

                    <if test="dto.afterSalePicTypes != null and dto.afterSalePicTypes.size() > 0 ">
                        AND ovtd.after_sale_pic_type IN
                        <foreach collection="dto.afterSalePicTypes" item="item" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </otherwise>
            </choose>
            <if test="dto.status != null and dto.status.size() > 0 ">
                AND ovtd.status IN
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.assigneeId != null and dto.assigneeId.size() > 0 ">
                AND ovtd.assignee_id IN
                <foreach collection="dto.assigneeId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.submitById != null and dto.submitById.size() > 0 ">
                AND ovtd.submit_by_id IN
                <foreach collection="dto.submitById" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.afterSaleTimeEnd != null">
                AND ovtd.confirm_time &lt;= #{dto.afterSaleTimeEnd}
                <if test="dto.afterSaleTimeBegin != null">
                    AND ovtd.confirm_time &gt;= #{dto.afterSaleTimeBegin}
                </if>
            </if>

            <if test="dto.relevanceUserId != null and dto.relevanceUserId.size() > 0 ">
                AND( ovtd.submit_by_id IN
                <foreach collection="dto.relevanceUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or ovtd.assignee_id IN
                <foreach collection="dto.relevanceUserId" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.videoCode != null and dto.videoCode != '' ">
                AND ov.video_code = #{dto.videoCode}
            </if>

        </where>
        <include refid="selectOrderTaskListGroupBy"/>
    </select>

    <select id="getOrderTaskStatus" resultType="com.ruoyi.system.api.domain.vo.order.OrderTaskStatusVO">
        select
            sum(CASE WHEN ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.getcode} THEN 1 ELSE 0 END) unHandleCount,
            sum(CASE WHEN ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.getcode} THEN 1 ELSE 0 END) handleIngCount,
            sum(CASE WHEN ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@APPLICATION_FOR_CANCELLATION.getcode} THEN 1 ELSE 0 END) applicationForCancellationCount,
            sum(CASE WHEN ovtd.status = ${@<EMAIL>} THEN 1 ELSE 0 END) handleCount,
            sum(CASE WHEN ovtd.status = ${@<EMAIL>} THEN 1 ELSE 0 END) rejectCount,
            sum(CASE WHEN ovtd.status = ${@<EMAIL>} THEN 1 ELSE 0 END) closeCount,
            count(1) totalCount
        from order_video_task ovt
        left join order_video_task_detail ovtd on ovt.id = ovtd.task_id
        where ovt.task_type = #{taskType}
    </select>

    <select id="checkDataScope" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            order_video_task
        WHERE
            task_num = #{taskNum}
            <if test="submitBy != null ">
                AND (submit_by != #{submitBy} OR submit_by IS NULL )
            </if>
            <if test="assigneeId != null ">
                AND (assignee_id != #{assigneeId} OR assignee_id IS NULL )
            </if>
    </select>
    <select id="selectVideoTaskOrderVOListByVideoIds"
            resultType="com.ruoyi.system.api.domain.vo.order.VideoTaskOrderVO">
        SELECT
            ovt.id,
            ovt.video_id,
            ovt.task_type,
            ovtd.`status`,
            ovtd.after_sale_video_type
        FROM
            order_video_task_detail ovtd
                LEFT JOIN order_video_task ovt ON ovt.id = ovtd.task_id
        <where>
            <if test="videoIds != null and videoIds.size() > 0">
                AND ovt.video_id IN
                <foreach collection="videoIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="shootModelIdList" resultType="java.lang.Long">
        select DISTINCT shoot_model_id FROM order_video ov WHERE id in (select DISTINCT video_id from order_video_task ovtt where task_type = #{taskType})
    </select>
    <select id="getAssigneeUserList" resultType="java.lang.Long">
        select DISTINCT assignee_id FROM order_video_task_detail ovtd where task_id in (select id from order_video_task ovtt where task_type = #{taskType})
    </select>
    <select id="getSubmitUserList" resultType="java.lang.Long">
        select DISTINCT submit_by_id FROM order_video_task_detail ovtd where task_id in (select id from order_video_task ovtt where task_type = #{taskType})
    </select>
    <select id="getOrderTaskStatistics" resultType="com.ruoyi.system.api.domain.vo.order.OrderTaskStatisticsVO">
        select
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode} THEN ovt.id END) AS afterSaleTotalCount,
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode} and ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.code} THEN ovt.id END) AS unHandleAfterSaleCount,
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@AFTER_SALE.getcode} and ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.code} THEN ovt.id END) AS handlingAfterSaleCount,
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode} THEN ovt.id END) AS workOrderTotalCount,
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode} and ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.code} THEN ovt.id END) AS unHandleWorkOrderCount,
        COUNT(CASE WHEN ovt.task_type = ${@com.ruoyi.common.core.enums.OrderTaskTypeEnum@WORK_ORDER.getcode} and ovtd.status = ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.code} THEN ovt.id END) AS handlingWorkOrderCount
        from order_video_task ovt
        inner join order_video_task_detail ovtd on ovt.id = ovtd.task_id
        <where>
            ovtd.status in(${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@UN_HANDLE.code}, ${@com.ruoyi.common.core.enums.OrderTaskStatusEnum@HANDLE_ING.code})
            <if test="userId != null">
                and (ovtd.submit_by_id = #{userId} or ovtd.assignee_id = #{userId})
            </if>
            <if test="assigneeId != null">
                and ovtd.assignee_id = #{assigneeId}
            </if>
        </where>
    </select>
</mapper>