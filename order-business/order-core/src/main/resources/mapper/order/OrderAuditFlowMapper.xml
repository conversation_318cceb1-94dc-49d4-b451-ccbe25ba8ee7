<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderAuditFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderAuditFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="BOOLEAN"/>
            <result property="auditUserName" column="audit_user_name" jdbcType="VARCHAR"/>
            <result property="auditUserId" column="audit_user_id" jdbcType="BIGINT"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="orderDocumentResource" column="order_document_resource" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_num,audit_status,
        audit_user_name,audit_user_id,audit_time,
        order_document_resource,remark
    </sql>
</mapper>
