<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.OrderPayeeAccountConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderPayeeAccountConfig">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
        <result property="detailId" column="detail_id" jdbcType="BIGINT"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="type" column="type" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,account_name,detail_id,
        status,type,create_by,
        create_by_id,create_time,update_by,
        update_by_id,update_time
    </sql>
    <select id="dataList" resultType="com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO">
        select <include refid="Base_Column_List"/> from order_payee_account_config where status = 1 order by `type` desc
    </select>
    <select id="typeList" resultType="com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigDTO">
        select <include refid="Base_Column_List"/> from order_payee_account_config where type = #{type} order by create_time desc
    </select>
    <select id="typeInfo" resultType="com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO">
        select payee_id as id,
                id as detailId,
                account_name,
               `type`,
               status,
               bank_account,
               bank_name,
               company_account_type,
               company_bank_code,
               company_bank_sub_code,
               company_bank_name,
               company_bank_address,
               company_bank_swift_code,
               company_bank_payee_address,
               create_by,
               create_by_id,
               create_time,
               update_by,
               update_by_id,
               update_time
        from order_payee_account_config_info
        where id = (select detail_id from order_payee_account_config where type = #{id} and status = 1)
    </select>
    <select id="getHistoryInfo"
            resultType="com.ruoyi.system.api.domain.dto.order.OrderPayeeAccountConfigInfoDTO">
        select  id,
                account_name,
                `type`,
                payee_id,
                status,
                bank_account,
                bank_name,
                company_account_type,
                company_bank_code,
                company_bank_name,
                company_bank_address,
                company_bank_sub_code,
                company_bank_swift_code,
                company_bank_payee_address,
                create_time,
                update_time
        from order_payee_account_config_info
        where id = #{id}
    </select>
</mapper>
