package com.wnkx.biz.business.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.biz.business.activity.BusinessMemberActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.business.activity.UpdateMemberActivityDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.wnkx.db.mapper.SuperMapper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_member_activity(商家会员活动)】的数据库操作Mapper
 * @createDate 2025-01-16 18:04:14
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity
 */
public interface BusinessMemberActivityMapper extends SuperMapper<BusinessMemberActivity> {

    /**
     * 获取保存、修改数据时间段下的交叉时间
     *
     * @param dto
     * @return
     */
    default List<BusinessMemberActivity> getCrossTimeList(BusinessMemberActivityDTO dto) {
        LambdaQueryWrapper<BusinessMemberActivity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinessMemberActivity::getMemberPackageType, dto.getMemberPackageType());
        wrapper.le(BusinessMemberActivity::getStartTime, dto.getEndTime());
        wrapper.ge(BusinessMemberActivity::getEndTime, dto.getStartTime());

        if (ObjectUtil.isNotNull(dto.getId())) {
            wrapper.ne(BusinessMemberActivity::getId, dto.getId());
        }
        wrapper.orderByDesc(BusinessMemberActivity::getStartTime);
        return this.selectList(wrapper);
    }

    /**
     * 根据时间获取活动列表
     * @param date
     * @return
     */
    default List<BusinessMemberActivity> getActivityListByDate(Integer date){
        return this.selectList(new LambdaQueryWrapper<BusinessMemberActivity>()
                .le(BusinessMemberActivity::getStartTime, date)
                .ge(BusinessMemberActivity::getEndTime, date)
        );

    }

    /**
     * 根据时间获取套餐类型下的会员活动
     * @param memberPackageType
     * @param date
     * @return
     */
    default BusinessMemberActivity getActivityByTime(Integer memberPackageType, Integer date) {
        return this.selectOne(new LambdaQueryWrapper<BusinessMemberActivity>()
                .eq(BusinessMemberActivity::getMemberPackageType, memberPackageType)
                .le(BusinessMemberActivity::getStartTime, date)
                .ge(BusinessMemberActivity::getEndTime, date)
        );

    }

    /**
     * 更新会员活动状态
     *
     * @param dto
     */
    default void updateStatus(UpdateMemberActivityDTO dto) {
        this.updateById(new BusinessMemberActivity().setId(dto.getId()).setStatus(dto.getStatus()));
    }
}




