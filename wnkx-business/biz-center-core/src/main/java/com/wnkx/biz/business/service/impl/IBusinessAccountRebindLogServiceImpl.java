package com.wnkx.biz.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.wnkx.biz.business.mapper.BusinessAccountRebindLogMapper;
import com.wnkx.biz.business.service.IBusinessAccountRebindLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class IBusinessAccountRebindLogServiceImpl extends ServiceImpl<BusinessAccountRebindLogMapper, BusinessAccountRebindLog> implements IBusinessAccountRebindLogService {
}
