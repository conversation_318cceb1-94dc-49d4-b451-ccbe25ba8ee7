package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail(商家余额详情表)】的数据库操作Service
 * @createDate 2024-12-25 16:59:33
 */
public interface IBusinessBalanceDetailService extends IService<BusinessBalanceDetail> {

    /**
     * 根据商家ID获取有效余额详情
     *
     * @param businessId
     * @return
     */
    List<BusinessBalanceDetail> getValidBusinessBalanceDetailListByBusinessId(Long businessId);

    /**
     * 获取vo数据
     * @param list
     * @param loadRemark 是否加载备注
     * @return
     */
    List<PayoutBusinessBalanceDetailVO> getBusinessBalanceDetailVOList(List<BusinessBalanceDetail> list, Integer loadRemark);

    /**
     * 保存余额详情
     *
     * @param businessBalanceDetail
     * @return
     */
    BusinessBalanceDetail saveBusinessBalanceDetail(BusinessBalanceDetail businessBalanceDetail);


    /**
     * 根据单号列表获取 商家余额详情
     *
     * @param numbers
     * @return
     */
    List<BusinessBalanceDetail> getDetailListByNumbers(List<String> numbers);


}
