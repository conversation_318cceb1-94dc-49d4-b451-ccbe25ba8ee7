package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.AccountAuditStatusEnum;
import com.ruoyi.system.api.domain.dto.biz.business.account.AccountApplyQueryDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountRebindLogVo;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessSubAccountListVo;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_account_apply(账号申请表)】的数据库操作Mapper
* @createDate 2024-08-01 11:39:06
* @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply
*/
@Mapper
public interface BusinessAccountApplyMapper extends SuperMapper<BusinessAccountApply> {

    /**
     * 查询账号申请列表
     * @param dto
     * @return
     */
    List<BusinessAccountApplyVO> list(AccountApplyQueryDTO dto);

    /**
     * 查询申请账号数量
     * @param dto
     * @return
     */
    Integer businessAccountApplyNum(AccountApplyQueryDTO dto);

    /**
     * 检测审核微信是否存在（待审核）
     *
     * @param unionid
     * @return 存在=true  不存在=false
     */
    default boolean checkUnionidStatus(String unionid) {
        return selectCount(new LambdaUpdateWrapper<BusinessAccountApply>()
                .eq(BusinessAccountApply::getUnionid, unionid)
                .in(BusinessAccountApply::getAuditStatus, List.of(AccountAuditStatusEnum.UN_AUDIT.getCode()))
        ) >= 1;
    }

    List<BusinessSubAccountListVo> selectSubAccountList(@Param("businessId") Long businessId);

    List<BusinessAccountRebindLogVo> selectApplyAndRebindLog();
}
