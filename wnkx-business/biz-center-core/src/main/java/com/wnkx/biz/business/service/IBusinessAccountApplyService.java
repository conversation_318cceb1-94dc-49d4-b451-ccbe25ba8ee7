package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.security.annotation.LoginUserType;
import com.ruoyi.system.api.domain.dto.biz.business.account.AccountApplyQueryDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.AuditAccountApplyDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.BusinessAccountApplyDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_account_apply(账号申请表)】的数据库操作Service
* @createDate 2024-08-01 11:39:06
*/
@Validated
public interface IBusinessAccountApplyService extends IService<BusinessAccountApply> {

    /**
     * 查询账号审核列表
     * @param dto
     * @return
     */
    List<BusinessAccountApplyVO> list(AccountApplyQueryDTO dto);

    /**
     * 查询申请账号数量
     * @param dto
     * @return
     */
    Integer businessAccountApplyNum(AccountApplyQueryDTO dto);

    /**
     * 保存账号申请
     * @param dto
     * @return
     */
    BusinessAccountApply save(@Valid BusinessAccountApplyDTO dto);

    /**
     * 审核账号申请数据
     * @param dto
     * @return
     */
    @LoginUserType(userTypes = UserTypeConstants.USER_TYPE, isOwnerAccount = true)
    void audit(@Valid AuditAccountApplyDTO dto);

    /**
     * 根据ID修改申请数据
     * @param businessAccountApply
     * @return
     */
    @Override
    boolean updateById(BusinessAccountApply businessAccountApply);

}
