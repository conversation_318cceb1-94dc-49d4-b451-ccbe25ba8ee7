package com.wnkx.biz.logistic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.LogisticConstant;
import com.ruoyi.common.core.enums.LogisticMainStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.exception.business.LogisticRegisterException;
import com.ruoyi.common.core.exception.business.LogisticSignException;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.LogisticFollowNotifyDTO;
import com.ruoyi.system.api.domain.entity.Logistic;
import com.ruoyi.system.api.domain.entity.LogisticInfo;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;
import com.wnkx.biz.logistic.mapper.LogisticMapper;
import com.wnkx.biz.logistic.service.ILogisticService;
import com.wnkx.biz.logistic.service.LogisticInfoService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 物流信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LogisticServiceImpl extends ServiceImpl<LogisticMapper, Logistic> implements ILogisticService {
    /**
     * 物流信息详情服务
     */
    private final LogisticInfoService logisticInfoService;
    private final RemoteService remoteService;

    private final OkHttpClient okHttpClient;

    @Value("${logistic.17-token}")
    private String seventeenToken;
    @Value("${logistic.url}")
    private String url;

    /**
     * 通过物流单号获取物流信息
     */
    @Override
    public LogisticVO getLogisticInfo(String number) {
        List<LogisticVO> logisticVOS = selectListByNumbers(List.of(number));
        if (CollUtil.isNotEmpty(logisticVOS)) {
            return logisticVOS.get(0);
        }
        return null;
    }

    /**
     * 通过物流单号获取最新的物流信息
     */
    @Override
    public List<LogisticInfoVO> getLastLogisticInfo(Collection<String> numbers) {
        return logisticInfoService.getLastLogisticInfo(numbers);
    }

    /**
     * 通过条件查询物流单号
     */
    @Override
    public Collection<String> getNumbersByCondition(LogisticListDTO dto) {
        return logisticInfoService.getNumbersByCondition(dto);
    }

    /**
     * 通过物流单号查询物流信息
     *
     * @return 物流信息
     */
    @Override
    public List<LogisticVO> selectListByNumbers(Collection<String> numbers) {
        List<LogisticInfo> logisticInfos = logisticInfoService.selectListByNumber(numbers);

        if (CollUtil.isEmpty(logisticInfos)) {
            return new ArrayList<>();
        }

        Map<String, List<LogisticInfo>> groupedByNumber = logisticInfos.stream()
                .collect(Collectors.groupingBy(LogisticInfo::getNumber));

        // 对每个分组内的列表进行 curTime 降序排序
        groupedByNumber.values().forEach(list ->
                list.sort(Comparator.comparing(LogisticInfo::getCurTime).reversed())
        );

        List<Logistic> logistics = baseMapper.selectListByNumber(numbers);
        List<LogisticVO> logisticVOS = BeanUtil.copyToList(logistics, LogisticVO.class);

        for (LogisticVO logisticVO : logisticVOS) {
            List<LogisticInfo> logisticInfoList = groupedByNumber.getOrDefault(logisticVO.getNumber(), new ArrayList<>());
            List<LogisticInfoVO> logisticInfoVOS = BeanUtil.copyToList(logisticInfoList, LogisticInfoVO.class);
            for (LogisticInfoVO logisticInfoVO : logisticInfoVOS) {
                logisticInfoVO.setMainStatusSketch(LogisticMainStatus.getSketchByLabel(logisticInfoVO.getMainStatus()));
                logisticInfoVO.setSubStatusSketch(LogisticMainStatus.LogisticSubStatus.getSketchByLabel(logisticInfoVO.getSubStatus()));
            }
            logisticVO.setLogisticInfo(logisticInfoVOS);
        }

        return logisticVOS;
    }

    /**
     * 接收API推送的物流信息
     *
     * @param request 请求
     */
    @Override
    public void notify(HttpServletRequest request) {
        try {
            log.info("接收到API推送的物流信息");
            String logisticBody = getRequestBody(request);
            String generatedSignature = getGeneratedSignature(logisticBody, seventeenToken);
            log.info("签名密钥：{}", generatedSignature);
            String sign = request.getHeader("sign");
            log.info("接收到的签名密钥：{}", sign);

            if (!generatedSignature.equals(sign)) {
                log.error("签名验证失败");
                throw new LogisticSignException("签名验证失败");
            }
            parseBody(logisticBody);
        } catch (Exception e) {
            log.error("处理API推送的物流消息失败", e);
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 注册物流单号
     */
    @Override
    public void register(Collection<String> numbers) {
        if (CollUtil.isNotEmpty(numbers) && numbers.size() > LogisticConstant.REGISTER_MAX) {
            throw new LogisticRegisterException("单次只能提交40个物流单号");
        }
        log.info("开始注册物流单号");
        SpringUtils.getAopProxy(this).createLogisticInitData(numbers);

        MediaType mediaType = MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);

        JSONArray jsonArray = this.createRequestNums(numbers);

        RequestBody body = RequestBody.create(jsonArray.toString(), mediaType);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader(HttpHeaders.CONTENT_TYPE, org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                .addHeader(LogisticConstant.LOGISTIC_TOKEN, seventeenToken)
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (null != response.body()) {
                log.info("请求注册单号：返回数据：{}", response.body().string());
            }
            log.info("请求注册单号：返回状态码：{}", response.code());

            if (!response.isSuccessful()) {
                String errorMsg = getErrorMsg(response);
                log.error("注册单号失败：错误消息：{}", errorMsg);
                throw new LogisticRegisterException(errorMsg);
            }
        } catch (Exception e) {
            log.error("注册单号失败：{}", e.getMessage());
            throw new LogisticRegisterException("注册单号失败：" + e.getMessage());
        }
    }

    /**
     * 创建物流初始数据
     *
     * @param numbers 物流单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void createLogisticInitData(Collection<String> numbers) {
        List<Logistic> logistics = new ArrayList<>();
        List<LogisticInfo> logisticInfos = new ArrayList<>();

        List<String> existNumbers = baseMapper.getExistNumbers(numbers);
        for (String number : numbers) {
            if (existNumbers.contains(number)) {
                continue;
            }
            Logistic logistic = new Logistic();
            logistic.setNumber(number);

            LogisticInfo logisticInfo = new LogisticInfo();
            logisticInfo.setNumber(number);
            logisticInfo.setMainStatus(LogisticMainStatus.NOT_FOUND.getLabel());

            logistics.add(logistic);
            logisticInfos.add(logisticInfo);
        }
        baseMapper.saveBatch(logistics);
        logisticInfoService.saveBatch(logisticInfos);
    }

    private JSONArray createRequestNums(Collection<String> numbers) {
        JSONArray jsonArray = new JSONArray();

        for (String number : numbers) {
            JSONObject json = new JSONObject();
            json.put("number", number);

            jsonArray.add(json);
        }
        return jsonArray;
    }

    /**
     * 注册物流单号并获取错误信息
     * 此方法处理响应，提取其中的错误信息。
     *
     * @param response 请求的响应对象，包含物流单号注册的结果信息。
     * @return 如果有错误信息，则返回错误信息字符串；如果没有错误信息，则返回空字符串。
     * @throws IOException 如果处理响应过程中发生IO异常。
     */
    private String getErrorMsg(Response response) throws IOException {
        if (response.body() == null) {
            return "";
        }
        //  响应体字符串
        String rpBodyStr = response.body().string();
        JSONObject rpBodyStrJb = JSONObject.parseObject(rpBodyStr);
        //  获取data对象
        JSONObject data = rpBodyStrJb.getJSONObject("data");
        //  获取错误信息 是个数组
        JSONArray errors = data.getJSONArray("errors");
        JSONObject errorJb = errors.getJSONObject(0);
        //  获取错误信息
        return errorJb.getString("message");
    }

    /**
     * requestText {String} 原始通知报文
     * key         {String} 密钥
     * return      {String} 生成签名内容
     */

    private String getGeneratedSignature(String requestText, String key) throws NoSuchAlgorithmException {
        String replaceRequestText =  requestText.replace("&lt;","<")
                .replace("&gt;",">")
                .replace("&quot;","\"")
                .replace("&amp;","&");
        String src = replaceRequestText + StrUtil.SLASH + key;

        MessageDigest md = MessageDigest.getInstance(Constants.SHA_256);
        byte[] hash = md.digest(src.getBytes(StandardCharsets.UTF_8));

        BigInteger number = new BigInteger(1, hash);
        StringBuilder hexString = new StringBuilder(number.toString(16));
        while (hexString.length() < 64) {
            hexString.insert(0, '0');
        }
        return hexString.toString();
    }

    /**
     * 解析数据入口
     *
     * @param body 接收的数据
     */
    private void parseBody(String body) {
        log.info("开始解析数据");
        log.debug("接收到的数据字符串：{}", body);

        JSONObject jsonObject = JSONObject.parseObject(body);
        //  物流跟踪状态
        String event = jsonObject.getString("event");
        //  请求对应的响应数据。
        JSONObject data = jsonObject.getJSONObject("data");
        if (CollUtil.isEmpty(data)) {
            return;
        }
        //  物流单号。
        String number = data.getString("number");
        log.info("物流单号：{}", number);
        //  物流信息主结构节点。
        JSONObject trackInfo = data.getJSONObject("track_info");
        if (CollUtil.isEmpty(trackInfo)) {
            return;
        }
        //  地区相关信息节点。
        JSONObject shippingInfo = Optional.ofNullable(trackInfo.getJSONObject("shipping_info")).orElse(new JSONObject());
        //  收件地信息节点。
        JSONObject recipientAddress = Optional.ofNullable(shippingInfo.getJSONObject("recipient_address")).orElse(new JSONObject());
        String raCountry = recipientAddress.getString("country");
        String raState = recipientAddress.getString("state");
        String raCity = recipientAddress.getString("city");
        String raStreet = recipientAddress.getString("street");
        String raPostalCode = recipientAddress.getString("postal_code");
        //  收件地-位置坐标（如经度纬度）
        JSONObject raCoordinates = Optional.ofNullable(recipientAddress.getJSONObject("coordinates")).orElse(new JSONObject());
        String raLongitude = raCoordinates.getString("longitude");
        String raLatitude = raCoordinates.getString("latitude");
        //  发件地信息节点。
        JSONObject shipperAddress = Optional.ofNullable(shippingInfo.getJSONObject("shipper_address")).orElse(new JSONObject());
        String saCountry = shipperAddress.getString("country");
        String saState = shipperAddress.getString("state");
        String saCity = shipperAddress.getString("city");
        String saStreet = shipperAddress.getString("street");
        String saPostalCode = shipperAddress.getString("postal_code");
        //  发件地-位置坐标（如经度纬度）
        JSONObject saCoordinates = Optional.ofNullable(shipperAddress.getJSONObject("coordinates")).orElse(new JSONObject());
        String saLongitude = saCoordinates.getString("longitude");
        String saLatitude = saCoordinates.getString("latitude");

        //  物流信息节点
        JSONObject tracking = Optional.ofNullable(trackInfo.getJSONObject("tracking")).orElse(new JSONObject());

        //  运输商集合节点
        JSONArray providersArr = Optional.ofNullable(tracking.getJSONArray("providers")).orElse(new JSONArray());
        JSONObject providers = Optional.ofNullable(providersArr.getJSONObject(0)).orElse(new JSONObject());

        //  运输商信息节点
        JSONObject provider = Optional.ofNullable(providers.getJSONObject("provider")).orElse(new JSONObject());
        String carrier = provider.getString("name");
        String carrierAlias = provider.getString("alias");
        String carrierCode = provider.getString("key");
        String carrierTel = provider.getString("tel");
        String carrierHomepage = provider.getString("homepage");
        String carrierCountry = provider.getString("country");

        JSONObject latestStatus = Optional.ofNullable(trackInfo.getJSONObject("latest_status")).orElse(new JSONObject());
        //  物流最新主子状态
        String latestMainStatus = latestStatus.getString("status");
        String latestSubStatus = latestStatus.getString("sub_status");
        if (StrUtil.isBlank(latestMainStatus) && StrUtil.isNotBlank(latestSubStatus)) {
            LogisticMainStatus mainStatusEnum = LogisticMainStatus.getMainStatusBySubStatusLabel(latestSubStatus);
            latestMainStatus = mainStatusEnum != null ? mainStatusEnum.getLabel() : null;
        }
        JSONObject latestEvent = Optional.ofNullable(trackInfo.getJSONObject("latest_event")).orElse(new JSONObject());
        String timeIso = latestEvent.getString("time_iso");
        DateTime curTime = DateUtil.parse(timeIso);

        Logistic logistic = baseMapper.getOneByNumber(number);

        logistic.setEvent(event);
        logistic.setNumber(number);
        logistic.setLatestMainStatus(latestMainStatus);
        logistic.setLatestSubStatus(latestSubStatus);
        logistic.setCarrier(carrier);
        logistic.setCarrierCode(carrierCode);
        logistic.setCarrierAlias(carrierAlias);
        logistic.setCarrierTel(carrierTel);
        logistic.setCarrierCountry(carrierCountry);
        logistic.setCarrierHomepage(carrierHomepage);
        logistic.setSignTime(LogisticMainStatus.DELIVERED.getLabel().equals(latestMainStatus) ? curTime : null);
        logistic.setSaCountry(saCountry);
        logistic.setSaState(saState);
        logistic.setSaCity(saCity);
        logistic.setSaStreet(saStreet);
        logistic.setSaPostalCode(saPostalCode);
        logistic.setSaLongitude(saLongitude);
        logistic.setSaLatitude(saLatitude);
        logistic.setRaCountry(raCountry);
        logistic.setRaState(raState);
        logistic.setRaCity(raCity);
        logistic.setRaStreet(raStreet);
        logistic.setRaPostalCode(raPostalCode);
        logistic.setRaLongitude(raLongitude);
        logistic.setRaLatitude(raLatitude);


        JSONArray events = Optional.ofNullable(providers.getJSONArray("events")).orElse(new JSONArray());
        List<LogisticInfo> logisticInfos = new ArrayList<>();
        for (int i = 0; i < events.size(); i++) {
            JSONObject joe = (JSONObject) events.get(i);

            String description = joe.getString("description");
            String mainStatus;
            String subStatus;
            //  如果是倒序首个事件并且最新状态是异常 取latest_status的主子状态
            if (i == 0 && LogisticMainStatus.EXCEPTION.getLabel().equals(latestMainStatus)) {
                mainStatus = latestMainStatus;
                subStatus = latestSubStatus;
            } else {
                mainStatus = joe.getString("stage");
                subStatus = joe.getString("sub_status");
            }

            LogisticMainStatus mainStatusEnum = LogisticMainStatus.getMainStatusBySubStatusLabel(subStatus);
            if (StrUtil.isBlank(mainStatus)) {
                mainStatus = mainStatusEnum != null ? mainStatusEnum.getLabel() : null;
            } else {
                if (mainStatusEnum != null && !CharSequenceUtil.equals(mainStatus, mainStatusEnum.getLabel())) {
                    mainStatus = mainStatusEnum.getLabel();
                }
            }
            String eTimeIso = joe.getString("time_iso");
            DateTime eCurTime = DateUtil.parse(eTimeIso);
            JSONObject address = Optional.ofNullable(joe.getJSONObject("address")).orElse(new JSONObject());
            String country = address.getString("country");
            String state = address.getString("state");
            String city = address.getString("city");
            String street = address.getString("street");
            JSONObject coordinates = Optional.ofNullable(address.getJSONObject("coordinates")).orElse(new JSONObject());
            String longitude = coordinates.getString("longitude");
            String latitude = coordinates.getString("latitude");
            String location = joe.getString("location");


            LogisticInfo logisticInfo = new LogisticInfo();
            logisticInfo.setNumber(number);
            logisticInfo.setDescription(description);
            logisticInfo.setMainStatus(mainStatus);
            logisticInfo.setSubStatus(subStatus);
            logisticInfo.setCurTime(eCurTime);
            logisticInfo.setCountry(country);
            logisticInfo.setState(state);
            logisticInfo.setCity(city);
            logisticInfo.setStreet(street);
            logisticInfo.setLongitude(longitude);
            logisticInfo.setLatitude(latitude);
            logisticInfo.setLocation(location);
            logisticInfos.add(logisticInfo);
        }

        saveOrUpdate(logistic);
        if (CollUtil.isNotEmpty(logisticInfos)) {
            logisticInfoService.removeByNumber(number);
            logisticInfoService.saveBatch(logisticInfos);
            log.debug("当前登录数据：{}", JSON.toJSONString(SecurityUtils.getLoginUserInfoVo()));
            remoteService.logisticFollowNotify(LogisticFollowNotifyDTO.builder()
                    .number(number)
                    .logisticMainStatus(latestMainStatus)
                    .logisticUpdateTime(ObjectUtil.isNotNull(curTime)? curTime : Optional.ofNullable(logisticInfos.get(0).getCurTime()).orElse(new Date())).build());
        }


        log.info("解析数据完成");
    }

    private String getRequestBody(HttpServletRequest request) {
        ByteArrayOutputStream body = new ByteArrayOutputStream();
        try {
            ServletInputStream inputStream = request.getInputStream();
            byte[] buffer = new byte[1024];
            for (int length; (length = inputStream.read(buffer)) != -1; ) {
                body.write(buffer, 0, length);
            }
        } catch (IOException ex) {
            log.error("物流回调，读取数据流异常", ex);
        }
        log.debug("物流回调，通知消息体：{}", body);
        return body.toString();
    }
}
