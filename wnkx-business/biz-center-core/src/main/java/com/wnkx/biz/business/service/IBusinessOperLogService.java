package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessOperLog;

/**
* <AUTHOR>
* @description 针对表【business_oper_log(商家信息操作日志)】的数据库操作Service
* @createDate 2024-09-20 10:26:28
*/
public interface IBusinessOperLogService extends IService<BusinessOperLog> {

    /**
     * 保存操作日志
     * @param originalBusiness
     * @param resultBusiness
     */
    void saveLog(Business originalBusiness, Business resultBusiness);
}
