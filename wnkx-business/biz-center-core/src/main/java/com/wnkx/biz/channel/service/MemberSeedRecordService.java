package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionAmountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.WithdrawalVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【member_seed_record(会员种草记录)】的数据库操作Service
* @createDate 2025-05-15 09:15:45
*/
public interface MemberSeedRecordService extends IService<MemberSeedRecord> {

    /**
     * 新增会员种草记录
     * @param dto
     */
    void saveMemberSeedRecord(OrderMemberChannelDTO dto);

    /**
     * 不可结算分销记录
     *
     * @param businessId
     */
    void memberChannelUnableSettlement(Long businessId);

    /**
     * 裂变渠道统计数据
     * @param channelId
     * @return
     */
    FissionAmountStatisticsVO getFissionStatisticsVO(Long channelId);

    /**
     * 获取种草转化记录列表
     * @param channelId
     * @return
     */
    List<MemberSeedRecordVO> memberSeedRecordList(Long channelId);

    /**
     * 发起提现
     * @param dto
     * @return
     */
    WithdrawalVO withdrawal(WithdrawalDTO dto);

    /**
     * 检查是否可提现
     * @param channelId
     * @return
     */
    Boolean checkWithdrawal(Long channelId);
}
