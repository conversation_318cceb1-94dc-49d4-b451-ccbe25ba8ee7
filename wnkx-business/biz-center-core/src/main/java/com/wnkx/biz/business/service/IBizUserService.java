package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserBusinessInfoDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserListDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalContactInfoDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【biz_user(登录用户表)】的数据库操作Service
 * @createDate 2024-08-26 11:22:30
 */
public interface IBizUserService extends IService<BizUser> {

    /**
     * 基于手机号初始化登录账号数据
     *
     * @param dto
     * @return
     */
    BizUser initBizUserBaseOnPhone(BizUserDTO dto);

    /**
     * 基于UnionId初始化登录账号数据
     */
    BizUser initBizUserBaseOnUnionId(BizUserDTO dto);

    /**
     * 根据unionId获取登录账号数据
     *
     * @param unionId
     * @return
     */
    BizUser getByUnionId(String unionId);

    /**
     * 获取账号列表
     *
     * @param dto
     * @return
     */
    List<BizUserListVO> bizUserList(BizUserListDTO dto);


    /**
     * 根据商家id获取登录账号数据
     * @param businessId
     * @return
     */
    List<BizUser> getBizUserByBusinessId(Long businessId);

    /**
     * 获取登账账号详情列表数据
     *
     * @param dto
     * @return
     */
    List<BizUserDetailVO> bizUserDetailList(BizUserDetailListDTO dto);

    /**
     * 根据手机号查询是否已存在信息
     *
     * @param phone
     * @return true - 存在  false - 不存在
     */
    Boolean isExist(String phone);

    /**
     * 根据手机号获取登录账号信息
     *
     * @param phone
     * @return
     */
    BizUser getByPhone(String phone);

    /**
     * 用于账户登录时，重新绑定了企业微信 设置外部联系人id
     *
     * @param unionId
     * @param externalUserid
     */
    void setUserExternalUserid(String unionId, String externalUserid);

    /**
     * 用于绑定微信手机号
     *
     * @param unionId unionId
     * @param phone   手机号
     */
    void setPhone(String unionId, String phone);


    /**
     * 用于填充种草官ID
     * @param id
     * @param seedId
     */
    void setSeedId(Long id, String seedId);

    /**
     * 换绑微信号
     *
     * @param id
     * @param dto
     */
    void updateWeChat(Long id, ExternalContactInfoDTO dto);

    /**
     * 企业微信事件回调，解除了企微好友
     *
     * @param externalUserId
     */
    void cleanUserExternalUserid(String externalUserId);


    /**
     * 清楚商家名称
     *
     * @param id
     */
    void cleanUserBusiness(Long id);

    /**
     * 更新最后登录时间
     *
     * @param phone
     */
    void updateLoginTime(String phone);

    /**
     * 修改登录表数据的商家数据
     *
     * @param dto
     */
    void updateUserBusinessInfo(BizUserBusinessInfoDTO dto);


    /**
     * 批量修改登录账号客服
     * *
     *
     * @param ids
     * @param waiterId
     */
    void updateWaiterIdByIds(List<Long> ids, Long waiterId);

    /**
     * 根据ID修改登录账号 如果数据为空则填充空
     *
     * @param bizUser
     */
    void updateFillNullById(BizUser bizUser);

    /**
     * 绑定用户商家子账号
     * @param memberCode
     * @param bizUserId
     */
    void bindUserBusinessSub(String memberCode, Long bizUserId);

    void updateContactUserName(String unionId, String connectUserName);

    /**
     * 校验手机号是否绑定用户
     */
    Boolean phoneCheck(String phone);
}
