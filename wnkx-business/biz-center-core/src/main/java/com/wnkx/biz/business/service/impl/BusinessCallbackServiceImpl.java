package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.BusinessCallbackEventEnum;
import com.ruoyi.common.core.enums.BusinessCallbackStatusEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessCallbackListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.business.WriteReturnVisitDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallback;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.wnkx.biz.business.mapper.BusinessCallbackMapper;
import com.wnkx.biz.business.service.BusinessCallbackEventService;
import com.wnkx.biz.business.service.BusinessCallbackRecordService;
import com.wnkx.biz.business.service.BusinessCallbackService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessCallbackServiceImpl extends ServiceImpl<BusinessCallbackMapper, BusinessCallback> implements BusinessCallbackService {

    private final BusinessCallbackEventService businessCallbackEventService;
    private final RemoteService remoteService;
    private final IBusinessAccountService businessAccountService;
    private final RedisService redisService;
    private final BusinessCallbackRecordService businessCallbackRecordService;

    /**
     * 商家回访-回访状态数统计
     */
    @Override
    public BusinessCallbackStatusCountVO returnVisitStatusCount() {
        return baseMapper.returnVisitStatusCount();
    }

    /**
     * 商家回访-回访记录
     */
    @Override
    public List<BusinessCallbackRecordListVO> returnVisitRecord(ReturnVisitRecordDTO dto) {
        return businessCallbackRecordService.returnVisitRecord(dto);
    }

    /**
     * 商家回访-回访详情
     */
    @Override
    public BusinessCallbackVO getCallbackDetailById(Long id) {
        List<BusinessCallbackListVO> businessCallbackListVOS = selectCallbackListByCondition(BusinessCallbackListDTO.builder().id(id).build());
        if (CollUtil.isEmpty(businessCallbackListVOS)) {
            return new BusinessCallbackVO();
        }
        return BeanUtil.copyProperties(businessCallbackListVOS.get(0), BusinessCallbackVO.class);
    }

    /**
     * 商家回访-填写回访记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void writeReturnVisit(WriteReturnVisitDTO dto) {
        BusinessCallback businessCallback = baseMapper.selectById(dto.getId());
        Assert.notNull(businessCallback, "回访记录不存在~");
        Assert.isTrue(BusinessCallbackStatusEnum.IN_THE_RETURN_VISIT.getCode().equals(businessCallback.getStatus()) || BusinessCallbackStatusEnum.ALREADY_VISITED.getCode().equals(businessCallback.getStatus()), "数据已处理，请刷新页面~");
        Assert.isTrue(redisService.getLock(CacheConstants.BUSINESS_RETURN_VISIT_KEY + dto.getId(), CacheConstants.BUSINESS_RETURN_VISIT_KEY_SECOND), "数据处理中，请稍后再试~");

        try {
            DateTime currentDate = DateUtil.date();

            if (BusinessCallbackStatusEnum.IN_THE_RETURN_VISIT.getCode().equals(businessCallback.getStatus())) {
                businessCallback.setStatus(BusinessCallbackStatusEnum.ALREADY_VISITED.getCode());
                businessCallback.setWriteTime(currentDate);
                baseMapper.updateById(businessCallback);
            }

            dto.setBusinessId(businessCallback.getBusinessId());
            dto.setWriteTime(currentDate);
            businessCallbackRecordService.addBusinessCallbackRecord(dto);
        } finally {
            redisService.releaseLock(CacheConstants.BUSINESS_RETURN_VISIT_KEY + dto.getId());
        }
    }

    /**
     * 商家回访-标记回访
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markReturnVisit(Long id) {
        BusinessCallback businessCallback = baseMapper.selectById(id);
        Assert.notNull(businessCallback, "待回访记录不存在~");
        Assert.isTrue(BusinessCallbackStatusEnum.WAIT_FOR_RETURN_VISIT.getCode().equals(businessCallback.getStatus()), "数据已处理，请刷新页面~");
        Assert.isTrue(redisService.getLock(CacheConstants.BUSINESS_RETURN_VISIT_KEY + id, CacheConstants.BUSINESS_RETURN_VISIT_KEY_SECOND), "数据处理中，请稍后再试~");

        try {
            List<BusinessCallbackEvent> businessCallbackEvents = businessCallbackEventService.selectListByCallbackId(businessCallback.getId());
            Assert.notEmpty(businessCallbackEvents, "待回访事件不能为空");

            //  获取回访中记录
            BusinessCallback inTheReturnVisit = baseMapper.getInTheReturnVisitByBusinessId(businessCallback.getBusinessId());

            if (ObjectUtil.isNull(inTheReturnVisit)) {
                //  没有回访中记录 修改 待回访 为 回访中 即可
                businessCallback.setStatus(BusinessCallbackStatusEnum.IN_THE_RETURN_VISIT.getCode());
                businessCallback.setMarkTime(DateUtil.date());
                baseMapper.updateById(businessCallback);
            } else {
                //  有回访中记录
                //  1、删除原有待回访记录
                //  2、待回访 和 回访中 相同类型事件保留较大的一条
                baseMapper.deleteById(businessCallback);

                List<BusinessCallbackEvent> inTheReturnVisitEvents = businessCallbackEventService.selectListByCallbackId(inTheReturnVisit.getId());
                inTheReturnVisitEvents.addAll(businessCallbackEvents);
                List<Long> deleteEventIds = inTheReturnVisitEvents.stream().map(BusinessCallbackEvent::getId).collect(Collectors.toList());

                List<BusinessCallbackEventEnum> businessCallbackEventEnums = filterHighestRankEvents(inTheReturnVisitEvents);
                List<Integer> eventCodes = businessCallbackEventEnums.stream().map(BusinessCallbackEventEnum::getCode).collect(Collectors.toList());

                List<BusinessCallbackEvent> saveBusinessCallbackEvents = new ArrayList<>();
                for (BusinessCallbackEvent event : inTheReturnVisitEvents) {
                    if (eventCodes.contains(event.getCallbackEvent())) {
                        event.setId(null);
                        event.setCallbackId(inTheReturnVisit.getId());
                        event.setCreateTime(null);
                        event.setUpdateTime(null);
                        saveBusinessCallbackEvents.add(event);
                    }
                }
                Assert.notEmpty(saveBusinessCallbackEvents, "要新增的回访事件不能为空");
                businessCallbackEventService.removeBatchByIds(deleteEventIds);
                businessCallbackEventService.saveBatch(saveBusinessCallbackEvents);

                //  更新标记时间
                inTheReturnVisit.setMarkTime(DateUtil.date());
                baseMapper.updateById(inTheReturnVisit);
            }
        } finally {
            redisService.releaseLock(CacheConstants.BUSINESS_RETURN_VISIT_KEY + id);
        }
    }

    /**
     * 新增回访记录
     */
    @Override
    public void addBusinessCallback(BusinessCallback businessCallback) {
        baseMapper.insert(businessCallback);
    }

    /**
     * 获取商家回访记录
     */
    @Override
    public List<BusinessCallbackListVO> selectBusinessCallbackList(Long businessId) {
        List<BusinessCallback> businessCallbacks = baseMapper.selectListByBusinessId(businessId);
        if (CollUtil.isEmpty(businessCallbacks)) {
            return Collections.emptyList();
        }

        List<Long> callbackIds = businessCallbacks.stream().map(BusinessCallback::getId).collect(Collectors.toList());
        List<BusinessCallbackEvent> businessCallbackEvents = businessCallbackEventService.selectListByCallbackIds(callbackIds);
        List<BusinessCallbackEventVO> businessCallbackEventVOS = BeanUtil.copyToList(businessCallbackEvents, BusinessCallbackEventVO.class);
        Map<Long, List<BusinessCallbackEventVO>> businessCallbackEventVOMap = businessCallbackEventVOS.stream().collect(Collectors.groupingBy(BusinessCallbackEventVO::getCallbackId));

        List<BusinessCallbackListVO> businessCallbackListVOS = BeanUtil.copyToList(businessCallbacks, BusinessCallbackListVO.class);
        for (BusinessCallbackListVO businessCallbackListVO : businessCallbackListVOS) {
            businessCallbackListVO.setBusinessCallbackEventVOS(businessCallbackEventVOMap.get(businessCallbackListVO.getId()));
        }

        return businessCallbackListVOS;
    }

    /**
     * 商家回访-回访列表
     */
    @Override
    public List<BusinessCallbackListVO> selectCallbackListByCondition(BusinessCallbackListDTO dto) {
        List<BusinessCallbackListVO> businessCallbackListVOS = baseMapper.selectCallbackListByCondition(dto);

        assembleWaitForReturnVisitList(businessCallbackListVOS);

        return businessCallbackListVOS;
    }

    /**
     * 组装回访列表
     */
    private void assembleWaitForReturnVisitList(List<BusinessCallbackListVO> businessCallbackListVOS) {
        if (CollUtil.isEmpty(businessCallbackListVOS)) {
            return;
        }
        //  获取用户map
        Set<Long> waiterIds = businessCallbackListVOS.stream().map(BusinessCallbackListVO::getWaiterId).collect(Collectors.toSet());
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(waiterIds).build());

        // 获取账号数据
        List<Long> businessIds = businessCallbackListVOS.stream().map(BusinessCallbackListVO::getBusinessId).collect(Collectors.toList());
        List<BusinessAccountVO> businessAccounts = businessAccountService.list(BusinessAccountDTO.builder().businessIds(businessIds).build());
        List<BusinessAccountVO> filterList = businessAccounts.stream().filter(ba -> null != ba.getBizUserId()).collect(Collectors.toList());

        // 获取商家订单统计数据
        List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail = remoteService.orderVideoStatisticsDetail(OrderVideoStatisticsDTO.builder().businessIds(businessIds).build());
        Map<Long, OrderVideoStatisticsDetailVO> orderVideoStatisticsDetailMap = orderVideoStatisticsDetail.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, Function.identity()));

        //  获取回访事件
        List<Long> callbackIds = businessCallbackListVOS.stream().map(BusinessCallbackListVO::getId).collect(Collectors.toList());
        List<BusinessCallbackEvent> businessCallbackEvents = businessCallbackEventService.selectListByCallbackIds(callbackIds);
        Map<Long, List<BusinessCallbackEvent>> businessCallbackEventMap = businessCallbackEvents.stream().collect(Collectors.groupingBy(BusinessCallbackEvent::getCallbackId));


        for (BusinessCallbackListVO businessCallbackListVO : businessCallbackListVOS) {
            //  对接客服名称
            businessCallbackListVO.setWaiterName(userMap.getOrDefault(businessCallbackListVO.getWaiterId(), new UserVO()).getName());

            //  子账号数量
            Map<Long, Long> countMap = filterList.stream().collect(Collectors.groupingBy(BusinessAccountVO::getBusinessId, Collectors.counting()));
            Long accountNum = Optional.ofNullable(countMap.get(businessCallbackListVO.getBusinessId())).map(count -> count - 1).orElse(0L);
            businessCallbackListVO.setAccountNum(Convert.toInt(accountNum));

            //  排单量
            OrderVideoStatisticsDetailVO orderVideoStatisticsDetailVO = Optional.ofNullable(orderVideoStatisticsDetailMap.get(businessCallbackListVO.getBusinessId())).orElse(new OrderVideoStatisticsDetailVO());
            businessCallbackListVO.setOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getOrderVideoTotal()).orElse(0));
            businessCallbackListVO.setRecentOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getRecentOrderTotal()).orElse(0));
            businessCallbackListVO.setPreFinishOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getPreFinishOrderTotal()).orElse(0));

            //  回访事件
            businessCallbackListVO.setBusinessCallbackEventVOS(BeanUtil.copyToList(businessCallbackEventMap.get(businessCallbackListVO.getId()), BusinessCallbackEventVO.class));
        }
    }

    /**
     * 获取等级较高的事件
     *
     * @param events 回访事件
     * @return 较高的事件 较低事件不保留
     */
    public List<BusinessCallbackEventEnum> filterHighestRankEvents(List<BusinessCallbackEvent> events) {
        // 根据 callbackEvent 值获取对应的枚举项
        List<BusinessCallbackEventEnum> eventEnums = events.stream()
                .map(event -> BusinessCallbackEventEnum.getByCode(event.getCallbackEvent()))
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());

        // 按 same 字段分组，并在每组中保留 rank 最高的事件
        Map<Integer, BusinessCallbackEventEnum> highestRankEventMap = eventEnums.stream()
                .collect(Collectors.toMap(
                        BusinessCallbackEventEnum::getSame, // 根据 same 字段进行分组
                        event -> event, // 将每个事件作为值
                        (existing, replacement) -> existing.getRank() >= replacement.getRank() ? existing : replacement // 保留 rank 更高的事件
                ));

        // 返回保留后的事件列表
        return new ArrayList<>(highestRankEventMap.values());
    }
}
