package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【order_member_marketing_channel(会员市场渠道记录表)】的数据库操作Service
* @createDate 2024-12-06 14:56:21
*/
public interface IOrderMemberMarketingChannelService extends IService<OrderMemberMarketingChannel> {

    /**
     * 保存会员市场渠道记录表
     * @param dto
     */
    void saveOrderMemberMarketingChannel(OrderMemberChannelDTO dto);
}
