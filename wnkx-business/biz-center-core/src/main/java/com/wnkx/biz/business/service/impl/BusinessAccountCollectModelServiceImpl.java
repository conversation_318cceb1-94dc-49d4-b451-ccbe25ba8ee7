package com.wnkx.biz.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel;
import com.wnkx.biz.business.mapper.BusinessAccountCollectModelMapper;
import com.wnkx.biz.business.service.IBusinessAccountCollectModelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 商家收藏模特Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
@Service
@RequiredArgsConstructor
public class BusinessAccountCollectModelServiceImpl extends ServiceImpl<BusinessAccountCollectModelMapper, BusinessAccountCollectModel> implements IBusinessAccountCollectModelService
{
    private final BusinessAccountCollectModelMapper businessAccountCollectModelMapper;

    /**
     * 获取模特被收藏次数
     */
    @Override
    public List<BusinessAccountCollectModel> getModelCollectCount(Collection<Long> modelIds) {
        return baseMapper.getModelCollectCount(modelIds);
    }

    @Override
    public void clearUserModel(Long bizUserId, Long modelId) {
        baseMapper.clearUserModel(bizUserId, modelId);
    }

    /**
     * 查询商家收藏模特
     * 
     * @param id 商家收藏模特主键
     * @return 商家收藏模特
     */
    @Override
    public BusinessAccountCollectModel selectMerchantCollectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询商家收藏模特列表
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 商家收藏模特
     */
    @Override
    public List<BusinessAccountCollectModel> selectMerchantCollectList(BusinessAccountCollectModel businessAccountCollectModel)
    {
        return businessAccountCollectModelMapper.selectMerchantCollectList(businessAccountCollectModel);
    }

    /**
     * 新增商家收藏模特
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    @Override
    public int insertMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel)
    {
        return businessAccountCollectModelMapper.insertMerchantCollect(businessAccountCollectModel);
    }

    /**
     * 修改商家收藏模特
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    @Override
    public int updateMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel)
    {
        return businessAccountCollectModelMapper.updateMerchantCollect(businessAccountCollectModel);
    }

    /**
     * 批量删除商家收藏模特
     * 
     * @param ids 需要删除的商家收藏模特主键
     * @return 结果
     */
    @Override
    public int deleteMerchantCollectByIds(Long[] ids)
    {
        return businessAccountCollectModelMapper.deleteMerchantCollectByIds(ids);
    }

    /**
     * 删除商家收藏模特信息
     * 
     * @param id 商家收藏模特主键
     * @return 结果
     */
    @Override
    public int deleteMerchantCollectById(Long id)
    {
        return businessAccountCollectModelMapper.deleteMerchantCollectById(id);
    }
}
