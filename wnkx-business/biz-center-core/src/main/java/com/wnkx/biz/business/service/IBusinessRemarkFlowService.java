package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.entity.order.BusinessRemarkFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.order.BusinessRemarkFlowVO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【business_remark_flow(商家备注流水)】的数据库操作Service
* @createDate 2024-12-17 17:01:19
*/
public interface IBusinessRemarkFlowService extends IService<BusinessRemarkFlow> {

    /**
     * 根据商家ID获取备注列表
     * @param businessId
     * @return
     */
    List<BusinessRemarkFlowVO> getListByBusinessId(Long businessId);

    /***
     * * 获取商家备注流水数据
     * @param businessIds
     * @return
     */
    Map<Long, List<BusinessRemarkFlow>> getRemarkFlowMap(List<Long> businessIds);
}
