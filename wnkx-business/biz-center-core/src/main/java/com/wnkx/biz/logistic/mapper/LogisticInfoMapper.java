package com.wnkx.biz.logistic.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.entity.LogisticInfo;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 物流信息详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Mapper
public interface LogisticInfoMapper extends SuperMapper<LogisticInfo> {
    /**
     * 通过物流单号查询物流信息
     *
     * @param number 物流单号
     * @return 物流信息
     */
    default List<LogisticInfo> selectListByNumber(Collection<String> number) {
        return this.selectList(new LambdaQueryWrapper<LogisticInfo>()
                .in(LogisticInfo::getNumber, number)
        );
    }

    /**
     * 通过物流单号删除物流信息
     *
     * @param number 物流单号
     */
    default void removeByNumber(String number) {
        this.delete(new LambdaQueryWrapper<LogisticInfo>()
                .eq(LogisticInfo::getNumber, number)
        );
    }

    Collection<String> getNumbersByCondition(@Param("dto") LogisticListDTO dto);

    /**
     * 通过物流单号获取最新的物流信息
     */
    List<LogisticInfoVO> getLastLogisticInfo(@Param("numbers") Collection<String> numbers);
}
