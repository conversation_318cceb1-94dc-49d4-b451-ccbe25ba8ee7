package com.wnkx.biz.statistics.mapper;

import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessOrderDataStatistics;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberTypeOrderCountVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_order_data_statistics(商家排单数统计)】的数据库操作Mapper
 * @createDate 2025-06-17 17:01:52
 * @Entity mygen.domain.BusinessOrderDataStatistics
 */
public interface BusinessOrderDataStatisticsMapper extends SuperMapper<BusinessOrderDataStatistics> {

    /**
     * 会员排单数占比统计
     * @param from
     * @param to
     * @return
     */
    List<PieChartVO> getBusinessOrderAnalysisVO(@Param("from") Date from, @Param("to") Date to);

    /**
     * 会员不同类型时间段下排单数统计
     * @param from
     * @param to
     * @param dateFormat  日期转化
     * @return
     */
    List<MemberTypeOrderCountVO> getMemberTypeOrderCountVO(@Param("from") Date from, @Param("to") Date to, @Param("dateFormat") String dateFormat);
}




