package com.wnkx.biz.translate.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.translate.TranslationCache;
import com.wnkx.biz.translate.service.TranslationCacheService;
import com.wnkx.biz.translate.mapper.TranslationCacheMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【translation_cache】的数据库操作Service实现
* @createDate 2025-03-17 09:05:14
*/
@Service
public class TranslationCacheServiceImpl extends ServiceImpl<TranslationCacheMapper, TranslationCache>
    implements TranslationCacheService{

    @Override
    public TranslationCache getBySourceSha256(String sourceSha256) {
        return baseMapper.getBySourceSha256(sourceSha256);
    }

    @Override
    public List<TranslationCache> getBySourceSha256List(List<String> sourceSha256List) {
        return baseMapper.getBySourceSha256List(sourceSha256List);
    }
}




