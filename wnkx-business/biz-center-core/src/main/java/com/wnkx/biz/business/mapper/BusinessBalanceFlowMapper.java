package com.wnkx.biz.business.mapper;

import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceFlowDetailListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailFlowExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_flow(余额流水表)】的数据库操作Mapper
 * @createDate 2024-06-27 17:12:52
 * @Entity
 */
@Mapper
public interface BusinessBalanceFlowMapper extends SuperMapper<BusinessBalanceFlow> {
    /**
     * 查询余额明细列表
     * @param dto
     * @return
     */
    List<BusinessBalanceFlow> queryList(BusinessBalanceFlowDTO dto);


    /**
     * 余额流水统计
     * @param businessIds
     * @return
     */
    List<BusinessBalanceFlowStatisticsVO> statistics(@Param("businessIds") List<Long> businessIds);


    /**
     * 全部余额明细数据*
     * @return
     */
    List<BusinessBalanceDetailFlowExportVO> getBusinessBalanceDetailFlowExports(BusinessBalanceFlowDetailListDTO dto);
}
