package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.MemberValidTypeEnum;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessMemberValidityFlowVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_member_validity_flow(商家会员有效期修改流水)】的数据库操作Mapper
* @createDate 2024-10-14 10:21:18
* @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow
*/
public interface BusinessMemberValidityFlowMapper extends SuperMapper<BusinessMemberValidityFlow> {

    /**
     * 根据商家Id获取商家有效期列表
     * @param dto
     * @return
     */
    List<BusinessMemberValidityFlowVO> queryList(BusinessMemberValidityFlowDTO dto);

    /**
     * 获取关联订单数据
     * @param businessId
     * @return
     */
    default BusinessMemberValidityFlow getRelationOrder(Long businessId){
        return this.selectOne(new LambdaQueryWrapper<BusinessMemberValidityFlow>()
                .eq(BusinessMemberValidityFlow::getBusinessId, businessId)
                .eq(BusinessMemberValidityFlow::getType, MemberValidTypeEnum.USER.getCode())
                .orderByDesc(BusinessMemberValidityFlow::getId)
                .last("limit 1")
        );
    }
}




