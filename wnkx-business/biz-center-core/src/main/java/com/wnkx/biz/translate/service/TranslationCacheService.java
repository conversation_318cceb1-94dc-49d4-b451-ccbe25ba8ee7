package com.wnkx.biz.translate.service;

import com.ruoyi.system.api.domain.entity.biz.translate.TranslationCache;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【translation_cache】的数据库操作Service
* @createDate 2025-03-17 09:05:14
*/
public interface TranslationCacheService extends IService<TranslationCache> {

    /**
     * 根据加密结果查询翻译数据
     *
     * @param sourceSha256
     * @return
     */
    TranslationCache getBySourceSha256(String sourceSha256);

    /**
     * 根据加密结果获取翻译数据列表
     * @param sourceSha256List
     * @return
     */
    List<TranslationCache> getBySourceSha256List(List<String> sourceSha256List);
}
