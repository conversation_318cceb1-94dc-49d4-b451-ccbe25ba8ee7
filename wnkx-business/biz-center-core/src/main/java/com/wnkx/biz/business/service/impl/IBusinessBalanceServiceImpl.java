package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountTokenDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.OrderPayLogDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.casus.BalancePayOutDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.wnkx.biz.business.mapper.BusinessMapper;
import com.wnkx.biz.business.service.*;
import com.wnkx.biz.model.service.BizResourceService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :商家余额处理
 * @create :2025-02-27 11:37
 **/
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class IBusinessBalanceServiceImpl implements IBusinessBalanceService {
    private final IBusinessService businessService;
    private final IBusinessAccountService businessAccountService;
    private final BusinessMapper businessMapper;
    private final IBusinessBalanceFlowService businessBalanceFlowService;
    private final IBusinessBalanceDetailLockService businessBalanceDetailLockService;
    private final RedisService redisService;
    private final IBusinessBalanceDetailService businessBalanceDetailService;
    private final BizResourceService bizResourceService;
    private final IBusinessBalanceAuditFlowService businessBalanceAuditFlowService;
    private final IBusinessBalancePrepayService businessBalancePrepayService;
    private final RemoteService remoteService;
    private final IBusinessBalanceService self;

    @Override
    public BigDecimal businessBalanceTotal() {
        return businessMapper.businessBalanceTotal();
    }

    /**
     * 现有流程不使用is_balance_lock
     * 1.视频订单支出、会员订单支出 && 财务审核状态 = 待审核（非余额支付）                        -----------------下单锁定余额
     * 请求参数：useBalance = 100.0,origin = 4|5,auditStatus = 0
     * balance 不变
     * use_balance + 100.0
     * 2.视频订单支出、会员订单支出 && 财务审核状态 = 审核通过（非余额支付）                      -----------------运营端审核通过、支付回调
     * 请求参数：useBalance = 100.0,origin = 4|5,auditStatus = 1
     * balance - 100.0
     * use_balance -100.0
     * 3.视频订单支出、会员订单支出 && 财务审核状态 = 审核通过（余额支付）                        -----------------余额支付
     * 请求参数：useBalance = 100.0,origin = 4|5,auditStatus = 1
     * balance - 100.0
     * 4.取消订单收入 && 财务审核状态 = 审核通过                                             ----------------支付成功后：商家端退款、运营端取消订单申请退款 退款审核通过后
     * 请求参数：useBalance = 100.0,origin = 2,auditStatus = 1
     * balance + 100.0
     * 5.取消订单收入 && !审核通过  订单使用余额不为空                                        ----------------商家端、运营端取消订单
     * 请求参数：useBalance = 100.0,origin = 2,auditStatus = 0
     * balance 不变
     * use_balance - 100.0
     * 6.取消订单收入 && !审核通过  订单使用余额为空                                          ----------------商家端、运营端取消订单
     * 不做处理
     * 7.补偿订单收入、取消选配收入                                                         ----------------审核申请退款成功后
     * 请求参数：useBalance = 100.0,origin = 1|3
     * 余额 + 100.0
     * 8.线下余额提现
     * 请求参数：useBalance = 100.0,origin = 6
     * 余额 - 100.0
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessBalance(BusinessBalanceDTO dto) {
        if (dto.getUseBalance().compareTo(BigDecimal.ZERO) == 0) {
            //使用余额为0 不需要操作锁
            return;
        }
        if (dto.getUseBalance().compareTo(BigDecimal.ZERO) < 0) {
            //使用余额为0 不需要操作锁
            dto.setUseBalance(dto.getUseBalance().abs());
        }

        Business business = businessMapper.getBusinessForUpdate(dto.getBusinessId());
        BigDecimal originBalance = business.getBalance();
        Business businessResult = this.checkUpdateBusinessBalance(dto, business);
        businessService.lambdaUpdate()
                .set(Business::getBalance, businessResult.getBalance())
                .set(Business::getUseBalance, businessResult.getUseBalance())
                .eq(Business::getId, dto.getBusinessId())
                .update();


        if (BalanceSourceTypeEnum.PAYOUT_SPEND.getCode().equals(dto.getOrigin()) && BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(dto.getBalanceAuditStatus())) {
            businessBalanceDetailLockService.flow(dto.getBusinessBalanceFlowDTOS().get(0).getWithdrawNumber(), BusinessBalanceAuditStatusEnum.CANCEL.getCode(), null);
        }

        //添加余额流水
        if (dto.getUseBalance().compareTo(BigDecimal.ZERO) != 0) {
            if (dto.isVideoOperate()) {
                for (BusinessBalanceFlowDTO businessBalanceFlowDTO : dto.getBusinessBalanceFlowDTOS()) {
                    //  该笔大订单使用的余额 = 所有小订单使用余额总和
                    BigDecimal orderUseBalance = businessBalanceFlowDTO.getBusinessBalanceDetailFlowList().stream().map(BusinessBalanceDetailFlowDTO::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                    if (BigDecimal.ZERO.compareTo(orderUseBalance) == 0) {
                        continue;
                    }
                    if (BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode().equals(dto.getOrigin())
                            || BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode().equals(dto.getOrigin())
                            || BalanceSourceTypeEnum.PREPAY_INCOME.getCode().equals(dto.getOrigin())
                            || BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode().equals(dto.getOrigin())

                    ) {
                        // 收入直接添加就好
                        originBalance = originBalance.add(orderUseBalance);
                    } else if (BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode().equals(dto.getOrigin())) {
                        if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                            // 取消订单收入 && 财务审核状态 = 审核通过 balance + 使用余额
                            originBalance = originBalance.add(orderUseBalance);
                        }
                    } else if (BalanceSourceTypeEnum.ORDER_SPEND.getCode().equals(dto.getOrigin()) || BalanceSourceTypeEnum.MEMBER_SPEND.getCode().equals(dto.getOrigin())) {
                        // 会员订单支出、视频订单支出
                        if (ObjectUtil.isNotNull(dto.getIsBalancePay()) && StatusTypeEnum.YES.getCode().equals(dto.getIsBalancePay())) {
                            // 余额支付 balance - 使用余额 条件: 有效余额 > 使用余额
                            originBalance = originBalance.subtract(orderUseBalance);
                        } else if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                            originBalance = originBalance.subtract(orderUseBalance);
                        }
                    }
                    businessBalanceFlowDTO.setBalance(originBalance);

                    if (ObjectUtil.isNull(businessBalanceFlowDTO.getOrderTime())) {
                        businessBalanceFlowDTO.setOrderTime(new Date());
                    }
                    businessBalanceFlowDTO.setBusinessId(dto.getBusinessId());
                    businessBalanceFlowDTO.setAmount(orderUseBalance);
                    businessBalanceFlowDTO.setOrigin(dto.getOrigin());

                    List<Integer> payIn = List.of(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode(),
                            BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode(),
                            BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode(),
                            BalanceSourceTypeEnum.PREPAY_INCOME.getCode(),
                            BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()
                    );
                    if (payIn.contains(dto.getOrigin())) {
                        businessBalanceFlowDTO.setType(BalanceType.INCOME.getCode());
                        businessBalanceFlowDTO.setAmount(orderUseBalance);
                    } else {
                        businessBalanceFlowDTO.setType(BalanceType.SPEND.getCode());
                        businessBalanceFlowDTO.setAmount(orderUseBalance.negate());
                    }
                    businessBalanceFlowService.addBusinessBalanceFlow(businessBalanceFlowDTO);
                }
            } else {
                BusinessBalanceFlowDTO businessBalanceFlowDTO = dto.getBusinessBalanceFlowDTOS().get(0);
                businessBalanceFlowDTO.setBalance(businessResult.getBalance());
                if (ObjectUtil.isNull(businessBalanceFlowDTO.getOrderTime())) {
                    businessBalanceFlowDTO.setOrderTime(new Date());
                }
                businessBalanceFlowDTO.setBusinessId(dto.getBusinessId());
                businessBalanceFlowDTO.setAmount(dto.getUseBalance());
                businessBalanceFlowDTO.setOrigin(dto.getOrigin());

                List<Integer> payIn = List.of(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode(),
                        BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode(),
                        BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode(),
                        BalanceSourceTypeEnum.PREPAY_INCOME.getCode(),
                        BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()
                );
                if (payIn.contains(dto.getOrigin())) {
                    businessBalanceFlowDTO.setType(BalanceType.INCOME.getCode());
                    businessBalanceFlowDTO.setAmount(dto.getUseBalance());
                } else {
                    businessBalanceFlowDTO.setType(BalanceType.SPEND.getCode());
                    businessBalanceFlowDTO.setAmount(dto.getUseBalance().negate());
                }
                businessBalanceFlowService.addBusinessBalanceFlow(businessBalanceFlowDTO);
            }
        }

        businessAccountService.updateLoginBusiness(Arrays.asList(business.getId()), BusinessAccountTokenDTO.builder().balance(businessResult.getBalance()).useBalance(businessResult.getUseBalance()).build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void balancePayOut(BalancePayOutDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId(), 60L), "商家余额提现中，请稍后重试！");
        try {
            List<BusinessBalanceDetailLockDTO> businessBalanceDetailLocks = dto.getBusinessBalanceDetailLocks();
            //总提现金额
            BigDecimal payOutTotal = BigDecimal.ZERO;
            Map<Long, BusinessBalanceDetailLockDTO> businessBalanceDetailLockMap = new HashMap<>();

            //余额详情ID
            List<Long> balanceDetailId = new ArrayList<>();
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                payOutTotal = payOutTotal.add(item.getPayOutAmount());
                businessBalanceDetailLockMap.put(item.getBalanceDetailId(), item);
                balanceDetailId.add(item.getBalanceDetailId());
            }
            Assert.isTrue(payOutTotal.compareTo(dto.getUseBalance()) == 0, "余额详情数据已更新，请刷新后重试~");
            List<BusinessBalanceDetail> validBusinessBalanceDetailList = businessBalanceDetailService.getValidBusinessBalanceDetailListByBusinessId(dto.getBusinessId());
            List<BusinessBalanceDetail> businessBalanceDetails = new ArrayList<>();
            for (BusinessBalanceDetail item : validBusinessBalanceDetailList) {
                if (balanceDetailId.contains(item.getId())) {
                    businessBalanceDetails.add(item);
                }
            }
            Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetails), "余额详情数据已更新，请刷新后重试~");
            Assert.isTrue(businessBalanceDetails.size() == dto.getBusinessBalanceDetailLocks().size(), "余额详情数据已更新，请刷新后重试~");

            List<PayoutBusinessBalanceDetailVO> businessBalanceDetailVOList = businessBalanceDetailService.getBusinessBalanceDetailVOList(validBusinessBalanceDetailList, StatusTypeEnum.NO.getCode());
            Map<Long, BusinessBalanceDetail> businessBalanceDetailMap = businessBalanceDetails.stream().collect(Collectors.toMap(BusinessBalanceDetail::getId, p -> p));

            Map<Long, PayoutBusinessBalanceDetailVO> businessBalanceDetailVOMap = businessBalanceDetailVOList.stream().collect(Collectors.toMap(PayoutBusinessBalanceDetailVO::getId, p -> p));
            Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetailVOList), "余额详情数据已更新，请刷新后重试~");
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                PayoutBusinessBalanceDetailVO detail = businessBalanceDetailVOMap.get(item.getBalanceDetailId());
                Assert.notNull(detail, "余额详情数据已更新，请刷新后重试~");
                Assert.isTrue(detail.getUseBalance().compareTo(item.getUseBalance()) == 0, "余额详情数据已更新，请刷新后重试~");
                Assert.isTrue(detail.getValidBalance().compareTo(item.getPayOutAmount()) == 0, "余额详情数据已更新，请刷新后重试~");
            }

            //处理余额
            BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
            businessBalanceDTO.setBusinessId(dto.getBusinessId());
            businessBalanceDTO.setUseBalance(dto.getUseBalance());
            businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
            businessBalanceDTO.setOrigin(BalanceSourceTypeEnum.PAYOUT_SPEND.getCode());
            businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder().loginBase(SecurityUtils.getLoginUser()).build()));
            businessBalanceDTO.setBalanceAuditStatus(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode());
            self.updateBusinessBalance(businessBalanceDTO);

            //添加提现订单
            BusinessBalanceAuditFlowSaveDTO businessBalanceAuditFlowSaveDTO = new BusinessBalanceAuditFlowSaveDTO();
            businessBalanceAuditFlowSaveDTO.setBusinessId(dto.getBusinessId());
            businessBalanceAuditFlowSaveDTO.setAmount(dto.getUseBalance());
            businessBalanceAuditFlowSaveDTO.setApplyRemark(dto.getApplyRemark());
            businessBalanceAuditFlowSaveDTO.setWithdrawWay(dto.getWithdrawWay());
            if (CollUtil.isNotEmpty(dto.getPayoutResourceUrlList())) {
                List<Long> payoutResourceUrls = bizResourceService.saveBatchBizResourceReturnIds(dto.getPayoutResourceUrlList());
                businessBalanceAuditFlowSaveDTO.setPayoutResourceUrl(StrUtil.join(StrUtil.COMMA, payoutResourceUrls));
            } else {
                businessBalanceAuditFlowSaveDTO.setPayoutResourceUrl(StrUtil.EMPTY);
            }
            BusinessBalanceAuditFlow businessBalanceAuditFlow = businessBalanceAuditFlowService.addBusinessBalanceAuditFlow(businessBalanceAuditFlowSaveDTO);

            //锁定余额详情
            List<BusinessBalanceDetailLock> businessBalanceDetailLockList = new ArrayList<>();
            List<BusinessBalanceDetail> businessBalanceDetailList = new ArrayList<>();
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                BusinessBalanceDetail businessBalanceDetail = businessBalanceDetailMap.get(item.getBalanceDetailId());

                BusinessBalanceDetailLock detailLock = new BusinessBalanceDetailLock();
                detailLock.setNumber(businessBalanceAuditFlow.getWithdrawNumber());
                detailLock.setBalanceDetailId(item.getBalanceDetailId());
                detailLock.setUseBalance(item.getUseBalance());
                detailLock.setPayOutAmount(item.getPayOutAmount());
                detailLock.setCreateOrderUserName(businessBalanceDetail.getCreateOrderUserName());
                detailLock.setCreateOrderUserNickName(businessBalanceDetail.getCreateOrderUserNickName());
                if (BalanceDetailTypeEnum.PREPAY.getCode().equals(businessBalanceDetail.getNumberType())) {
                    detailLock.setPrepayNum(businessBalanceDetail.getOriginNumber());
                }
                detailLock.setVideoCode(businessBalanceDetail.getVideoCode());
                detailLock.setBalanceNumber(businessBalanceDetail.getNumber());
                detailLock.setBalanceCreateTime(businessBalanceDetail.getCreateTime());
                detailLock.setOrigin(businessBalanceDetail.getOrigin());
                businessBalanceDetailLockList.add(detailLock);

                BusinessBalanceDetail detail = new BusinessBalanceDetail();
                detail.setId(businessBalanceDetail.getId());
                detail.setLockBalance(businessBalanceDetail.getLockBalance().add(item.getPayOutAmount()));
                detail.setValidBalance(businessBalanceDetail.getValidBalance().subtract(item.getPayOutAmount()));
                businessBalanceDetailList.add(detail);
            }
            businessBalanceDetailService.updateBatchById(businessBalanceDetailList);
            businessBalanceDetailLockService.saveBatch(businessBalanceDetailLockList);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPayOut(BusinessBalanceAuditFlowAuditDTO dto) {
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId(), 60L), "商家余额处理中，请稍后重试！");
            BusinessBalanceAuditFlow entity = businessBalanceAuditFlowService.getById(dto.getId());
            BusinessBalanceAuditFlow businessBalanceAuditFlow = this.checkAuditPayOut(dto, entity);
            businessBalanceAuditFlowService.updateById(businessBalanceAuditFlow);

            BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
            businessBalanceDTO.setBusinessId(dto.getBusinessId());
            businessBalanceDTO.setUseBalance(entity.getAmount());
            businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
            businessBalanceDTO.setOrigin(BalanceSourceTypeEnum.PAYOUT_SPEND.getCode());
            businessBalanceDTO.setBalanceAuditStatus(dto.getAuditStatus());
            businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder().loginBase(SecurityUtils.getLoginUser()).withdrawNumber(entity.getWithdrawNumber()).build()));
            self.updateBusinessBalance(businessBalanceDTO);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditBusinessBalancePrepay(AuditBusinessBalancePrepayDTO dto) {

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId(), 60L), "商家余额处理中，请稍后重试！");
            BusinessBalancePrepay businessBalancePrepay = businessBalancePrepayService.auditBusinessBalancePrepay(dto);
            if (BusinessPrepayAuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {

                remoteService.saveOrderPayLog(OrderPayLogDTO.builder()
                        .orderNum(businessBalancePrepay.getPrepayNum())
                        .orderType(businessBalancePrepay.getOrderType())
                        .businessId(businessBalancePrepay.getBusinessId())
                        .payType(businessBalancePrepay.getPayType())
                        .payTypeDetail(businessBalancePrepay.getPayTypeDetail())
                        .payTime(businessBalancePrepay.getPayTime())
                        .payAmount(businessBalancePrepay.getPayAmount())
                        .realPayAmount(businessBalancePrepay.getRealPayAmount())
                        .build());
                BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
                businessBalanceDTO.setBusinessId(dto.getBusinessId());
                businessBalanceDTO.setUseBalance(dto.getRealAmount());
                businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
                businessBalanceDTO.setOrigin(OrderTypeEnum.PREPAY_ORDER.getCode().equals(businessBalancePrepay.getOrderType()) ? BalanceSourceTypeEnum.PREPAY_INCOME.getCode() : BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode());
                businessBalanceDTO.setBalanceAuditStatus(dto.getAuditStatus());
                businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder().loginBase(SecurityUtils.getLoginUser()).prepayNum(businessBalancePrepay.getPrepayNum()).build()));
                self.updateBusinessBalance(businessBalanceDTO);
            }

        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBalancePrepay updateOrderPayStatus(PrepayUpdatePayStatusDTO dto) {
        BusinessBalancePrepay businessBalancePrepay = businessBalancePrepayService.updateOrderPayStatus(dto);
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + businessBalancePrepay.getBusinessId(), 60L), "商家余额处理中，请稍后重试！");
            BusinessAccountVO businessAccountVO = businessAccountService.queryOne(BusinessAccountDTO.builder().id(businessBalancePrepay.getCreateById()).build());
            LoginBusiness login = new LoginBusiness(businessAccountVO);
            BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
            businessBalanceDTO.setBusinessId(businessBalancePrepay.getBusinessId());
            businessBalanceDTO.setUseBalance(businessBalancePrepay.getRealAmount());
            businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
            businessBalanceDTO.setOrigin(BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode());
            businessBalanceDTO.setBalanceAuditStatus(businessBalancePrepay.getAuditStatus());
            businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder()
                    .loginBase(login)
                    .prepayNum(businessBalancePrepay.getPrepayNum()).build()));
            self.updateBusinessBalance(businessBalanceDTO);

        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + businessBalancePrepay.getBusinessId());
        }

        return businessBalancePrepay;
    }

    @Override
    public List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(BusinessBalanceAuditFlowValidListDTO dto) {
        return businessBalanceAuditFlowService.queryValidList(dto);
    }

    @Override
    public List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto) {
        return businessBalanceDetailLockService.queryValidList(dto);
    }

    /**
     * 获取视频订单提现记录
     */
    @Override
    public List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto) {
        return businessBalanceDetailLockService.withdrawDepositRecord(dto);
    }

    public Business checkUpdateBusinessBalance(BusinessBalanceDTO dto, Business business) {

        if (ObjectUtil.isNull(business)) {
            throw new ServiceException("商家数据不能为空");
        }

        List<Integer> balanceSources = Arrays.stream(BalanceSourceTypeEnum.values()).map(BalanceSourceTypeEnum::getCode).collect(Collectors.toList());
        List<Integer> audits = Arrays.stream(AuditStatusEnum.values()).map(AuditStatusEnum::getCode).collect(Collectors.toList());
        if (!balanceSources.contains(dto.getOrigin())) {
            throw new ServiceException("订单来源有误");
        }
        if (ObjectUtil.isNotNull(dto.getAuditStatus()) && !audits.contains(dto.getAuditStatus())) {
            throw new ServiceException("订单状态有误");
        }

        if (BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode().equals(dto.getOrigin())
                || BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode().equals(dto.getOrigin())
                || BalanceSourceTypeEnum.PREPAY_INCOME.getCode().equals(dto.getOrigin())
                || BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode().equals(dto.getOrigin())

        ) {
            //收入直接添加就好
            business.setBalance(business.getBalance().add(dto.getUseBalance()));
        } else if (BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode().equals(dto.getOrigin())) {
            Assert.notNull(dto.getAuditStatus(), "财务审核状态不能为空！");
            if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                //取消订单收入 && 财务审核状态 = 审核通过 balance + 使用余额
                business.setBalance(business.getBalance().add(dto.getUseBalance()));
            } else {
                checkUserBalance(dto, business);
                //解锁使用余额
                //取消订单收入 && 财务审核状态 != 审核通过 use_balance - 使用余额
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            }
        } else if (BalanceSourceTypeEnum.ORDER_SPEND.getCode().equals(dto.getOrigin()) || BalanceSourceTypeEnum.MEMBER_SPEND.getCode().equals(dto.getOrigin())) {
            //会员订单支出、视频订单支出
            Assert.notNull(dto.getAuditStatus(), "订单状态有误不能为空！");
            if (ObjectUtil.isNotNull(dto.getIsBalancePay()) && StatusTypeEnum.YES.getCode().equals(dto.getIsBalancePay())) {
                checkValidBalance(dto, business);
                //余额支付 balance - 使用余额 条件: 有效余额 > 使用余额
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            } else if (AuditStatusEnum.UN_CHECK.getCode().equals(dto.getAuditStatus())) {
                checkValidBalance(dto, business);
                //下单锁定 use_balance + 使用余额  条件：有效余额 > 使用余额
                business.setUseBalance(business.getUseBalance().add(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            } else if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                checkBalance(dto, business);
                checkUserBalance(dto, business);
                //订单支付完成 use_balance - 使用余额、balance - 使用余额
                //若之前校验都无误 理应不需要验证数据  为确保无误 进行一次兜底校验
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            }
        } else if (BalanceSourceTypeEnum.PAYOUT_SPEND.getCode().equals(dto.getOrigin())) {
            Assert.notNull(dto.getBalanceAuditStatus(), "余额提现审核状态不能为空！");
            if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(dto.getBalanceAuditStatus())) {
                //审核通过 余额扣除
                checkBalance(dto, business);
                checkUserBalance(dto, business);
                //订单支付完成 use_balance - 使用余额、balance - 使用余额
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            } else if (BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(dto.getBalanceAuditStatus())) {
                checkUserBalance(dto, business);
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            } else {
                checkValidBalance(dto, business);
                //下单锁定 use_balance + 使用余额
                business.setUseBalance(business.getUseBalance().add(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            }

        } else {
            throw new ServiceException("订单来源有误");
        }
        return business;
    }
    private void checkBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getBalance().compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家余额");
        }
    }

    private void checkUserBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getUseBalance().compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家使用余额");
        }
    }

    private void checkValidBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getBalance().subtract(business.getUseBalance()).compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家有效余额");
        }
    }

    private BusinessBalanceAuditFlow checkAuditPayOut(BusinessBalanceAuditFlowAuditDTO dto, BusinessBalanceAuditFlow entity) {
        Assert.notNull(entity, "余额提现审核记录不存在！");
        Assert.isTrue(entity.getBusinessId().equals(dto.getBusinessId()), "数据库商家数据与请求不一致！");
        Assert.isTrue(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(entity.getAuditStatus()), "只有待审核状态能够进行审核处理！");

        BusinessBalanceAuditFlow businessBalanceAuditFlow = BeanUtil.copyProperties(dto, BusinessBalanceAuditFlow.class);
        if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            Assert.notNull(dto.getRealAmount(), "[实付金额]不能为空");
            Assert.notNull(dto.getPayTime(), "[支付时间]不能为空");
            Assert.notNull(dto.getResourceUrl(), "[图片资源地址Url]不能为空");
            Assert.isTrue(entity.getAmount().compareTo(dto.getRealAmount()) == 0, "实际提现金额需要与原提现一致");
        } else if (BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(dto.getAuditStatus())) {
            businessBalanceAuditFlow.setRealAmount(null);
            businessBalanceAuditFlow.setPayTime(null);
            businessBalanceAuditFlow.setResourceUrl(null);
            businessBalanceAuditFlow.setRemark(null);
        } else {
            throw new ServiceException("审核状态有误！");
        }
        businessBalanceAuditFlow.setAuditUserId(SecurityUtils.getUserId());
        businessBalanceAuditFlow.setAuditTime(new Date());
        return businessBalanceAuditFlow;
    }
}
