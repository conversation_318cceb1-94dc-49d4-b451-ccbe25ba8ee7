package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 商家收藏模特Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-27
 */
@Mapper
public interface BusinessAccountCollectModelMapper extends SuperMapper<BusinessAccountCollectModel>
{
    /**
     * 查询商家收藏模特
     *
     * @param id 商家收藏模特主键
     * @return 商家收藏模特
     */
    public BusinessAccountCollectModel selectMerchantCollectById(Long id);

    /**
     * 查询商家收藏模特列表
     *
     * @param businessAccountCollectModel 商家收藏模特
     * @return 商家收藏模特集合
     */
    public List<BusinessAccountCollectModel> selectMerchantCollectList(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 新增商家收藏模特
     *
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    public int insertMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 修改商家收藏模特
     *
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    public int updateMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 删除商家收藏模特
     *
     * @param id 商家收藏模特主键
     * @return 结果
     */
    public int deleteMerchantCollectById(Long id);

    /**
     * 批量删除商家收藏模特
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMerchantCollectByIds(Long[] ids);

    /**
     * 获取模特被收藏次数
     */
    default List<BusinessAccountCollectModel> getModelCollectCount(Collection<Long> modelIds) {
        return this.selectList(new LambdaQueryWrapper<BusinessAccountCollectModel>()
                .in(BusinessAccountCollectModel::getModelId, modelIds)
        );
    }

    /**
     * 清楚登录账号收藏模特
     * @param bizUserId
     * @param modelId
     */
    default void clearUserModel(Long bizUserId, Long modelId){
        this.delete(new LambdaQueryWrapper<BusinessAccountCollectModel>()
                .eq(BusinessAccountCollectModel::getBizUserId, bizUserId)
                .eq(BusinessAccountCollectModel::getModelId, modelId)
        );
    }
}
