package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.ConfigConstants;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.enums.ChannelActivityTypeEnum;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.ruoyi.system.api.domain.dto.order.PosterDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivity;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.channel.mapper.DistributionChannelActivityMapper;
import com.wnkx.biz.channel.mapper.DistributionChannelMapper;
import com.wnkx.biz.channel.service.DistributionChannelActivityInfoService;
import com.wnkx.biz.channel.service.DistributionChannelActivityService;
import com.wnkx.biz.utlis.TarGzImageProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_activity(渠道活动表)】的数据库操作Service实现
 * @createDate 2024-12-02 14:27:22
 */
@Service
@RequiredArgsConstructor
public class DistributionChannelActivityServiceImpl extends ServiceImpl<DistributionChannelActivityMapper, DistributionChannelActivity>
        implements DistributionChannelActivityService {
    private final DistributionChannelActivityInfoService distributionChannelActivityInfoService;
    private final RedisService redisService;
    private final DistributionChannelMapper distributionChannelMapper;
    private final TarGzImageProcessor tarGzImageProcessor;
    private final BizUserMapper bizUserMapper;

    public static final String NORM_DOT_DATE_PATTERN = "yyyy.MM.dd";

    /**
     * 下载活动海报
     */
    @Override
    public void download(Long id, HttpServletResponse response) {
        DistributionChannelActivity distributionChannelActivity = baseMapper.selectById(id);
        Assert.notNull(distributionChannelActivity, "活动不存在，请刷新后重试~");

        List<ChannelActivityInfoDTO> channelInfo = getChannelListByActivityId(id);
        Assert.notEmpty(channelInfo, "未关联渠道，无法导出~");

        long betweenDay = DateUtil.betweenDay(distributionChannelActivity.getStartTime(), distributionChannelActivity.getEndTime(), true) + 1;

        List<PosterDTO> collect = channelInfo.stream().map(item -> {
            PosterDTO posterDTO = new PosterDTO();
            posterDTO.setPosterName(item.getPosterName());
            posterDTO.setChannelName(item.getChannelName());
            posterDTO.setDiscount(distributionChannelActivity.getDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString());
            posterDTO.setActivityTimeLimit("活动仅限" + betweenDay + "天");
            posterDTO.setActivityTime(DateUtil.format(distributionChannelActivity.getStartTime(), NORM_DOT_DATE_PATTERN) + " - " + DateUtil.format(distributionChannelActivity.getEndTime(), NORM_DOT_DATE_PATTERN));
            posterDTO.setSeedCode(item.getSeedCode());
            posterDTO.setWeChatUrl(item.getWeChatUrl());
            return posterDTO;
        }).collect(Collectors.toList());

        tarGzImageProcessor.generatePosterToTarGz(distributionChannelActivity.getActivityName() + "（" + DateUtil.format(distributionChannelActivity.getStartTime(), DatePattern.NORM_DATE_PATTERN) + "," + DateUtil.format(distributionChannelActivity.getEndTime(), DatePattern.NORM_DATE_PATTERN) + "）" + StrPool.DASHED + "会员" + distributionChannelActivity.getDiscount().divide(BigDecimal.TEN, 2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折", collect, response);
    }

    @Override
    public ChannelActivityDTO getLatestChannelDiscount(Long channelId) {
        return baseMapper.getLatestChannelDiscount(channelId);
    }

    private List<ChannelActivityInfoDTO> getChannelListByActivityId(Long id) {
//        获取类型
        DistributionChannelActivity distributionChannelActivity = baseMapper.selectById(id);
        if (distributionChannelActivity.getType().equals(ChannelActivityTypeEnum.ALL.getCode())) {
//            所有未结束活动
            List<DistributionChannel> distributionChannels = distributionChannelMapper.selectNormalDistributionChannelList();
            return distributionChannels.stream().map(distributionChannel -> ChannelActivityInfoDTO.builder()
                    .posterName(distributionChannel.getPosterName())
                    .channelName(distributionChannel.getChannelName())
                    .seedCode(distributionChannel.getSeedCode())
                    .weChatUrl(distributionChannel.getWeChatUrl()).build()).collect(Collectors.toList());
        }
        if (distributionChannelActivity.getType().equals(ChannelActivityTypeEnum.PART.getCode())) {
//            部分活动
            return distributionChannelActivityInfoService.getChannelInfo(id);
        }
        if (distributionChannelActivity.getType().equals(ChannelActivityTypeEnum.OTHER.getCode())) {
//            除了部分活动
            List<Long> idList = distributionChannelActivityInfoService.getChannelInfo(id).stream().map(ChannelActivityInfoDTO::getId).collect(Collectors.toList());
            List<DistributionChannel> distributionChannels = distributionChannelMapper.selectNormalDistributionChannelList();
            return distributionChannels.stream().filter(distributionChannel -> !idList.contains(distributionChannel.getId()))
                    .map(distributionChannel -> ChannelActivityInfoDTO.builder()
                            .posterName(distributionChannel.getPosterName())
                            .channelName(distributionChannel.getChannelName())
                            .seedCode(distributionChannel.getSeedCode())
                            .weChatUrl(distributionChannel.getWeChatUrl()).build()).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public List<ChannelActivityDTO> list(ChannelActivityDTO dto) {
        PageUtils.startPage();
        List<ChannelActivityDTO> channelActivityDTOS = baseMapper.channelList(dto);
//        所有已经激活的渠道
        Long count = distributionChannelMapper.getChannelCount();
        channelActivityDTOS.forEach(channelActivityDTO -> {
            if (channelActivityDTO.getType().equals(ChannelActivityTypeEnum.ALL.getCode())) {
//                全部活动
                channelActivityDTO.setChannelCount(count);
            }
            if (channelActivityDTO.getType().equals(ChannelActivityTypeEnum.PART.getCode())) {
//                部分活动
                channelActivityDTO.setChannelCount(baseMapper.getPartChannelCount(channelActivityDTO.getId()));
            }
            if (channelActivityDTO.getType().equals(ChannelActivityTypeEnum.OTHER.getCode())) {
//                除了部分活动
                channelActivityDTO.setChannelCount(baseMapper.getOtherChannelCount(channelActivityDTO.getId()));
            }
        });
        return channelActivityDTOS;
    }

    @Override
    @Transactional
    public void saveChannelActivity(ChannelActivityEditDTO dto) {
        Assert.isTrue(baseMapper.checkNameStatus(dto.getActivityName()), "活动名称已存在");
        Long channelId = baseMapper.saveChannelActivity(dto);
        distributionChannelActivityInfoService.save(dto.getChannelIds(), channelId);
    }

    @Override
    @Transactional
    public void editChannelActivity(ChannelActivityEditDTO dto) {
        if (dto.getId() != null) {
            Assert.isTrue(baseMapper.checkNameStatus(dto.getId(), dto.getActivityName()), "活动名称已存在");
        }
        baseMapper.editChannelActivity(dto);
        distributionChannelActivityInfoService.updateActivity(dto);
    }

    @Override
    public ChannelActivityEditDTO getActivityInfo(Long activityId) {
        ChannelActivityEditDTO channelActivityBaseInfo = baseMapper.getChannelActivityBaseInfo(List.of(activityId));
        List<ChannelActivityInfoDTO> channelInfo = distributionChannelActivityInfoService.getChannelInfo(activityId);
        //todo 优化
        channelInfo.forEach(channelActivityInfoDTO -> {
            channelActivityInfoDTO.setActivityList(baseMapper.getChannelActivityList(channelActivityInfoDTO.getId(), channelActivityBaseInfo.getStartTime(), channelActivityBaseInfo.getEndTime(), activityId));
        });
        channelActivityBaseInfo.setChannelActivityInfoList(channelInfo);
        return channelActivityBaseInfo;
    }

//    @Override
//    public BigDecimal getDiscount(String code) {
//        //        判断code是否有效
//        DistributionChannel bySeedCode = distributionChannelMapper.getBySeedCode(code);
//        if (bySeedCode == null || bySeedCode.getStatus() == 1) {
//            return null;
//        }
//        if (ChannelTypeEnum.FISSION.getCode().equals(bySeedCode.getChannelType())){
//            //查询是否展示活动 如果不展示活动则返回null
//
//            //校验是否失效
//            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().bizUserIds(Arrays.asList(bySeedCode.getBizUserId())).build());
//            if (CollUtil.isEmpty(bizUserChannelListVOS)
//                    || BizUserAccountTypeEnum.COMMON.getCode().equals(bizUserChannelListVOS.get(0).getAccountType())
//                    || ObjectUtil.isNull(bizUserChannelListVOS.get(0).getMemberValidity())
//                    || DateUtils.getEndOfToday().compareTo(DateUtils.getEndOfDay(bizUserChannelListVOS.get(0).getMemberValidity())) > 0
//                    || !getFissionActivityShow()
//            ){
//                return null;
//            }
//        }
//        return getChannelDiscount(bySeedCode.getId(), bySeedCode.getChannelType());
//    }

    @Override
    public ChannelBrokeRageVO getChannelDiscount(String code) {
        DistributionChannel distributionChannel = distributionChannelMapper.getBySeedCode(code);
        if (distributionChannel == null || distributionChannel.getStatus() == 1) {
            return null;
        }
        if (ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
            //校验配置是否失效
            if (!getFissionActivityShow()) {
                return null;
            }
        }
        ChannelBrokeRageVO channelBrokeRageVO = getChannelDiscountV1(distributionChannel.getId(), distributionChannel.getChannelType());
        if (ObjectUtil.isNotNull(channelBrokeRageVO)) {
            channelBrokeRageVO.setDistributionChannel(distributionChannel);
        }
        if (ChannelTypeEnum.FISSION.getCode().equals(distributionChannel.getChannelType())) {
            //裂变需要活动还在时间内
            if (channelBrokeRageVO == null
                    || DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, channelBrokeRageVO.getEndTime()).before(new Date())
                    || DateUtils.dateTime(DateUtils.YYYY_MM_DD_HH_MM_SS, channelBrokeRageVO.getStartTime()).after(new Date())
            ) {
                return null;
            }
        }
        return channelBrokeRageVO;
    }

    @Override
    public List<ChannelActivityInfoDTO> getActivityChannelList(String channelName, Date startTime, Date endTime, Long activityId) {
        List<ChannelActivityInfoDTO> channelInfo = distributionChannelActivityInfoService.getChannelInfo(channelName, activityId);
        //todo 优化
        channelInfo.forEach(channelActivityInfoDTO -> {
            channelActivityInfoDTO.setActivityList(baseMapper.getChannelActivityList(channelActivityInfoDTO.getId(), startTime, endTime, activityId));
        });
        return channelInfo;
    }

    /**
     * 获取系统默认折扣
     *
     * @return
     */
    private ChannelBrokeRageVO getSystemDefaultDisCount() {
        String result = Convert.toStr(redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1));
        if (StrUtil.isNotBlank(result)){
            return JSON.parseObject(result, ChannelBrokeRageVO.class);
        }
        return null;
    }

//    /**
//     * 获取裂变系统默认折扣
//     *
//     * @return
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    private BigDecimal getFissionSystemDefaultDisCount() {
//        String configValue = Convert.toStr(redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT));
//        return new BigDecimal(configValue);
//    }
    /**
     * 获取裂变系统默认折扣
     *
     * @return
     */
    private ChannelBrokeRageVO getFissionSystemDefaultDisCountV1() {
        String result = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_MEMBER_DISCOUNT_V1);
        if (StrUtil.isNotBlank(result)){
            return JSON.parseObject(result, ChannelBrokeRageVO.class);
        }
        return null;
    }
    /**
     * 获取裂变系统默认折扣
     *
     * @return
     */
    private ChannelBrokeRageVO getMemberDefaultDisCountV1() {
        String result = redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_MEMBER_DISCOUNT_V1);
        if (StrUtil.isNotBlank(result)){
            return JSON.parseObject(result, ChannelBrokeRageVO.class);
        }
        return null;
    }
    private Boolean getFissionActivityShow() {
        return Convert.toBool(redisService.getCacheObject(Constants.SYS_CONFIG_KEY + ConfigConstants.SYS_CHANNEL_FISSION_SHOW));
    }
//
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    private BigDecimal getChannelDiscount(Long id, Integer channelType) {
////        1.渠道活动的结算价
//        BigDecimal discountActivity = baseMapper.getMinDiscount(id);
////        2.渠道会员的折扣
//        if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(channelType)){
//            BigDecimal disCountChannel = getSystemDefaultDisCount();
////        3.取最小值
//            return discountActivity == null ? disCountChannel : discountActivity.compareTo(disCountChannel) > 0 ? disCountChannel : discountActivity;
//        }
//        //裂变折扣
//        return getFissionSystemDefaultDisCount();
//    }


    /**
     * 默认 固定折扣 折扣0
     *
     * @param id
     * @param channelType
     * @return
     */
    private ChannelBrokeRageVO getChannelDiscountV1(Long id, Integer channelType) {
        if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(channelType)) {
            return getDistributionSystemDefaultDisCount(id);
        }
        //裂变折扣
        return getFissionSystemDefaultDisCountV1();
    }

    /**
     * 先比例 后金额
     * @param id
     * @return
     */
    private ChannelBrokeRageVO getDistributionSystemDefaultDisCount(Long id) {
        ChannelBrokeRageVO channelBrokeRageVO = new ChannelBrokeRageVO();
        channelBrokeRageVO.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
        channelBrokeRageVO.setMemberDiscountType(ChannelDiscountTypeEnum.FIXED_RATIO.getCode());
        channelBrokeRageVO.setMemberDiscount(new BigDecimal("0"));
        //        1.根据渠道ID获取渠道活动的结算价
        BigDecimal discountActivity = baseMapper.getMinDiscount(id);
        //        2.渠道会员的折扣
        ChannelBrokeRageVO distributionChannelBrokeRage = getSystemDefaultDisCount();
        if (ObjectUtil.isNull(distributionChannelBrokeRage)) {
            //渠道会员折扣为空
            if (ObjectUtil.isNull(discountActivity)) {
                //活动也为空
                return null;
            } else {
                //活动不为空
                channelBrokeRageVO.setMemberDiscount(discountActivity);
                return channelBrokeRageVO;
            }
        }
        //渠道会员的折扣 不为空且为固定金额
        if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(distributionChannelBrokeRage.getMemberDiscountType())) {
            //固定金额
            if (ObjectUtil.isNotNull(discountActivity)) {
                //优选选比例
                channelBrokeRageVO.setMemberDiscount(discountActivity);
                return channelBrokeRageVO;
            } else {
                channelBrokeRageVO.setMemberDiscountType(ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode());
                channelBrokeRageVO.setMemberDiscount(distributionChannelBrokeRage.getMemberDiscount());
                return channelBrokeRageVO;
            }
        } else if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(distributionChannelBrokeRage.getMemberDiscountType())) {
            if (ObjectUtil.isNotNull(discountActivity) && discountActivity.compareTo(distributionChannelBrokeRage.getMemberDiscount()) < 0) {
                channelBrokeRageVO.setMemberDiscount(discountActivity);
            } else {
                channelBrokeRageVO.setMemberDiscount(distributionChannelBrokeRage.getMemberDiscount());
            }
            return channelBrokeRageVO;
        } else {
            return null;
        }
    }
}




