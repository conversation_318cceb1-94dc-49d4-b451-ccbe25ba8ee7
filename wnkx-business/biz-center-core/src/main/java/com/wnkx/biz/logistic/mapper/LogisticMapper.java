package com.wnkx.biz.logistic.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.Logistic;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Mapper
public interface LogisticMapper extends SuperMapper<Logistic> {

    /**
     * 通过物流单号查询物流信息
     *
     * @param number 物流单号
     * @return 物流信息
     */
    default List<Logistic> selectListByNumber(Collection<String> number) {
        return this.selectList(new LambdaQueryWrapper<Logistic>()
                .in(Logistic::getNumber, number)
        );
    }

    /**
     * 通过物流单号查询物流信息
     *
     * @param number 物流单号
     * @return 物流信息
     */
    default Logistic getOneByNumber(String number) {
        Logistic logistic = this.selectOne(new LambdaQueryWrapper<Logistic>()
                .eq(Logistic::getNumber, number)
        );

        return logistic == null ? new Logistic() : logistic;
    }

    /**
     * 获取已存在的物流单号
     */
    default List<String> getExistNumbers(Collection<String> numbers) {
        return this.selectList(new LambdaQueryWrapper<Logistic>()
                .select(Logistic::getNumber)
                .in(CollUtil.isNotEmpty(numbers), Logistic::getNumber, numbers)
        ).stream().map(Logistic::getNumber).collect(Collectors.toList());
    }
}
