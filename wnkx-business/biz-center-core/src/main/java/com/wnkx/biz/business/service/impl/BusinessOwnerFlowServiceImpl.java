package com.wnkx.biz.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessOwnerFlowVO;
import com.wnkx.biz.business.service.IBusinessOwnerFlowService;
import com.wnkx.biz.business.mapper.BusinessOwnerFlowMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_owner_flow(商家主账号换绑记录)】的数据库操作Service实现
 * @createDate 2025-01-16 11:04:07
 */
@Service
@RequiredArgsConstructor
public class BusinessOwnerFlowServiceImpl extends ServiceImpl<BusinessOwnerFlowMapper, BusinessOwnerFlow>
        implements IBusinessOwnerFlowService {

    @Override
    public List<BusinessOwnerFlowVO> getListByBusinessId(Long businessId) {
        return baseMapper.getListByBusinessId(businessId);
    }

    @Override
    public BusinessOwnerFlow saveEntity(BusinessOwnerFlow businessOwnerFlow) {
        businessOwnerFlow.setCreateById(SecurityUtils.getUserId());
        businessOwnerFlow.setCreateBy(SecurityUtils.getUsername());
        baseMapper.insert(businessOwnerFlow);
        return businessOwnerFlow;
    }
}




