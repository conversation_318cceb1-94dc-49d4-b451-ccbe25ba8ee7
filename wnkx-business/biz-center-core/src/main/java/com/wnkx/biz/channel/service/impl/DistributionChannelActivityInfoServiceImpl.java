package com.wnkx.biz.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.wnkx.biz.channel.mapper.DistributionChannelActivityInfoMapper;
import com.wnkx.biz.channel.service.DistributionChannelActivityInfoService;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivityInfo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_activity_info(渠道活动关联表)】的数据库操作Service实现
 * @createDate 2024-12-02 14:47:46
 */
@Service
public class DistributionChannelActivityInfoServiceImpl extends ServiceImpl<DistributionChannelActivityInfoMapper, DistributionChannelActivityInfo>
        implements DistributionChannelActivityInfoService {

    @Override
    public void save(List<Long> channelIds, Long activityId) {
        baseMapper.saveBatch(channelIds.stream().map(channelId -> {
            DistributionChannelActivityInfo entity = new DistributionChannelActivityInfo();
            entity.setChannelId(channelId);
            entity.setActivityId(activityId);
            return entity;
        }).collect(Collectors.toList()));
    }

    @Override
    public void updateActivity(ChannelActivityEditDTO dto) {
        baseMapper.deleteByActivity(dto.getId());
        save(dto.getChannelIds(), dto.getId());
    }

    @Override
    public List<ChannelActivityInfoDTO> getChannelInfo(Long id) {
        return baseMapper.getActivityChannelInfo(null, id, true);
    }

    @Override
    public List<ChannelActivityInfoDTO> getChannelInfo(String channelName, Long id) {
        return baseMapper.getActivityChannelInfo(channelName, id, false);
    }
}




