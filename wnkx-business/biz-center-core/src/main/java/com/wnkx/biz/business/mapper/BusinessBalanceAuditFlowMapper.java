package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowValidListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.PayoutAmountStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceAuditFlowDetailExportVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_audit_flow(余额提现审核表)】的数据库操作Mapper
 * @createDate 2024-08-09 18:19:51
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow
 */
@Mapper
public interface BusinessBalanceAuditFlowMapper extends SuperMapper<BusinessBalanceAuditFlow> {


    /**
     * 获取余额提现审核列表
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlowVO> queryList(@Param("dto") BusinessBalanceAuditFlowListDTO dto,@Param("bizUserId")  Long bizUserId, @Param("currentUserIsAdmin") Boolean currentUserIsAdmin);

    /**
     * 获取提现明细列表
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlowDetailExportVO> queryExportList(@Param("dto")BusinessBalanceAuditFlowListDTO dto);
    /**
     * 获取有效余额提现审核数据
     *
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlow> queryValidList(BusinessBalanceAuditFlowValidListDTO dto);


    /**
     * 获取提现统计
     *
     * @return
     */
    BusinessBalanceAuditFlowStatisticsVO getStatistics();

    /**
     * 统计商家体现锁定余额
     *
     * @param businessId
     * @return
     */
    BigDecimal getBusinessLockAmount(Long businessId);

    /**
     * 获取商家提现统计金额
     * @param businessId
     * @return
     */
    PayoutAmountStatisticsVO getPayoutAmountStatistics(Long businessId);

    /**
     * 获取未结算的提现数量
     * @return
     */
    default Long getPreApproveCount(){
        return selectCount(new LambdaQueryWrapper<>(BusinessBalanceAuditFlow.class)
                .eq(BusinessBalanceAuditFlow::getAuditStatus, BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode())
        );
    }

    default void generateWithdrawNumber(Long id) {
        update(null, new LambdaUpdateWrapper<>(BusinessBalanceAuditFlow.class)
                .eq(BusinessBalanceAuditFlow::getId, id)
                .set(BusinessBalanceAuditFlow::getWithdrawNumber, String.format("TX%05d", id)));
    }

    default void markNotice(Long id){
        update(null, new LambdaUpdateWrapper<>(BusinessBalanceAuditFlow.class)
                .eq(BusinessBalanceAuditFlow::getId, id)
                .set(BusinessBalanceAuditFlow::getNotifyStatus, 1)
                .set(BusinessBalanceAuditFlow::getNotifyTime, new Date())
        );
    }
}
