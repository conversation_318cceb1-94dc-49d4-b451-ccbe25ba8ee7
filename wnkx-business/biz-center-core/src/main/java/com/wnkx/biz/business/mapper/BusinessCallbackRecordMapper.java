package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackRecord;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackRecordListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:27
 */
@Mapper
public interface BusinessCallbackRecordMapper extends SuperMapper<BusinessCallbackRecord> {


    /**
     * 商家回访-回访记录-回访账号下拉框
     */
    default List<BusinessCallbackRecord> returnVisitAccount(Long businessId) {
        return selectList(new LambdaQueryWrapper<BusinessCallbackRecord>()
                .eq(BusinessCallbackRecord::getBusinessId, businessId)
                .select(BusinessCallbackRecord::getAccountIsOwnerAccount, BusinessCallbackRecord::getAccountName, BusinessCallbackRecord::getAccountNickName)
        );
    }

    /**
     * 商家回访-回访记录
     */
    List<BusinessCallbackRecordListVO> returnVisitRecord(@Param("dto") ReturnVisitRecordDTO dto);
}
