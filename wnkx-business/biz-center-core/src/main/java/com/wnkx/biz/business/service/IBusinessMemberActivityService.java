package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.dto.biz.business.activity.BusinessMemberActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.business.activity.UpdateMemberActivityDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_member_activity(商家会员活动)】的数据库操作Service
* @createDate 2025-01-16 18:04:14
*/
public interface IBusinessMemberActivityService extends IService<BusinessMemberActivity> {

    /**
     * 保存会员活动
     * @param businessMemberActivity
     * @return
     */
    BusinessMemberActivity saveMemberActivity(BusinessMemberActivityDTO businessMemberActivity);

    /**
     * 更新会员活动
     * @param dto
     */
    void updateMemberActivity(BusinessMemberActivityDTO dto);

    /**
     * 更新会员活动状态
     * @param dto
     */
    void updateStatus(UpdateMemberActivityDTO dto);

    /**
     * 查询会员活动列表
     * @return
     */
    List<BusinessMemberActivity> queryList();

    /**
     * 查询今日会员活动
     * @return
     */
    List<BusinessMemberActivity> getTodayActivity();
}
