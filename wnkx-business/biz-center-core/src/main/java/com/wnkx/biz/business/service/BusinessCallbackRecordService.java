package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.business.WriteReturnVisitDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackRecord;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackRecordListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
public interface BusinessCallbackRecordService extends IService<BusinessCallbackRecord> {

    /**
     * 新增商家回访记录
     */
    void addBusinessCallbackRecord(WriteReturnVisitDTO dto);

    /**
     * 商家回访-回访记录-回访账号下拉框
     */
    List<BusinessCallbackRecord> returnVisitAccount(Long businessId);

    /**
     * 商家回访-回访记录
     */
    List<BusinessCallbackRecordListVO> returnVisitRecord(ReturnVisitRecordDTO dto);
}
