package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.AccountAuditStatusEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.AccountApplyQueryDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.AuditAccountApplyDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.BusinessAccountApplyDTO;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.business.mapper.BusinessAccountApplyMapper;
import com.wnkx.biz.business.mapper.BusinessAccountRebindLogMapper;
import com.wnkx.biz.business.service.IBusinessAccountApplyService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_account_apply(账号申请表)】的数据库操作Service实现
* @createDate 2024-08-01 11:39:06
*/
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BusinessAccountApplyServiceImpl extends ServiceImpl<BusinessAccountApplyMapper, BusinessAccountApply>
implements IBusinessAccountApplyService {
    private final IBusinessAccountApplyService self;
    private final IBusinessAccountService businessAccountService;
    private final RemoteService remoteService;
    private final BusinessAccountRebindLogMapper rebindLogMapper;
    private final BizUserMapper bizUserMapper;

    @Override
    public List<BusinessAccountApplyVO> list(AccountApplyQueryDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.list(dto);
    }

    @Override
    public Integer businessAccountApplyNum(AccountApplyQueryDTO dto) {
        return baseMapper.businessAccountApplyNum(dto);
    }

    @Override
    public BusinessAccountApply save(BusinessAccountApplyDTO dto) {
        Assert.isFalse(baseMapper.checkUnionidStatus(dto.getUnionid()), "已存在申请记录 无法再次添加！");
        BusinessAccountApply businessAccountApply = BeanUtil.copyProperties(dto, BusinessAccountApply.class);
        baseMapper.insert(businessAccountApply);
        return businessAccountApply;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MemberAuth
    public void audit(AuditAccountApplyDTO dto) {
        BusinessAccountApply tableEntity = self.getById(dto.getId());
        Assert.notNull(tableEntity, "不存在该申请数据");
        Assert.isTrue(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount().equals(tableEntity.getOwnerAccount()), "当前登录人与修改记录账号不一致！");
        Assert.isTrue(AccountAuditStatusEnum.UN_AUDIT.getCode().equals(tableEntity.getAuditStatus()), "非待审核状态数据无法进行账号申请审批！");

        BusinessAccountApply businessAccountApply = BeanUtil.copyProperties(dto, BusinessAccountApply.class);
        businessAccountApply.setAuditTime(new Date());
        self.updateById(businessAccountApply);
        BusinessAccount businessAccount = new BusinessAccount();
        if (AccountAuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())){
            businessAccount = businessAccountService.initBusinessSon(tableEntity);
        }


        BusinessAccountRebindLog rebindLog = rebindLogMapper.selectOneByApplyId(tableEntity.getId());
        if (null != rebindLog) {
            String contactUserName =  bizUserMapper.getUserContactUserNameByUid(rebindLog.getBizUserId());
            if (dto.getAuditStatus() == 1) {
                rebindLog.setStatus(0);
            }
            rebindLog.setConnectUserName(contactUserName);
            rebindLog.setAccount(businessAccount.getAccount());
            rebindLog.setAuditStatus(dto.getAuditStatus());
            rebindLog.setAuditTime(new Date());
            rebindLogMapper.updateById(rebindLog);
        }
    }

    @Override
    public boolean updateById(BusinessAccountApply entity) {
        entity.setOwnerAccount(null);
        entity.setUnionid(null);
        return super.updateById(entity);
    }

}
