package com.wnkx.biz.business.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.enums.BusinessStatusEnum;
import com.ruoyi.common.core.enums.CustomerTypeEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserBusinessInfoDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserListDTO;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalContactInfoDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【biz_user(登录用户表)】的数据库操作Mapper
 * @createDate 2024-08-26 11:22:30
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.BizUser
 */
@Mapper
public interface BizUserMapper extends SuperMapper<BizUser> {

    /**
     * 获取账号列表
     *
     * @param dto
     * @return
     */
    List<BizUserListVO> bizUserList(BizUserListDTO dto);

    /**
     * 查询渠道登录账号数据
     *
     * @param dto
     * @return
     */
    List<BizUserChannelListVO> channelBizUserId(BizUserChannelListDTO dto);

    /**
     * 获取登账账号详情列表数据
     *
     * @param dto
     * @return
     */
    List<BizUserDetailVO> bizUserDetailList(BizUserDetailListDTO dto);

    /**
     * 根据商家id获取登录账号数据
     * @param businessId
     * @return
     */
    List<BizUser> getBizUserByBusinessId(Long businessId);

    /**
     * 根据会员类型获取登录账号ID
     * @param memberStatus
     * @return
     */
    List<Long> getBizUserIdByMemberStatus(Integer memberStatus);

    /**
     * 根据unionId获取登录数据
     *
     * @param unionId
     * @return
     */
    default BizUser getByUnionId(String unionId) {
        return this.selectOne(new LambdaQueryWrapper<BizUser>()
                .eq(BizUser::getUnionid, unionId));

    }

    /**
     * 根据手机号获取登录数据
     *
     * @param phone
     * @return
     */
    default BizUser getByPhone(String phone) {
        return this.selectOne(new LambdaQueryWrapper<BizUser>()
                .eq(BizUser::getPhone, phone));

    }

    /**
     * 用于账户登录时，重新绑定了企业微信 设置外部联系人id
     *
     * @param unionId
     * @param externalUserid
     */
    default void setUserExternalUserid(String unionId, String externalUserid) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getExternalUserId, externalUserid)
                .set(BizUser::getStatus, BusinessStatusEnum.ENABLED.getCode())
                .eq(BizUser::getUnionid, unionId));

    }

    /**
     * 用于绑定微信手机号
     *
     * @param unionId unionId
     * @param phone   手机号
     */
    default void setPhone(String unionId, String phone) {
        if (StringUtils.isBlank(unionId) || StringUtils.isBlank(phone)) {
            log.error("绑定登录账号手机号参数：unionId：" + unionId + "，phone：" + phone);
            throw new ServiceException("绑定手机号参数不能为空!");
        }
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getPhone, phone)
                .eq(BizUser::getUnionid, unionId));
    }

    /**
     * 用于填充种草官ID
     * @param id
     * @param seedId
     */
    default void setSeedId(Long id, String seedId){
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getSeedId, seedId)
                .eq(BizUser::getId, id));
    }

    /**
     * 用于换绑微信号
     *
     * @param id  主键ID
     * @param dto unionId
     */
    default void updateWeChat(Long id, ExternalContactInfoDTO dto) {
        if (ObjectUtil.isNull(id) || ObjectUtil.isNull(dto)) {
            log.error("参数：：微信信息" + dto + "，id：" + id);
            throw new ServiceException("换绑微信号参数不能为空!");
        }
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getUnionid, dto.getUnionid())
                .set(BizUser::getExternalUserId, dto.getExternalUserid())
                .set(BizUser::getPic, dto.getAvatar())
                .set(BizUser::getNickName, dto.getName())
                .eq(BizUser::getId, id));
    }

    /**
     * 企业微信事件回调，解除了企微好友
     *
     * @param externalUserId
     */
    default void cleanUserExternalUserid(String externalUserId) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getStatus, BusinessStatusEnum.DELETE.getCode())
                .eq(BizUser::getExternalUserId, externalUserId));
    }

    /**
     * 清楚商家数据
     *
     * @param id
     */
    default void cleanUserBusiness(Long id) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getUnbindTime, new Date())
                .set(BizUser::getAccountType, BizUserAccountTypeEnum.COMMON.getCode())
                .set(BizUser::getWaiterId, null)
                .eq(BizUser::getId, id));
    }

    /**
     * 更新最后登录时间
     *
     * @param phone
     */
    default void updateLoginTime(String phone) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getLastLoginTime, new Date())
                .eq(BizUser::getPhone, phone));
    }

    /**
     * 修改登录表数据的商家数据
     *
     * @param dto
     */
    default void updateUserBusinessInfo(BizUserBusinessInfoDTO dto) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(ObjectUtil.isNotNull(dto.getAccountType()), BizUser::getAccountType, dto.getAccountType())
                .set(BizUser::getCustomerType, Optional.ofNullable(dto.getCustomerType()).orElse(CustomerTypeEnum.COMMON.getCode()))
                .set(ObjectUtil.isNotNull(dto.getWaiterId()), BizUser::getWaiterId, dto.getWaiterId())
                .eq(BizUser::getId, dto.getId()));

    }

    /**
     * 批量修改登录账号客服
     *
     * @param ids
     * @param waiterId
     */
    default void updateWaiterIdByIds(List<Long> ids, Long waiterId) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getWaiterId, waiterId)
                .in(BizUser::getId, ids));
    }

    /**
     * 根据ID修改登录账号 如果数据为空则填充空
     *
     * @param bizUser
     */
    default void updateFillNullById(BizUser bizUser) {
        this.update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getWaiterId, bizUser.getWaiterId())
                .set(BizUser::getName, Optional.ofNullable(bizUser.getName()).orElse(""))
                .set(BizUser::getCustomerType, bizUser.getCustomerType())
                .eq(BizUser::getId, bizUser.getId()));

    }

    default BizUser getExternalUserIdById(Long id) {
        return selectOne(new LambdaQueryWrapper<BizUser>()
                .select(BizUser::getExternalUserId)
                .eq(BizUser::getId, id)
                .last("limit 1")
        );
    }

    List<BusinessAccountDetailVO> queryUserInfo(@Param("dto") BusinessAccountDetailDTO dto);

    default void updateContactUserName(String unionId, String connectUserName) {
        update(null, new LambdaUpdateWrapper<BizUser>()
                .set(BizUser::getConnectUserName, connectUserName)
                .eq(BizUser::getUnionid, unionId)
        );
    }

    default String getUserContactUserNameByUid(Long bizUserId){
        return selectOne(new LambdaQueryWrapper<BizUser>()
                .select(BizUser::getConnectUserName)
                .eq(BizUser::getId,bizUserId)
                .last("limit 1")
        ).getConnectUserName();
    }

    /**
     * 校验手机号是否绑定用户
     */
    default Boolean phoneCheck(String phone) {
        return exists(new LambdaQueryWrapper<BizUser>()
                .eq(BizUser::getPhone, phone)
                .eq(BizUser::getStatus, BusinessStatusEnum.ENABLED.getCode())
                .eq(BizUser::getIsDel, StatusEnum.ENABLED.getCode())
        );
    }
}




