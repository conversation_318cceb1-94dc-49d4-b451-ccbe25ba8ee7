package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wnkx.biz.channel.service.DistributionChannelDiscountLogService;
import com.wnkx.biz.channel.mapper.DistributionChannelDiscountLogMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_discount_log(渠道折扣日志)】的数据库操作Service实现
* @createDate 2025-05-15 09:15:45
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class DistributionChannelDiscountLogServiceImpl extends ServiceImpl<DistributionChannelDiscountLogMapper, DistributionChannelDiscountLog>
    implements DistributionChannelDiscountLogService{

    @Override
    public void saveDistributionChannelDiscountLog(EditFissionChannelDiscountDTO dto) {
        DistributionChannelDiscountLog distributionChannelDiscountLog = BeanUtil.copyProperties(dto, DistributionChannelDiscountLog.class);
        distributionChannelDiscountLog.setCreateBy(SecurityUtils.getUsername());
        distributionChannelDiscountLog.setCreateById(SecurityUtils.getUserId());
        baseMapper.insert(distributionChannelDiscountLog);
    }

    @Override
    public List<DistributionChannelDiscountLog> queryListByChannelId(Long channelId) {
        return baseMapper.queryListByChannelId(channelId);
    }
}




