package com.wnkx.biz.business.mapper;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalancePrepayListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.PrepayUpdateAppIdDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_balance_prepay(商家预付表)】的数据库操作Mapper
* @createDate 2024-11-04 14:32:59
* @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay
*/
public interface BusinessBalancePrepayMapper extends SuperMapper<BusinessBalancePrepay> {

    /**
     * 查询应收审批-预付款列表
     * @param dto
     * @return
     */
    List<BusinessBalancePrepayVO> queryList(BusinessBalancePrepayListDTO dto);


    /**
     * 获取预付款列表-不做过滤
     * @param dto
     * @return
     */
    List<BusinessBalancePrepayVO> innerQueryList(BusinessBalancePrepayListDTO dto);

    /**
     *  获取统计数据
     * @return
     */
    BusinessBalancePrepayStatisticsVO getStatistics();

    /**
     * 获取线上钱包订单
     * @param businessId
     * @return
     */
    default List<BusinessBalancePrepay> getOnlineList(Long businessId){
        return this.selectList(new LambdaQueryWrapper<BusinessBalancePrepay>()
                .eq(BusinessBalancePrepay::getBusinessId, businessId)
                .eq(BusinessBalancePrepay::getOrderType, OrderTypeEnum.ONLINE_RECHARGE.getCode())
        );
    }

    /**
     * 设置appId
     * @param dto
     */
    default void updateAppId(PrepayUpdateAppIdDTO dto) {
        this.update(null, new LambdaUpdateWrapper<BusinessBalancePrepay>()
                .set(StrUtil.isNotBlank(dto.getAppId()) && PrepayPayTypeEnum.WECHAT.getCode().equals(dto.getPayType()), BusinessBalancePrepay::getWechatPayAppId,  dto.getAppId())
                .set(StrUtil.isNotBlank(dto.getAppId()) && PrepayPayTypeEnum.ALIPAY.getCode().equals(dto.getPayType()), BusinessBalancePrepay::getAlipayPayAppId,  dto.getAppId())
                .set(ObjectUtil.isNotNull(dto.getAccountId()), BusinessBalancePrepay::getAccountId, dto.getAccountId())
                .set(ObjectUtil.isNotNull(dto.getPayUserId()), BusinessBalancePrepay::getPayUserId, dto.getPayUserId())
                .set(ObjectUtil.isNotNull(dto.getPayType()) && !StatusTypeEnum.YES.getCode().equals(dto.getIsAnother()), BusinessBalancePrepay::getPayType, dto.getPayType())
                .in(BusinessBalancePrepay::getPrepayNum, dto.getPrepayNums())
        );
    }

    /**
     * 根据商家Id获取线上钱包充值数据
     * @param businessId
     * @return
     */
    default List<BusinessBalancePrepay> getOnlineUnPay(Long businessId){
        return this.selectList(new LambdaQueryWrapper<BusinessBalancePrepay>()
                .eq(BusinessBalancePrepay::getBusinessId, businessId)
                .eq(BusinessBalancePrepay::getStatus, OrderStatusEnum.UN_PAY.getCode())
                .eq(BusinessBalancePrepay::getOrderType, OrderTypeEnum.ONLINE_RECHARGE.getCode())
        );
    }


    /**
     * 获取去下订单列表
     * @param beginTime
     * @param endTime
     * @return
     */
    default List<BusinessBalancePrepay> getCloseOnlineOrders(Date beginTime, Date endTime){
        return this.selectList(new LambdaQueryWrapper<BusinessBalancePrepay>()
                .eq(BusinessBalancePrepay::getStatus, OrderStatusEnum.UN_PAY.getCode())
                .eq(BusinessBalancePrepay::getOrderType, OrderTypeEnum.ONLINE_RECHARGE.getCode())
                .between(BusinessBalancePrepay::getCreatTime, beginTime, endTime)
        );
    }

    /**
     * 重置数据
     * @param orderNums
     */
    default void updateBatchFieldNullToNull(Collection<String> orderNums) {
        this.update(null, new LambdaUpdateWrapper<BusinessBalancePrepay>()
                .set(BusinessBalancePrepay::getPayType, null)
                .set(BusinessBalancePrepay::getPayUserId, null)
                .in(BusinessBalancePrepay::getPrepayNum, orderNums)
        );
    }

    /**
     * 关闭订单
     * @param id
     */
    default void cancelOnlineOrder(Long id){
        this.update(null, new LambdaUpdateWrapper<BusinessBalancePrepay>()
                        .set(BusinessBalancePrepay::getStatus, OrderStatusEnum.TRADE_CLOSE.getCode())
                        .set(BusinessBalancePrepay::getCloseOrderTime, new Date())
                        .set(BusinessBalancePrepay::getAuditStatus, BusinessPrepayAuditStatusEnum.CLOSE.getCode())
                .eq(BusinessBalancePrepay::getId, id)
        );
    }

    /**
     * 获取最后一个预付款数据
     * @return
     */
    default BusinessBalancePrepay getLastEntity(){
        return this.selectOne(new LambdaQueryWrapper<BusinessBalancePrepay>().orderByDesc(BusinessBalancePrepay::getId).last("limit 1"));

    }

    /**
     * preNum
     * @param preNum
     * @return
     */
    default BusinessBalancePrepay getByPreNum(String preNum){
        return this.selectOne(new LambdaQueryWrapper<BusinessBalancePrepay>().eq(BusinessBalancePrepay::getPrepayNum, preNum));

    }


    default List<BusinessBalancePrepay> selectPrepayListByOriginNumbers(List<String> numbers) {
        return selectList(Wrappers.lambdaQuery(BusinessBalancePrepay.class).in(BusinessBalancePrepay::getPrepayNum, numbers));
    }
}




