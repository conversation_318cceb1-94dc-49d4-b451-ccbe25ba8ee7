package com.wnkx.biz.channel.service.impl;

import java.math.BigDecimal;
import java.util.*;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.ChannelDiscountTypeEnum;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum;
import com.ruoyi.common.core.enums.WithdrawTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.MemberSeedRecordWithdrawalDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.*;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.channel.mapper.MemberSeedRecordMapper;
import com.wnkx.biz.channel.service.MemberSeedRecordRelevanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wnkx.biz.channel.service.MemberSeedRecordWithdrawalService;
import com.wnkx.biz.channel.mapper.MemberSeedRecordWithdrawalMapper;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【member_seed_record_withdrawal(会员种草提现)】的数据库操作Service实现
 * @createDate 2025-05-15 09:15:45
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MemberSeedRecordWithdrawalServiceImpl extends ServiceImpl<MemberSeedRecordWithdrawalMapper, MemberSeedRecordWithdrawal>
        implements MemberSeedRecordWithdrawalService {
    private final RedisService redisService;
    private final MemberSeedRecordRelevanceService memberSeedRecordRelevanceService;
    private final MemberSeedRecordMapper memberSeedRecordMapper;
    private final BizUserMapper bizUserMapper;

    @Override
    public List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalListByChannelId(Long channelId) {
        Assert.notNull(channelId, "渠道Id不能为空");
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryMemberSeedRecordWithdrawalListByChannelId(channelId);
    }

    @Override
    public List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalList(WithdrawalListDTO dto) {
        wrapperCondition(dto);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msrw.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<MemberSeedRecordWithdrawalVO> memberSeedRecordWithdrawalVOS = baseMapper.queryMemberSeedRecordWithdrawalList(dto);
        //加载申请人信息
        if (CollUtil.isEmpty(memberSeedRecordWithdrawalVOS)) {
            return memberSeedRecordWithdrawalVOS;
        }
        loadList(memberSeedRecordWithdrawalVOS);
        return memberSeedRecordWithdrawalVOS;
    }

    @Override
    public List<MemberSeedRecordWithdrawalExportVO> queryMemberSeedRecordWithdrawalListExport(WithdrawalListDTO dto) {
        wrapperCondition(dto);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msrw.create_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("msrr.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<MemberSeedRecordWithdrawalVO> memberSeedRecordWithdrawalVOS = baseMapper.queryMemberSeedRecordWithdrawalListExport(dto);
        //加载申请人信息
        if (CollUtil.isEmpty(memberSeedRecordWithdrawalVOS)) {
            return Collections.emptyList();
        }
        loadList(memberSeedRecordWithdrawalVOS);

        List<MemberSeedRecordWithdrawalExportVO> result = new ArrayList<>();
        String cr = StrPool.LF;
        String dashed = StrPool.DASHED;
        for (MemberSeedRecordWithdrawalVO item :memberSeedRecordWithdrawalVOS){
            MemberSeedRecordWithdrawalExportVO memberSeedRecordWithdrawalExportVO = BeanUtil.copyProperties(item, MemberSeedRecordWithdrawalExportVO.class);
            StringBuilder sb = new StringBuilder();
            if (ChannelTypeEnum.FISSION.getCode().equals(item.getChannelType())){
                sb.append("微信：").append(Optional.ofNullable(item.getApplicantNickName()).orElse(dashed)).append(cr);
                sb.append("姓名：").append(Optional.ofNullable(item.getApplicantName()).orElse(dashed)).append(cr);
                sb.append("公司名称：").append(Optional.ofNullable(item.getApplicantBusinessName()).orElse(dashed)).append(cr);
                sb.append("会员编码：").append(Optional.ofNullable(item.getApplicantMemberCode()).orElse(dashed));
            }else {
                sb.append("渠道名称：").append(Optional.ofNullable(item.getChannelName()).orElse(dashed)).append(cr);
            }


            memberSeedRecordWithdrawalExportVO.setApplyInfo(sb.toString());

            if (ObjectUtil.isNotNull(item.getAuditTime())){
                memberSeedRecordWithdrawalExportVO.setFlowTime(item.getAuditTime());
                memberSeedRecordWithdrawalExportVO.setRemark(item.getAuditRemark());
            }
            if (ObjectUtil.isNotNull(item.getWithdrawalTime())){
                memberSeedRecordWithdrawalExportVO.setFlowTime(item.getWithdrawalTime());
                memberSeedRecordWithdrawalExportVO.setRemark(item.getWithdrawalRemark());
            }
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(item.getSettleType())){
                memberSeedRecordWithdrawalExportVO.setSettleRageStr(item.getSettleRage().toString());
            }else if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(item.getSettleType())){
                memberSeedRecordWithdrawalExportVO.setSettleRageStr(item.getSettleRage().toString() + "%");
            }

            if (WithdrawTypeEnum.PUBLIC.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setPublicAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setPublicPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setPublicBankName(item.getBankName());
            } else if (WithdrawTypeEnum.ALIPAY.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setAlipayAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setBankPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setBankPayeeIdentityCard(item.getPayeeIdentityCard());
            } else if (WithdrawTypeEnum.BANK.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setBankAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setBankPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setBankPayeeIdentityCard(item.getPayeeIdentityCard());
            }
            result.add(memberSeedRecordWithdrawalExportVO);
        }

        return result;
    }

    @Override
    public List<FissionSettleRecordVO> queryFissionSettleRecordList(WithdrawalListDTO dto) {
        wrapperCondition(dto);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msrw.withdrawal_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("msrr.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        if (StrUtil.isNotBlank(dto.getKeyword())){
            dto.setKeyword(dto.getKeyword().toUpperCase());
        }
        List<FissionSettleRecordVO> fissionSettleRecordVOS = baseMapper.queryFissionSettleRecordList(dto);
        loadList(fissionSettleRecordVOS);
        return fissionSettleRecordVOS;
    }

    @Override
    public List<FissionSettleRecordExportVO> queryFissionSettleRecordListExport(WithdrawalListDTO dto) {
        wrapperCondition(dto);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("msrw.withdrawal_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("msrr.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        if (StrUtil.isNotBlank(dto.getKeyword())){
            dto.setKeyword(dto.getKeyword().toUpperCase());
        }
        List<FissionSettleRecordVO> fissionSettleRecordVOS = baseMapper.queryFissionSettleRecordList(dto);
        loadList(fissionSettleRecordVOS);

        List<FissionSettleRecordExportVO> result = new ArrayList<>();
        String cr = StrPool.LF;
        String dashed = StrPool.DASHED;
        for (MemberSeedRecordWithdrawalVO item :fissionSettleRecordVOS){
            FissionSettleRecordExportVO memberSeedRecordWithdrawalExportVO = BeanUtil.copyProperties(item, FissionSettleRecordExportVO.class);
            StringBuilder sb = new StringBuilder();
            if (ChannelTypeEnum.FISSION.getCode().equals(item.getChannelType())){
                sb.append("微信：").append(Optional.ofNullable(item.getApplicantNickName()).orElse(dashed)).append(cr);
                sb.append("姓名：").append(Optional.ofNullable(item.getApplicantName()).orElse(dashed)).append(cr);
                sb.append("公司名称：").append(Optional.ofNullable(item.getApplicantBusinessName()).orElse(dashed)).append(cr);
                sb.append("会员编码：").append(Optional.ofNullable(item.getApplicantMemberCode()).orElse(dashed));
            }else {
                sb.append("渠道名称：").append(Optional.ofNullable(item.getChannelName()).orElse(dashed)).append(cr);
            }

            memberSeedRecordWithdrawalExportVO.setApplyInfo(sb.toString());
            if (ChannelDiscountTypeEnum.FIXED_AMOUNT.getCode().equals(item.getSettleType())){
                memberSeedRecordWithdrawalExportVO.setSettleRageStr(item.getSettleRage().toString());
            }else if (ChannelDiscountTypeEnum.FIXED_RATIO.getCode().equals(item.getSettleType())){
                memberSeedRecordWithdrawalExportVO.setSettleRageStr(item.getSettleRage().toString() + "%");
            }
            memberSeedRecordWithdrawalExportVO.setOrderNum(item.getOrderNumStr());

            if (WithdrawTypeEnum.PUBLIC.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setPublicAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setPublicPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setPublicBankName(item.getBankName());
            } else if (WithdrawTypeEnum.ALIPAY.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setAlipayAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setBankPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setBankPayeeIdentityCard(item.getPayeeIdentityCard());
            } else if (WithdrawTypeEnum.BANK.getCode().equals(item.getWithdrawalAccountType())) {
                memberSeedRecordWithdrawalExportVO.setBankAccount(item.getPayeeAccount());
                memberSeedRecordWithdrawalExportVO.setBankPayeeName(item.getPayeeName());
                memberSeedRecordWithdrawalExportVO.setBankPayeeIdentityCard(item.getPayeeIdentityCard());
            }
            result.add(memberSeedRecordWithdrawalExportVO);
        }

        return result;
    }

    /**
     * 组装请求列表
     * @param dto
     */
    private void wrapperCondition(WithdrawalListDTO dto) {
        if (StrUtil.isNotBlank(dto.getKeyword())) {
            List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().keyword(dto.getKeyword()).build());
            if (CollUtil.isNotEmpty(bizUserChannelListVOS)) {
                dto.setKeywordBizUserIds(bizUserChannelListVOS.stream().map(BizUserChannelListVO::getId).collect(Collectors.toList()));
            }
        }
    }


    private void loadList(List<? extends MemberSeedRecordWithdrawalVO> memberSeedRecordWithdrawalVOS) {
        if (CollUtil.isEmpty(memberSeedRecordWithdrawalVOS)) {
            return;
        }
        List<Long> channelIds = memberSeedRecordWithdrawalVOS.stream().map(MemberSeedRecordWithdrawalVO::getChannelId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(channelIds)) {
            return;
        }
        List<BizUserChannelListVO> bizUserChannelListVOS = bizUserMapper.channelBizUserId(BizUserChannelListDTO.builder().channelIds(channelIds).build());
        if (CollUtil.isEmpty(bizUserChannelListVOS)) {
            return;
        }

        Map<Long, BizUserChannelListVO> channelListVOMap = bizUserChannelListVOS.stream().collect(Collectors.toMap(BizUserChannelListVO::getChannelId, p -> p));
        for (MemberSeedRecordWithdrawalVO memberSeedRecordWithdrawalVO : memberSeedRecordWithdrawalVOS) {
            BizUserChannelListVO bizUserChannelListVO = channelListVOMap.get(memberSeedRecordWithdrawalVO.getChannelId());
            if (ObjectUtil.isNull(bizUserChannelListVO)){
                continue;
            }
            memberSeedRecordWithdrawalVO.setApplicantMemberCode(bizUserChannelListVO.getMemberCode());
            memberSeedRecordWithdrawalVO.setApplicantBusinessName(bizUserChannelListVO.getBusinessName());
            memberSeedRecordWithdrawalVO.setApplicantNickName(bizUserChannelListVO.getNickName());
            memberSeedRecordWithdrawalVO.setApplicantName(bizUserChannelListVO.getName());
        }
    }


    @Override
    public MemberSeedRecordWithdrawalVO getMemberSeedRecordWithdrawalDetail(Long id) {
        MemberSeedRecordWithdrawalVO memberSeedRecordWithdrawalVO = baseMapper.getMemberSeedRecordWithdrawalDetail(id);
        loadList(Arrays.asList(memberSeedRecordWithdrawalVO));
        return memberSeedRecordWithdrawalVO;
    }

    @Override
    public MemberSeedRecordWithdrawalVO getLastWithdrawalDetailByAccountType(Integer accountType, Long channelId) {
        return BeanUtil.copyProperties(baseMapper.getLastWithdrawalDetailByAccountType(accountType, channelId), MemberSeedRecordWithdrawalVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberSeedRecordWithdrawal initEntity(MemberSeedRecordWithdrawalDTO dto) {
        List<MemberSeedRecord> memberSeedRecordList = dto.getMemberSeedRecordList();
        Assert.isTrue(CollUtil.isNotEmpty(memberSeedRecordList), "会员种草记录不能为空");
        WithdrawalDTO withdrawalDTO = dto.getWithdrawalDTO();
        Assert.notNull(withdrawalDTO, "提现信息不能为空");
        Assert.notNull(withdrawalDTO.getChannelId(), "渠道id不能为空");

        BigDecimal settleAmount = BigDecimal.ZERO;
        for (MemberSeedRecord item : memberSeedRecordList) {
            Assert.isTrue(item.getChannelId().equals(withdrawalDTO.getChannelId()), "会员种草记录渠道Id不一致");
            settleAmount = settleAmount.add(item.getSettleAmount());
        }
        Assert.isTrue(settleAmount.compareTo(BigDecimal.ZERO) > 0, "提现金额不能为0");
        Assert.isTrue(settleAmount.compareTo(withdrawalDTO.getSettleAmount()) == 0, "数据已刷新，请刷新页面~");

        MemberSeedRecord memberSeedRecord = memberSeedRecordList.get(0);
        //填充数据
        MemberSeedRecordWithdrawal memberSeedRecordWithdrawal = new MemberSeedRecordWithdrawal();
        memberSeedRecordWithdrawal.setWithdrawalNum(initWithdrawalNum(withdrawalDTO.getChannelId()));
        memberSeedRecordWithdrawal.setChannelId(memberSeedRecord.getChannelId());
        memberSeedRecordWithdrawal.setChannelSeedId(memberSeedRecord.getChannelSeedId());
        memberSeedRecordWithdrawal.setChannelSeedCode(memberSeedRecord.getSeedCode());
        memberSeedRecordWithdrawal.setChannelType(memberSeedRecord.getChannelType());
        memberSeedRecordWithdrawal.setSettleAmount(settleAmount);
        memberSeedRecordWithdrawal.setWithdrawalAccountType(withdrawalDTO.getWithdrawalAccountType());
        memberSeedRecordWithdrawal.setPayeeName(withdrawalDTO.getPayeeName());
        memberSeedRecordWithdrawal.setPayeePhone(withdrawalDTO.getPayeePhone());
        memberSeedRecordWithdrawal.setPayeeIdentityCard(withdrawalDTO.getPayeeIdentityCard());
        memberSeedRecordWithdrawal.setPayeeAccount(withdrawalDTO.getPayeeAccount());
        memberSeedRecordWithdrawal.setBankName(withdrawalDTO.getBankName());
        baseMapper.insert(memberSeedRecordWithdrawal);

        //添加关联关系
        List<MemberSeedRecordRelevance> memberSeedRecordRelevanceList = new ArrayList<>();
        List<MemberSeedRecord> updateList = new ArrayList<>();
        for (MemberSeedRecord item : memberSeedRecordList) {
            MemberSeedRecordRelevance memberSeedRecordRelevance = new MemberSeedRecordRelevance();
            memberSeedRecordRelevance.setMemberSeedRecordId(item.getId());
            memberSeedRecordRelevance.setMemberSeedRecordWithdrawalId(memberSeedRecordWithdrawal.getId());
            memberSeedRecordRelevanceList.add(memberSeedRecordRelevance);

            MemberSeedRecord updateEntity = new MemberSeedRecord();
            updateEntity.setId(item.getId());
            updateEntity.setStatus(MemberSeedRecordStatusEnum.UNDER_REVIEW.getCode());
            updateList.add(updateEntity);
        }
        memberSeedRecordRelevanceService.saveBatch(memberSeedRecordRelevanceList);

        //同步更新 会员种草记录状态
        memberSeedRecordMapper.updateBatchById(updateList);

        //初始化提现单号
        return memberSeedRecordWithdrawal;
    }

    @Override
    public String initWithdrawalNum(Long channelId) {
        Long reentrancyCount = 5L;
        Long expireTime = 10L;
        String key = CacheConstants.MEMBER_SEED_RECORD_WITHDRAWAL_NUM_KEY + channelId;
        if (redisService.setIncr(key, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String withdrawalNum = BusinessConstants.MEMBER_SEED_RECORD_WITHDRAWAL_NUM_FXTX + RandomUtil.randomString(RandomUtil.BASE_NUMBER, 8);


        Long count = baseMapper.selectCount(new LambdaQueryWrapper<MemberSeedRecordWithdrawal>()
                .eq(MemberSeedRecordWithdrawal::getWithdrawalNum, withdrawalNum));
        if (count.compareTo(0L) > 0) {
            return initWithdrawalNum(channelId);
        }
        return withdrawalNum;
    }

    @Override
    public FissionCountStatisticsVO getFissionCountStatisticsVO() {
        FissionCountStatisticsVO fissionCountStatisticsVO = baseMapper.getFissionCountStatisticsVO();
        if (ObjectUtil.isNull(fissionCountStatisticsVO)) {
            fissionCountStatisticsVO = new FissionCountStatisticsVO();
            fissionCountStatisticsVO.setUnderReviewCount(0);
            fissionCountStatisticsVO.setPendingTransferCount(0);
            fissionCountStatisticsVO.setWithdrawSuccessCount(0);
            fissionCountStatisticsVO.setReviewRejectedCount(0);
            fissionCountStatisticsVO.setTransferExceptionCount(0);
        } else {
            fissionCountStatisticsVO.setUnderReviewCount(Optional.ofNullable(fissionCountStatisticsVO.getUnderReviewCount()).orElse(0));
            fissionCountStatisticsVO.setPendingTransferCount(Optional.ofNullable(fissionCountStatisticsVO.getPendingTransferCount()).orElse(0));
            fissionCountStatisticsVO.setWithdrawSuccessCount(Optional.ofNullable(fissionCountStatisticsVO.getWithdrawSuccessCount()).orElse(0));
            fissionCountStatisticsVO.setReviewRejectedCount(Optional.ofNullable(fissionCountStatisticsVO.getReviewRejectedCount()).orElse(0));
            fissionCountStatisticsVO.setTransferExceptionCount(Optional.ofNullable(fissionCountStatisticsVO.getTransferExceptionCount()).orElse(0));
        }
        return fissionCountStatisticsVO;
    }
}




