package com.wnkx.biz.business.service.core.member;

import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.MemberStatusDto;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.business.service.core.BaseMemberStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 即将过期
 * @create :2024-06-21 17:40
 **/
@Component
@RequiredArgsConstructor
public class UnExpireStatus extends BaseMemberStatus {

    private final IBusinessService businessService;
    private final IBusinessAccountService businessAccountService;

    @Override
    public void recharge(MemberStatusDto memberStatusDto) {
        if (StringUtils.isNull(memberStatusDto.getMemberValidity())) {
            throw new ServiceException("[即将过期]会员状态下，会员有效期不能为空");
        }
        BusinessDTO businessDTO = super.getBusinessDto(memberStatusDto);
        businessDTO.setMemberValidity(getResultDate(memberStatusDto.getMemberValidity(), memberStatusDto.getMonthNum(), memberStatusDto.getPresentedTime(), memberStatusDto.getPresentedTimeType()));
        businessService.update(businessDTO);
    }


    @Override
    public void preExpire(MemberStatusDto business) {
        throw new ServiceException("即将过期不需要再次修改为：[即将过期]状态");
    }

    @Override
    public void expire(MemberStatusDto memberStatusDto) {
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setId(memberStatusDto.getBusinessId());
        businessDTO.setMemberStatus(MemberTypeEnum.EXPIRE.getCode());
        businessDTO.setMemberType(StatusTypeEnum.NO.getCode());
        businessService.update(businessDTO);
    }
}
