package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailLockInfoDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_balance_detail_lock(商家余额详情锁定表)】的数据库操作Service
* @createDate 2024-12-27 14:40:22
*/
public interface IBusinessBalanceDetailLockService extends IService<BusinessBalanceDetailLock> {


    /**
     * 根据 单号取消订单锁定数据
     * @param number                提现单号
     * @param status                状态
     * @param businessFlowId        business_balance_flow.id
     */
    void flow(String number, Integer status, Long businessFlowId);

    /**
     * 根据锁定单号获取商家余额锁定数据
     * @param numbers
     * @return
     */
    List<BusinessBalanceDetailLock> queryListByNumberList(List<String> numbers);

    /**
     * 获取有效提现数据
     * @param dto
     * @return
     */
    List<BusinessBalanceDetailLockInfoVO> queryValidList(BusinessBalanceDetailLockInfoDTO dto);

    /**
     * 获取视频订单提现记录
     */
    List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto);
}
