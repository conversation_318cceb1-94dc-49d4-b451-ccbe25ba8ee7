package com.wnkx.biz.business.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:27
 */
@Mapper
public interface BusinessCallbackEventMapper extends SuperMapper<BusinessCallbackEvent> {


    /**
     * 根据回访ID查询回访事件
     */
    default List<BusinessCallbackEvent> selectListByCallbackIds(Collection<Long> callbackIds) {
        return selectList(new LambdaQueryWrapper<BusinessCallbackEvent>()
                .in(CollUtil.isNotEmpty(callbackIds), BusinessCallbackEvent::getCallbackId, callbackIds)
        );
    }

    /**
     * 根据回访ID删除回访事件
     */
    default void deleteByCallbackIds(List<Long> deleteCallbackIds) {
        delete(new LambdaQueryWrapper<BusinessCallbackEvent>()
                .in(BusinessCallbackEvent::getCallbackId, deleteCallbackIds)
        );
    }
}
