package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel;

import java.util.Collection;
import java.util.List;

/**
 * 商家收藏模特Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-27
 */
public interface IBusinessAccountCollectModelService extends IService<BusinessAccountCollectModel>
{
    /**
     * 查询商家收藏模特
     * 
     * @param id 商家收藏模特主键
     * @return 商家收藏模特
     */
    public BusinessAccountCollectModel selectMerchantCollectById(Long id);

    /**
     * 查询商家收藏模特列表
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 商家收藏模特集合
     */
    public List<BusinessAccountCollectModel> selectMerchantCollectList(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 新增商家收藏模特
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    public int insertMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 修改商家收藏模特
     * 
     * @param businessAccountCollectModel 商家收藏模特
     * @return 结果
     */
    public int updateMerchantCollect(BusinessAccountCollectModel businessAccountCollectModel);

    /**
     * 批量删除商家收藏模特
     * 
     * @param ids 需要删除的商家收藏模特主键集合
     * @return 结果
     */
    public int deleteMerchantCollectByIds(Long[] ids);

    /**
     * 删除商家收藏模特信息
     * 
     * @param id 商家收藏模特主键
     * @return 结果
     */
    public int deleteMerchantCollectById(Long id);

    /**
     * 获取模特被收藏次数
     */
    public List<BusinessAccountCollectModel> getModelCollectCount(Collection<Long> modelIds);

    /**
     * 清楚收藏登录账号收藏模特
     * @param bizUserId
     * @param modelId
     */
    void clearUserModel(Long  bizUserId,Long modelId);
}
