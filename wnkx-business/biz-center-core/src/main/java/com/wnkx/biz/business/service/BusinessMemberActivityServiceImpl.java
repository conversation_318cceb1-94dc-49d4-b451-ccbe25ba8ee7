package com.wnkx.biz.business.service;
import java.util.Collections;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.activity.BusinessMemberActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.business.activity.UpdateMemberActivityDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.wnkx.biz.business.mapper.BusinessMemberActivityMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_member_activity(商家会员活动)】的数据库操作Service实现
 * @createDate 2025-01-16 18:04:14
 */
@Service
public class BusinessMemberActivityServiceImpl extends ServiceImpl<BusinessMemberActivityMapper, BusinessMemberActivity>
        implements IBusinessMemberActivityService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessMemberActivity saveMemberActivity(BusinessMemberActivityDTO dto) {
        checkMemberActivity(dto);
        BusinessMemberActivity businessMemberActivity = BeanUtil.copyProperties(dto, BusinessMemberActivity.class);
        businessMemberActivity.setCreateById(SecurityUtils.getUserId());
        businessMemberActivity.setCreateBy(SecurityUtils.getUsername());
        businessMemberActivity.setUpdateById(SecurityUtils.getUserId());
        businessMemberActivity.setUpdateBy(SecurityUtils.getUsername());
        baseMapper.insert(businessMemberActivity);
        return businessMemberActivity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberActivity(BusinessMemberActivityDTO dto) {
        checkMemberActivity(dto);
        BusinessMemberActivity businessMemberActivity = BeanUtil.copyProperties(dto, BusinessMemberActivity.class);
        businessMemberActivity.setUpdateById(SecurityUtils.getUserId());
        businessMemberActivity.setUpdateBy(SecurityUtils.getUsername());
        baseMapper.updateById(businessMemberActivity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(UpdateMemberActivityDTO dto) {
        baseMapper.updateStatus(dto);
    }

    @Override
    public List<BusinessMemberActivity> queryList() {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return this.list();
    }

    @Override
    public List<BusinessMemberActivity> getTodayActivity() {
        List<BusinessMemberActivity> list = baseMapper.getActivityListByDate(DateUtils.getDaysOfMonth(new Date()));
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    private void checkMemberActivity(BusinessMemberActivityDTO dto) {
        Assert.isTrue(dto.getEndTime().compareTo(dto.getStartTime()) >= 0, "活动结束时间不能小于活动开始时间");
        List<BusinessMemberActivity> crossTimeList = baseMapper.getCrossTimeList(dto);
        if (CollUtil.isNotEmpty(crossTimeList)){
            throw new ServiceException("当前类型会员在每月" + crossTimeList.get(0).getStartTime() + "-" + crossTimeList.get(0).getEndTime() + "号已存在活动，同一时间同类型会员活动无法重复~");
        }
    }
}




