package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessBalanceFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceFlowDetailListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailFlowExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_flow(余额流水表)】的数据库操作Service
 * @createDate 2024-06-27 17:12:52
 */
@Validated
public interface IBusinessBalanceFlowService extends IService<BusinessBalanceFlow> {

    /**
     * 获取余额明细列表
     * @param dto
     * @return
     */
    List<BusinessBalanceFlow> businessBalanceFlowList(BusinessBalanceFlowListDTO dto);

    /**
     * entity转VO
     * @param list
     * @return
     */
    List<BusinessBalanceFlowVO> transitionVo(List<BusinessBalanceFlow> list);

    /**
     * 获取余额明细列表
     *
     * @param dto
     * @return
     */
    List<BusinessBalanceFlow> queryList(BusinessBalanceFlowDTO dto);

    /**
     * 添加余额流水
     *
     * @param dto
     */
    void addBusinessBalanceFlow(@Valid BusinessBalanceFlowDTO dto);


    /**
     * * 获取余额流水统计
     * @param businessIds
     * @return
     */
    List<BusinessBalanceFlowStatisticsVO> statistics(List<Long> businessIds);

    /**
     * 全部余额明细数据
     * @return
     */
    List<BusinessBalanceDetailFlowExportVO> getBusinessBalanceDetailFlowExports(BusinessBalanceFlowDetailListDTO dto);
}
