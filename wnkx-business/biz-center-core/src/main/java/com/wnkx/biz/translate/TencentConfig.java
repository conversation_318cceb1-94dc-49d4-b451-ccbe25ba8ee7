package com.wnkx.biz.translate;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.sms.v20190711.SmsClient;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯tmtClient
 *
 * <AUTHOR>
 * @date 2024/6/11
 */

@Configuration
public class TencentConfig {

    @Value("${tencent.sms.ak:''}")
    private String ak;
    @Value("${tencent.sms.sk:''}")
    private String sk;
    @Value("${tencent.region:ap-guangzhou}")
    private String region;


    @Bean
    TmtClient tmtClient() {
        return new TmtClient(new Credential(ak, sk), region);
    }
    @Bean
    SmsClient smsClient() {
        return new SmsClient(new Credential(ak, sk), region);
    }
}
