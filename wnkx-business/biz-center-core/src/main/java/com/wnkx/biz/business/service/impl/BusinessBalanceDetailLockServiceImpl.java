package com.wnkx.biz.business.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.BalanceSourceTypeEnum;
import com.ruoyi.common.core.enums.BalanceType;
import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailLockInfoDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawalSuccessDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.wnkx.biz.business.mapper.BusinessBalanceDetailLockMapper;
import com.wnkx.biz.business.service.IBusinessBalanceAuditFlowService;
import com.wnkx.biz.business.service.IBusinessBalanceDetailFlowService;
import com.wnkx.biz.business.service.IBusinessBalanceDetailLockService;
import com.wnkx.biz.business.service.IBusinessBalanceDetailService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail_lock(商家余额详情锁定表)】的数据库操作Service实现
 * @createDate 2024-12-27 14:40:22
 */
@Service
@RequiredArgsConstructor
public class BusinessBalanceDetailLockServiceImpl extends ServiceImpl<BusinessBalanceDetailLockMapper, BusinessBalanceDetailLock>
        implements IBusinessBalanceDetailLockService {
    private final IBusinessBalanceDetailService businessBalanceDetailService;
    private final IBusinessBalanceDetailFlowService businessBalanceDetailFlowService;
    private final RemoteService remoteService;

    /**
     * 获取视频订单提现记录
     */
    @Override
    public List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto) {
        return baseMapper.withdrawDepositRecord(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void flow(String number, Integer status, Long businessFlowId) {
        Assert.isFalse(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(status), "审核状态需要时非待处理");
        baseMapper.setStatus(number, status);
        List<BusinessBalanceDetailLock> businessBalanceDetailLockList = baseMapper.queryListByNumber(number);
        Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetailLockList), "流转提现审核错误：提现锁定数据为空~");
        List<BusinessBalanceDetail> businessBalanceDetails = businessBalanceDetailService.listByIds(businessBalanceDetailLockList.stream().map(BusinessBalanceDetailLock::getBalanceDetailId).collect(Collectors.toList()));
        Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetails), "流转提现审核错误：余额详情数据为空~");
        Map<Long, BusinessBalanceDetail> balanceDetailMap = businessBalanceDetails.stream().collect(Collectors.toMap(BusinessBalanceDetail::getId, p -> p));

        List<BusinessBalanceDetail> updateBatch = new ArrayList<>();
        List<BusinessBalanceDetailFlow> businessBalanceDetailFlowList = new ArrayList<>();
        for (BusinessBalanceDetailLock item : businessBalanceDetailLockList) {
            BusinessBalanceDetail update = new BusinessBalanceDetail();
            update.setId(item.getBalanceDetailId());
            BusinessBalanceDetail businessBalanceDetail = balanceDetailMap.get(item.getBalanceDetailId());
            Assert.notNull(businessBalanceDetail, "提现锁定数据与余额详情有误~");

            update.setLockBalance(businessBalanceDetail.getLockBalance().subtract(item.getPayOutAmount()));
            if (BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(status)) {
                update.setValidBalance(businessBalanceDetail.getValidBalance().add(item.getPayOutAmount()));
            } else if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(status)) {
                update.setUseBalance(businessBalanceDetail.getUseBalance().add(item.getPayOutAmount()));

                //添加流水数据
                Assert.notNull(businessFlowId, "余额流水数据不能为空~");
                BusinessBalanceDetailFlow businessBalanceDetailFlow = new BusinessBalanceDetailFlow();
                businessBalanceDetailFlow.setBalanceFlowId(businessFlowId);
                businessBalanceDetailFlow.setBalanceNumber(businessBalanceDetail.getNumber());
                businessBalanceDetailFlow.setNumber(number);
                businessBalanceDetailFlow.setVideoId(null);
                businessBalanceDetailFlow.setVideoCode(null);
                businessBalanceDetailFlow.setUseBalance(item.getPayOutAmount().negate());
                businessBalanceDetailFlow.setType(BalanceType.SPEND.getCode());
                businessBalanceDetailFlow.setCreateById(SecurityUtils.getUserId());
                businessBalanceDetailFlow.setCreateBy(SecurityUtils.getUsername());
                businessBalanceDetailFlowList.add(businessBalanceDetailFlow);
            }
            updateBatch.add(update);
        }

        if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(status)) {
            // 视频订单提现成功后 对发票的处理
            List<BusinessBalanceDetailLock> businessBalanceDetailLocks = businessBalanceDetailLockList.stream().filter(businessBalanceDetailLock -> !BalanceSourceTypeEnum.PREPAY_INCOME.getCode().equals(businessBalanceDetailLock.getOrigin())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(businessBalanceDetailLocks)) {
                BusinessBalanceAuditFlow businessBalanceAuditFlow = SpringUtils.getBean(IBusinessBalanceAuditFlowService.class)
                        .lambdaQuery()
                        .eq(BusinessBalanceAuditFlow::getWithdrawNumber, businessBalanceDetailLocks.get(0).getNumber())
                        .one();

                List<WithdrawalSuccessDTO> withdrawalSuccessDTOS = businessBalanceDetailLocks.stream().map(businessBalanceDetailLock -> {
                    WithdrawalSuccessDTO withdrawalSuccessDTO = new WithdrawalSuccessDTO();
                    withdrawalSuccessDTO.setVideoCode(businessBalanceDetailLock.getVideoCode());
                    withdrawalSuccessDTO.setPrepayNum(businessBalanceDetailLock.getPrepayNum());
                    withdrawalSuccessDTO.setRefundType(businessBalanceDetailLock.getOrigin());
                    withdrawalSuccessDTO.setWithdrawDepositTime(businessBalanceAuditFlow.getAuditTime());
                    withdrawalSuccessDTO.setWithdrawDepositAmount(businessBalanceDetailLock.getPayOutAmount());

                    return withdrawalSuccessDTO;
                }).collect(Collectors.toList());

                remoteService.withdrawalSuccess(withdrawalSuccessDTOS);
            }
        }

        businessBalanceDetailService.updateBatchById(updateBatch);
        if (CollUtil.isNotEmpty(businessBalanceDetailFlowList)) {
            businessBalanceDetailFlowService.saveBatch(businessBalanceDetailFlowList);
        }
    }

    @Override
    public List<BusinessBalanceDetailLock> queryListByNumberList(List<String> numbers) {
        return baseMapper.queryListByNumbers(numbers);
    }

    @Override
    public List<BusinessBalanceDetailLockInfoVO> queryValidList(BusinessBalanceDetailLockInfoDTO dto) {
        return baseMapper.queryValidList(dto);
    }
}




