package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.PrepayUnPayVO;
import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_balance_prepay(商家预付表)】的数据库操作Service
* @createDate 2024-11-04 14:32:59
*/
public interface IBusinessBalancePrepayService extends IService<BusinessBalancePrepay> {

    /**
     * 添加商家余额预付数据
     * @param businessBalancePrepay
     * @return
     */
    BusinessBalancePrepay insertBusinessBalancePrepay(BusinessBalancePrepayDTO businessBalancePrepay);

    /**
     * 预付款审批列表
     * @param dto
     * @return
     */
    List<BusinessBalancePrepayVO> queryList(BusinessBalancePrepayListDTO dto);

    /**
     * 湖区预付款审批列表
     * @param dto
     * @return
     */
    List<BusinessBalancePrepayVO> innerQueryList(BusinessBalancePrepayListDTO dto);

    /**
     * 预付款导出列表
     * @param dto
     * @return
     */
    List<BusinessBalancePrepayExportVO> exportBusinessBalancePrepayList(BusinessBalancePrepayListDTO dto);

    /**
     * 加载对应数据
     * @param list
     * @return
     */
    void load(List<BusinessBalancePrepayVO> list);

    /**
     * 审核商家预付数据
     * @param dto
     * @return
     */
    BusinessBalancePrepay auditBusinessBalancePrepay(AuditBusinessBalancePrepayDTO dto);

    /**
     * 获取预付详情
     * @param id
     * @return
     */
    BusinessBalancePrepayDetailVO getDetailById(Long id);

    /**
     * 获取统计数据
     * @return
     */
    BusinessBalancePrepayStatisticsVO getStatistics();


    /**
     * 初始化预付款
     * @param businessBalancePrepay
     * @return
     */
    BusinessBalancePrepay initBusinessBalancePrepay(OnlineBusinessBalancePrepayDTO businessBalancePrepay);


    /**
     * 获取钱包充值记录
     * @return
     */
    List<BusinessBalancePrepay> onlineBusinessBalancePrepay();

    /**
     * 提交凭证
     * @param dto
     */
    void submitCredential(OnlineRechargeSubmitCredentialDTO dto);


    /**
     * 获取商家未支付钱包充值数据
     * @param businessId
     */
    PrepayUnPayVO getOnlineUnPay(Long businessId);

    /**
     * 根据预付单号获取预付单数据
     * @param preNum
     * @return
     */
    BusinessBalancePrepay getByPreNum(String preNum);

    /**
     * 取消线上钱包充值订单
     * @param id
     */
    void cancelOnlineOrder(Long id);

    /**
     * 修改预付款appId
     * @param dto
     */
    void updateAppId(PrepayUpdateAppIdDTO dto);


    /**
     * 支付成功更新状态
     * @param dto
     * @return
     */
    BusinessBalancePrepay updateOrderPayStatus(PrepayUpdatePayStatusDTO dto);

    /**
     * 获取所有需关闭订单
     * @param beginTime
     * @param endTime
     * @return
     */
    List<BusinessBalancePrepay> getCloseOnlineOrders(Date beginTime, Date endTime);

    /**
     * 获取当前汇率
     * @return
     */
    RealTimeExchangeRateVO getCurrentExchange();

    /**
     * 重置数据
     * @param orderNums
     */
    void updateBatchFieldNullToNull(Collection<String> orderNums);
}
