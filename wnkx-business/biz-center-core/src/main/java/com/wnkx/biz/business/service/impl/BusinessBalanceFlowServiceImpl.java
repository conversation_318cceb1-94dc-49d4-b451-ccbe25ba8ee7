package com.wnkx.biz.business.service.impl;

import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessBalanceFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceFlowDetailListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailFlowExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO;
import com.wnkx.biz.business.mapper.BusinessBalanceFlowMapper;
import com.wnkx.biz.business.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_flow(余额流水表)】的数据库操作Service实现
 * @createDate 2024-06-27 17:12:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessBalanceFlowServiceImpl extends ServiceImpl<BusinessBalanceFlowMapper, BusinessBalanceFlow>
        implements IBusinessBalanceFlowService {

    private final IBusinessBalanceDetailService businessBalanceDetailService;
    private final IBusinessBalanceDetailFlowService businessBalanceDetailFlowService;
    private final IBusinessBalanceDetailLockService businessBalanceDetailLockService;
    private final RedisService redisService;

    @Override
    public List<BusinessBalanceFlow> businessBalanceFlowList(BusinessBalanceFlowListDTO dto) {
        if (UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType())) {
            dto.setOwnerAccount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessVO().getOwnerAccount());
        }
        BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
        businessAccountDetailDTO.setAccount(dto.getOwnerAccount());
        businessAccountDetailDTO.setMemberCode(dto.getMemberCode());
        businessAccountDetailDTO.setBusinessId(dto.getBusinessId());
        businessAccountDetailDTO.setMemberStatusList(List.of(MemberTypeEnum.RECHARGE.getCode(), MemberTypeEnum.NO_EXPIRE.getCode(), MemberTypeEnum.EXPIRE.getCode()));
        List<BusinessAccountDetailVO> businessAccountDetailVOS = SpringUtils.getBean(IBusinessAccountService.class).getBusinessAccountDetailVOs(businessAccountDetailDTO);
        if (StringUtils.isEmpty(businessAccountDetailVOS)) {
            return new ArrayList<>();
        }
        Set<Long> businessIds = businessAccountDetailVOS.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toSet());

        BusinessBalanceFlowDTO businessBalanceFlowDTO = BeanUtil.copyProperties(dto, BusinessBalanceFlowDTO.class);
        businessBalanceFlowDTO.setBusinessIds(businessIds);
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbf.create_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bbf.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessBalanceFlow> businessBalanceFlows = baseMapper.queryList(businessBalanceFlowDTO);
        if (StringUtils.isEmpty(businessBalanceFlows)) {
            return new ArrayList<>();
        }
        return businessBalanceFlows;
    }

    @Override
    public List<BusinessBalanceFlowVO> transitionVo(List<BusinessBalanceFlow> list) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<BusinessBalanceFlowVO> businessBalanceFlowVOS = BeanUtil.copyToList(list, BusinessBalanceFlowVO.class);
        List<Long> ids = list.stream().map(BusinessBalanceFlow::getId).collect(Collectors.toList());
        List<BusinessBalanceDetailFlow> businessBalanceDetailFlows = businessBalanceDetailFlowService.getListByBalanceFlowIds(ids);
        if (CollUtil.isEmpty(businessBalanceDetailFlows)) {
            return businessBalanceFlowVOS;
        }
        Map<Long, List<BusinessBalanceDetailFlow>> detailFlowMap = businessBalanceDetailFlows.stream().collect(Collectors.groupingBy(BusinessBalanceDetailFlow::getBalanceFlowId));
        for (BusinessBalanceFlowVO item : businessBalanceFlowVOS) {
            List<BusinessBalanceDetailFlow> businessBalanceDetailFlowList = detailFlowMap.get(item.getId());
            if (CollUtil.isEmpty(businessBalanceDetailFlowList)) {
                continue;
            }
            item.setNumberList(businessBalanceDetailFlowList.stream().map(BusinessBalanceDetailFlow::getBalanceNumber).distinct().collect(Collectors.toList()));
        }

        return businessBalanceFlowVOS;
    }

    @Override
    public List<BusinessBalanceFlow> queryList(BusinessBalanceFlowDTO dto) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotNull(dto.getBusinessId()), BusinessBalanceFlow::getBusinessId, dto.getBusinessId())
                .like(StringUtils.isNotNull(dto.getOrderNum()), BusinessBalanceFlow::getOrderNum, dto.getOrderNum())
                .like(StringUtils.isNotNull(dto.getRefundNum()), BusinessBalanceFlow::getRefundNum, dto.getRefundNum())
                .like(StringUtils.isNotNull(dto.getVideoCode()), BusinessBalanceFlow::getVideoCode, dto.getVideoCode())
                .in(StringUtils.isNotEmpty(dto.getBusinessIds()), BusinessBalanceFlow::getBusinessId, dto.getBusinessIds())
                .in(StringUtils.isNotEmpty(dto.getOrigins()), BusinessBalanceFlow::getOrigin, dto.getOrigins())
                .between(StringUtils.isNotNull(dto.getOrderTimeBegin()) && StringUtils.isNotNull(dto.getOrderTimeEnd()), BusinessBalanceFlow::getOrderTime, dto.getOrderTimeBegin(), dto.getOrderTimeEnd())
                .eq(StringUtils.isNotNull(dto.getOrigin()), BusinessBalanceFlow::getOrigin, dto.getOrigin())
                .eq(StringUtils.isNotNull(dto.getType()), BusinessBalanceFlow::getType, dto.getType())
                .list();
    }
    public String getFlowOrderNo(Long businessId){
        try {
            Long reentrancyCount = 5L;
            Long expireTime = 10L;
            String flowOrderNo = IdUtils.createFlowOrderNo();
            if (redisService.setIncr(CacheConstants.BUSINESS_BALANCE_FLOW_NO_KEY + businessId, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
                throw new ServiceException("系统生成随机数失败，请稍后重试！");
            }
            Long count = baseMapper.selectCount(new LambdaQueryWrapper<BusinessBalanceFlow>()
                            .eq(BusinessBalanceFlow::getFlowOrderNo, flowOrderNo));
            if (count.compareTo(0L) > 0) {
                return getFlowOrderNo(businessId);
            }
            return flowOrderNo;
        } finally {
            redisService.deleteObject(CacheConstants.BUSINESS_BALANCE_FLOW_NO_KEY + businessId);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBusinessBalanceFlow(BusinessBalanceFlowDTO dto) {
        BalanceSourceTypeEnum flowOrigin = BalanceSourceTypeEnum.getByCode(dto.getOrigin());
        if (dto.getAmount().compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException("余额流水使用余额数据不能为0~");
        }
        if (List.of(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME, BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME, BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME, BalanceSourceTypeEnum.ORDER_SPEND).contains(flowOrigin)) {
            Assert.isTrue(CollUtil.isNotEmpty(dto.getBusinessBalanceDetailFlowList()), "商家详情列表不能为空~");
            for (BusinessBalanceDetailFlowDTO item : dto.getBusinessBalanceDetailFlowList()) {
                Assert.notBlank(item.getNumber(), "单号不能为空~");
                Assert.notBlank(item.getVideoCode(), "视频编码不能为空~");
                Assert.notNull(item.getVideoId(), "视频ID不能为空~");
                Assert.notNull(item.getUseBalance(), "使用余额不能为空~");
                Assert.notNull(item.getType(), "订单类型不能为空~");
            }
        }
        if (List.of(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME, BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME, BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME).contains(flowOrigin)){
            for (BusinessBalanceDetailFlowDTO item : dto.getBusinessBalanceDetailFlowList()) {
                Assert.notNull(item.getAmount(), "视频金额不能为空~");
                Assert.notNull(item.getPayAmount(), "视频实付金额不能为空~");
                Assert.notNull(item.getDifferenceAmount(), "视频差额不能为空~");
            }
        }

        BusinessBalanceFlow businessBalanceFlow = BeanUtil.copyProperties(dto, BusinessBalanceFlow.class);
        businessBalanceFlow.setFlowOrderNo(getFlowOrderNo(dto.getBusinessId()));
        if (StringUtils.isNotNull(dto.getLoginBase())) {
            businessBalanceFlow.setCreateUserId(dto.getLoginBase().getUserid());
            businessBalanceFlow.setCreateUserName(dto.getLoginBase().getUsername());

            businessBalanceFlow.setUpdateUserId(dto.getLoginBase().getUserid());
            businessBalanceFlow.setUpdateUserName(dto.getLoginBase().getUsername());
        }

        baseMapper.insert(businessBalanceFlow);

        //处理余额详情、余额流水
        if (List.of(BalanceSourceTypeEnum.PREPAY_INCOME, BalanceSourceTypeEnum.ONLINE_RECHARGE).contains(flowOrigin)) {
            //预付款直接初始化
            initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.PREPAY, dto.getAmount(), dto.getPrepayNum());
        } else if (List.of(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME, BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME, BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME).contains(flowOrigin)) {
            Assert.isTrue(dto.getBusinessBalanceDetailFlowList().size() == 1, "视频退款商家详情列表只能有一条数据");
            List<BusinessBalanceDetailFlow> validBalanceByVideoCodes = businessBalanceDetailFlowService.getListByVideoCode(dto.getVideoCode());
            BusinessBalanceDetailFlowDTO businessBalanceDetailFlowDTO = dto.getBusinessBalanceDetailFlowList().get(0);
            BigDecimal realAmount = businessBalanceDetailFlowDTO.getPayAmount().subtract(businessBalanceDetailFlowDTO.getDifferenceAmount());

            //获取 使用现金支付的金额
            BigDecimal userCase = realAmount.subtract(businessBalanceDetailFlowDTO.getUseBalance());

            //退款统计
            BigDecimal incomeTotal = BigDecimal.ZERO;
            //支付统计
            BigDecimal spendTotal = BigDecimal.ZERO;
            //获取收入信息
            if (CollUtil.isNotEmpty(validBalanceByVideoCodes)) {
                for (BusinessBalanceDetailFlow item : validBalanceByVideoCodes) {
                    if (BalanceType.INCOME.getCode().equals(item.getType())) {
                        incomeTotal = incomeTotal.add(item.getUseBalance());
                    } else {
                        spendTotal = spendTotal.add(item.getUseBalance());
                    }
                }

            }

            if (spendTotal.compareTo(BigDecimal.ZERO) == 0) {
                //未使用余额支付 直接初始化
                Assert.isTrue(businessBalanceDetailFlowDTO.getUseBalance().compareTo(BigDecimal.ZERO) == 0, "暂时无法使用余额支付~");
                //本视频订单未使用余额数据（纯现金支付）  直接初始化
                initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.VIDEO, dto.getAmount(), dto.getRefundNum());
            } else {
                /**
                 * 视频退款：
                 *      订单详情流使用金额==0：未使用余额支付
                 *              使用余额 == 0   直接初始化：订单详情、订单详情流水
                 *      订单详情流水有数据：使用余额
                 *              退款数据（收入）：
                 *                   退款数据统计 >= 视频订单金额：已退款完毕 超出
                 *                         订单类型 = 收入：sum(BusinessBalanceDetailFlow.use_balance) >= 视频订单金额
                 *                         1.已退款完毕：  直接初始化：订单详情、订单详情流水
                 *                   退款数据统计 < 视频订单金额：
                 *                          有使用现金支付：
                 *                                  现金支付未退款完毕：
                 *                                          本次退款金额 + 退款统计 >= 订单金额   直接初始化 参数   本次退款金额 - 使用余额（订单金额 -现金）
                 *                                          本次退款金额 + 退款统计 < 订单金额   直接初始化  参数   dto.getAmount().min(userCase.subtract(incomeTotal))
                 *                                  现金支付退款完毕、本次退款金额 + 已退金额 > 订单金额：初始化 参数     本次退款 - (订单金额 - 已退金额)
                 *
                 *                          3.视频订单使用现金 - 退款数据统计 >= 退款金额 ：无需处理
                 *                          4.视频订单使用现金 - 退款数据统计 < 退款金额 ：本次退款超过退款现金退款范围
                 *                                      4.1：退款金额 - （视频订单使用现金 - 退款数据统计）<= 使用余额：使用余额足够退款
                 *                                                  退款金额 - （视频订单使用现金 - 退款数据统计）：作为参数 退回订单详情
                 *                                      4.2：退款金额 - （视频订单使用现金 - 退款数据统计）> 使用余额：使用余额不够退款
                 *                                                  使用余额：作为参数 退回订单详情
                 *                                                  退款金额 - （视频订单使用现金 - 退款数据统计） - 使用余额：作为参数  初始化：订单详情、订单详情流水
                 *                                                  .filter(item-> item.compareTo(BigDecimal.ZERO) < 0).reduce(BigDecimal.ZERO, BigDecimal::add);*
                 */

                if (incomeTotal.compareTo(realAmount) >= 0) {
                    //已退款完毕  后续超出部分都需要初始化
                    initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.VIDEO, dto.getAmount(), dto.getRefundNum());
                } else {
                    //使用余额支付
                    //还需退款
                    BigDecimal refundAmount = dto.getAmount();

                    if (userCase.compareTo(BigDecimal.ZERO) > 0 && userCase.subtract(incomeTotal).compareTo(BigDecimal.ZERO) > 0) {
                        //有现金退款 现金退款金额 - 已退金额 > 0 现金还需退款：现金退款金额 - 已退金额
                        if (incomeTotal.add(dto.getAmount()).compareTo(realAmount) >= 0) {
                            //已退金额 + 本次退款  >= 订单金额  退款金额超出订单金额   需退款  userCase.subtract(incomeTotal)(剩余现金退款) + incomeTotal.add(dto.getAmount()).subtract(realAmount)(超出订单金额)
                            //订单金额：300 使用现金：200 使用余额：100  已退 100 本次退款：400
                            //需退款 =   本次退款金额 - 余额 = 300
                            initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.VIDEO, dto.getAmount().subtract(businessBalanceDetailFlowDTO.getUseBalance()), dto.getRefundNum());
                            refundAmount = refundAmount.subtract(dto.getAmount().subtract(businessBalanceDetailFlowDTO.getUseBalance()));
                        } else {
                            //本次退款 + 已退金额 < 订单金额
                            initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.VIDEO, dto.getAmount().min(userCase.subtract(incomeTotal)), dto.getRefundNum());
                            if (userCase.subtract(incomeTotal).compareTo(dto.getAmount()) > 0) {
                                refundAmount = BigDecimal.ZERO;
                            } else {
                                refundAmount = refundAmount.subtract(userCase.subtract(incomeTotal));
                            }
                        }
                    } else if (incomeTotal.add(dto.getAmount()).compareTo(realAmount) > 0) {
                        //订单金额：300 使用现金：200 使用余额：100  已退 210 本次退款：400   初始化310 = 本次退款 - （订单金额 - 已退）
                        //未使用现金支付 已退金额 + 本次退款  > 订单金额：还需退款
                        BigDecimal initAmount = refundAmount.subtract(realAmount.subtract(incomeTotal));
                        initBusinessBalanceDetail(dto, flowOrigin, businessBalanceFlow.getId(), BalanceDetailTypeEnum.VIDEO, initAmount, dto.getRefundNum());
                        refundAmount = dto.getAmount().subtract(initAmount);
                    }

                    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        //视频订单使用现金 - 退款数据统计 < 退款金额 ：本次退款超过退款现金退款范围
                        //已退 视频订单使用现金 - 退款数据统计 还需退款：退款金额 - （视频订单使用现金 - 退款数据统计）
                        /**
                         * 视频编码下 每个订单详情金额
                         *   视频编码    余额单号   使用余额    类型      单号
                         *   AUTQ3      TK001     -100      支出      DDWN0002
                         *   AUTQ3      TK002     -200      支出      DDWN0002
                         *   AUTQ3      TK003     50        收入      TK000001 (未使用现金 直接退款TK001，如果使用现金 余额单号为新增数据（TK003）)
                         *   Map<String, BigDecimal> detailFlowMap：余额单号,使用余额统计
                         *      TK001，-100
                         *      TK002，-200
                         *      TK003，50
                         */
                        Map<String, BigDecimal> detailFlowMap = validBalanceByVideoCodes.stream().collect(Collectors.groupingBy(BusinessBalanceDetailFlow::getBalanceNumber, Collectors.
                                mapping(BusinessBalanceDetailFlow::getUseBalance,
                                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
                        //进入这里代表现金已退款完成 这里需要进行余额退款及后续操作
                        //余额可退金额  TK001，-100  + TK002，-200  = -300
                        BigDecimal canBackBalance = detailFlowMap.values().stream().filter(item -> item.compareTo(BigDecimal.ZERO) < 0).reduce(BigDecimal.ZERO, BigDecimal::add);
                        //需要新增的 余额流水数据
                        List<BusinessBalanceDetailFlow> saveBatchDetailFlow = new ArrayList<>();
                        //需要修改的余额详情数据
                        List<BusinessBalanceDetail> updateBusinessBalanceDetail = new ArrayList<>();

                        //获取余额详情数据
                        List<BusinessBalanceDetail> businessBalanceDetails = businessBalanceDetailService.getDetailListByNumbers(new ArrayList<>(detailFlowMap.keySet()));
                        Map<String, BusinessBalanceDetail> balanceDetailMap = businessBalanceDetails.stream().collect(Collectors.toMap(BusinessBalanceDetail::getNumber, p -> p));

                        /**
                         *  canBackBalance  可退金额       -300
                         *  refundAmount    本次退款        0-300
                         */
                        Assert.isTrue(canBackBalance.add(refundAmount).compareTo(BigDecimal.ZERO) <= 0, "暂时无法使用余额支付~");
                        /**
                         *  canBackBalance  可退金额       -400
                         *  refundAmount    本次退款        300
                         * 每个视频订单列表的 使用余额数据
                         * 视频编码      详情单号   剩余可退金额(residueAmount)
                         * AUTQ3         TK001     -100
                         * AUTQ3         TK002     -300
                         *  Map<详情单号, 可退余额>
                         */
                        for (String item : detailFlowMap.keySet()) {
                            //订单详情  剩余可退金额
                            BigDecimal residueAmount = detailFlowMap.get(item);
                            if (residueAmount.compareTo(BigDecimal.ZERO) >= 0) {
                                //退款完成（==0）、无退款数据（>0:退款新增数据）
                                continue;
                            }
                            if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
                                continue;
                            }
                            if (residueAmount.add(refundAmount).compareTo(BigDecimal.ZERO) > 0) {
                                //第一次：refundAmount（300） + residueAmount（-100） = 200
                                //需添加流水数据:residueAmount.negate() = 100
                                loadBusinessDetailAndFlow(dto, flowOrigin, businessBalanceFlow, residueAmount.negate(), saveBatchDetailFlow, updateBusinessBalanceDetail, balanceDetailMap, item, dto.getRefundNum());
                                refundAmount = refundAmount.add(residueAmount);
                            } else if (residueAmount.add(refundAmount).compareTo(BigDecimal.ZERO) <= 0) {
                                //第二次：refundAmount（200） + residueAmount（-300） = -100
                                loadBusinessDetailAndFlow(dto, flowOrigin, businessBalanceFlow, refundAmount, saveBatchDetailFlow, updateBusinessBalanceDetail, balanceDetailMap, item, dto.getRefundNum());
                                refundAmount = BigDecimal.ZERO;
                            }
                        }
                        businessBalanceDetailService.updateBatchById(updateBusinessBalanceDetail);
                        businessBalanceDetailFlowService.saveBatch(saveBatchDetailFlow);
                    }
                }
            }
        } else if (flowOrigin == BalanceSourceTypeEnum.ORDER_SPEND) {
            List<BusinessBalanceDetailFlowDTO> detailFlowDto = dto.getBusinessBalanceDetailFlowList();
            BigDecimal videoUseBalanceTotal = detailFlowDto.stream().map(BusinessBalanceDetailFlowDTO::getUseBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal surplusBalance = businessBalanceFlow.getAmount().abs();
            if (videoUseBalanceTotal.compareTo(surplusBalance) != 0) {
                throw new ServiceException("使用余额与视频使用余额不匹配~");
            }
            //获取有效余额详情
            List<BusinessBalanceDetail> validBusinessBalanceDetailList = businessBalanceDetailService.getValidBusinessBalanceDetailListByBusinessId(businessBalanceFlow.getBusinessId());
            if (CollUtil.isEmpty(validBusinessBalanceDetailList)) {
                throw new ServiceException("商家无余额详情数据,暂时无法使用余额支付~");
            }
            BigDecimal canBackBalance = validBusinessBalanceDetailList.stream().map(BusinessBalanceDetail::getValidBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

            if (canBackBalance.compareTo(surplusBalance) < 0) {
                throw new ServiceException("商家余额数据暂不可用~");
            }

            List<BusinessBalanceDetail> updateBusinessBalanceDetailList = new ArrayList<>();
            Map<String, BigDecimal> useBalanceMap = new HashMap<>();
            //分配余额详情使用余额
            for (BusinessBalanceDetail item : validBusinessBalanceDetailList) {
                if (surplusBalance.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                if (item.getValidBalance().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (item.getBalance().subtract(item.getUseBalance()).subtract(item.getLockBalance()).compareTo(item.getValidBalance()) != 0) {
                    throw new ServiceException("商家余额数据有误,暂时无法使用余额支付~");
                }
                BusinessBalanceDetail updateItem = new BusinessBalanceDetail();
                updateItem.setId(item.getId());
                /**
                 * surplusBalance   200     剩余支付金额
                 * validBalance     100     可提现金额
                 */
                if (surplusBalance.compareTo(item.getValidBalance()) >= 0) {
                    useBalanceMap.put(item.getNumber(), item.getValidBalance());

                    updateItem.setUseBalance(item.getUseBalance().add(item.getValidBalance()));
                    updateItem.setValidBalance(BigDecimal.ZERO);
                    surplusBalance = surplusBalance.subtract(item.getValidBalance());

                } else {
                    /**
                     * surplusBalance   50     剩余支付金额
                     * validBalance     100     可提现金额
                     */
                    useBalanceMap.put(item.getNumber(), surplusBalance);

                    updateItem.setUseBalance(item.getUseBalance().add(surplusBalance));
                    updateItem.setValidBalance(item.getValidBalance().subtract(surplusBalance));
                    surplusBalance = BigDecimal.ZERO;
                }
                updateBusinessBalanceDetailList.add(updateItem);
            }
            if (CollUtil.isNotEmpty(updateBusinessBalanceDetailList)) {
                List<BusinessBalanceDetailFlow> insertDetailFlow = new ArrayList<>();
                for (BusinessBalanceDetailFlowDTO item : dto.getBusinessBalanceDetailFlowList()) {
                    //使用金额
                    BigDecimal useBalance = item.getUseBalance();
                    if (useBalance.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    for (String number : useBalanceMap.keySet()) {
                        BigDecimal detailUseBalance = useBalanceMap.get(number);
                        if (detailUseBalance.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        if (useBalance.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }

                        if (useBalance.compareTo(detailUseBalance) >= 0) {
                            /**
                             * useBalance           200     剩余支付金额
                             * detailUseBalance     100     可提现金额
                             */
                            useBalanceMap.put(number, BigDecimal.ZERO);
                            loadInsertDetailFlow(dto, flowOrigin, businessBalanceFlow, insertDetailFlow, item.getVideoCode(), item.getVideoId(), detailUseBalance.negate(), number);
                            useBalance = useBalance.subtract(detailUseBalance);

                        } else {
                            /**
                             * useBalance           100     剩余支付金额
                             * detailUseBalance     200     可提现金额
                             */
                            useBalanceMap.put(number, detailUseBalance.subtract(useBalance));
                            loadInsertDetailFlow(dto, flowOrigin, businessBalanceFlow, insertDetailFlow, item.getVideoCode(), item.getVideoId(), useBalance.negate(), number);
                            useBalance = BigDecimal.ZERO;
                        }
                    }
                }
                businessBalanceDetailService.updateBatchById(updateBusinessBalanceDetailList);
                businessBalanceDetailFlowService.saveBatch(insertDetailFlow);
            }
        } else if (flowOrigin == BalanceSourceTypeEnum.MEMBER_SPEND) {
            BigDecimal surplusBalance = businessBalanceFlow.getAmount().abs();
            List<BusinessBalanceDetail> validBusinessBalanceDetailList = businessBalanceDetailService.getValidBusinessBalanceDetailListByBusinessId(businessBalanceFlow.getBusinessId());
            if (CollUtil.isEmpty(validBusinessBalanceDetailList)) {
                throw new ServiceException("商家无余额详情数据,暂时无法使用余额支付~");
            }
            BigDecimal canBackBalance = validBusinessBalanceDetailList.stream().map(BusinessBalanceDetail::getValidBalance).reduce(BigDecimal.ZERO, BigDecimal::add);

            if (canBackBalance.compareTo(surplusBalance) < 0) {
                throw new ServiceException("商家余额数据暂不可用~");
            }
            Map<String, BusinessBalanceDetail> balanceDetailMap = validBusinessBalanceDetailList.stream().collect(Collectors.toMap(BusinessBalanceDetail::getNumber, p -> p));

            List<BusinessBalanceDetailFlow> saveBatchDetailFlow = new ArrayList<>();
            List<BusinessBalanceDetail> updateBusinessBalanceDetailList = new ArrayList<>();
            /**
             * surplusBalance       500
             *      validBalance    200
             *      validBalance    200
             *      validBalance    200
             */
            for (BusinessBalanceDetail item : validBusinessBalanceDetailList) {
                if (surplusBalance.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                if (surplusBalance.compareTo(item.getValidBalance()) >= 0) {
                    //item.getValidBalance().negate() = -200
                    loadBusinessDetailAndFlow(dto, flowOrigin, businessBalanceFlow, item.getValidBalance().negate(), saveBatchDetailFlow, updateBusinessBalanceDetailList, balanceDetailMap, item.getNumber(), dto.getOrderNum());
                    surplusBalance = surplusBalance.subtract(item.getValidBalance());
                } else {
                    loadBusinessDetailAndFlow(dto, flowOrigin, businessBalanceFlow, surplusBalance.negate(), saveBatchDetailFlow, updateBusinessBalanceDetailList, balanceDetailMap, item.getNumber(), dto.getOrderNum());
                    surplusBalance = BigDecimal.ZERO;
                }
            }
            businessBalanceDetailService.updateBatchById(updateBusinessBalanceDetailList);
            businessBalanceDetailFlowService.saveBatch(saveBatchDetailFlow);
        } else if (flowOrigin == BalanceSourceTypeEnum.PAYOUT_SPEND) {
            businessBalanceDetailLockService.flow(dto.getWithdrawNumber(), BusinessBalanceAuditStatusEnum.APPROVE.getCode(), businessBalanceFlow.getId());
        }
    }

    @Override
    public List<BusinessBalanceFlowStatisticsVO> statistics(List<Long> businessIds) {
        return baseMapper.statistics(businessIds);
    }

    @Override
    public List<BusinessBalanceDetailFlowExportVO> getBusinessBalanceDetailFlowExports(BusinessBalanceFlowDetailListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbdf.create_time" , OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bbdf.id" , OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<BusinessBalanceDetailFlowExportVO> businessBalanceDetailFlowExports = baseMapper.getBusinessBalanceDetailFlowExports(dto);
        if (CollUtil.isEmpty(businessBalanceDetailFlowExports)) {
            return Collections.emptyList();
        }
        for (BusinessBalanceDetailFlowExportVO item : businessBalanceDetailFlowExports) {
            if (ObjectUtil.isNull(item.getOrigin())) {
                item.setOrigin(BalanceSourceTypeEnum.PREPAY_INCOME.getCode());
            }
            if (StrUtil.isBlank(item.getOriginCode())) {
                item.setOriginCode(item.getMemberCode());
            }
            if (BalanceType.SPEND.getCode().equals(item.getDetailFlowType())) {
                item.setOriginType(item.getOrigin() + item.getNumberType() * 10);
                item.setUseOrigin(item.getOrigin());
                if (StrUtil.isNotBlank(item.getUseVideoCode())){
                    item.setUseOrderNum(item.getUseVideoCode());
                }
            } else {
                item.setOriginType(item.getOrigin());
                item.setUseOrderNum(null);
            }

        }
        return businessBalanceDetailFlowExports;
    }

    private void loadInsertDetailFlow(BusinessBalanceFlowDTO dto, BalanceSourceTypeEnum flowOrigin, BusinessBalanceFlow businessBalanceFlow, List<BusinessBalanceDetailFlow> insertDetailFlow, String videoCode, Long videoId, BigDecimal useBalance, String number) {
        BusinessBalanceDetailFlow detailFlow = new BusinessBalanceDetailFlow();
        detailFlow.setBalanceFlowId(businessBalanceFlow.getId());
        detailFlow.setBalanceNumber(number);
        detailFlow.setNumber(dto.getOrderNum());
        detailFlow.setVideoId(videoId);
        detailFlow.setVideoCode(videoCode);
        detailFlow.setUseBalance(useBalance);
        detailFlow.setType(flowOrigin.getType());
        detailFlow.setCreateById(businessBalanceFlow.getCreateUserId());
        detailFlow.setCreateBy(businessBalanceFlow.getCreateUserName());
        insertDetailFlow.add(detailFlow);
    }

    /**
     * 加载 余额详情、余额详情流水数据
     *
     * @param dto
     * @param flowOrigin
     * @param businessBalanceFlow
     * @param refundAmount
     * @param saveBatchDetailFlow
     * @param updateBusinessBalanceDetail
     * @param balanceDetailMap
     * @param number                      business_balance_detail.number
     */
    private void loadBusinessDetailAndFlow(BusinessBalanceFlowDTO dto, BalanceSourceTypeEnum flowOrigin, BusinessBalanceFlow businessBalanceFlow, BigDecimal refundAmount, List<BusinessBalanceDetailFlow> saveBatchDetailFlow, List<BusinessBalanceDetail> updateBusinessBalanceDetail, Map<String, BusinessBalanceDetail> balanceDetailMap, String number, String originNumber) {
        BusinessBalanceDetailFlow businessBalanceDetailFlow = new BusinessBalanceDetailFlow();
        businessBalanceDetailFlow.setBalanceFlowId(businessBalanceFlow.getId());
        businessBalanceDetailFlow.setBalanceNumber(number);
        businessBalanceDetailFlow.setNumber(originNumber);
        businessBalanceDetailFlow.setVideoId(dto.getVideoId());
        businessBalanceDetailFlow.setVideoCode(dto.getVideoCode());
        businessBalanceDetailFlow.setUseBalance(refundAmount);
        businessBalanceDetailFlow.setType(flowOrigin.getType());
        businessBalanceDetailFlow.setCreateById(businessBalanceFlow.getCreateUserId());
        businessBalanceDetailFlow.setCreateBy(businessBalanceFlow.getCreateUserName());
        saveBatchDetailFlow.add(businessBalanceDetailFlow);
        //余额详情需要回退订单金额
        BusinessBalanceDetail originDetail = balanceDetailMap.get(number);
        if (ObjectUtil.isNull(originDetail)) {
            throw new ServiceException("商家余额数据有误,暂时无法使用余额支付~");
        }
        BusinessBalanceDetail businessBalanceDetail = new BusinessBalanceDetail();
        businessBalanceDetail.setId(originDetail.getId());
        businessBalanceDetail.setUseBalance(originDetail.getUseBalance().subtract(businessBalanceDetailFlow.getUseBalance()));
        businessBalanceDetail.setValidBalance(originDetail.getValidBalance().add(businessBalanceDetailFlow.getUseBalance()));
        updateBusinessBalanceDetail.add(businessBalanceDetail);
    }

    private void initBusinessBalanceDetail(BusinessBalanceFlowDTO dto, BalanceSourceTypeEnum flowOrigin, Long businessBalanceFlowId, BalanceDetailTypeEnum videoType, BigDecimal balance, String number) {
        BusinessBalanceDetail businessBalanceDetail = new BusinessBalanceDetail();
        businessBalanceDetail.setBusinessId(dto.getBusinessId());
        businessBalanceDetail.setOriginNumber(number);
        businessBalanceDetail.setVideoCode(dto.getVideoCode());
        businessBalanceDetail.setNumberType(videoType.getCode());
        businessBalanceDetail.setOrigin(dto.getOrigin());
        businessBalanceDetail.setBalance(balance);
        businessBalanceDetail.setUseBalance(new BigDecimal("0"));
        businessBalanceDetail.setLockBalance(new BigDecimal("0"));
        businessBalanceDetail.setValidBalance(balance);
        businessBalanceDetail.setCreateById(dto.getLoginBase().getUserid());
        businessBalanceDetail.setCreateBy(dto.getLoginBase().getUsername());

        businessBalanceDetail.setCreateOrderUserName(dto.getCreateOrderUserName());
        businessBalanceDetail.setCreateOrderUserNickName(dto.getCreateOrderUserNickName());
        businessBalanceDetail.setPayType(dto.getPayType());
        businessBalanceDetail.setOrderTime(dto.getOrderTableOrderTime());

        businessBalanceDetailService.saveBusinessBalanceDetail(businessBalanceDetail);

        BusinessBalanceDetailFlow businessBalanceDetailFlow = new BusinessBalanceDetailFlow();
        businessBalanceDetailFlow.setBalanceFlowId(businessBalanceFlowId);
        businessBalanceDetailFlow.setBalanceNumber(businessBalanceDetail.getNumber());
        businessBalanceDetailFlow.setNumber(number);
        businessBalanceDetailFlow.setVideoId(dto.getVideoId());
        businessBalanceDetailFlow.setVideoCode(dto.getVideoCode());
        businessBalanceDetailFlow.setUseBalance(balance);
        businessBalanceDetailFlow.setType(flowOrigin.getType());
        businessBalanceDetailFlow.setCreateById(businessBalanceDetail.getCreateById());
        businessBalanceDetailFlow.setCreateBy(businessBalanceDetail.getCreateBy());
        businessBalanceDetailFlowService.save(businessBalanceDetailFlow);
    }


}
