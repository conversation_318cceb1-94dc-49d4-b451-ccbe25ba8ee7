package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.BusinessCallbackEventEnum;
import com.ruoyi.common.core.enums.BusinessCallbackStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.UpdateWechatRemarkType;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ResidentBusinessDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallback;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import com.wnkx.biz.business.mapper.BusinessBalanceAuditFlowMapper;
import com.wnkx.biz.business.mapper.BusinessBalanceFlowMapper;
import com.wnkx.biz.business.mapper.BusinessMapper;
import com.wnkx.biz.business.service.BusinessCallbackEventService;
import com.wnkx.biz.business.service.BusinessCallbackService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import com.wnkx.biz.wechat.service.WechatService;
import com.wnkx.biz.wechat.service.WorkWechatApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business(商家表)】的数据库操作Service实现
 * @createDate 2024-06-20 10:30:40
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessServiceImpl extends ServiceImpl<BusinessMapper, Business>
        implements IBusinessService {
    private final BusinessBalanceAuditFlowMapper businessBalanceAuditFlowMapper;
    private final WorkWechatApiService workWechatApiService;
    private final RemoteOrderService remoteOrderService;
    private final BusinessBalanceFlowMapper businessBalanceFlowMapper;
    private final BusinessCallbackService businessCallbackService;
    private final BusinessCallbackEventService businessCallbackEventService;
    private final IOrderMemberChannelService orderMemberChannelService;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;

    /**
     * 定时更新回访表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessCallback(Long businessId) {
        log.debug("更新回访表，businessId：{}", businessId);
        List<Business> businesses = baseMapper.selectBusinessList(businessId);
        if (CollUtil.isEmpty(businesses)) {
            return;
        }

        DateTime currentDate = DateUtil.date();

        List<BusinessCallbackListVO> businessCallbackListVOS = businessCallbackService.selectBusinessCallbackList(businessId);
        List<BusinessCallbackEvent> saveBatchBusinessCallbackEvents = new ArrayList<>();
        List<Long> deleteCallbackIds = new ArrayList<>();
        for (Business business : businesses) {
            //  获取该商家 回访中、已回访 的事件
            List<Integer> callbackEvents = businessCallbackListVOS.stream()
                    .filter(callback -> callback.getBusinessId().equals(business.getId())) // 筛选出 businessId 为 1 的对象
                    .filter(callback -> !BusinessCallbackStatusEnum.WAIT_FOR_RETURN_VISIT.getCode().equals(callback.getStatus())) // 筛选出 businessId 为 1 的对象
                    .flatMap(callback -> callback.getBusinessCallbackEventVOS().stream()) // 提取 event 列表
                    .map(BusinessCallbackEventVO::getCallbackEvent) // 获取 callbackEvent 属性
                    .collect(Collectors.toList());// 返回所有的 callbackEvent

            List<BusinessCallbackEvent> businessCallbackEvents = new ArrayList<>();

            //  校验 0排单
            if (ObjectUtil.isNull(business.getRecentOrderTime())) {
                //  校验几天未排单 是 会员5天0排单 还是 会员1个月0排单
                //  首先校验 当前时间是否大于首次购买会员往后推2天
                if (DateUtil.compare(currentDate, DateUtil.offsetDay(business.getMemberFirstTime(), 2)) >= 0) {
                    BusinessCallbackEvent businessCallbackEvent = new BusinessCallbackEvent();
                    if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberFirstTime(), 1), -3)) >= 0) {
                        if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERS_1_MONTH_0_LIST.getCode())) {
                            businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERS_1_MONTH_0_LIST.getCode());
                            businessCallbackEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberFirstTime(), 1));
                            businessCallbackEvents.add(businessCallbackEvent);
                        }
                    } else if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERS_5_DAYS_0_BOOKING.getCode())) {
                        businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERS_5_DAYS_0_BOOKING.getCode());
                        businessCallbackEvent.setCallbackTime(DateUtil.offsetDay(business.getMemberFirstTime(), 5));
                        businessCallbackEvents.add(businessCallbackEvent);
                    }
                }

                //  校验购买至今是否已超过30天无排单
                if (DateUtil.compare(currentDate, DateUtil.offsetDay(business.getMemberFirstTime(), 27)) >= 0
                        && !callbackEvents.contains(BusinessCallbackEventEnum.NO_LIST_FOR_NEARLY_30_DAYS.getCode())) {
                    BusinessCallbackEvent businessCallbackEvent = new BusinessCallbackEvent();
                    businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.NO_LIST_FOR_NEARLY_30_DAYS.getCode());
                    businessCallbackEvent.setCallbackTime(DateUtil.offsetDay(business.getMemberFirstTime(), 30));
                    businessCallbackEvents.add(businessCallbackEvent);
                }
            }

            //  校验近30天无排单
            else if (DateUtil.compare(currentDate, DateUtil.offsetDay(business.getRecentOrderTime(), 27)) >= 0
                    && !callbackEvents.contains(BusinessCallbackEventEnum.NO_LIST_FOR_NEARLY_30_DAYS.getCode())) {
                BusinessCallbackEvent businessCallbackEvent = new BusinessCallbackEvent();
                businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.NO_LIST_FOR_NEARLY_30_DAYS.getCode());
                businessCallbackEvent.setCallbackTime(DateUtil.offsetDay(business.getRecentOrderTime(), 30));
                businessCallbackEvents.add(businessCallbackEvent);
            }

            //  校验已购会员月数
            if (ObjectUtil.isNotNull(business.getMemberFirstTime())) {
                BusinessCallbackEvent businessCallbackEvent = new BusinessCallbackEvent();
                //  是否已购超过一个月（基本条件）
                if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberFirstTime(), 1), -3)) >= 0) {
                    if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberFirstTime(), 6), -3)) >= 0) {
                        if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERS_HAVE_BEEN_BUYING_FOR_6_MONTHS.getCode())) {
                            businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERS_HAVE_BEEN_BUYING_FOR_6_MONTHS.getCode());
                            businessCallbackEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberFirstTime(), 6));
                            businessCallbackEvents.add(businessCallbackEvent);
                        }
                    } else if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberFirstTime(), 3), -3)) >= 0) {
                        if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERS_HAVE_BEEN_BUYING_FOR_3_MONTHS.getCode())) {
                            businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERS_HAVE_BEEN_BUYING_FOR_3_MONTHS.getCode());
                            businessCallbackEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberFirstTime(), 3));
                            businessCallbackEvents.add(businessCallbackEvent);
                        }
                    } else if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERS_HAVE_PURCHASED_IT_FOR_1_MONTH.getCode())) {
                        businessCallbackEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERS_HAVE_PURCHASED_IT_FOR_1_MONTH.getCode());
                        businessCallbackEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberFirstTime(), 1));
                        businessCallbackEvents.add(businessCallbackEvent);
                    }
                }
            }

            //  校验未续费
            if (ObjectUtil.isNotNull(business.getMemberFirstTime())
                    && ObjectUtil.isNotNull(business.getMemberLastTime())
                    && DateUtil.compare(business.getMemberFirstTime(), business.getMemberLastTime()) == 0
            ) {
                BusinessCallbackEvent businessCallbackExpireEvent = new BusinessCallbackEvent();
                //  校验即将过期
                if (DateUtil.compare(currentDate, DateUtil.offsetDay(business.getMemberValidity(), -4)) >= 0) {
                    if (!callbackEvents.contains(BusinessCallbackEventEnum.ONE_DAY_BEFORE_MEMBERSHIP_EXPIRES.getCode())) {
                        businessCallbackExpireEvent.setCallbackEvent(BusinessCallbackEventEnum.ONE_DAY_BEFORE_MEMBERSHIP_EXPIRES.getCode());
                        businessCallbackExpireEvent.setCallbackTime(DateUtil.offsetDay(business.getMemberValidity(), -1));
                        businessCallbackEvents.add(businessCallbackExpireEvent);
                    }
                } else if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetWeek(business.getMemberValidity(), -1), -3)) >= 0) {
                    if (!callbackEvents.contains(BusinessCallbackEventEnum.ONE_WEEK_BEFORE_MEMBERSHIP_EXPIRES.getCode())) {
                        businessCallbackExpireEvent.setCallbackEvent(BusinessCallbackEventEnum.ONE_WEEK_BEFORE_MEMBERSHIP_EXPIRES.getCode());
                        businessCallbackExpireEvent.setCallbackTime(DateUtil.offsetWeek(business.getMemberValidity(), -1));
                        businessCallbackEvents.add(businessCallbackExpireEvent);
                    }
                } else if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberValidity(), -1), -3)) >= 0) {
                    if (!callbackEvents.contains(BusinessCallbackEventEnum.ONE_MONTH_BEFORE_MEMBERSHIP_EXPIRES.getCode())) {
                        businessCallbackExpireEvent.setCallbackEvent(BusinessCallbackEventEnum.ONE_MONTH_BEFORE_MEMBERSHIP_EXPIRES.getCode());
                        businessCallbackExpireEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberValidity(), -1));
                        businessCallbackEvents.add(businessCallbackExpireEvent);
                    }
                }

                BusinessCallbackEvent businessCallbackHaveExpireEvent = new BusinessCallbackEvent();
                //  校验已过期
                if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetMonth(business.getMemberValidity(), 1), -3)) >= 0) {
                    if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERSHIP_HAS_EXPIRED_FOR_1_MONTH.getCode())) {
                        businessCallbackHaveExpireEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERSHIP_HAS_EXPIRED_FOR_1_MONTH.getCode());
                        businessCallbackHaveExpireEvent.setCallbackTime(DateUtil.offsetMonth(business.getMemberValidity(), 1));
                        businessCallbackEvents.add(businessCallbackHaveExpireEvent);
                    }
                } else if (DateUtil.compare(currentDate, DateUtil.offsetDay(DateUtil.offsetWeek(business.getMemberValidity(), 1), -3)) >= 0) {
                    if (!callbackEvents.contains(BusinessCallbackEventEnum.MEMBERSHIP_HAS_EXPIRED_FOR_1_WEEK.getCode())) {
                        businessCallbackHaveExpireEvent.setCallbackEvent(BusinessCallbackEventEnum.MEMBERSHIP_HAS_EXPIRED_FOR_1_WEEK.getCode());
                        businessCallbackHaveExpireEvent.setCallbackTime(DateUtil.offsetWeek(business.getMemberValidity(), 1));
                        businessCallbackEvents.add(businessCallbackHaveExpireEvent);
                    }
                }
            }

            //  待回访事件
            List<BusinessCallbackListVO> waitForReturnVisit = businessCallbackListVOS.stream()
                    .filter(callback -> callback.getBusinessId().equals(business.getId()))
                    .filter(callback -> BusinessCallbackStatusEnum.WAIT_FOR_RETURN_VISIT.getCode().equals(callback.getStatus()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(businessCallbackEvents)) {
                Long callbackId;
                //  没有待回访事件 新建数据
                if (CollUtil.isEmpty(waitForReturnVisit)) {
                    BusinessCallback businessCallback = new BusinessCallback();
                    businessCallback.setBusinessId(business.getId());
                    businessCallbackService.addBusinessCallback(businessCallback);
                    callbackId = businessCallback.getId();
                } else {
                    deleteCallbackIds.add(waitForReturnVisit.get(0).getId());
                    callbackId = waitForReturnVisit.get(0).getId();
                }

                for (BusinessCallbackEvent businessCallbackEvent : businessCallbackEvents) {
                    businessCallbackEvent.setCallbackId(callbackId);
                }
                saveBatchBusinessCallbackEvents.addAll(businessCallbackEvents);
            } else if (CollUtil.isNotEmpty(waitForReturnVisit)) {
                deleteCallbackIds.addAll(waitForReturnVisit.stream().map(BusinessCallbackListVO::getId).collect(Collectors.toList()));
            }
        }
        businessCallbackEventService.deleteByCallbackIds(deleteCallbackIds);
        businessCallbackEventService.saveBatch(saveBatchBusinessCallbackEvents);
    }

    @Override
    public List<Business> getAllBusinessCodeList() {
        return baseMapper.getAllBusinessCodeList();
    }

    @Override
    public List<BusinessOwnerAccountContactUserDTO> getBusinessOwnerUserContactUserName() {
        return baseMapper.selectBusinessOwnerUserContactUserName();
    }

    @Override
    public BusinessOwnerAccountContactUserDTO getBusinessOwnerUserContactUserNameByMemberCode(String memberCode) {
        return baseMapper.getBusinessOwnerUserContactUserNameByMemberCode(memberCode);
    }

    @Override
    public void initBusinessWechatUrlBaseOnContactUser(List<BusinessOwnerAccountContactUserDTO> allBusinessList, HashMap<String, String> kvMap) {
        for (BusinessOwnerAccountContactUserDTO businessOwnerAccountContactUserDTO : allBusinessList) {
            String url = workWechatApiService.contactSubAccountQrcode(businessOwnerAccountContactUserDTO.getMemberCode(),
                    kvMap.getOrDefault(businessOwnerAccountContactUserDTO.getContactUserName(), "365up"));
            baseMapper.setBusinessWechatUrlByMemberCode(url, businessOwnerAccountContactUserDTO.getMemberCode());
        }
    }

    @Override
    public WorkbenchVO getFinanceWorkbenchVo() {
        WorkbenchVO vo = new WorkbenchVO();
        Long preApproveCount = businessBalanceAuditFlowMapper.getPreApproveCount();
        Long unSettledCount = orderMemberChannelService.getUnSettledCount();

        vo.setPayoutUnAuditCount(Optional.ofNullable(preApproveCount).orElse(0L));
        vo.setDistributionUnSettleCount(Optional.ofNullable(unSettledCount).orElse(0L));
        return vo;
    }

    /**
     * 商家排单时 更新最近排单时间 以及删除未排单事件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRecentOrderTime(Long businessId) {
        Business business = baseMapper.selectById(businessId);
        Assert.notNull(business, "商家不存在");
        if (StatusTypeEnum.NO.getCode().equals(business.getPaySucceed())) {
            //最近派单时间为空代表第一次排单 更新分销渠道记录表状态为  未结算
            // orderMemberChannelService.memberChannelUnSettlement(businessId);
            baseMapper.updatePaySucceed(businessId);
        }

        baseMapper.updateRecentOrderTime(businessId);
        updateBusinessCallback(businessId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePaySucceed(Long businessId) {
        Business business = baseMapper.selectById(businessId);
        Assert.notNull(business, "商家不存在");
        if (StatusTypeEnum.YES.getCode().equals(business.getPaySucceed())) {
            return;
        }
        //最近派单时间为空代表第一次排单 更新分销渠道记录表状态为  未结算
        // orderMemberChannelService.memberChannelUnSettlement(businessId);
        baseMapper.updatePaySucceed(businessId);
    }

    @Override
    public BusinessVO queryBusinessVo(BusinessDTO dto) {
        Business business = this.queryOne(dto);
        if (ObjectUtil.isNull(business)) {
            return new BusinessVO();
        }
        BusinessVO businessVO = BeanUtil.copyProperties(business, BusinessVO.class);
        businessVO.setValidBalance(businessVO.getBalance().subtract(businessVO.getUseBalance()));
        if (businessVO.getValidBalance().compareTo(BigDecimal.ZERO) == 0) {
            businessVO.setIsBalanceLock(StatusTypeEnum.YES.getCode());
        } else {
            businessVO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
        }
        return businessVO;
    }

    @Override
    public BusinessBalanceInfoVO getBusinessBalanceDetailVo(BusinessDTO dto) {
        Business business = this.queryOne(dto);
        if (StringUtils.isNull(business)) {
            return null;
        }
        BusinessBalanceInfoVO businessBalanceInfoVO = BeanUtil.copyProperties(business, BusinessBalanceInfoVO.class);
        businessBalanceInfoVO.setValidBalance(businessBalanceInfoVO.getBalance().subtract(businessBalanceInfoVO.getUseBalance()));
        //获取商家体现锁定余额
        PayoutAmountStatisticsVO payoutAmountStatistics = Optional.ofNullable(businessBalanceAuditFlowMapper.getPayoutAmountStatistics(business.getId())).orElse(PayoutAmountStatisticsVO.builder()
                .approveAmount(BigDecimal.ZERO)
                .cancelAmount(BigDecimal.ZERO)
                .preApproveAmount(BigDecimal.ZERO)
                .build());
        List<BusinessBalanceFlowStatisticsVO> businessBalanceFlowStatisticsVOS = businessBalanceFlowMapper.statistics(Arrays.asList(business.getId()));
        BigDecimal useBalanceTotal = BigDecimal.ZERO;
        BigDecimal withdrawTotal = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(businessBalanceFlowStatisticsVOS)) {
            BusinessBalanceFlowStatisticsVO businessBalanceFlowStatisticsVO = businessBalanceFlowStatisticsVOS.get(0);
            withdrawTotal = Optional.ofNullable(businessBalanceFlowStatisticsVO.getWithdrawTotal()).orElse(BigDecimal.ZERO).abs();
            useBalanceTotal = Optional.ofNullable(businessBalanceFlowStatisticsVO.getBalancePayTotal()).orElse(BigDecimal.ZERO).abs();
        }

        businessBalanceInfoVO.setUseBalanceTotal(useBalanceTotal);
        businessBalanceInfoVO.setWithdrawTotal(withdrawTotal);

        businessBalanceInfoVO.setPayOutLockBalance(Optional.ofNullable(payoutAmountStatistics.getPreApproveAmount()).orElse(BigDecimal.ZERO));
        businessBalanceInfoVO.setOrderLockBalance(businessBalanceInfoVO.getUseBalance().subtract(businessBalanceInfoVO.getPayOutLockBalance()));
        return businessBalanceInfoVO;
    }

    @Override
    public List<Business> queryList(BusinessDTO dto) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotNull(dto.getIsProxy()), Business::getIsProxy, dto.getIsProxy())
                .eq(StringUtils.isNotNull(dto.getWaiterId()), Business::getWaiterId, dto.getWaiterId())
                .eq(StringUtils.isNotNull(dto.getMemberType()), Business::getMemberType, dto.getMemberType())
                .eq(StringUtils.isNotNull(dto.getStatus()), Business::getStatus, dto.getStatus())
                .eq(StringUtils.isNotNull(dto.getCustomerType()), Business::getCustomerType, dto.getCustomerType())
                .eq(StringUtils.isNotNull(dto.getBusinessIdentifier()), Business::getBusinessIdentifier, dto.getBusinessIdentifier())
                .eq(StringUtils.isNotNull(dto.getMemberStatus()), Business::getMemberStatus, dto.getMemberStatus())
                .like(StringUtils.isNotNull(dto.getMemberCode()), Business::getMemberCode, dto.getMemberCode())
                .eq(StringUtils.isNotNull(dto.getIsExistRecentOrder()), Business::getIsExistRecentOrder, dto.getIsExistRecentOrder())
                .and(StringUtils.isNotEmpty(dto.getSearchName()), item -> item.like(Business::getMemberCode, dto.getSearchName()).or().like(Business::getName, dto.getSearchName()).or().like(Business::getOwnerAccount, dto.getSearchName()))
                .between(StringUtils.isNotNull(dto.getBusinessCreateBegin()) && StringUtils.isNotNull(dto.getBusinessCreateEnd()),
                        Business::getCreateTime, dto.getBusinessCreateBegin(), dto.getBusinessCreateEnd())
                .between(StringUtils.isNotNull(dto.getMemberValidityBegin()) && StringUtils.isNotNull(dto.getMemberValidityEnd()),
                        Business::getMemberValidity, dto.getMemberValidityBegin(), dto.getMemberValidityEnd())
                .isNotNull(StatusTypeEnum.YES.getCode().equals(dto.getIsAssignWaiter()), Business::getWaiterId)
                .isNull(StatusTypeEnum.NO.getCode().equals(dto.getIsAssignWaiter()), Business::getWaiterId)
                .like(StringUtils.isNotEmpty(dto.getOwnerAccount()), Business::getOwnerAccount, dto.getOwnerAccount())
                .like(StringUtils.isNotEmpty(dto.getName()), Business::getName, dto.getName())
                .in(StringUtils.isNotEmpty(dto.getAccounts()), Business::getOwnerAccount, dto.getAccounts())
                .in(StringUtils.isNotEmpty(dto.getMemberStatusList()), Business::getMemberStatus, dto.getMemberStatusList())
                .in(StringUtils.isNotEmpty(dto.getBusinessIds()), Business::getId, dto.getBusinessIds())
                .list();
    }

    @Override
    public List<BusinessDetailVO> queryBusinessList(BusinessDTO dto) {
        OrderByDto orderByDto = new OrderByDto();

        Map<Long, Integer> orderCountMap = new HashMap<>();
        if (ObjectUtil.isNotNull(dto.getOrderByType())) {
            List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetails = remoteOrderService.orderVideoStatisticsDetail(new OrderVideoStatisticsDTO());
            if (CollUtil.isNotEmpty(orderVideoStatisticsDetails)) {
                orderCountMap = orderVideoStatisticsDetails.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, OrderVideoStatisticsDetailVO::getOrderVideoTotal));
                if (CollUtil.isNotEmpty(orderCountMap)) {
                    dto.setOrderCountMap(orderCountMap);
                }
            }
            orderByDto.setField("orderNum", StatusTypeEnum.YES.getCode().equals(dto.getIsAsc()) ? OrderByDto.DIRECTION.ASC : OrderByDto.DIRECTION.DESC);
        }

        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessDetailVO> businessDetailVOS = baseMapper.queryBusinessList(dto);

        return businessDetailVOS;
    }

    @Override
    public List<ResidentBusinessVO> queryResidentBusiness(ResidentBusinessDTO dto) {
        return baseMapper.queryResidentBusiness(dto);
    }

    @Override
    public Business queryOne(BusinessDTO dto) {
        return this.lambdaQuery()
                .eq(StringUtils.isNotEmpty(dto.getOwnerAccount()), Business::getOwnerAccount, dto.getOwnerAccount())
                .eq(StringUtils.isNotNull(dto.getId()), Business::getId, dto.getId())
                .eq(StringUtils.isNotNull(dto.getMemberCode()), Business::getMemberCode, dto.getMemberCode())
                .one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BusinessDTO dto) {
        if (StringUtils.isNull(dto.getId())
                && StringUtils.isEmpty(dto.getOwnerAccount())
                && StringUtils.isEmpty(dto.getBusinessIds())) {
            throw new ServiceException("修改商家失败，修改条件不能都为空");
        }
        this.lambdaUpdate()
                .set(StringUtils.isNotEmpty(dto.getName()), Business::getName, dto.getName())
                .set(StringUtils.isNotNull(dto.getScale()), Business::getScale, dto.getScale())
                .set(StringUtils.isNotNull(dto.getIsProxy()), Business::getIsProxy, dto.getIsProxy())
                .set(StringUtils.isNotNull(dto.getStatus()), Business::getStatus, dto.getStatus())
                .set(StringUtils.isNotNull(dto.getCustomerType()), Business::getCustomerType, dto.getCustomerType())
                .set(StringUtils.isNotNull(dto.getBusinessIdentifier()), Business::getBusinessIdentifier, dto.getBusinessIdentifier())
                .set(StringUtils.isNotNull(dto.getWaiterId()), Business::getWaiterId, dto.getWaiterId())
                .set(StringUtils.isNotNull(dto.getInvoiceTitleType()), Business::getInvoiceTitleType, dto.getInvoiceTitleType())
                .set(StringUtils.isNotEmpty(dto.getInvoiceTitle()), Business::getInvoiceTitle, dto.getInvoiceTitle())
                .set(StringUtils.isNotEmpty(dto.getInvoiceDutyParagraph()), Business::getInvoiceDutyParagraph, dto.getInvoiceDutyParagraph())
                .set(StringUtils.isNotEmpty(dto.getInvoiceContent()), Business::getInvoiceContent, dto.getInvoiceContent())
                .set(StringUtils.isNotEmpty(dto.getMemberCode()), Business::getMemberCode, dto.getMemberCode())
                .set(StringUtils.isNotNull(dto.getMemberType()), Business::getMemberType, dto.getMemberType())
                .set(StringUtils.isNotNull(dto.getMemberStatus()), Business::getMemberStatus, dto.getMemberStatus())
                .set(StringUtils.isNotNull(dto.getMemberPackageType()), Business::getMemberPackageType, dto.getMemberPackageType())
                .set(StringUtils.isNotNull(dto.getMemberFirstTime()), Business::getMemberFirstTime, dto.getMemberFirstTime())
                .set(StringUtils.isNotNull(dto.getMemberFirstType()), Business::getMemberFirstType, dto.getMemberFirstType())
                .set(StringUtils.isNotNull(dto.getMemberValidity()), Business::getMemberValidity, dto.getMemberValidity())
                .set(StringUtils.isNotNull(dto.getMemberLastTime()), Business::getMemberLastTime, dto.getMemberLastTime())
                .set(StringUtils.isNotNull(dto.getPhoneVisible()), Business::getPhoneVisible, dto.getPhoneVisible())
                .set(StringUtils.isNotNull(dto.getRechargeCount()), Business::getRechargeCount, dto.getRechargeCount())
                .set(StrUtil.isNotBlank(dto.getRemark()), Business::getRemark, dto.getRemark())
                .eq(StringUtils.isNotNull(dto.getId()), Business::getId, dto.getId())
                .eq(StringUtils.isNotEmpty(dto.getOwnerAccount()), Business::getOwnerAccount, dto.getOwnerAccount())
                .in(StringUtils.isNotEmpty(dto.getBusinessIds()), Business::getId, dto.getBusinessIds())
                .update();
    }

    @Override
    public String getBusinessWechatUrlByMemberCode(String memberCode) {
        return baseMapper.getBusinessWechatUrlByMemberCode(memberCode);
    }

    @Override
    public void initBusinessWechatUrl(String memberCode) {
        BusinessOwnerAccountContactUserDTO businessOwnerUserContactUserNameByMemberCode = baseMapper.getBusinessOwnerUserContactUserNameByMemberCode(memberCode);
        asyncPoolTaskExecutor.submit(()->{
            log.debug("开始初始化商家子账号url");
            log.info(businessOwnerUserContactUserNameByMemberCode.toString());
            String url = workWechatApiService.contactMeQrcode(memberCode,businessOwnerUserContactUserNameByMemberCode.getContactUserId());
            baseMapper.setBusinessWechatUrlByMemberCode(url, memberCode);
        });
    }

    @Override
    public void initBusinessWechatRemark(Long businessId, String memberCode) {
        try {
            SpringUtils.getBean(WechatService.class).asyncUpdateWorkWechatRemark(Business.builder().id(businessId).memberCode(memberCode).build(), UpdateWechatRemarkType.VIP);
        } catch (Exception e) {
            log.error("更新企业微信备注数据,异常:{}", e.getMessage());
        }
    }

    @Override
    public String getBusinessMainAccountExternalUserId(Long businessId) {
        return baseMapper.getBusinessMainAccountExternalUserId(businessId);
    }


    @Override
    public List<Business> getUnInitWechatBusinessList() {
        return baseMapper.getUnInitWechatBusinessList();
    }

    @Override
    public void initBusinessWechatUrl(List<Business> businessList) {
        businessList.forEach(business -> {
//            String url = workWechatApiService.contactMeQrcode(business.getMemberCode());
//            baseMapper.setBusinessWechatUrlByMemberCode(url, business.getMemberCode());
        });
    }

    @Override
    public String getmemberCodeByBusinessId(Long businessId) {
        return baseMapper.getmemberCodeByBusinessId(businessId);
    }
}
