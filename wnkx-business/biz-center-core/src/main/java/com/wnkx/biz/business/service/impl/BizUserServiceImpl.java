package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.BizUserAccountTypeEnum;
import com.ruoyi.common.core.enums.ChannelTypeEnum;
import com.ruoyi.common.core.enums.UpdateWechatRemarkType;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.*;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalContactInfoDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.wnkx.biz.business.mapper.BizUserMapper;
import com.wnkx.biz.business.service.IBizUserService;
import com.wnkx.biz.channel.service.IBizUserChannelService;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.wechat.service.WechatService;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【biz_user(登录用户表)】的数据库操作Service实现
 * @createDate 2024-08-26 11:22:30
 */
@Service
@RequiredArgsConstructor
public class BizUserServiceImpl extends ServiceImpl<BizUserMapper, BizUser>
        implements IBizUserService {

    private final RemoteService remoteService;
    private final IBizUserChannelService bizUserChannelService;
    private final ChannelCore channelCore;

    /**
     * 校验手机号是否绑定用户
     */
    @Override
    public Boolean phoneCheck(String phone) {
        return baseMapper.phoneCheck(phone);
    }

    /**
     * 基于UnionId初始化登录账号数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUser initBizUserBaseOnUnionId(BizUserDTO dto) {
        BizUser bizUser = baseMapper.getByUnionId(dto.getUnionid());
        if (bizUser != null) {
            return bizUser;
        }
        return initBizUser(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BizUser initBizUserBaseOnPhone(BizUserDTO dto) {
        BizUser bizUser = baseMapper.getByPhone(dto.getPhone());
        if (bizUser != null) {
            return bizUser;
        }
        return initBizUser(dto);
    }

    @NotNull
    private BizUser initBizUser(BizUserDTO dto) {
        BizUser entity = BeanUtil.copyProperties(dto, BizUser.class);
        baseMapper.insert(entity);
        bizUserChannelService.initBizUserChannel(BizUserChannelDTO.builder()
                .bizUserId(entity.getId())
                .addWechatTime(new Date())
                .registerTime(new Date())
                .build());
        return entity;
    }

    @Override
    public BizUser getByUnionId(String unionId) {
        return baseMapper.getByUnionId(unionId);
    }

    @Override
    public List<BizUser> getBizUserByBusinessId(Long businessId) {
        return baseMapper.getBizUserByBusinessId(businessId);
    }

    @Override
    public List<BizUserListVO> bizUserList(BizUserListDTO dto) {
        List<BizUserListVO> bizUserListVOS = baseMapper.bizUserList(dto);
        if (CollUtil.isNotEmpty(bizUserListVOS)) {
            List<Long> userIds = bizUserListVOS.stream().map(BizUserListVO::getWaiterId).collect(Collectors.toList());
            //  获取用户map
            SysUserListDTO sysUserListDTO = new SysUserListDTO();
            sysUserListDTO.setUserId(userIds);
            Map<Long, UserVO> userMap = remoteService.getUserMap(sysUserListDTO);
            List<Long> mkTChannelIds = bizUserListVOS.stream().filter(bizUserListVO -> bizUserListVO.getChannelType() != null && bizUserListVO.getChannelType().equals(ChannelTypeEnum.MARKETING.getCode())).map(BizUserListVO::getChannelId).collect(Collectors.toList());
            List<Long> disChannelIds = bizUserListVOS.stream().filter(bizUserListVO -> bizUserListVO.getChannelType() != null && List.of(ChannelTypeEnum.DISTRIBUTION.getCode(), ChannelTypeEnum.FISSION.getCode()).contains(bizUserListVO.getChannelType())).map(BizUserListVO::getChannelId).collect(Collectors.toList());
            Map<Long, MarketingChannel> marketingChannelMap = CollUtil.isNotEmpty(mkTChannelIds) ? channelCore.getMarketingChannelsByIds(mkTChannelIds).stream().collect(Collectors.toMap(MarketingChannel::getId, marketingChannel -> marketingChannel)) : new HashMap<>();
            Map<Long, DistributionChannel> distributionChannelMap = CollUtil.isNotEmpty(disChannelIds) ? channelCore.getChannelByIds(disChannelIds).stream().collect(Collectors.toMap(DistributionChannel::getId, distributionChannel -> distributionChannel)) : new HashMap<>();
            bizUserListVOS.forEach(item ->
                    {
                        item.setWaiterName(CollUtil.isNotEmpty(userMap) &&
                                ObjectUtil.isNotNull(userMap.get(item.getWaiterId())) ? userMap.get(item.getWaiterId()).getName() : "");
                        if (item.getChannelType() != null && item.getChannelType().equals(ChannelTypeEnum.MARKETING.getCode())) {
                            item.setChannelName(marketingChannelMap.getOrDefault(item.getChannelId(), new MarketingChannel()).getMarketingChannelName());
                        }
                        if (item.getChannelType() != null && List.of(ChannelTypeEnum.DISTRIBUTION.getCode(),
                                ChannelTypeEnum.FISSION.getCode()).contains(item.getChannelType())) {
                            item.setChannelName(distributionChannelMap.getOrDefault(item.getChannelId(), new DistributionChannel()).getChannelName());
                        }

                    }
            );
        }
        return bizUserListVOS;
    }

    @Override
    public List<BizUserDetailVO> bizUserDetailList(BizUserDetailListDTO dto) {
        return baseMapper.bizUserDetailList(dto);
    }

    @Override
    public Boolean isExist(String phone) {
        BizUser bizUser = baseMapper.getByPhone(phone);
        return !ObjectUtil.isNull(bizUser) && !StrUtil.isBlank(bizUser.getUnionid());
    }

    @Override
    public BizUser getByPhone(String phone) {
        return baseMapper.getByPhone(phone);
    }

    @Override
    public void setUserExternalUserid(String unionId, String externalUserid) {
        baseMapper.setUserExternalUserid(unionId, externalUserid);
    }

    @Override
    public void setPhone(String unionId, String phone) {
        baseMapper.setPhone(unionId, phone);
    }

    @Override
    public void setSeedId(Long id, String seedId) {
        baseMapper.setSeedId(id, seedId);
    }

    @Override
    public void updateWeChat(Long id, ExternalContactInfoDTO dto) {
        baseMapper.updateWeChat(id, dto);
    }

    @Override
    public void cleanUserExternalUserid(String externalUserId) {
        baseMapper.cleanUserExternalUserid(externalUserId);
    }

    @Override
    public void cleanUserBusiness(Long id) {
        String externalUserId = baseMapper.getExternalUserIdById(id).getExternalUserId();
        baseMapper.cleanUserBusiness(id);
        ThreadUtil.execAsync(() -> SpringUtils.getBean(WechatService.class).cleanAccountRemark(externalUserId, UpdateWechatRemarkType.UNBIND_BUSINESS, null));
    }

    @Override
    public void updateLoginTime(String phone) {
        baseMapper.updateLoginTime(phone);
    }

    @Override
    public void updateUserBusinessInfo(BizUserBusinessInfoDTO dto) {
        baseMapper.updateUserBusinessInfo(dto);
    }

    @Override
    public void updateWaiterIdByIds(List<Long> ids, Long waiterId) {
        baseMapper.updateWaiterIdByIds(ids, waiterId);
    }

    @Override
    public void updateFillNullById(BizUser bizUser) {
        baseMapper.updateFillNullById(bizUser);
    }

    @Override
    public void bindUserBusinessSub(String memberCode, Long bizUserId) {
        String externalUserId = baseMapper.getExternalUserIdById(bizUserId).getExternalUserId();
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(externalUserId, memberCode);
        Map<String, BizUserAccountTypeEnum> accountTypeMap = new HashMap<>();
        accountTypeMap.put(externalUserId, BizUserAccountTypeEnum.ACCOUNT);
        ThreadUtil.execAsync(() -> {
            try {
                SpringUtils.getBean(WechatService.class).updateAccountRemarkBatch(dataMap, UpdateWechatRemarkType.BIND_BUSINESS, accountTypeMap);
            } catch (Exception e) {
                log.error("绑定商家失败", e);
            }
        });
    }

    @Override
    public void updateContactUserName(String unionId, String connectUserName) {
        baseMapper.updateContactUserName(unionId, connectUserName);
    }
}




