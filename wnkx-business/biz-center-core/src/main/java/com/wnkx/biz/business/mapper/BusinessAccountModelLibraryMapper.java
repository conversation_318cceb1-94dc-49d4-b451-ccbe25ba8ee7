package com.wnkx.biz.business.mapper;

import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模特商家端信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@Mapper
public interface BusinessAccountModelLibraryMapper {

    /**
     * 商家端-模特库-模特列表
     */
    List<BusinessAccountCollectModelVO> selectBusinessAccountModelListByCondition(@Param("dto") BusinessAccountCollectModelDTO businessAccountCollectModelDTO,
                                                                                  @Param("modelCategoryId") Long modelCategoryId,
                                                                                  @Param("modelTagId") Long modelTagId,
                                                                                  @Param("bizUserId") Long bizUserId
    );

    /**
     * 商家收藏模特列表
     */
    List<BusinessAccountCollectModelVO> selectBusinessAccountCollectModelListByCondition(@Param("dto") BusinessAccountCollectModelDTO businessAccountCollectModelDTO,
                                                                                         @Param("modelCategoryId") Long modelCategoryId,
                                                                                         @Param("modelTagId") Long modelTagId,
                                                                                         @Param("bizUserId") Long bizUserId);


    List<Long> selectModelStatusByModelIds(@Param("dto") BusinessAccountCollectModelDTO dto);
}
