package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.*;
import com.ruoyi.common.core.context.SecurityContextHolder;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.web.domain.LoginBaseEntity;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.annotation.MemberAuth;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.RemoteOrderService;
import com.ruoyi.system.api.config.WechatConfig;
import com.ruoyi.system.api.domain.dto.*;
import com.ruoyi.system.api.domain.dto.biz.business.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.*;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.biz.wechat.ExternalContactInfoDTO;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPayDetailDTO;
import com.ruoyi.system.api.domain.dto.order.UpdateOrderContactDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.casus.BalancePayOutDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.business.*;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser;
import com.ruoyi.system.api.domain.entity.order.BusinessRemarkFlow;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.SaveBizUserVO;
import com.ruoyi.system.api.domain.vo.order.BusinessRemarkFlowVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import com.ruoyi.system.api.model.LoginBusiness;
import com.wnkx.biz.business.mapper.*;
import com.wnkx.biz.business.service.*;
import com.wnkx.biz.business.service.core.MemberContext;
import com.wnkx.biz.channel.service.IBizUserChannelService;
import com.wnkx.biz.channel.service.IOrderMemberChannelService;
import com.wnkx.biz.channel.service.IOrderMemberMarketingChannelService;
import com.wnkx.biz.channel.service.MemberSeedRecordService;
import com.wnkx.biz.core.ChannelCore;
import com.wnkx.biz.model.service.BizResourceService;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.sms.service.SmsService;
import com.wnkx.biz.wechat.service.IWeChatExternalUserService;
import com.wnkx.biz.wechat.service.WechatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_account(商家账号表)】的数据库操作Service实现
 * @createDate 2024-06-20 10:30:41
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class BusinessAccountServiceImpl extends ServiceImpl<BusinessAccountMapper, BusinessAccount>
        implements IBusinessAccountService {
    private final IBusinessService businessService;

    private final RedisService redisService;

    private final TokenService tokenService;

    private final RemoteOrderService remoteOrderService;

    private final RemoteService remoteService;

    private final BusinessMapper businessMapper;

    private final IBusinessBalanceFlowService businessBalanceFlowService;

    private final IBusinessAccountService self;

    private final IBusinessBalanceAuditFlowService businessBalanceAuditFlowService;

    private final IBizUserService bizUserService;

    private final WechatConfig wechatConfig;

    private final SmsService smsService;

    private final IBusinessOperLogService businessOperLogService;

    private final IWeChatExternalUserService weChatExternalUserService;

    private final ChannelCore channelCore;

    private final IBizUserChannelService bizUserChannelService;

    private final IOrderMemberChannelService orderMemberChannelService;

    private final IOrderMemberMarketingChannelService orderMemberMarketingChannelService;

    private final IBusinessMemberValidityFlowService businessMemberValidityFlowService;

    private final IBusinessBalancePrepayService businessBalancePrepayService;
    private String INITIALIZE_ACCOUNT = "000000";

    private final IBusinessRemarkFlowService businessRemarkFlowService;

    private final IBusinessBalanceDetailLockService businessBalanceDetailLockService;

    private final IBusinessBalanceDetailService businessBalanceDetailService;

    private final BizResourceService bizResourceService;

    private final BusinessAccountApplyMapper accountApplyMapper;
    private final BusinessAccountRebindLogMapper businessAccountRebindLogMapper;
    private final BusinessAccountMapper businessAccountMapper;
    private final IBusinessOwnerFlowService businessOwnerFlowService;
    private final BusinessMemberActivityMapper businessMemberActivityMapper;
    private final BizUserMapper bizUserMapper;
    private final IBusinessBalanceService businessBalanceService;
    private final MemberSeedRecordService memberSeedRecordService;


    /**
     * 查询商家用户下拉框
     */
    @Override
    public List<BusinessAccountSelectVO> businessAccountSelect(BusinessAccountDetailDTO dto) {
        return baseMapper.businessAccountSelect(dto);
    }

    /**
     * 商家回访-填写回访记录-回访账号下拉框
     */
    @Override
    public List<BusinessAccountVO> writeReturnVisitAccount(Long businessId) {
        return baseMapper.writeReturnVisitAccount(businessId);
    }

    /**
     * 获取视频订单提现记录
     */
    @Override
    public List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto) {
        return businessBalanceDetailLockService.withdrawDepositRecord(dto);
    }

    /**
     * 四个数据都是可以保证唯一的
     *
     * @param dto
     * @return
     */
    @Override
    public BusinessAccountVO queryOne(BusinessAccountDTO dto) {
        return baseMapper.queryOne(dto);
    }

    @Override
    public List<BusinessAccountVO> queryList(BusinessAccountDTO dto) {
        return baseMapper.queryList(dto);
    }

    @Override
    public List<BusinessAccountVO> list(BusinessAccountDTO dto) {
        return baseMapper.list(dto);
    }

    /**
     * 根据unionId获取账号数据
     *
     * @param unionid
     * @return
     */
    @Override
    public BusinessAccountVO getBusinessAccount(String unionid) {
        //获取账号信息
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setUnionid(unionid);
        BusinessAccountVO businessAccount = baseMapper.queryOne(dto);
        if (null == businessAccount) {

            BizUser bizUser = bizUserService.getByUnionId(unionid);
            businessAccount = getInitBusinessAccountVo(bizUser);
            return businessAccount;
        }
        //填充商家、子账号数据
        return self.getBusinessAccountVO(businessAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PhoneLoginVO phoneLogin(PhoneLoginDTO dto) {
        //获取验证码
        if (!smsService.verifyCode(dto.getPhone(), dto.getPhoneCaptcha())) {
            //验证码错误异常
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR)
                    .msg(WxChatLoginStatusEnum.CAPTCHA_FAULT_ERROR.getDesc())
                    .build();
        }
        //根据手机号获取登录信息
        BizUser bizUserByPhone = bizUserService.getByPhone(dto.getPhone());
        Assert.notNull(bizUserByPhone, "登录失败，请联系蜗牛客服处理~");

        WechatService wechatService = SpringUtils.getBean(WechatService.class);
        //获取登录信息
        if (StrUtil.isNotBlank(dto.getTicket())) {
            if (ObjectUtil.isNull(bizUserByPhone)) {
                //初始化登录账号数据
                bizUserByPhone = bizUserService.initBizUserBaseOnPhone(BizUserDTO.builder()
                        .phone(dto.getPhone())
                        .build());
            }

            if (StrUtil.isNotBlank(bizUserByPhone.getUnionid())) {
                //手机号对应账号已存在微信信息
                return PhoneLoginVO.builder()
                        .loginStatus(WxChatLoginStatusEnum.BINDING)
                        .build();
            }
            //获取unionId
            String unionId = wechatService.getUnionIdByTicket(dto.getTicket());
            //获取登录信息
            BizUser bizUserByUnionId = bizUserService.getByUnionId(unionId);

            //不存在对应微信的登录信息
            if (ObjectUtil.isNotNull(bizUserByUnionId)) {
                //错误状态 微信已存在账号数据
                return PhoneLoginVO.builder()
                        .loginStatus(WxChatLoginStatusEnum.BINDING)
                        .build();
            }

            if (wechatService.getTicketByUnionId(unionId).equals(dto.getTicket())) {
                //填充微信信息
                WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);
                if (ObjectUtil.isNull(weChatExternalUser)) {
                    return PhoneLoginVO.builder()
                            .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                            .build();
                }
                bizUserService.updateById(bizUserByPhone.setNickName(weChatExternalUser.getName())
                        .setExternalUserId(weChatExternalUser.getExternalUserid())
                        .setPic(weChatExternalUser.getAvatar())
                        .setConnectUserName(weChatExternalUser.getConnectUserName())
                        .setUnionid(weChatExternalUser.getUnionid()));

                //填充添加微信渠道、注册账号渠道信息
                channelCore.reSetBizUserChannelInfo(weChatExternalUser, bizUserByPhone.getId(), dto.getTicket());

                redisService.deleteObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + dto.getTicket());
                redisService.deleteObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
                BusinessAccountVO businessAccount = self.getBusinessAccount(unionId);

                self.updateLoginTime(businessAccount);

                //登录 返回token
                return PhoneLoginVO.builder()
                        .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                        .businessAccountVO(businessAccount)
                        .build();
            } else {
                throw new ServiceException("业务异常");

            }
        }

        if (ObjectUtil.isNull(bizUserByPhone)) {
            //初始化登录账号数据
            bizUserService.initBizUserBaseOnPhone(BizUserDTO.builder()
                    .phone(dto.getPhone())
                    .build());
        }

        //手机号登录
        if (ObjectUtil.isNull(bizUserByPhone) || StrUtil.isBlank(bizUserByPhone.getUnionid())) {
            QrCodeDTO qrCodeDTO = self.generateQrcode(TokenConstants.TOKEN_REGISTER, dto.getLinkCode());
            //设置 手机号 - ticket 关系
            updateRedisTicketPhone(qrCodeDTO.getTicket(), dto.getPhone());
            //初始化登录信息
            return PhoneLoginVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_NO_WE_CHAT)
                    .qrcode(qrCodeDTO.getQrcode())
                    .ticket(qrCodeDTO.getTicket())
                    .build();
        }
        BusinessAccountVO businessAccount = self.getBusinessAccount(bizUserByPhone.getUnionid());
        self.updateLoginTime(businessAccount);
        return PhoneLoginVO.builder()
                .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                .businessAccountVO(businessAccount)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLoginTime(BusinessAccountVO businessAccount) {
        if (UserStatus.OK.getCode().equals(String.valueOf(businessAccount.getUserStatus()))
                && UserStatus.OK.getCode().equals(String.valueOf(businessAccount.getStatus()))
                && UserStatus.OK.getCode().equals(String.valueOf(businessAccount.getBusinessVO().getStatus()))
        ) {
            try {
                bizUserService.updateLoginTime(businessAccount.getPhone());
                bizUserChannelService.activate(businessAccount.getBizUserId());
                if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsMock())) {
                    return;
                }
                updateLoginTime(businessAccount.getAccount());
            } catch (Exception e) {
                log.warn("登录时间更新失败，不影响流程");
            }
        }
    }

    private void updateRedisTicketPhone(String ticket, String phone) {
        redisService.setCacheObject(CacheConstants.PHONE_LOGIN_TICKET_PHONE_KEY + ticket, phone, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
        redisService.setCacheObject(CacheConstants.PHONE_LOGIN_PHONE_TICKET_KEY + phone, ticket, CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
    }

    @Override
    public QrCodeDTO generateQrcode(Integer type, String code) {


        String randomString = RandomUtil.randomString(TokenConstants.BASE_CHAR_NUMBER, 6);
        if (StrUtil.isNotBlank(code)) {
//            if (code.length() > 10) {
//                throw new ServiceException("code长度过长");
//            }
            randomString = randomString + code;
        }
        final QrCodeDTO qrCodeDTO = new QrCodeDTO();
        if (type.equals(TokenConstants.TOKEN_REGISTER)) {
            randomString = TokenConstants.REG + randomString;
        }
        if (type.equals(TokenConstants.TOKEN_REBIND)) {
            randomString = TokenConstants.REB + randomString;
        }
        if (type.equals(TokenConstants.TOKEN_VERIFY)) {
            randomString = TokenConstants.VER + randomString;
        }
        if (type.equals(TokenConstants.TOKEN_REGISTER_PLUS)) {
            randomString = TokenConstants.REG_PLUS + randomString;
        }
        if (type.equals(TokenConstants.TOKEN_SUB_ACCOUNT_VER_PLUS)) {
            randomString = TokenConstants.SUB_ACCOUNT_VER_PLUS + randomString;
        }
        qrCodeDTO.setTicket(randomString);
        if (type.equals(TokenConstants.TOKEN_REGISTER_PLUS)) {
            qrCodeDTO.setQrcode(String.format(WxConstant.WEIXIN_OAUTH2_URL, wechatConfig.getAppId(), wechatConfig.getRedirectPlusUrl(), randomString));
        } else {
            qrCodeDTO.setQrcode(String.format(WxConstant.WEIXIN_OAUTH2_URL, wechatConfig.getAppId(), wechatConfig.getRedirectUrl(), randomString));
        }

        if (Boolean.TRUE.equals(redisService.setNx(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + randomString, WxChatLoginStatusEnum.WAITING.getCode(), CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT))) {
            return qrCodeDTO;
        }
        throw new Biz200Exception("系统繁忙");
    }

    @Override
    public void bizUserCheckPhone(CheckPhoneDTO dto) {
        BizUser bizUser = bizUserService.getById(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId());
        if (ObjectUtil.isNull(bizUser)) {
            throw new ServiceException("登录账号错误，系统不存在当前登录账号");
        }
        if (StrUtil.isBlank(bizUser.getPhone())) {
            throw new ServiceException("登录账号手机号为空！");
        }
        if (!bizUser.getPhone().equals(dto.getPhone())) {
            throw new ServiceException("手机号错误，请重新填写~");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBizUserPhone(UpdatePhoneDTO dto) {
        updateBizUserPhoneCheck(dto);

        BizUser presentBizUser = bizUserService.getById(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId());
        bizUserService.setPhone(presentBizUser.getUnionid(), dto.getPhone());
    }

    private void updateBizUserPhoneCheck(UpdatePhoneDTO dto) {
        if (!smsService.verifyCode(dto.getPhone(), dto.getPhoneCaptcha())) {
            throw new ServiceException("验证码错误");
        }

        checkPhone(dto.getPhone());
    }

    @Override
    public void checkPhone(String phone) {
        BizUser bizUser = bizUserService.getByPhone(phone);
        if (ObjectUtil.isNotNull(bizUser)) {
            throw new ServiceException("该手机号已注册，无法变更");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateBizUserWeChat(UpdateWeChatDTO dto) {

        self.checkAccount(dto.getAccountTicket(), SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount());
        ExternalContactInfoDTO infoByTicket = SpringUtils.getBean(WechatService.class).getInfoByTicket(dto.getTicket());
        BizUser bizUserByUnionId = bizUserService.getByUnionId(infoByTicket.getUnionid());
        if (ObjectUtil.isNotNull(bizUserByUnionId)) {
            throw new ServiceException("微信号已存在");
        }
        BizUser presentBizUser = bizUserService.getById(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId());
        bizUserService.updateWeChat(presentBizUser.getId(), infoByTicket);

        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setNickName(infoByTicket.getName());
        businessAccountTokenDTO.setPic(infoByTicket.getAvatar());
        businessAccountTokenDTO.setUnionid(infoByTicket.getUnionid());
        businessAccountTokenDTO.setExternalUserId(infoByTicket.getExternalUserid());
        tokenService.updateLoginBusiness(List.of(presentBizUser.getId()), businessAccountTokenDTO);

        return infoByTicket.getName();
    }


    @Override
    public void bizUserUpdateName(UpdateNameDTO dto) {
        bizUserService.updateById(BizUser.builder().id(SecurityUtils.getLoginUser().getBizUserId()).name(dto.getName()).phone(dto.getPhone()).build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BusinessAccountDTO dto) {
        if (ObjectUtil.isNull(dto.getId()) && StrUtil.isBlank(dto.getAccount())) {
            throw new ServiceException("修改商家账号失败，修改条件不能都为空");
        }
        self.lambdaUpdate()
                .set(ObjectUtil.isNotNull(dto.getLastLoginTime()), BusinessAccount::getLastLoginTime, dto.getLastLoginTime())
                .set(ObjectUtil.isNotNull(dto.getStatus()), BusinessAccount::getStatus, dto.getStatus())
                .set(StrUtil.isNotBlank(dto.getName()), BusinessAccount::getName, dto.getName())
                .set(StrUtil.isNotBlank(dto.getPassword()), BusinessAccount::getPassword, dto.getPassword())
                .set(ObjectUtil.isNotNull(dto.getUserId()), BusinessAccount::getBizUserId, dto.getUserId())
                .eq(ObjectUtil.isNotNull(dto.getId()), BusinessAccount::getId, dto.getId())
                .eq(StrUtil.isNotBlank(dto.getAccount()), BusinessAccount::getAccount, dto.getAccount())
                .update();
    }

    /**
     * 禁用账号（商家端使用）
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessAccountStatus(BusinessAccountStatusDTO dto) {
        //禁用账号 如果是主账号
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setOwnerAccount(dto.getAccount());
        Business business = businessService.queryOne(businessDTO);

        if (ObjectUtil.isNotNull(business)) {
            throw new ServiceException("无法禁用主账号");
        }
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setAccount(dto.getAccount());
        businessAccountDTO.setStatus(dto.getStatus());
        self.update(businessAccountDTO);


        BusinessAccount businessAccount = baseMapper.getByAccount(dto.getAccount());
        if (StatusEnum.UN_ENABLED.getCode().equals(businessAccount.getStatus()) && ObjectUtil.isNotNull(businessAccount.getBizUserId())) {
            //如果是禁用 则需要清除已登录账号Token
            tokenService.delAccessToken(Arrays.asList(businessAccount.getBizUserId()));
        }

        businessAccountRebindLogMapper.updateRebindLogToUnbind(businessAccount, businessAccount.getBizUserId());
    }


    @Override
    public BigDecimal businessBalanceTotal() {
        return businessMapper.businessBalanceTotal();
    }

    @Override
    public BusinessStatisticsVO businessStatistics() {
        //todo 后续使用缓存处理 在每次更新时处理缓存数据
        BusinessStatisticsVO vo = new BusinessStatisticsVO();
        BusinessStatisticsVO businessStatisticsVO = businessMapper.businessStatistics();
        if (ObjectUtil.isNotNull(businessStatisticsVO)) {
            BeanUtil.copyProperties(businessStatisticsVO, vo);
            vo.setHistoryMemberTotal(vo.getMemberTotal() + vo.getExpireMemberTotal());
        }
        OrderVideoStatisticsVO orderVideoStatisticsVOR = remoteOrderService.orderVideoStatistics(new OrderVideoStatisticsDTO());
        BeanUtil.copyProperties(orderVideoStatisticsVOR, vo);
        return vo;
    }


    /**
     * 用于账户登录时，重新绑定了企业微信 设置外部联系人id
     */
    @Override
    public void setUserExternalUserid(String unionId, String externalUserid) {
        bizUserService.setUserExternalUserid(unionId, externalUserid);
    }


    @Override
    public List<BusinessAccountVO> orderUserList(BusinessAccountDTO dto) {
        LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
        if (loginUser instanceof LoginBusiness) {
            Long businessId = ((LoginBusiness) loginUser).getBusinessAccountVO().getBusinessId();
            dto.setBusinessId(businessId);
        }
        //获取下单人列表
        PageUtils.startPage();
        return baseMapper.list(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessAccount initBusinessSon(BusinessAccountApply businessAccountApply) {
        Business business = businessService.getById(businessAccountApply.getBusinessId());

        Assert.notNull(business, "主账号不存在，无法添加子账号");
        Assert.isTrue(StatusTypeEnum.YES.getCode().equals(business.getMemberType()), "只有会员才能添加子账号");

        BusinessAccount businessAccount = baseMapper.getByBizUserId(businessAccountApply.getBizUserId());
        if (ObjectUtil.isNotNull(businessAccount)) {
            if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsOwnerAccount())) {
                throw new ServiceException("绑定失败！该用户已是主账号，无法成为子账号");
            }
            throw new ServiceException("绑定失败！该用户已是子账号，无法成为子账号");
        }
        Assert.isFalse(remoteService.getValidOrderCount(businessAccountApply.getBizUserId()).compareTo(0L) > 0, "当前账号存在未支付的会员订单，无法成为您的子账号，可让当前账号先行取消会员订单");
        String account = self.initAccount(StatusTypeEnum.NO.getCode(), businessAccountApply.getOwnerAccount(), businessAccountApply.getUnionid());

        BusinessAccount ownerAccount = baseMapper.getByAccount(businessAccountApply.getOwnerAccount());

        BusinessAccount businessAccountSon = new BusinessAccount();
        businessAccountSon.setAccount(account);
        businessAccountSon.setPassword("");
        businessAccountSon.setBusinessId(businessAccountApply.getBusinessId());
        businessAccountSon.setBizUserId(businessAccountApply.getBizUserId());
        businessAccountSon.setOwnerAccount(ownerAccount.getAccount());
        businessAccountSon.setOwnerAccountBizUserId(ownerAccount.getBizUserId());
        businessAccountSon.setIsOwnerAccount(StatusTypeEnum.NO.getCode());
        baseMapper.insert(businessAccountSon);

        bizUserService.updateById(BizUser.builder()
                .id(businessAccountApply.getBizUserId())
                .name(businessAccountApply.getName())
                .accountType(BizUserAccountTypeEnum.ACCOUNT.getCode())
                .connectUserName(
                        businessService.getBusinessOwnerUserContactUserNameByMemberCode(business.getMemberCode()).getContactUserName()
                )
                .build());
        bizUserService.bindUserBusinessSub(business.getMemberCode(), businessAccountApply.getBizUserId());

        //  刷新申请账号的token
        refreshCompanyUserTokenBy(businessAccountApply.getBizUserId(), businessAccountApply.getUnionid());

        return businessAccountSon;
    }

    /**
     * 刷新指定账号的所有token
     */
    private void refreshCompanyUserTokenBy(Long bizUserId, String unionid) {
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setBizUserId(bizUserId);
        BusinessAccountVO businessAccountVO = baseMapper.queryOne(dto);
        if (ObjectUtil.isNull(businessAccountVO)) {
            BizUser bizUser = bizUserService.getByUnionId(unionid);
            businessAccountVO = getInitBusinessAccountVo(bizUser);
        } else {
            businessAccountVO = getBusinessAccountVO(businessAccountVO);
        }

        String accountLoginKey = tokenService.getLoginAccountKey(bizUserId);
        Long aLong = redisService.zCard(accountLoginKey);
        if (StringUtils.isNull(aLong) || aLong.compareTo(0L) == 0){
            return;
        }
        Set<String> userKeys = redisService.getZSet(accountLoginKey, 0, System.currentTimeMillis(), 0, aLong);
        if (CollUtil.isNotEmpty(userKeys)){
            for (String key : userKeys){
                LoginBusiness user = redisService.getCacheObject(key);
                refreshCompanyUserToken(businessAccountVO, user);
            }
        }
    }

    /**
     * 初始化账号
     *
     * @param isOwner
     * @param ownerAccount
     * @param unionid      唯一键做重入次数校验
     * @return
     */
    @Override
    public String initAccount(Integer isOwner, String ownerAccount, String unionid) {
        Long reentrancyCount = 5L;
        Long expireTime = 10L;
        if (redisService.setIncr(CacheConstants.BUSINESS_ACCOUNT_KEY + unionid, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String randomAccount = "";
        if (StatusTypeEnum.YES.getCode().equals(isOwner)) {
            //初始化生成账号信息
            // 2. 使用Snowflake算法生成唯一ID
            long uniqueId = IdUtil.getSnowflakeNextId();
            // 3. 截取唯一ID的最后8位，确保是数字
            randomAccount = String.format("%08d", uniqueId % 100_000_000) + Convert.toStr(RandomUtil.randomLong(100, 999));
        } else {
            if (StrUtil.isBlank(ownerAccount)) {
                throw new ServiceException("未填写主账号，生成子账号失败！");
            }
            randomAccount = ownerAccount.substring(0, 8) + Convert.toStr(RandomUtil.randomLong(100, 999));
        }
        if (StrUtil.isBlank(randomAccount)) {
            throw new ServiceException("生成随机数失败，账号生成失败");
        }

        Long count = self.lambdaQuery()
                .eq(BusinessAccount::getAccount, randomAccount)
                .count();
        if (count.compareTo(0L) > 0) {
            return this.initAccount(isOwner, ownerAccount, unionid);
        }
        return randomAccount;
    }

    /**
     * 检查账号是否是主账号
     *
     * @param ticket  扫码账号
     * @param account 修改账号
     */
    @Override
    public BusinessAccountVO checkAccount(String ticket, String account) {
        Assert.isTrue(StrUtil.isNotBlank(ticket), "账号ticket不能为空");
        String unionId = SpringUtils.getBean(WechatService.class).getUnionIdByTicket(ticket);

        BusinessAccountVO businessAccount = self.getBusinessAccount(unionId);
        Assert.notNull(businessAccount, "不存在对应微信的账号");
        if (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsMock()) && account.equals(businessAccount.getAccount())) {
            return businessAccount;
        }

        //扫码账号商家数据
        Business business = businessService.getById(businessAccount.getBusinessId());
        Assert.notNull(business, "数据有误，账号无商家数据");

        //获取修改账号数据
        BusinessAccount updateAccount = baseMapper.getByAccount(account);
        Assert.notNull(updateAccount, "数据有误，修改账号数据为空~");

        //不能非主账号、非对应账号
        Assert.isTrue(account.equals(businessAccount.getAccount()) ||
                (StatusTypeEnum.YES.getCode().equals(businessAccount.getIsOwnerAccount()) && businessAccount.getBusinessId().equals(updateAccount.getBusinessId())), "账号错误");

        return businessAccount;

    }

    @Override
    public BusinessAccountVO getBusinessAccountOne(BusinessAccountDTO dto) {
        BusinessAccountVO businessAccount = baseMapper.queryOne(dto);
        BusinessAccountVO businessAccountVO = self.getBusinessAccountVO(businessAccount);
        if (ObjectUtil.isNotNull(businessAccountVO) && StatusTypeEnum.YES.getCode().equals(dto.getNeedActivity()) && ObjectUtil.isNotNull(dto.getPackageType())) {
            //加载今日活动
            BusinessMemberActivity activityByTime = businessMemberActivityMapper.getActivityByTime(dto.getPackageType(), DateUtils.getDaysOfMonth(new Date()));
            businessAccountVO.setBusinessMemberActivity(activityByTime);
        }
        return businessAccountVO;
    }

    @Override
    public BusinessMemberActivity getBusinessMemberActivity(Integer packageType) {
        return businessMemberActivityMapper.getActivityByTime(packageType, DateUtils.getDaysOfMonth(new Date()));
    }

    @Override
    public List<BusinessAccountDetailVO> getBusinessAccountDetailVOs(BusinessAccountDetailDTO dto) {
        return baseMapper.getBusinessAccountDetailVOs(dto);
    }

    @Override
    public BusinessAccountVO getBusinessAccountVO(BusinessAccountVO businessAccount) {
        if (ObjectUtil.isNull(businessAccount)) {
            return null;
        }
        businessAccount.setIsMock(StatusTypeEnum.NO.getCode());
        //获取商家信息
        Business business = businessService.getById(businessAccount.getBusinessId());
        if (ObjectUtil.isNull(business)) {
            throw new ServiceException("数据有误，账号无商家数据");
        }
        BusinessAccountVO businessAccountVO = BeanUtil.copyProperties(businessAccount, BusinessAccountVO.class);
        BusinessVO businessVO = BeanUtil.copyProperties(business, BusinessVO.class);
        businessVO.setValidBalance(businessVO.getBalance().subtract(businessVO.getUseBalance()));
        businessVO.setMemberValidityTime(businessVO.getMemberValidity());
        businessAccountVO.setBusinessVO(businessVO);
        businessAccountVO.setOwnerAccount(businessVO.getOwnerAccount());

        //获取子账号数据
        BusinessAccountDTO dto = new BusinessAccountDTO();
        dto.setBusinessId(businessAccount.getBusinessId());
        dto.setIsOwnerAccount(StatusTypeEnum.NO.getCode());
        List<BusinessAccountVO> list = self.list(dto);
        if (CollUtil.isNotEmpty(list)) {
            //排序
            list.sort(Comparator.comparing(BusinessAccountVO::getId).reversed());
            //添加子账号数据
            businessAccountVO.setBusinessAccountVOS(list);
        } else {
            businessAccountVO.setBusinessAccountVOS(new ArrayList<>());
        }

        return businessAccountVO;
    }

    @Override
    public List<BusinessDetailVO> loadBusinessDetail(List<Business> businesses) {
        if (CollUtil.isEmpty(businesses)) {
            return new ArrayList<>();
        }
        List<BusinessDetailVO> businessDetailVos = BeanUtil.copyToList(businesses, BusinessDetailVO.class);
        return loadBusinessVODetail(businessDetailVos);
    }


    @Override
    public List<BusinessDetailVO> loadBusinessVODetail(List<BusinessDetailVO> businessDetailVos) {
        //获取账号数据
        List<Long> businessIds = businessDetailVos.stream().map(BusinessDetailVO::getId).collect(Collectors.toList());
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setBusinessIds(businessIds);
        List<BusinessAccountVO> businessAccounts = self.list(businessAccountDTO);
        if (CollUtil.isEmpty(businessAccounts)) {
            throw new ServiceException("数据有误，商家不存在账号，请联系管理员！");
        }
        //获取商家子账号数量（包含主账号）
        //方法三个地方存在使用，不干扰
        List<BusinessAccountVO> filterList = businessAccounts.stream().filter(ba -> null != ba.getBizUserId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(filterList)) {
            Map<Long, Long> countMap = filterList.stream().collect(Collectors.groupingBy(BusinessAccountVO::getBusinessId, Collectors.counting()));
            Map<String, BusinessAccountVO> businessAccountMap = businessAccounts.stream().collect(Collectors.toMap(BusinessAccountVO::getAccount, p -> p));
            //填充对接客服
            List<Long> userIds = businessDetailVos.stream().map(BusinessDetailVO::getWaiterId).collect(Collectors.toList());
            //  获取用户map
            SysUserListDTO dto = new SysUserListDTO();
            dto.setUserId(userIds);
            Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

            //分销渠道Id列表
            List<Long> distributionChannelId = new ArrayList<>();
            //市场渠道Id列表
            List<Long> marketingChannelId = new ArrayList<>();
            //填充子账号数量、微信昵称
            businessDetailVos.forEach(item -> {
                Long accountNum = Optional.ofNullable(countMap.get(item.getId())).map(count -> count - 1).orElse(0L);
                BusinessAccountVO businessAccountVO = Optional.ofNullable(businessAccountMap.get(item.getOwnerAccount())).orElse(new BusinessAccountVO());
                UserVO userVO = Optional.ofNullable(userMap.get(item.getWaiterId())).orElse(new UserVO());
                item.setAccountNum(Convert.toInt(accountNum));
                item.setNickName(Optional.ofNullable(businessAccountVO.getNickName()).orElse(""));
                item.setAccountName(Optional.ofNullable(businessAccountVO.getName()).orElse(""));
                item.setPhone(Optional.ofNullable(businessAccountVO.getPhone()).orElse(""));
                item.setWaiterName(Optional.ofNullable(userVO.getName()).orElse(""));
                item.setOrderNum(0);
                item.setRecentOrderNum(0);
                item.setPreFinishOrderNum(0);
                if (StrUtil.isNotBlank(item.getSeedCode())) {
                    item.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
                } else if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(item.getRegisterChannelType())) {
                    distributionChannelId.add(item.getRegisterChannelId());
                    item.setChannelType(ChannelTypeEnum.DISTRIBUTION.getCode());
                } else if (ChannelTypeEnum.MARKETING.getCode().equals(item.getRegisterChannelType())) {
                    marketingChannelId.add(item.getRegisterChannelId());
                    item.setChannelType(ChannelTypeEnum.MARKETING.getCode());
                } else if (ChannelTypeEnum.WEBSITE.getCode().equals(item.getRegisterChannelType())) {
                    item.setChannelType(ChannelTypeEnum.WEBSITE.getCode());
                } else {
                    item.setChannelType(ChannelTypeEnum.NORMAL.getCode());
                }
            });
            Map<Long, DistributionChannel> distributionChannelMap = new HashMap<>();
            Map<Long, MarketingChannel> marketingChannelMap = new HashMap<>();
            if (CollUtil.isNotEmpty(distributionChannelId)) {
                List<DistributionChannel> distributionChannels = channelCore.getChannelByIds(distributionChannelId);
                if (CollUtil.isNotEmpty(distributionChannels)) {
                    distributionChannelMap = distributionChannels.stream().collect(Collectors.toMap(DistributionChannel::getId, p -> p));
                }
            }
            if (CollUtil.isNotEmpty(marketingChannelId)) {
                List<MarketingChannel> marketingChannels = channelCore.getMarketingChannelsByIds(marketingChannelId);
                if (CollUtil.isNotEmpty(marketingChannels)) {
                    marketingChannelMap = marketingChannels.stream().collect(Collectors.toMap(MarketingChannel::getId, p -> p));
                }
            }

            //账号列表
            OrderVideoStatisticsDTO orderVideoStatisticsDTO = new OrderVideoStatisticsDTO();
            orderVideoStatisticsDTO.setBusinessIds(businessIds);

            //获取商家订单统计数据
            List<OrderVideoStatisticsDetailVO> data = remoteOrderService.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
            if (CollUtil.isEmpty(data)) {
                return businessDetailVos;
            }
            Map<Long, OrderVideoStatisticsDetailVO> orderStatisticsMap = data.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, p -> p));

            for (BusinessDetailVO item : businessDetailVos) {
                OrderVideoStatisticsDetailVO orderVideoStatisticsDetailVO = Optional.ofNullable(orderStatisticsMap.get(item.getId())).orElse(new OrderVideoStatisticsDetailVO());
                item.setOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getOrderVideoTotal()).orElse(0));
                item.setRecentOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getRecentOrderTotal()).orElse(0));
                item.setPreFinishOrderNum(Optional.ofNullable(orderVideoStatisticsDetailVO.getPreFinishOrderTotal()).orElse(0));
                item.setAfterSaleRate(Optional.ofNullable(orderVideoStatisticsDetailVO.getAfterSaleRate()).orElse(BigDecimal.ZERO));
                if (StrUtil.isBlank(item.getChannelName())) {
                    if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(item.getRegisterChannelType())) {
                        DistributionChannel distributionChannel = Optional.ofNullable(distributionChannelMap.get(item.getRegisterChannelId())).orElse(new DistributionChannel());
                        item.setChannelName(Optional.ofNullable(distributionChannel.getChannelName()).orElse(""));
                    } else if (ChannelTypeEnum.MARKETING.getCode().equals(item.getRegisterChannelType())) {
                        MarketingChannel marketingChannel = Optional.ofNullable(marketingChannelMap.get(item.getRegisterChannelId())).orElse(new MarketingChannel());
                        item.setChannelName(Optional.ofNullable(marketingChannel.getMarketingChannelName()).orElse(""));
                    }
                }
            }
            //查询订单数据
            return businessDetailVos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<BusinessDetailExportVO> businessListExport(List<BusinessDetailVO> businessDetailVOS) {
        if (CollUtil.isEmpty(businessDetailVOS)) {
            return Collections.emptyList();
        }
        List<BusinessDetailExportVO> resultList = new ArrayList<>();

        List<Long> businessIds = businessDetailVOS.stream().map(BusinessDetailVO::getId).collect(Collectors.toList());

        List<BusinessMemberValidityFlowVO> businessMemberValidityFlowVOS = businessMemberValidityFlowService.queryList(BusinessMemberValidityFlowDTO.builder().businessIds(businessIds).type(MemberValidTypeEnum.MANAGER.getCode()).build());

        Map<Long, List<BusinessMemberValidityFlowVO>> businessMemberValidityMaps = new HashMap<>();
        if (CollUtil.isNotEmpty(businessMemberValidityFlowVOS)) {
            businessMemberValidityMaps = businessMemberValidityFlowVOS.stream().collect(Collectors.groupingBy(BusinessMemberValidityFlowVO::getBusinessId));
        }

        for (BusinessDetailVO item : businessDetailVOS) {
            List<BusinessMemberValidityFlowVO> businessMemberValidityFlows = businessMemberValidityMaps.get(item.getId());

            BusinessDetailExportVO businessDetailExportVO = BeanUtil.copyProperties(item, BusinessDetailExportVO.class);

            businessDetailExportVO.setUpdateRemark(getBusinessExportRemarkFlow(businessMemberValidityFlows));
            resultList.add(businessDetailExportVO);
        }

        return resultList;
    }

    private String getBusinessExportRemarkFlow(List<BusinessMemberValidityFlowVO> businessMemberValidityFlows) {
        if (CollUtil.isEmpty(businessMemberValidityFlows)) {
            return "";
        }
        //XXX在2024-12-02 10:53:57进行修改，备注：XXXX
        StringBuilder stringBuilder = new StringBuilder();
        for (BusinessMemberValidityFlowVO item : businessMemberValidityFlows) {
            stringBuilder.append(item.getCreateBy());
            stringBuilder.append("在");
            stringBuilder.append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getCreateTime()));
            stringBuilder.append("进行修改，备注：");
            stringBuilder.append(item.getRemark());
            stringBuilder.append(StrPool.LF);
        }
        return stringBuilder.toString();
    }

    @Override
    public List<ResidentBusinessVO> queryResidentBusiness(ResidentBusinessDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<ResidentBusinessVO> residentBusinessVOS = businessService.queryResidentBusiness(dto);
        if (CollUtil.isEmpty(residentBusinessVOS)) {
            return Collections.emptyList();
        }
        //加载商家客服名称
        List<Long> waiterIds = residentBusinessVOS.stream().map(ResidentBusinessVO::getWaiterId).filter(item -> ObjectUtil.isNotNull(item)).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(waiterIds)) {
            return residentBusinessVOS;
        }
        //  获取用户map
        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(waiterIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(sysUserListDTO);

        residentBusinessVOS.forEach(item -> {
            UserVO userVO = Optional.ofNullable(userMap.get(item.getWaiterId())).orElse(new UserVO());
            item.setWaiterName(Optional.ofNullable(userVO.getName()).orElse(""));
        });
        return residentBusinessVOS;
    }

    @Override
    public List<ResidentBusinessExportVO> exportResidentBusinessList(ResidentBusinessDTO dto) {
        List<ResidentBusinessVO> residentBusinessVOS = this.queryResidentBusiness(dto);
        if (CollUtil.isEmpty(residentBusinessVOS)) {
            return Collections.emptyList();
        }

        List<ResidentBusinessExportVO> residentBusinessExportList = new ArrayList<>();
        Map<Integer, String> map = Map.of(PackageTypeEnum.QUARTER.getCode(), "季度会员",
                PackageTypeEnum.YEAR.getCode(), "年度会员",
                PackageTypeEnum.TRIENNIUM.getCode(), "三年会员");
        for (ResidentBusinessVO item : residentBusinessVOS) {
            ResidentBusinessExportVO residentBusinessExportVO = BeanUtil.copyProperties(item, ResidentBusinessExportVO.class);
            residentBusinessExportVO.setMemberFirst(StrUtil.builder()
                    .append(map.get(item.getMemberFirstType())).append(StrPool.LF)
                    .append(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, item.getMemberFirstTime()))
                    .toString());
            residentBusinessExportList.add(residentBusinessExportVO);
        }
        return residentBusinessExportList;
    }

    @Override
    public List<BusinessBalanceVO> businessBalanceList(BusinessDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("balance", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        //获取主表数据
        List<Business> businesses = businessService.queryList(dto);
        if (CollUtil.isEmpty(businesses)) {
            return new ArrayList<>();
        }
        return self.loadBusinessAccount(businesses);
    }

    @Override
    public List<BusinessBalanceExportVO> exportBusinessBalanceExportList(List<BusinessBalanceVO> businessBalanceVOS) {
        if (CollUtil.isEmpty(businessBalanceVOS)) {
            return Collections.emptyList();
        }
        List<Long> businessIds = businessBalanceVOS.stream().map(BusinessBalanceVO::getId).collect(Collectors.toList());
        //获取余额支付统计数据
        //获取提现统计
        //获取预付统计
        List<BusinessBalanceFlowStatisticsVO> statistics = businessBalanceFlowService.statistics(businessIds);
        Map<Long, BusinessBalanceFlowStatisticsVO> businessBalanceFlowStatisticsVOMap = new HashMap<>();

        if (CollUtil.isNotEmpty(statistics)) {
            for (BusinessBalanceFlowStatisticsVO item : statistics) {
                businessBalanceFlowStatisticsVOMap.put(item.getBusinessId(), item);
            }
        }


        List<BusinessBalanceExportVO> resultList = new ArrayList<>();
        for (BusinessBalanceVO item : businessBalanceVOS) {
            BusinessBalanceExportVO businessBalanceExportVO = BeanUtil.copyProperties(item, BusinessBalanceExportVO.class);

            BusinessBalanceFlowStatisticsVO orDefault = businessBalanceFlowStatisticsVOMap.getOrDefault(item.getId(), BusinessBalanceFlowStatisticsVO.builder()
                    .balancePayTotal(BigDecimal.ZERO)
                    .withdrawTotal(BigDecimal.ZERO)
                    .prepayAmountTotal(BigDecimal.ZERO)
                    .payoutTotal(BigDecimal.ZERO)
                    .build());
            businessBalanceExportVO.setPrepayAmountTotal(orDefault.getPrepayAmountTotal());
            businessBalanceExportVO.setBalancePayTotal(orDefault.getBalancePayTotal().abs());
            businessBalanceExportVO.setWithdrawTotal(orDefault.getWithdrawTotal().abs());
            businessBalanceExportVO.setPayoutTotal(orDefault.getPayoutTotal());
            resultList.add(businessBalanceExportVO);
        }

        return resultList;
    }

    @Override
    public List<BusinessBalanceVO> loadBusinessAccount(List<Business> businesses) {
        if (CollUtil.isEmpty(businesses)) {
            return new ArrayList<>();
        }
        List<BusinessBalanceVO> businessDetailVos = BeanUtil.copyToList(businesses, BusinessBalanceVO.class);

        //获取账号数据
        List<String> accountList = businessDetailVos.stream().map(BusinessBalanceVO::getOwnerAccount).collect(Collectors.toList());
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setAccountList(accountList);
        List<BusinessAccountVO> businessAccounts = self.list(businessAccountDTO);
        if (CollUtil.isEmpty(businessAccounts)) {
            return new ArrayList<>();
        }
        Map<String, BusinessAccountVO> businessAccountMap = businessAccounts.stream().collect(Collectors.toMap(BusinessAccountVO::getAccount, p -> p));
        //填充子账号数量、微信昵称
        businessDetailVos.forEach(item -> {
            BusinessAccountVO businessAccountVO = businessAccountMap.get(item.getOwnerAccount());
            if (ObjectUtil.isNotNull(businessAccountVO)) {
                item.setNickName(StrUtil.isNotBlank(businessAccountVO.getName()) ? businessAccountVO.getName() : businessAccountVO.getNickName());
            }
            item.setValidBalance(item.getBalance().subtract(item.getUseBalance()));
        });
        return businessDetailVos;
    }

    @Override
    public void refreshToken() {
        if (ObjectUtil.isNull(SecurityUtils.getLoginUserType())) {
            //如果没有登录信息 则不需要更新
            return;
        }
        if (SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER)) {
            BusinessAccountDTO dto = new BusinessAccountDTO();
            dto.setBizUserId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBizUserId());
            BusinessAccountVO businessAccount = baseMapper.queryOne(dto);
            if (ObjectUtil.isNull(businessAccount)) {
                BizUser bizUser = bizUserService.getByUnionId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getUnionid());
                businessAccount = getInitBusinessAccountVo(bizUser);
            } else {
                businessAccount = getBusinessAccountVO(businessAccount);
            }

            LoginBaseEntity loginUser = SecurityUtils.getLoginUser();
            refreshCompanyUserToken(businessAccount, loginUser);
        }
    }

    /**
     * 刷新指定商家用戶token
     */
    private void refreshCompanyUserToken(BusinessAccountVO businessAccount, LoginBaseEntity loginUser) {
        if (ObjectUtil.isNull(loginUser)) {
            return;
        }
        final LoginBusiness login = new LoginBusiness(businessAccount);
        login.setBaseEntity(loginUser);
        SecurityContextHolder.set(SecurityConstants.LOGIN_USER, login);
        tokenService.setLoginUser(login);
    }

    @Override
    public String getPhone(String phone) {
        if (StrUtil.isBlank(phone)) {
            return phone;
        }
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 企业微信事件回调，解除了企微好友
     */
    @Override
    public void cleanUserExternalUserid(String externalUserId) {
        bizUserService.cleanUserExternalUserid(externalUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessStatus(BusinessStatusDTO dto) {
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setOwnerAccount(dto.getOwnerAccount());
        Business business = businessService.queryOne(businessDTO);

        if (ObjectUtil.isNull(business)) {
            throw new ServiceException("不存在对应商家数据，account = " + dto.getOwnerAccount());
        }
        //判断是否存在这条数据
        BusinessDTO businessDto = new BusinessDTO();
        businessDto.setOwnerAccount(dto.getOwnerAccount());
        businessDto.setStatus(dto.getStatus());
        businessService.update(businessDto);

        if (StatusEnum.UN_ENABLED.getCode().equals(dto.getStatus())) {
            //如果是禁用 则需要清除已登录账号Token
            BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
            businessAccountDTO.setBusinessId(business.getId());
            List<BusinessAccountVO> businessAccounts = self.queryList(businessAccountDTO);
            List<Long> bizUserIds = businessAccounts.stream().filter(item -> ObjectUtil.isNotNull(item.getBizUserId())).map(BusinessAccountVO::getBizUserId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(bizUserIds)) {
                tokenService.delAccessToken(bizUserIds);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessRemark(BusinessRemarkDTO dto) {
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setOwnerAccount(dto.getOwnerAccount());
        Business business = businessService.queryOne(businessDTO);

        if (ObjectUtil.isNull(business)) {
            throw new ServiceException("不存在对应商家数据，account = " + dto.getOwnerAccount());
        }

        BusinessDTO businessDto = new BusinessDTO();
        businessDto.setOwnerAccount(dto.getOwnerAccount());
        businessDto.setRemark(dto.getRemark());
        businessService.update(businessDto);
        //加入备注流水
        businessRemarkFlowService.save(BusinessRemarkFlow.builder()
                .businessId(business.getId())
                .remark(dto.getRemark())
                .createBy(SecurityUtils.getUsername())
                .createById(SecurityUtils.getUserId())
                .creatTime(new Date())
                .build());
    }

    @Override
    public List<BusinessRemarkFlowVO> getListByBusinessId(Long businessId) {
        return businessRemarkFlowService.getListByBusinessId(businessId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusiness(EditBusinessDTO dto) {
        Assert.isFalse(CustomerTypeEnum.COMMON.getCode().equals(dto.getCustomerType()), "商家不能修改重要程度为普通状态");

        BusinessDTO businessDto = new BusinessDTO();
        businessDto.setOwnerAccount(dto.getOwnerAccount());
        final Business business = businessService.queryOne(businessDto);
        BusinessDTO businessDTO = BeanUtil.copyProperties(dto, BusinessDTO.class);

        businessService.update(businessDTO);
        if (ObjectUtil.isNull(businessDTO.getScale())) {
            businessService.update(new LambdaUpdateWrapper<Business>()
                    .set(Business::getScale, null)
                    .eq(Business::getOwnerAccount, dto.getOwnerAccount())
            );
        }
        Business resultBusiness = BeanUtil.copyProperties(business, Business.class);
        resultBusiness.setName(businessDTO.getName())
                .setPhoneVisible(businessDTO.getPhoneVisible())
                .setIsProxy(businessDTO.getIsProxy())
                .setCustomerType(businessDTO.getCustomerType())
                .setWaiterId(businessDTO.getWaiterId())
        ;
        businessOperLogService.saveLog(business, resultBusiness);

        //修改登录账号数据
        BusinessAccountVO businessAccountVO = baseMapper.queryOne(BusinessAccountDTO.builder().account(business.getOwnerAccount()).build());
        Assert.notNull(businessAccountVO, "登录主账号不能为空");
        bizUserService.updateUserBusinessInfo(BizUserBusinessInfoDTO.builder()
                .id(businessAccountVO.getBizUserId())
                .customerType(dto.getCustomerType())
                .waiterId(dto.getWaiterId())
                .build());

        BusinessAccountTokenDTO businessAccountTokenDTO = BeanUtil.copyProperties(businessDTO, BusinessAccountTokenDTO.class);
        businessAccountTokenDTO.setBusinessName(dto.getName());
        businessAccountTokenDTO.setName(null);
        self.updateLoginBusiness(Arrays.asList(business.getId()), businessAccountTokenDTO);

        if (business.getWaiterId() != null && business.getWaiterId().compareTo(dto.getWaiterId()) == 0) {
            return;
        }
        UpdateOrderContactDTO updateOrderContactDTO = new UpdateOrderContactDTO();
        updateOrderContactDTO.setBusinessIds(Collections.singletonList(business.getId()));
        updateOrderContactDTO.setContactId(dto.getWaiterId());
        remoteService.updateOrderVideoContact(updateOrderContactDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberValidity(EditMemberValidityDTO dto) {
        final Business business = businessService.getById(dto.getId());
        Assert.notNull(business, "商家不存在！");
        //根据商家信息判断是否能够七天无理由
        if (BusinessValidityChangeReasonTypeEnum.SEVEN_DAYS_WITHOUT_REASON.getCode().equals(dto.getChangeReasonType())) {
            Assert.isTrue(DateUtils.getDatePoorDay(new Date(), dto.getMemberValidity()) == 1, "七天无理由会员有效期需要是昨天~");
            checkUnableSettlement(business.getId(), true);
        }

        BusinessDTO businessDTO = BeanUtil.copyProperties(dto, BusinessDTO.class);
        if (ObjectUtil.isNotNull(dto.getMemberValidity())) {
            Assert.isFalse(dto.getMemberValidity().compareTo(business.getMemberValidity()) == 0, "会员到期时间不能相同");
            if (StrUtil.isBlank(business.getMemberCode())) {
                throw new ServiceException("非会员不能设置订单到期时间");
            }
            if (DateUtils.getDatePoorDay(DateUtils.getStartOfDay(DateUtil.offsetDay(dto.getMemberValidity(), 1)), new Date()) > BusinessConstants.preTimeout) {
                //会员
                businessDTO.setMemberStatus(MemberTypeEnum.RECHARGE.getCode());
                businessDTO.setMemberType(StatusTypeEnum.YES.getCode());
                //更新企业微信备注数据
                asyncUpdateWorkWechatRemark(business, UpdateWechatRemarkType.VIP);
            } else if (DateUtils.getDatePoorDay(DateUtils.getStartOfDay(dto.getMemberValidity()), new Date()) >= 0) {
                //即将过期会员
                businessDTO.setMemberStatus(MemberTypeEnum.NO_EXPIRE.getCode());
                businessDTO.setMemberType(StatusTypeEnum.YES.getCode());
                asyncUpdateWorkWechatRemark(business, UpdateWechatRemarkType.VIP);
            } else {
                //非会员
                businessDTO.setMemberStatus(MemberTypeEnum.EXPIRE.getCode());
                businessDTO.setMemberType(StatusTypeEnum.NO.getCode());
                //更新企业微信备注数据
                asyncUpdateWorkWechatRemark(business, UpdateWechatRemarkType.EXPIRE);
            }
        }
        businessDTO.setRemark(null);

        //根据渠道结算数据进行七天无理由操作
        if (BusinessValidityChangeReasonTypeEnum.SEVEN_DAYS_WITHOUT_REASON.getCode().equals(dto.getChangeReasonType())) {
            DistributionChannel distributionChannel = channelCore.getDistributionChannelEntityBySeedCode(business.getSeedCode());
            if (ObjectUtil.isNotNull(distributionChannel)) {
                memberSeedRecordService.memberChannelUnableSettlement(business.getId());
            }
            remoteService.banMemberInvoice(business.getId());
            businessDTO.setRechargeCount(business.getRechargeCount() - 1);
        }

        businessService.update(businessDTO);
        BusinessMemberValidityFlowVO relationOrder = businessMemberValidityFlowService.getRelationOrder(business.getId());
        Assert.notNull(relationOrder, "关联订单不能为空~");
        businessMemberValidityFlowService.save(BusinessMemberValidityFlow.builder()
                .businessId(business.getId())
                .orderNum(relationOrder.getOrderNum())
                .originMemberValidity(business.getMemberValidity())
                .resultMemberValidity(dto.getMemberValidity())
                .remark(dto.getRemark())
                .changeReasonType(dto.getChangeReasonType())
                .memberPackageType(relationOrder.getMemberPackageType())
                .realPayAmount(relationOrder.getRealPayAmount())
                .realPayAmountCurrency(relationOrder.getRealPayAmountCurrency())
                .currency(relationOrder.getCurrency())
                .createBy(SecurityUtils.getUsername())
                .createById(SecurityUtils.getUserId())
                .build());

        BusinessAccountTokenDTO businessAccountTokenDTO = BeanUtil.copyProperties(businessDTO, BusinessAccountTokenDTO.class);
        self.updateLoginBusiness(Arrays.asList(business.getId()), businessAccountTokenDTO);
        businessService.updateBusinessCallback(business.getId());
    }

    @Override
    public CheckUnableSettlementVO checkUnableSettlement(Long businessId, boolean isInner) {
        Business business = businessService.getById(businessId);
        Assert.notNull(business, "商家不存在");

        //只要发生七天无理由 无法再次发生
        List<BusinessMemberValidityFlowVO> businessMemberValidityFlowVOS = businessMemberValidityFlowService.queryList(BusinessMemberValidityFlowDTO.builder()
                .businessId(businessId)
                .changeReasonType(BusinessValidityChangeReasonTypeEnum.SEVEN_DAYS_WITHOUT_REASON.getCode())
                .build());
        if (CollUtil.isNotEmpty(businessMemberValidityFlowVOS)) {
            if (isInner) {
                throw new ServiceException("不支持发起7天无理由");
            }
            return CheckUnableSettlementVO.builder().canApply(false).reason(1).build();
        }

        if (DateUtil.betweenDay(business.getMemberFirstTime(), DateUtil.date(), false) >= 7) {
            return CheckUnableSettlementVO.builder().canApply(false).reason(3).build();
        }

        if (Boolean.FALSE.equals(!remoteService.hasValidVideoOrderByBusinessId(businessId, DateUtil.format(business.getMemberFirstTime(), DatePattern.NORM_DATETIME_PATTERN)))) {
            if (isInner) {
                throw new ServiceException("当前商家存在进行中订单，不支持选择7天无理由");
            }
            return CheckUnableSettlementVO.builder().canApply(false).reason(2).build();
        }
        return CheckUnableSettlementVO.builder().canApply(true).build();
    }

    /**
     * 只能在数据不变情况下进行
     * @param business
     * @param updateWechatRemarkType
     */
    private void asyncUpdateWorkWechatRemark(Business business, UpdateWechatRemarkType updateWechatRemarkType) {
        //数据需要再异步之前处理 防止脏读
        List<BizUser> bizUserList = bizUserService.getBizUserByBusinessId(business.getId());
        ThreadUtil.execAsync(() -> {
            if (CollUtil.isEmpty(bizUserList)){
                return;
            }
            Map<String, String> accountmap = new HashMap<>();
            Map<String, BizUserAccountTypeEnum> accountTypeEnumMap = new HashMap<>();
            for (BizUser item : bizUserList){
                accountmap.put(item.getExternalUserId(), business.getMemberCode());
                accountTypeEnumMap.put(item.getExternalUserId(), BizUserAccountTypeEnum.getByCode(item.getAccountType()));
            }

            try {
                SpringUtils.getBean(WechatService.class).updateAccountRemarkBatch(
                        accountmap,
                        updateWechatRemarkType,
                        accountTypeEnumMap
                );
            } catch (Exception e) {
                log.error("更新企业微信备注数据,商家编码:{},异常:{}", business.getMemberCode(), e.getMessage());
            }
        });
    }

    @Override
    public List<BusinessMemberValidityFlowVO> getBusinessMemberValidityFlowListByBusinessId(Long businessId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        //获取商家调整会员有效数据
        return businessMemberValidityFlowService.queryList(BusinessMemberValidityFlowDTO.builder().businessId(businessId).type(MemberValidTypeEnum.MANAGER.getCode()).build());
    }

    @Override
    public List<BusinessOwnerFlowVO> getOwnerFlowListByBusinessId(Long businessId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bof.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        //获取商家调整会员有效数据
        return businessOwnerFlowService.getListByBusinessId(businessId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchangeBindOwner(UnBindOwnerDTO dto) {
        //获取商家账号数据
        BusinessAccountVO businessAccountVO = businessAccountMapper.queryOne(BusinessAccountDTO.builder()
                .businessId(dto.getBusinessId())
                .isOwnerAccount(StatusTypeEnum.YES.getCode())
                .build());
        Assert.notNull(businessAccountVO, "商家主账号数据不能为空~");
        Business originBusiness = businessService.getById(dto.getBusinessId());
        if (businessAccountVO.getId().equals(dto.getAccountId())){
            throw new ServiceException("页面状态异常，请刷新后重试~");
        }
        BusinessAccountVO newAccount = businessAccountMapper.queryOne(BusinessAccountDTO.builder().id(dto.getAccountId()).businessId(dto.getBusinessId()).build());
        Assert.notNull(newAccount, "换绑账号不能为空~");
        Assert.isTrue(newAccount.getUserStatus().compareTo(StatusEnum.UN_ENABLED.getCode()) != 0, "您的账户已被禁用!");
        Assert.isTrue(newAccount.getBusinessId().equals(dto.getBusinessId()), "换绑账号不属于该商家");
        //换绑商家账号
        List<BusinessAccount> businessAccounts = businessAccountMapper.selectListByBusinessId(businessAccountVO.getBusinessId());
        for (BusinessAccount businessAccount : businessAccounts) {
            businessAccount.setOwnerAccount(newAccount.getAccount());
            businessAccount.setOwnerAccountBizUserId(newAccount.getBizUserId());
            if (businessAccount.getId().equals(businessAccountVO.getId())) {
                businessAccount.setIsOwnerAccount(StatusTypeEnum.NO.getCode());
                businessAccount.setStatus(StatusEnum.ENABLED.getCode());
            } else if (businessAccount.getId().equals(newAccount.getId())) {
                businessAccount.setIsOwnerAccount(StatusTypeEnum.YES.getCode());
                businessAccount.setStatus(StatusEnum.ENABLED.getCode());
            }
        }
        baseMapper.updateBatchById(businessAccounts);

        //修改商家主账号
        Business business = new Business();
        business.setId(dto.getBusinessId());
        business.setOwnerAccount(newAccount.getAccount());
        businessService.updateById(business);

        List<BizUser> bizUsers = new ArrayList<>();
        //修改登录账号状态
        BizUser originUser = new BizUser();
        originUser.setId(businessAccountVO.getBizUserId());
        originUser.setAccountType(BizUserAccountTypeEnum.ACCOUNT.getCode());

        BizUser bindBizUser = new BizUser();
        bindBizUser.setId(newAccount.getBizUserId());
        bindBizUser.setAccountType(BizUserAccountTypeEnum.OWNER_ACCOUNT.getCode());
        bindBizUser.setCustomerType(originBusiness.getCustomerType());
        bindBizUser.setWaiterId(originBusiness.getWaiterId());
        bizUsers.add(originUser);
        bizUsers.add(bindBizUser);
        bizUserService.updateBatchById(bizUsers);

        //保存日志
        BusinessOwnerFlow businessOwnerFlow = new BusinessOwnerFlow();
        businessOwnerFlow.setBusinessId(dto.getBusinessId());
        businessOwnerFlow.setOriginAccountId(businessAccountVO.getId());
        businessOwnerFlow.setOriginAccountNickName(businessAccountVO.getNickName());
        businessOwnerFlow.setOriginAccountName(businessAccountVO.getName());
        businessOwnerFlow.setAccountId(newAccount.getId());
        businessOwnerFlow.setAccountNickName(newAccount.getNickName());
        businessOwnerFlow.setAccountName(newAccount.getName());

        businessOwnerFlowService.saveEntity(businessOwnerFlow);

        //copy:indingLoginUser 主账号变成子账号 加入流水
        BusinessAccountRebindLog log = new BusinessAccountRebindLog();
        log.setName(businessAccountVO.getName());
        log.setNickName(businessAccountVO.getNickName());
        log.setPhone(businessAccountVO.getPhone());
        log.setConnectUserName(businessAccountVO.getConnectUserName());
        log.setBusinessId(originBusiness.getId());
        log.setBizUserId(businessAccountVO.getBizUserId());
        log.setAccount(businessAccountVO.getAccount());
        //获取当前坑位状态
        log.setStatus(0);
        //在坑位重新绑定的直接是审核通过
        log.setAuditStatus(AuditStatusEnum.APPROVE.getCode());
        log.setAuditTime(new Date());
        businessAccountRebindLogMapper.insert(log);

        //成为主账号的子账号需要解绑数据
        businessAccountRebindLogMapper.unbind(newAccount.getAccount(), originBusiness.getId(), newAccount.getBizUserId());

        //BusinessAccountVO businessAccountVO  BusinessAccountVO newAccount
        Map<String, String> accountmap = new HashMap<>();
        Map<String, BizUserAccountTypeEnum> accountTypeEnumMap = new HashMap<>();
        accountmap.put(businessAccountVO.getExternalUserId(), originBusiness.getMemberCode());
        accountTypeEnumMap.put(businessAccountVO.getExternalUserId(), BizUserAccountTypeEnum.ACCOUNT);
        accountmap.put(newAccount.getExternalUserId(), originBusiness.getMemberCode());
        accountTypeEnumMap.put(newAccount.getExternalUserId(), BizUserAccountTypeEnum.OWNER_ACCOUNT);
        ThreadUtil.execAsync(() ->{
            SpringUtils.getBean(WechatService.class).updateAccountRemarkBatch(accountmap, UpdateWechatRemarkType.VIP, accountTypeEnumMap);
        });
    }

    @Override
    public List<BusinessMemberValidityFlowVO> getBusinessMemberValidFlowList(BusinessMemberValidityFlowDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        //获取商家调整会员有效数据
        return businessMemberValidityFlowService.queryList(dto);

    }

    @Override
    public BusinessMemberValidityFlowVO getRelationOrder(Long businessId) {
        return businessMemberValidityFlowService.getRelationOrder(businessId);
    }

    @Override
    public List<BusinessMemberValidityFlowExportVO> getBusinessMemberValidFlowExportList(BusinessMemberValidityFlowDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("b.create_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bmvf.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<BusinessMemberValidityFlowVO> businessMemberValidFlowList = businessMemberValidityFlowService.queryList(dto);
        //订单号、交易流水号、支付方式、订单总价、余额支付、剩余支付、实际支付字段、支付时间
        if (CollUtil.isEmpty(businessMemberValidFlowList)) {
            return Collections.emptyList();
        }

        List<BusinessMemberValidityFlowExportVO> businessMemberValidityFlowExportVOS = BeanUtil.copyToList(businessMemberValidFlowList, BusinessMemberValidityFlowExportVO.class);

        List<String> orderNumList = businessMemberValidFlowList.stream().map(BusinessMemberValidityFlowVO::getOrderNum).filter(item -> StrUtil.isNotBlank(item)).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(orderNumList)) {
            return businessMemberValidityFlowExportVOS;
        }

        //查询订单数据列表
        List<OrderPayDetailVO> basePayDetailVOS = remoteService.getBasePayDetailVOS(OrderPayDetailDTO.builder().orderNums(orderNumList).build());
        if (CollUtil.isEmpty(basePayDetailVOS)) {
            return businessMemberValidityFlowExportVOS;
        }
        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());
        Map<String, OrderPayDetailVO> orderPayMap = basePayDetailVOS.stream().collect(Collectors.toMap(OrderPayDetailVO::getOrderNum, item -> item));
        for (BusinessMemberValidityFlowExportVO item : businessMemberValidityFlowExportVOS) {
            OrderPayDetailVO orderPayDetailVO = orderPayMap.getOrDefault(item.getOrderNum(), new OrderPayDetailVO());
            item.setMchntOrderNo(orderPayDetailVO.getMchntOrderNo());
            item.setPayTime(orderPayDetailVO.getPayTime());
            item.setPayType(orderPayDetailVO.getPayType());
            item.setPayAmount(orderPayDetailVO.getPayAmount());
            item.setPayAmountDollar(orderPayDetailVO.getPayAmountDollar());
            item.setCurrentExchangeRate(orderPayDetailVO.getCurrentExchangeRate());
            item.setUseBalance(orderPayDetailVO.getUseBalance());
            item.setSurplusAmount(orderPayDetailVO.getPayAmount() == null ? BigDecimal.ZERO : orderPayDetailVO.getPayAmount().subtract(orderPayDetailVO.getUseBalance() == null ? BigDecimal.ZERO : item.getUseBalance()));
            if (ObjectUtil.isNotNull(item.getPayTime())) {
                item.setRealPayAmount(orderPayDetailVO.getRealPayAmount());
                item.setRealPayAmountCurrency(orderPayDetailVO.getRealPayAmountCurrency());
                item.setCurrency(dictTypeMap.get(Convert.toStr(orderPayDetailVO.getCurrency())) == null ? CurrencyEnum.CNY.getLabel() : dictTypeMap.get(Convert.toStr(orderPayDetailVO.getCurrency())));
            } else {
                item.setRealPayAmount(null);
                item.setRealPayAmountCurrency(null);
                item.setCurrency(null);
            }
        }
        return businessMemberValidityFlowExportVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetBusinessName(ResetBusinessNameDTO dto) {
        businessService.update(BusinessDTO.builder()
                .id(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
                .name(dto.getName())
                .build());

        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setBusinessName(dto.getName());
        self.updateLoginBusiness(Arrays.asList(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), businessAccountTokenDTO);
    }

    @Override
    public void resetBusinessScale(ResetBusinessScaleDTO dto) {
        businessService.update(BusinessDTO.builder()
                .id(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId())
                .scale(dto.getScale())
                .build());
        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setBusinessScale(dto.getScale());
        self.updateLoginBusiness(Arrays.asList(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId()), businessAccountTokenDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initBusinessInfo(InitBusinessInfoDTO dto) {
        final Business business = businessService.getById(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
        Business updateBusiness = BeanUtil.copyProperties(business, Business.class);
        updateBusiness.setScale(dto.getScale());
        updateBusiness.setName(dto.getName());
        businessService.updateById(updateBusiness);

        //修改登录账号数据
        BusinessAccountVO businessAccountVO = baseMapper.queryOne(BusinessAccountDTO.builder().account(business.getOwnerAccount()).build());
        if (CharSequenceUtil.isBlank(businessAccountVO.getPhone())) {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getPhone()) && CharSequenceUtil.isNotBlank(dto.getPhoneCaptcha()), "请填写手机号以继续");
            updateBizUserPhoneCheck(UpdatePhoneDTO.builder().phone(dto.getPhone()).phoneCaptcha(dto.getPhoneCaptcha()).build());
        }
        Assert.notNull(businessAccountVO, "登录主账号不能为空");
        self.bizUserUpdateName(UpdateNameDTO.builder().name(dto.getAccountName()).phone(dto.getPhone()).build());

        BusinessAccountTokenDTO businessAccountTokenDTO = BeanUtil.copyProperties(dto, BusinessAccountTokenDTO.class);
        businessAccountTokenDTO.setBusinessName(dto.getName());
        businessAccountTokenDTO.setBusinessScale(dto.getScale());
        businessAccountTokenDTO.setName(dto.getAccountName());
        self.updateLoginBusiness(Arrays.asList(business.getId()), businessAccountTokenDTO);
    }

    @Override
    public void updateInvoice(BusinessInvoiceDTO dto) {
        BusinessDTO businessDTO = BeanUtil.copyProperties(dto, BusinessDTO.class);
        businessService.update(businessDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWaiter(UpdateWaiterDTO dto) {
        //校验两个对接客服是否一致
        BusinessDTO businessDTO = BeanUtil.copyProperties(dto, BusinessDTO.class);
        List<Business> businesses = businessService.queryList(businessDTO);
        if (CollUtil.isNotEmpty(businesses)) {
            List<Long> sameWaiterBusinessIds = businesses.stream().map(Business::getId).collect(Collectors.toList());
            dto.getBusinessIds().removeAll(sameWaiterBusinessIds);
            if (CollUtil.isEmpty(dto.getBusinessIds())) {
                return;
            }
        }

        businessService.update(businessDTO);

        //批量修改登录账号的客服
        List<BusinessAccountVO> businessAccountVOS = baseMapper.queryList(BusinessAccountDTO.builder().businessIds(dto.getBusinessIds()).isOwnerAccount(StatusTypeEnum.YES.getCode()).build());
        if (CollUtil.isNotEmpty(businessAccountVOS)) {
            bizUserService.updateWaiterIdByIds(businessAccountVOS.stream().map(BusinessAccountVO::getBizUserId).collect(Collectors.toList()), dto.getWaiterId());
        }

        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setWaiterId(dto.getWaiterId());
        self.updateLoginBusiness(dto.getBusinessIds(), businessAccountTokenDTO);


        UpdateOrderContactDTO updateOrderContactDTO = new UpdateOrderContactDTO();
        updateOrderContactDTO.setBusinessIds(dto.getBusinessIds());
        updateOrderContactDTO.setContactId(dto.getWaiterId());
        remoteService.updateOrderVideoContact(updateOrderContactDTO);
    }

    @Override
    public String initMemberCode(Long businessId) {
        Long reentrancyCount = 5L;
        Long expireTime = 10L;
        String key = CacheConstants.BUSINESS_ACCOUNT_KEY + businessId;
        if (redisService.setIncr(key, expireTime, TimeUnit.SECONDS).compareTo(reentrancyCount) > 0) {
            throw new ServiceException("系统生成随机数失败，请稍后重试！");
        }
        String randomKey = BusinessConstants.randomKey;
        String memberCode = RandomUtil.randomString(randomKey, 4);


        Long count = businessService.lambdaQuery()
                .eq(Business::getMemberCode, memberCode)
                .count();
        if (count.compareTo(0L) > 0) {
            return self.initMemberCode(businessId);
        }
        return memberCode;
    }

    @Override
    public void updateLoginBusiness(List<Long> businessIds, BusinessAccountTokenDTO business) {
        if (CollUtil.isEmpty(businessIds)) {
            return;
        }
        //获取账号列表
        BusinessAccountDTO businessAccountDto = new BusinessAccountDTO();
        businessAccountDto.setBusinessIds(businessIds);
        List<BusinessAccountVO> businessAccounts = self.queryList(businessAccountDto);
        if (CollUtil.isEmpty(businessAccounts)) {
            return;
        }
        List<Long> bizUserIds = businessAccounts.stream().filter(item -> ObjectUtil.isNotNull(item.getBizUserId())).map(BusinessAccountVO::getBizUserId).collect(Collectors.toList());
        tokenService.updateLoginBusiness(bizUserIds, business);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void balancePayOut(BalancePayOutDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId(), 60L), "商家余额提现中，请稍后重试！");
        try {
            List<BusinessBalanceDetailLockDTO> businessBalanceDetailLocks = dto.getBusinessBalanceDetailLocks();
            //总提现金额
            BigDecimal payOutTotal = BigDecimal.ZERO;
            Map<Long, BusinessBalanceDetailLockDTO> businessBalanceDetailLockMap = new HashMap<>();

            //余额详情ID
            List<Long> balanceDetailId = new ArrayList<>();
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                payOutTotal = payOutTotal.add(item.getPayOutAmount());
                businessBalanceDetailLockMap.put(item.getBalanceDetailId(), item);
                balanceDetailId.add(item.getBalanceDetailId());
            }
            Assert.isTrue(payOutTotal.compareTo(dto.getUseBalance()) == 0, "余额详情数据已更新，请刷新后重试~");
            List<BusinessBalanceDetail> validBusinessBalanceDetailList = businessBalanceDetailService.getValidBusinessBalanceDetailListByBusinessId(dto.getBusinessId());
            List<BusinessBalanceDetail> businessBalanceDetails = new ArrayList<>();
            for (BusinessBalanceDetail item : validBusinessBalanceDetailList) {
                if (balanceDetailId.contains(item.getId())) {
                    businessBalanceDetails.add(item);
                }
            }
            Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetails), "余额详情数据已更新，请刷新后重试~");
            Assert.isTrue(businessBalanceDetails.size() == dto.getBusinessBalanceDetailLocks().size(), "余额详情数据已更新，请刷新后重试~");

            List<PayoutBusinessBalanceDetailVO> businessBalanceDetailVOList = businessBalanceDetailService.getBusinessBalanceDetailVOList(validBusinessBalanceDetailList, StatusTypeEnum.NO.getCode());
            Map<Long, BusinessBalanceDetail> businessBalanceDetailMap = businessBalanceDetails.stream().collect(Collectors.toMap(BusinessBalanceDetail::getId, p -> p));

            Map<Long, PayoutBusinessBalanceDetailVO> businessBalanceDetailVOMap = businessBalanceDetailVOList.stream().collect(Collectors.toMap(PayoutBusinessBalanceDetailVO::getId, p -> p));
            Assert.isTrue(CollUtil.isNotEmpty(businessBalanceDetailVOList), "余额详情数据已更新，请刷新后重试~");
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                PayoutBusinessBalanceDetailVO detail = businessBalanceDetailVOMap.get(item.getBalanceDetailId());
                Assert.notNull(detail, "余额详情数据已更新，请刷新后重试~");
                Assert.isTrue(detail.getUseBalance().compareTo(item.getUseBalance()) == 0, "余额详情数据已更新，请刷新后重试~");
                Assert.isTrue(detail.getValidBalance().compareTo(item.getPayOutAmount()) == 0, "余额详情数据已更新，请刷新后重试~");
            }

            //处理余额
            BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
            businessBalanceDTO.setBusinessId(dto.getBusinessId());
            businessBalanceDTO.setUseBalance(dto.getUseBalance());
            businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
            businessBalanceDTO.setOrigin(BalanceSourceTypeEnum.PAYOUT_SPEND.getCode());
            businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder().loginBase(SecurityUtils.getLoginUser()).build()));
            businessBalanceDTO.setBalanceAuditStatus(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode());
            businessBalanceService.updateBusinessBalance(businessBalanceDTO);

            //添加提现订单
            BusinessBalanceAuditFlowSaveDTO businessBalanceAuditFlowSaveDTO = new BusinessBalanceAuditFlowSaveDTO();
            businessBalanceAuditFlowSaveDTO.setBusinessId(dto.getBusinessId());
            businessBalanceAuditFlowSaveDTO.setAmount(dto.getUseBalance());
            businessBalanceAuditFlowSaveDTO.setApplyRemark(dto.getApplyRemark());
            businessBalanceAuditFlowSaveDTO.setWithdrawWay(dto.getWithdrawWay());
            if (CollUtil.isNotEmpty(dto.getPayoutResourceUrlList())) {
                List<Long> payoutResourceUrls = bizResourceService.saveBatchBizResourceReturnIds(dto.getPayoutResourceUrlList());
                businessBalanceAuditFlowSaveDTO.setPayoutResourceUrl(StrUtil.join(StrUtil.COMMA, payoutResourceUrls));
            } else {
                businessBalanceAuditFlowSaveDTO.setPayoutResourceUrl(StrUtil.EMPTY);
            }
            BusinessBalanceAuditFlow businessBalanceAuditFlow = businessBalanceAuditFlowService.addBusinessBalanceAuditFlow(businessBalanceAuditFlowSaveDTO);

            //锁定余额详情
            List<BusinessBalanceDetailLock> businessBalanceDetailLockList = new ArrayList<>();
            List<BusinessBalanceDetail> businessBalanceDetailList = new ArrayList<>();
            for (BusinessBalanceDetailLockDTO item : businessBalanceDetailLocks) {
                BusinessBalanceDetail businessBalanceDetail = businessBalanceDetailMap.get(item.getBalanceDetailId());

                BusinessBalanceDetailLock detailLock = new BusinessBalanceDetailLock();
                detailLock.setNumber(businessBalanceAuditFlow.getWithdrawNumber());
                detailLock.setBalanceDetailId(item.getBalanceDetailId());
                detailLock.setUseBalance(item.getUseBalance());
                detailLock.setPayOutAmount(item.getPayOutAmount());
                detailLock.setCreateOrderUserName(businessBalanceDetail.getCreateOrderUserName());
                detailLock.setCreateOrderUserNickName(businessBalanceDetail.getCreateOrderUserNickName());
                if (BalanceDetailTypeEnum.PREPAY.getCode().equals(businessBalanceDetail.getNumberType())) {
                    detailLock.setPrepayNum(businessBalanceDetail.getOriginNumber());
                }
                detailLock.setVideoCode(businessBalanceDetail.getVideoCode());
                detailLock.setBalanceNumber(businessBalanceDetail.getNumber());
                detailLock.setBalanceCreateTime(businessBalanceDetail.getCreateTime());
                detailLock.setOrigin(businessBalanceDetail.getOrigin());
                businessBalanceDetailLockList.add(detailLock);

                BusinessBalanceDetail detail = new BusinessBalanceDetail();
                detail.setId(businessBalanceDetail.getId());
                detail.setLockBalance(businessBalanceDetail.getLockBalance().add(item.getPayOutAmount()));
                detail.setValidBalance(businessBalanceDetail.getValidBalance().subtract(item.getPayOutAmount()));
                businessBalanceDetailList.add(detail);
            }
            businessBalanceDetailService.updateBatchById(businessBalanceDetailList);
            businessBalanceDetailLockService.saveBatch(businessBalanceDetailLockList);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditPayOut(BusinessBalanceAuditFlowAuditDTO dto) {
        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId(), 60L), "商家余额处理中，请稍后重试！");
            BusinessBalanceAuditFlow entity = businessBalanceAuditFlowService.getById(dto.getId());
            BusinessBalanceAuditFlow businessBalanceAuditFlow = this.checkAuditPayOut(dto, entity);
            businessBalanceAuditFlowService.updateById(businessBalanceAuditFlow);

            BusinessBalanceDTO businessBalanceDTO = new BusinessBalanceDTO();
            businessBalanceDTO.setBusinessId(dto.getBusinessId());
            businessBalanceDTO.setUseBalance(entity.getAmount());
            businessBalanceDTO.setIsBalanceLock(StatusTypeEnum.NO.getCode());
            businessBalanceDTO.setOrigin(BalanceSourceTypeEnum.PAYOUT_SPEND.getCode());
            businessBalanceDTO.setBalanceAuditStatus(dto.getAuditStatus());
            businessBalanceDTO.setBusinessBalanceFlowDTOS(List.of(BusinessBalanceFlowDTO.builder().loginBase(SecurityUtils.getLoginUser()).withdrawNumber(entity.getWithdrawNumber()).build()));
            businessBalanceService.updateBusinessBalance(businessBalanceDTO);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_PAY_LOCK_KEY + dto.getBusinessId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessAccountVO flowMember(FlowMemberDto dto) {
        if (StringUtils.isNotBlank(dto.getSeedCode())) {
            dto.setSeedCode(dto.getSeedCode().toUpperCase());
        }
        MemberStatusDto memberStatusDto = new MemberStatusDto();
        memberStatusDto.setBusinessId(dto.getBusinessId());
        memberStatusDto.setBizUserId(dto.getBizUserId());
        memberStatusDto.setChooseMemberStatus(dto.getChooseMemberStatus());
        memberStatusDto.setChoosePackageType(dto.getChoosePackageType());
        memberStatusDto.setOrderNum(dto.getOrderNum());
        memberStatusDto.setRealPayAmount(dto.getRealPayAmount());
        memberStatusDto.setRealPayAmountCurrency(dto.getRealPayAmountCurrency());
        memberStatusDto.setCurrency(dto.getCurrency());
        memberStatusDto.setPresentedTime(dto.getPresentedTime());
        memberStatusDto.setPresentedTimeType(dto.getPresentedTimeType());
        BusinessAccountVO businessAccountVO = SpringUtils.getBean(MemberContext.class).flowMember(memberStatusDto);

        if (dto.getBusinessId().compareTo(0L) == 0) {
            OrderMemberChannelDTO orderMemberChannelDTO = OrderMemberChannelDTO.builder()
                    .businessId(businessAccountVO.getBusinessId())
                    .businessName(businessAccountVO.getBusinessVO().getName())
                    .bizUserId(businessAccountVO.getBizUserId())
                    .bizUserNickName(businessAccountVO.getNickName())
                    .bizUserPhone(businessAccountVO.getPhone())
                    .memberCode(businessAccountVO.getBusinessVO().getMemberCode())
                    .memberPackageType(dto.getChoosePackageType())
                    .buyTime(dto.getPayTime())
                    .seedCode(dto.getSeedCode())
                    .orderNum(dto.getOrderNum())
                    .orderAmount(dto.getOrderAmount())
                    .realPayAmount(dto.getRealPayAmount())
                    .realPayAmountCurrency(dto.getRealPayAmountCurrency())
                    .currency(dto.getCurrency())
                    .payType(dto.getPayType())
                    .taxPointCost(Optional.ofNullable(dto.getTaxPointCost()).orElse(BigDecimal.ZERO))
                    .build();
            if (StrUtil.isNotBlank(dto.getSeedCode())) {
                businessService.updateById(Business.builder().id(businessAccountVO.getBusinessId()).seedCode(dto.getSeedCode()).build());
                if (ChannelTypeEnum.DISTRIBUTION.getCode().equals(dto.getChannelType())) {
                    memberSeedRecordService.saveMemberSeedRecord(orderMemberChannelDTO);
                } else if (ChannelTypeEnum.FISSION.getCode().equals(dto.getChannelType())) {
                    memberSeedRecordService.saveMemberSeedRecord(orderMemberChannelDTO);
                }

            } else {
                orderMemberMarketingChannelService.saveOrderMemberMarketingChannel(orderMemberChannelDTO);
            }
        }
        return businessAccountVO;
    }

    @Override
    public List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(BusinessBalanceAuditFlowValidListDTO dto) {
        return businessBalanceAuditFlowService.queryValidList(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unbindLoginUser(UnbindAccountDTO dto) {
        BusinessAccount businessAccount = baseMapper.selectById(dto.getBusinessAccountId());
        Assert.notNull(businessAccount, "页面状态已过期，请刷新后重试");
        Assert.notNull(businessAccount.getBizUserId(), "此账号已解绑");
        BizUser bizUser = bizUserService.getById(businessAccount.getBizUserId());

        Long bizUserId = businessAccount.getBizUserId();
        Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
        if (businessId.compareTo(businessAccount.getBusinessId()) != 0) {
            throw new ServiceException("登录账号商家和解绑账号商家不一致");
        }
        baseMapper.unbindLoginUser(dto.getBusinessAccountId());
        bizUserService.cleanUserBusiness(businessAccount.getBizUserId());

        businessAccountRebindLogMapper.updateRebindLogToUnbind(businessAccount, bizUserId);

        refreshCompanyUserTokenBy(bizUser.getId(), bizUser.getUnionid());
    }

    @Override
    @MemberAuth
    public CheckPhoneVO checkPhoneStatus(CheckPhoneDTO dto) {
        Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
        BizUser bizUser = bizUserService.getByPhone(dto.getPhone());
        String code = TokenConstants.SUB_ACCOUNT_VER + SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessVO().getMemberCode();
        if (ObjectUtil.isNull(bizUser) || StrUtil.isBlank(bizUser.getUnionid())) {
            //未初始化、未绑定微信
            QrCodeDTO qrCodeDTO = self.generateQrcode(TokenConstants.TOKEN_REGISTER, code);
            updateRedisTicketPhone(qrCodeDTO.getTicket(), dto.getPhone());
            redisService.setCacheObject(CacheConstants.BANG_ACCOUNT + qrCodeDTO.getTicket(), dto.getPhone(), CacheConstants.WECHAT_LOGIN_EXPIRATION_TIMEOUT);
            return CheckPhoneVO.builder()
                    .phoneStatusEnum(PhoneStatusEnum.NEW_ACCOUNT)
                    .qrcode(qrCodeDTO.getQrcode())
                    .ticket(qrCodeDTO.getTicket())
                    .build();
        } else if (CharSequenceUtil.isNotBlank(dto.getTicket())) {
            String unionId = redisService.getCacheObject(CacheConstants.WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX + dto.getTicket());
            Assert.isTrue(CharSequenceUtil.isNotBlank(unionId),  "二维码已过期");
            if (!bizUser.getUnionid().equals(unionId)) {
                return CheckPhoneVO.builder()
                        .phoneStatusEnum(PhoneStatusEnum.BINDING_OTHER_ACCOUNT)
                        .build();
            }
        }
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setPhone(dto.getPhone());
        BusinessAccountVO businessAccountVO = baseMapper.queryOne(businessAccountDTO);

        if (ObjectUtil.isNull(businessAccountVO)) {
            QrCodeDTO qrCodeDTO = self.generateQrcode(TokenConstants.TOKEN_VERIFY, code);
            updateRedisTicketPhone(qrCodeDTO.getTicket(), dto.getPhone());
            return CheckPhoneVO.builder()
                    .phoneStatusEnum(PhoneStatusEnum.NEW_ACCOUNT)
                    .qrcode(qrCodeDTO.getQrcode())
                    .ticket(qrCodeDTO.getTicket())
                    .build();
        }
        if (StatusTypeEnum.YES.getCode().equals(businessAccountVO.getIsOwnerAccount())) {
            return CheckPhoneVO.builder()
                    .phoneStatusEnum(PhoneStatusEnum.BINDING_OWNER_ACCOUNT)
                    .build();
        }

        if (businessId.compareTo(businessAccountVO.getBusinessId()) != 0) {
            return CheckPhoneVO.builder()
                    .phoneStatusEnum(PhoneStatusEnum.BINDING_OTHER_ACCOUNT)
                    .build();
        }
        return CheckPhoneVO.builder()
                .phoneStatusEnum(PhoneStatusEnum.BINDING_ACCOUNT)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @MemberAuth
    public void bindingLoginUser(BindingAccountDTO dto) {
        //设置 手机号 - ticket 关系
        //检查新账号是不是绑定
        String unionId = SpringUtils.getBean(WechatService.class).getRedisUnionIdByTicket(dto.getTicket());
        final BusinessAccountVO businessAccount = self.getBusinessAccount(unionId);
        if (ObjectUtil.isNull(businessAccount)) {
            throw new ServiceException("微信对应登录账号为空");

        }
        if (ObjectUtil.isNotNull(businessAccount) && StatusTypeEnum.NO.getCode().equals(businessAccount.getIsMock())) {
            if (businessAccount.getAccount().equals(businessAccount.getOwnerAccount())) {
                throw new ServiceException("绑定失败！该用户已是主账号，无法成为子账号");
            }
            throw new ServiceException("绑定失败！该用户已是子账号，无法成为子账号");
        }
        BizUser byPhone = bizUserService.getByPhone(dto.getPhone());
        if (StrUtil.isBlank(businessAccount.getPhone())) {
            Assert.isNull(byPhone, "该手机号已注册，无法重复绑定");
        } else if (ObjectUtil.isNotNull(byPhone)) {
            Assert.isTrue(businessAccount.getBizUserId().equals(byPhone.getId()), "该手机号已注册，无法重复绑定");
        }

        if (remoteService.getValidOrderCount(businessAccount.getBizUserId()).compareTo(0L) > 0) {
            throw new ServiceException("您的账号存在未支付的会员订单，无法成为子账号，请先取消会员订单");
        }
        Assert.isTrue(businessAccount.getUserStatus().compareTo(StatusEnum.UN_ENABLED.getCode()) != 0, "您的账户已被禁用!");
        //绑定商家
        baseMapper.bindingLoginUser(dto.getBusinessAccountId(), businessAccount.getBizUserId());

        String memberCode = baseMapper.getmemberCodeByAccountId(dto.getBusinessAccountId());
        BusinessOwnerAccountContactUserDTO businessOwnerUserContactUserNameByMemberCode = businessMapper.getBusinessOwnerUserContactUserNameByMemberCode(memberCode);

        bizUserService.updateById(BizUser.builder()
                .id(businessAccount.getBizUserId())
                .accountType(BizUserAccountTypeEnum.ACCOUNT.getCode())
                .name(dto.getName())
                .phone(dto.getPhone())
                .connectUserName(businessOwnerUserContactUserNameByMemberCode.getContactUserName())
                .build());
        weChatExternalUserService.updateContactUserInfo(businessAccount.getExternalUserId(),
                businessOwnerUserContactUserNameByMemberCode.getContactUserId(),
                businessOwnerUserContactUserNameByMemberCode.getContactUserName()
        );
        bizUserService.bindUserBusinessSub(memberCode, businessAccount.getBizUserId());

        BusinessAccountVO updateAfter = self.getBusinessAccount(unionId);
        BusinessAccountRebindLog log = new BusinessAccountRebindLog();
        log.setName(updateAfter.getName());
        log.setNickName(updateAfter.getNickName());
        log.setPhone(updateAfter.getPhone());
        log.setConnectUserName(updateAfter.getConnectUserName());
        log.setBusinessId(updateAfter.getBusinessId());
        log.setBizUserId(businessAccount.getBizUserId());
        log.setAccount(updateAfter.getAccount());
        //获取当前坑位状态
        BusinessAccountDTO bad = new BusinessAccountDTO();
        bad.setId(dto.getBusinessAccountId());
        BusinessAccountVO businessAccountVO = businessAccountMapper.queryOne(bad);
        log.setStatus(null != businessAccountVO && null != businessAccountVO.getStatus() ? businessAccountVO.getStatus() : 0);
        //在坑位重新绑定的直接是审核通过
        log.setAuditStatus(1);
        businessAccountRebindLogMapper.insert(log);

        refreshCompanyUserTokenBy(businessAccount.getBizUserId(), businessAccount.getUnionid());
    }

    /**
     * 换绑微信
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rebind(RebindDTO dto) {
        //检查新账号是不是绑定
        ExternalContactInfoDTO infoByTicket = SpringUtils.getBean(WechatService.class).getInfoByTicket(dto.getTicket());
        checkBusinessAccountBind(infoByTicket.getUnionid());
        BusinessDTO businessDto = new BusinessDTO();
        businessDto.setOwnerAccount(dto.getAccount());
        Business business = businessService.queryOne(businessDto);

        if (ObjectUtil.isNotNull(business)) {
            //如果是主账号，需要校验检查账号是否相同
            self.checkAccount(dto.getAccountTicket(), dto.getAccount());
        } else {
            Assert.isTrue(dto.getAccount().startsWith(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getAccount()), "账号不属于：" + SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessVO().getName());
        }

        //需要查询系统中是否已存在该微信号
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setUnionid(infoByTicket.getUnionid());
        businessAccountDTO.setNickName(infoByTicket.getName());
        businessAccountDTO.setPic(infoByTicket.getAvatar());
        businessAccountDTO.setExternalUserId(infoByTicket.getExternalUserid());
        businessAccountDTO.setAccount(dto.getAccount());
        self.update(businessAccountDTO);

        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setUnionid(infoByTicket.getUnionid());
        businessAccountTokenDTO.setNickName(infoByTicket.getName());
        businessAccountTokenDTO.setPic(infoByTicket.getAvatar());
        businessAccountTokenDTO.setExternalUserId(infoByTicket.getExternalUserid());
        self.updateLoginBusiness(Arrays.asList(business.getId()), businessAccountTokenDTO);
    }

    public void checkBusinessAccountBind(String unionId) {
        if (StrUtil.isEmpty(unionId)) {
            throw new ServiceException("unionId不能为空");
        }
        BusinessAccountDTO queryOne = new BusinessAccountDTO();
        queryOne.setUnionid(unionId);
        BusinessAccountVO businessAccount = baseMapper.queryOne(queryOne);
        if (ObjectUtil.isNotNull(businessAccount)) {
            throw new ServiceException("该微信号已绑定账号：" + businessAccount.getAccount() + "，无法绑定");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveBizUserVO saveBizUser(BizUserSaveDTO dto) {
        checkBizUser(dto.getCustomerType(), dto.getWaiterId());
        BizUser bizUser = bizUserService.initBizUserBaseOnPhone(BizUserDTO.builder()
                .phone(dto.getPhone())
                .build());
        //需要未绑定微信
        Assert.isTrue(StrUtil.isBlank(bizUser.getUnionid()), "手机号已绑定其他微信！");

        final Integer code = redisService.getCacheObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + dto.getTicket());
        WechatService wechatService = SpringUtils.getBean(WechatService.class);
        String unionId = wechatService.getUnionIdByTicket(dto.getTicket());
        String ticket = wechatService.getTicketByUnionId(unionId);
        if (ObjectUtil.isNull(code) || StrUtil.isBlank(unionId)) {
            return SaveBizUserVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.EXPIRE)
                    .build();
        } else {
            redisService.deleteObject(CacheConstants.WECHAT_LOGIN_STATUS_PREFIX + dto.getTicket());
            redisService.deleteObject(CacheConstants.WORK_WECHAT_LOGIN_STATUS_PREFIX + unionId);
        }

        //获取登录信息
        BizUser bizUserByUnionId = bizUserService.getByUnionId(unionId);
        //存在微信数据
        if (ObjectUtil.isNotNull(bizUserByUnionId)) {
            return SaveBizUserVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.BINDING)
                    .build();
        }

        if (ticket.equals(dto.getTicket())) {
            //绑定手机号
            BizUser updateBizUser = BeanUtil.copyProperties(dto, BizUser.class);
            updateBizUser.setId(bizUser.getId());
            WeChatExternalUser weChatExternalUser = weChatExternalUserService.getOneByUnionid(unionId);
            if (ObjectUtil.isNull(weChatExternalUser)) {
                return SaveBizUserVO.builder()
                        .loginStatus(WxChatLoginStatusEnum.UNKNOWN)
                        .build();
            }
            bizUserService.updateById(updateBizUser.setNickName(weChatExternalUser.getName())
                    .setConnectUserName(weChatExternalUser.getConnectUserName())
                    .setExternalUserId(weChatExternalUser.getExternalUserid())
                    .setPic(weChatExternalUser.getAvatar())
                    .setUnionid(weChatExternalUser.getUnionid()));

            return SaveBizUserVO.builder()
                    .loginStatus(WxChatLoginStatusEnum.LOGIN_SUCCESS)
                    .bizUser(BeanUtil.copyProperties(updateBizUser, BizUserVO.class))
                    .build();
        } else {
            throw new ServiceException("业务异常");
        }
    }

    private void checkBizUser(Integer customerType, Long waiterId) {
        if ((CustomerTypeEnum.NORMAL.getCode().equals(customerType) || CustomerTypeEnum.VIP.getCode().equals(customerType)) && ObjectUtil.isNull(waiterId)) {
            throw new ServiceException("当重要程度为" + CustomerTypeEnum.getByCode(customerType).getLabel() + "时，对接客服不能为空！");
        }

    }

    @Override
    public List<BizUserListVO> bizUserList(BizUserListDTO dto) {
        //获取客服列表
        if (StrUtil.isNotBlank(dto.getSearchName())) {
            //  获取用户map
            SysUserListDTO sysUserListDTO = new SysUserListDTO();
            sysUserListDTO.setUserName(dto.getSearchName());
            List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
            if (CollUtil.isNotEmpty(userList)) {
                dto.setWaiterIds(userList.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return bizUserService.bizUserList(dto);
    }

    @Override
    public List<BizUserDetailVO> bizUserDetailList(BizUserDetailListDTO dto) {
        return bizUserService.bizUserDetailList(dto);
    }


    /**
     * 如果是主账号：
     * 修改重要程度（无法选择普通账号）、对接客服需要同步商家
     * 非主账号：
     * 只修改账号数据
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editBizUser(BizUserEditDTO dto) {
        checkBizUser(dto.getCustomerType(), dto.getWaiterId());
        //获取登录账号
        BizUser bizUser = bizUserService.getById(dto.getId());
        Assert.notNull(bizUser, "登录账号不能为空！");
        bizUserService.updateFillNullById(BeanUtil.copyProperties(dto, BizUser.class));
        if (BizUserAccountTypeEnum.OWNER_ACCOUNT.getCode().equals(bizUser.getAccountType())) {
            //主账号无法选择普通账号
            Assert.isFalse(CustomerTypeEnum.COMMON.getCode().equals(dto.getCustomerType()), "主账号无法选择客户类型为：普通客户");

            BusinessAccount businessAccount = baseMapper.getByBizUserId(bizUser.getId());
            //根据商家id修改商家账号数据
            businessService.update(BusinessDTO.builder()
                    .id(businessAccount.getBusinessId())
                    .customerType(dto.getCustomerType())
                    .waiterId(dto.getWaiterId())
                    .build());
            if (ObjectUtil.isNotNull(dto.getWaiterId()) && (ObjectUtil.isNull(bizUser.getWaiterId()) || bizUser.getWaiterId().compareTo(dto.getWaiterId()) != 0)) {
                UpdateOrderContactDTO updateOrderContactDTO = new UpdateOrderContactDTO();
                updateOrderContactDTO.setBusinessIds(Collections.singletonList(businessAccount.getBusinessId()));
                updateOrderContactDTO.setContactId(dto.getWaiterId());
                remoteService.updateOrderVideoContact(updateOrderContactDTO);
            }
        }
        tokenService.updateLoginBusiness(Arrays.asList(dto.getId()), BusinessAccountTokenDTO.builder()
                .name(dto.getName())
                .customerType(BizUserAccountTypeEnum.OWNER_ACCOUNT.getCode().equals(bizUser.getAccountType()) ? dto.getCustomerType() : null)
                .waiterId(BizUserAccountTypeEnum.OWNER_ACCOUNT.getCode().equals(bizUser.getAccountType()) ? dto.getWaiterId() : null)
                .build());
    }


    /**
     * 编辑 Biz 用户状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editBizUserStatus(BizUserEditStatusDTO dto) {
        BizUser bizUser = bizUserService.getById(dto.getId());
        Assert.notNull(bizUser, "登录账号不能为空！");
        bizUser.setStatus(dto.getStatus());
        bizUserMapper.updateById(bizUser);

        if (BusinessStatusEnum.UN_ENABLED.getCode().equals(dto.getStatus())) {
            tokenService.delAccessToken(List.of(dto.getId()));
        }
    }

    @Override
    public void editBizUserPhone(BizUserEditPhoneDTO dto) {
        BizUser bizUser = bizUserService.getById(dto.getId());
        Assert.notNull(bizUser, "登录账号不能为空！");
        Assert.isFalse(bizUser.getPhone().equals(dto.getPhone()), "手机号未修改！");
        Assert.isFalse(bizUserService.isExist(dto.getPhone()), "手机号已绑定其他微信！");
        bizUserService.setPhone(bizUser.getUnionid(), dto.getPhone());
        tokenService.updateLoginBusiness(Arrays.asList(dto.getId()), BusinessAccountTokenDTO.builder()
                .phone(dto.getPhone())
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Business initBusinessV1(BizUser bizUser) {
        BusinessAccountDTO businessAccountDTO = new BusinessAccountDTO();
        businessAccountDTO.setBizUserId(bizUser.getId());
        BusinessAccountVO businessAccountVO = baseMapper.queryOne(businessAccountDTO);

        Assert.isNull(businessAccountVO, "初始化商家失败，登录账号已绑定商家~");
        String account = this.initAccount(StatusTypeEnum.YES.getCode(), null, bizUser.getUnionid());
        //创建商家表数据
        Business business = new Business();
        business.setOwnerAccount(account);
        if (CustomerTypeEnum.COMMON.getCode().equals(bizUser.getCustomerType())) {
            business.setCustomerType(CustomerTypeEnum.NORMAL.getCode());
        } else {
            business.setCustomerType(bizUser.getCustomerType());
        }
        business.setIsProxy(bizUser.getIsProxy());
        business.setWaiterId(bizUser.getWaiterId());
        business.setCreateUserId(SecurityUtils.getUserId());
        business.setCreateUserName(SecurityUtils.getUsername());
        business.setCreateTime(new Date());

        business.setUpdateUserId(SecurityUtils.getUserId());
        business.setUpdateUserName(SecurityUtils.getUsername());
        business.setUpdateTime(new Date());
        business.setMemberStatus(MemberTypeEnum.NO_RECHARGE.getCode());
        businessService.save(business);

        BusinessAccount initBusinessAccount = new BusinessAccount();
        initBusinessAccount.setAccount(account);
        initBusinessAccount.setOwnerAccount(account);
        initBusinessAccount.setPassword(SecurityUtils.encryptPassword("123456"));
        initBusinessAccount.setBusinessId(business.getId());
        initBusinessAccount.setOwnerAccountBizUserId(bizUser.getId());
        initBusinessAccount.setBizUserId(bizUser.getId());
        initBusinessAccount.setIsOwnerAccount(StatusTypeEnum.YES.getCode());
        initBusinessAccount.setName(StrUtil.isBlank(bizUser.getName()) ? bizUser.getNickName() : bizUser.getName());
        this.save(initBusinessAccount);

        //同步登录账号商家数据
        bizUserService.updateUserBusinessInfo(BizUserBusinessInfoDTO.builder()
                .id(bizUser.getId())
                .accountType(BizUserAccountTypeEnum.OWNER_ACCOUNT.getCode())
                .customerType(business.getCustomerType())
                .build());

        return business;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBusiness(Long businessId) {
        log.warn("执行删除商家操作》》》》》》》》》》》》》》》》》》》》》");
        final Business business = businessService.getById(businessId);
        log.warn("删除商家数据：{}", JSON.toJSONString(business));
        businessService.removeById(businessId);

        final BusinessAccount businessAccount = baseMapper.getByAccount(business.getOwnerAccount());
        log.warn("删除商家账号数据：{}", JSON.toJSONString(business));
        baseMapper.deleteById(businessAccount.getId());

        log.warn("删除会员渠道记录表数据");
        orderMemberChannelService.deleteByBusinessId(businessId);
        log.warn("删除商家会员渠道记录表：{}", JSON.toJSONString(business));


        //重置登录账号数据
        bizUserService.updateUserBusinessInfo(BizUserBusinessInfoDTO.builder()
                .id(businessAccount.getBizUserId())
                .accountType(BizUserAccountTypeEnum.COMMON.getCode())
                .customerType(business.getCustomerType())
                .build());
    }

    @Override
    public BusinessAccount getByBizUserId(Long bizUserId) {
        return baseMapper.getByBizUserId(bizUserId);
    }

    @Override
    public List<String> businessManagerList(Integer type) {
        List<String> businessManagerList = baseMapper.getBusinessManagerList(type);
        if (CollUtil.isNotEmpty(businessManagerList)) {
            businessManagerList = businessManagerList.stream().filter(item -> ObjectUtil.isNotNull(item)).collect(Collectors.toList());

        }
        return businessManagerList;
    }

    @Override
    public List<BusinessSubAccountListVo> getSubAccountList(Long businessId) {
        return accountApplyMapper.selectSubAccountList(businessId);
    }

    @Override
    public List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto) {
        return businessBalanceDetailLockService.queryValidList(dto);
    }

    @Override
    public List<BusinessAccountDetailVO> getBizUserInfo(BusinessAccountDetailDTO dto) {
        return bizUserMapper.queryUserInfo(dto);
    }

    /**
     * 更新最后登录时间
     */
    private void updateLoginTime(String account) {
        self.lambdaUpdate()
                .set(BusinessAccount::getLastLoginTime, new Date())
                .eq(BusinessAccount::getAccount, account)
                .update();
    }


    private BusinessAccountVO getInitBusinessAccountVo(BizUser bizUser) {
        BusinessAccountVO businessAccount = new BusinessAccountVO();
        if (bizUser == null) {
            return null;
        }
        businessAccount.setId(0L);
        businessAccount.setAccount(INITIALIZE_ACCOUNT);
        businessAccount.setBusinessId(0L);
        businessAccount.setBizUserId(bizUser.getId());
        businessAccount.setIsOwnerAccount(StatusTypeEnum.YES.getCode());
        businessAccount.setOwnerAccount(INITIALIZE_ACCOUNT);
        businessAccount.setName(bizUser.getName());
        businessAccount.setNickName(bizUser.getNickName());
        businessAccount.setPic(bizUser.getPic());
        businessAccount.setUnionid(bizUser.getUnionid());
        businessAccount.setSeedId(bizUser.getSeedId());
        businessAccount.setExternalUserId(bizUser.getExternalUserId());
        businessAccount.setPhone(bizUser.getPhone());
        businessAccount.setStatus(BusinessStatusEnum.ENABLED.getCode());
        businessAccount.setUserStatus(bizUser.getStatus());
        businessAccount.setUserLastLoginTime(bizUser.getLastLoginTime());
        businessAccount.setLastLoginTime(new Date());
        businessAccount.setBusinessVO(getInitBusiness(bizUser));
        businessAccount.setBusinessAccountVOS(new ArrayList<>());
        businessAccount.setIsMock(StatusTypeEnum.YES.getCode());
        return businessAccount;
    }

    private BusinessVO getInitBusiness(BizUser bizUser) {
        BusinessVO businessVO = new BusinessVO();
        businessVO.setId(0L);
        businessVO.setOwnerAccount(INITIALIZE_ACCOUNT);
        businessVO.setName("");
        businessVO.setIsProxy(bizUser.getIsProxy());
        businessVO.setStatus(0);
        businessVO.setCustomerType(bizUser.getCustomerType());
        businessVO.setBalance(new BigDecimal("0"));
        businessVO.setUseBalance(new BigDecimal("0"));
        businessVO.setValidBalance(new BigDecimal("0"));
        businessVO.setIsBalanceLock(0);
        businessVO.setWaiterId(bizUser.getWaiterId());
        businessVO.setInvoiceTitleType(0);
        businessVO.setInvoiceTitle("");
        businessVO.setInvoiceDutyParagraph("");
        businessVO.setInvoiceContent("");
        businessVO.setMemberCode("");
        businessVO.setMemberType(0);
        businessVO.setMemberStatus(0);
        businessVO.setMemberPackageType(0);
        businessVO.setMemberFirstTime(null);
        businessVO.setMemberFirstType(0);
        businessVO.setMemberLastTime(null);
        businessVO.setMemberValidity(null);
        return businessVO;
    }


    private BusinessBalanceAuditFlow checkAuditPayOut(BusinessBalanceAuditFlowAuditDTO dto, BusinessBalanceAuditFlow entity) {
        Assert.notNull(entity, "余额提现审核记录不存在！");
        Assert.isTrue(entity.getBusinessId().equals(dto.getBusinessId()), "数据库商家数据与请求不一致！");
        Assert.isTrue(BusinessBalanceAuditStatusEnum.PRE_APPROVE.getCode().equals(entity.getAuditStatus()), "只有待审核状态能够进行审核处理！");

        BusinessBalanceAuditFlow businessBalanceAuditFlow = BeanUtil.copyProperties(dto, BusinessBalanceAuditFlow.class);
        if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            Assert.notNull(dto.getRealAmount(), "[实付金额]不能为空");
            Assert.notNull(dto.getPayTime(), "[支付时间]不能为空");
            Assert.notNull(dto.getResourceUrl(), "[图片资源地址Url]不能为空");
            Assert.isTrue(entity.getAmount().compareTo(dto.getRealAmount()) == 0, "实际提现金额需要与原提现一致");
        } else if (BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(dto.getAuditStatus())) {
            businessBalanceAuditFlow.setRealAmount(null);
            businessBalanceAuditFlow.setPayTime(null);
            businessBalanceAuditFlow.setResourceUrl(null);
            businessBalanceAuditFlow.setRemark(null);
        } else {
            throw new ServiceException("审核状态有误！");
        }
        businessBalanceAuditFlow.setAuditUserId(SecurityUtils.getUserId());
        businessBalanceAuditFlow.setAuditTime(new Date());
        return businessBalanceAuditFlow;
    }

    public Business checkUpdateBusinessBalance(BusinessBalanceDTO dto, Business business) {

        if (ObjectUtil.isNull(business)) {
            throw new ServiceException("商家数据不能为空");
        }

        List<Integer> balanceSources = Arrays.stream(BalanceSourceTypeEnum.values()).map(BalanceSourceTypeEnum::getCode).collect(Collectors.toList());
        List<Integer> audits = Arrays.stream(AuditStatusEnum.values()).map(AuditStatusEnum::getCode).collect(Collectors.toList());
        if (!balanceSources.contains(dto.getOrigin())) {
            throw new ServiceException("订单来源有误");
        }
        if (ObjectUtil.isNotNull(dto.getAuditStatus()) && !audits.contains(dto.getAuditStatus())) {
            throw new ServiceException("订单状态有误");
        }

        if (BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode().equals(dto.getOrigin())
                || BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode().equals(dto.getOrigin())
                || BalanceSourceTypeEnum.PREPAY_INCOME.getCode().equals(dto.getOrigin())

        ) {
            //收入直接添加就好
            business.setBalance(business.getBalance().add(dto.getUseBalance()));
        } else if (BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode().equals(dto.getOrigin())) {
            Assert.notNull(dto.getAuditStatus(), "财务审核状态不能为空！");
            if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                //取消订单收入 && 财务审核状态 = 审核通过 balance + 使用余额
                business.setBalance(business.getBalance().add(dto.getUseBalance()));
            } else {
                checkUserBalance(dto, business);
                //解锁使用余额
                //取消订单收入 && 财务审核状态 != 审核通过 use_balance - 使用余额
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            }
        } else if (BalanceSourceTypeEnum.ORDER_SPEND.getCode().equals(dto.getOrigin()) || BalanceSourceTypeEnum.MEMBER_SPEND.getCode().equals(dto.getOrigin())) {
            //会员订单支出、视频订单支出
            Assert.notNull(dto.getAuditStatus(), "订单状态有误不能为空！");
            if (ObjectUtil.isNotNull(dto.getIsBalancePay()) && StatusTypeEnum.YES.getCode().equals(dto.getIsBalancePay())) {
                checkValidBalance(dto, business);
                //余额支付 balance - 使用余额 条件: 有效余额 > 使用余额
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            } else if (AuditStatusEnum.UN_CHECK.getCode().equals(dto.getAuditStatus())) {
                checkValidBalance(dto, business);
                //下单锁定 use_balance + 使用余额  条件：有效余额 > 使用余额
                business.setUseBalance(business.getUseBalance().add(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            } else if (AuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
                checkBalance(dto, business);
                checkUserBalance(dto, business);
                //订单支付完成 use_balance - 使用余额、balance - 使用余额
                //若之前校验都无误 理应不需要验证数据  为确保无误 进行一次兜底校验
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            }
        } else if (BalanceSourceTypeEnum.PAYOUT_SPEND.getCode().equals(dto.getOrigin())) {
            Assert.notNull(dto.getBalanceAuditStatus(), "余额提现审核状态不能为空！");
            if (BusinessBalanceAuditStatusEnum.APPROVE.getCode().equals(dto.getBalanceAuditStatus())) {
                //审核通过 余额扣除
                checkBalance(dto, business);
                checkUserBalance(dto, business);
                //订单支付完成 use_balance - 使用余额、balance - 使用余额
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                business.setBalance(business.getBalance().subtract(dto.getUseBalance()));
            } else if (BusinessBalanceAuditStatusEnum.CANCEL.getCode().equals(dto.getBalanceAuditStatus())) {
                checkUserBalance(dto, business);
                business.setUseBalance(business.getUseBalance().subtract(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            } else {
                checkValidBalance(dto, business);
                //下单锁定 use_balance + 使用余额
                business.setUseBalance(business.getUseBalance().add(dto.getUseBalance()));
                dto.setUseBalance(BigDecimal.ZERO);
            }

        } else {
            throw new ServiceException("订单来源有误");
        }
        return business;
    }

    private void checkBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getBalance().compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家余额");
        }
    }

    private void checkUserBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getUseBalance().compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家使用余额");
        }
    }

    private void checkValidBalance(BusinessBalanceDTO dto, Business business) {
        if (business.getBalance().subtract(business.getUseBalance()).compareTo(dto.getUseBalance()) < 0) {
            throw new ServiceException("使用余额不能大于商家有效余额");
        }
    }

    @Override
    public List<BusinessAccountDetailVO> getUserMemberStatusBySeedId(Collection<String> seedIds) {
        if (CollUtil.isEmpty(seedIds)) {
            return new ArrayList<>();
        }
        
        // 根据种子ID查询用户详细信息
        BusinessAccountDetailDTO dto = new BusinessAccountDetailDTO();
        dto.setSeedIds(seedIds);
        return baseMapper.getBusinessAccountDetailVOs(dto);
    }

}
