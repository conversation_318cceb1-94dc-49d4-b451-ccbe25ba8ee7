package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.BalanceDetailTypeEnum;
import com.ruoyi.common.core.enums.BalanceSourceTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import com.wnkx.biz.business.mapper.BusinessBalancePrepayMapper;
import com.wnkx.biz.business.service.IBusinessBalanceDetailService;
import com.wnkx.biz.business.mapper.BusinessBalanceDetailMapper;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail(商家余额详情表)】的数据库操作Service实现
 * @createDate 2024-12-25 16:59:33
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BusinessBalanceDetailServiceImpl extends ServiceImpl<BusinessBalanceDetailMapper, BusinessBalanceDetail> implements IBusinessBalanceDetailService {
    private final BusinessBalancePrepayMapper businessBalancePrepayMapper;
    private final RemoteService remoteService;

    @Override
    public List<BusinessBalanceDetail> getValidBusinessBalanceDetailListByBusinessId(Long businessId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbd.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessBalanceDetail> validBusinessBalanceDetailList = baseMapper.getValidBusinessBalanceDetailList(businessId);
        if (CollUtil.isEmpty(validBusinessBalanceDetailList)) {
            return Collections.emptyList();
        }
        return validBusinessBalanceDetailList;
    }

    @Override
    public List<PayoutBusinessBalanceDetailVO> getBusinessBalanceDetailVOList(List<BusinessBalanceDetail> list, Integer loadRemark) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Business business = SpringUtils.getBean(IBusinessService.class).getById(list.get(0).getBusinessId());
        BigDecimal validBalance = business.getBalance().subtract(business.getUseBalance());
        if (validBalance.compareTo(BigDecimal.ZERO) == 0){
            return Collections.emptyList();
        }
        Map<String, BusinessBalancePrepay> prepayMap = new HashMap<>();
        Map<String, OrderVideoRefund> refundMap = new HashMap<>();
        List<PayoutBusinessBalanceDetailVO> resultList = new ArrayList<>();

        if (StatusTypeEnum.YES.getCode().equals(loadRemark)) {
            List<String> prepayIncomeOriginNumbers = list.stream().filter(vb -> vb.getOrigin() != null && List.of(BalanceSourceTypeEnum.PREPAY_INCOME.getCode(), BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()).contains(vb.getOrigin()))
                    .map(BusinessBalanceDetail::getOriginNumber).distinct().collect(Collectors.toList());
            List<String> cancelOriginNumbers = list.stream().filter(vb -> vb.getOrigin() != null
                    && (vb.getOrigin().equals(BalanceSourceTypeEnum.CANCEL_ORDER_PART_INCOME.getCode())
                    || vb.getOrigin().equals(BalanceSourceTypeEnum.CANCEL_ORDER_INCOME.getCode())
                    || vb.getOrigin().equals(BalanceSourceTypeEnum.CANCEL_CHOOSE_INCOME.getCode()))).map(BusinessBalanceDetail::getOriginNumber).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(prepayIncomeOriginNumbers)) {
                List<BusinessBalancePrepay> prepayList = businessBalancePrepayMapper.selectPrepayListByOriginNumbers(prepayIncomeOriginNumbers);
                prepayMap = prepayList.stream().collect(Collectors.toMap(BusinessBalancePrepay::getPrepayNum, pl -> pl, (e, r) -> e));
            }
            if (CollUtil.isNotEmpty(cancelOriginNumbers)) {
                List<OrderVideoRefund> orderVideoRefundList = remoteService.getOrderVideoRefundList(cancelOriginNumbers);
                refundMap = orderVideoRefundList.stream().collect(Collectors.toMap(OrderVideoRefund::getRefundNum, ovr -> ovr, (e, r) -> e));
            }
        }
        for (BusinessBalanceDetail businessBalanceDetail : list) {
            BusinessBalancePrepay businessBalancePrepay = prepayMap.get(businessBalanceDetail.getOriginNumber());
            OrderVideoRefund orderVideoRefunds = refundMap.get(businessBalanceDetail.getOriginNumber());
            PayoutBusinessBalanceDetailVO vo = BeanUtil.copyProperties(businessBalanceDetail, PayoutBusinessBalanceDetailVO.class);
            if (null != orderVideoRefunds && orderVideoRefunds.getRefundType().equals(businessBalanceDetail.getOrigin())) {
                vo.setRemark(orderVideoRefunds.getRefundCause());
            }
            if (null != businessBalancePrepay && List.of(BalanceSourceTypeEnum.PREPAY_INCOME.getCode(), BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()).contains(businessBalanceDetail.getOrigin())) {
                vo.setRemark(businessBalancePrepay.getApplyRemark());
                vo.setContainPresentedAmount(businessBalancePrepay.getContainPresentedAmount());
            }
            if (validBalance.compareTo(BigDecimal.ZERO) == 0){
                break;
            }
            if (businessBalanceDetail.getValidBalance().compareTo(BigDecimal.ZERO) == 0){
                continue;
            }
            //余额详情有效金额 <= 商家有效余额
            if (businessBalanceDetail.getValidBalance().compareTo(validBalance) <= 0) {
                vo.setUseBalance(businessBalanceDetail.getUseBalance().add(businessBalanceDetail.getLockBalance()));
                validBalance = validBalance.subtract(businessBalanceDetail.getValidBalance());
            }else {
                //余额详情有效金额 > 商家有效余额: 200   100
                vo.setUseBalance(businessBalanceDetail.getUseBalance().add(businessBalanceDetail.getLockBalance()).add(businessBalanceDetail.getValidBalance().subtract(validBalance)));
                vo.setValidBalance(validBalance);
                validBalance = BigDecimal.ZERO;
            }
            resultList.add(vo);
        }
        return resultList;
    }

    /**
     * @param businessBalanceDetail
     * @return
     */
    @Override
    public BusinessBalanceDetail saveBusinessBalanceDetail(BusinessBalanceDetail businessBalanceDetail) {
        BusinessBalanceDetail lastEntity = Optional.ofNullable(baseMapper.getLastEntity()).orElse(BusinessBalanceDetail.builder().id(0L).build());
        businessBalanceDetail.setNumber((BalanceDetailTypeEnum.VIDEO.getCode().equals(businessBalanceDetail.getNumberType()) ? OrderConstant.REFUND_NUM_PREFIX_WN : OrderConstant.PREPAY_NUM) + String.format("%06d", lastEntity.getId() + 1));
        baseMapper.insert(businessBalanceDetail);
        return businessBalanceDetail;
    }

    @Override
    public List<BusinessBalanceDetail> getDetailListByNumbers(List<String> numbers) {
        return baseMapper.getDetailListByNumbers(numbers);
    }


}




