package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailLockInfoDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_balance_detail_lock(商家余额详情锁定表)】的数据库操作Mapper
* @createDate 2024-12-27 14:40:22
* @Entity com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock
*/
public interface BusinessBalanceDetailLockMapper extends SuperMapper<BusinessBalanceDetailLock> {

    /**
     * 获取有效提现数据
     * @param dto
     * @return
     */
    List<BusinessBalanceDetailLockInfoVO> queryValidList(@Param("dto") BusinessBalanceDetailLockInfoDTO dto);

    /**
     * 根据锁定单号获取商家余额锁定数据
     * @param numbers
     * @return
     */
    default List<BusinessBalanceDetailLock> queryListByNumbers(List<String> numbers){
        return this.selectList(new LambdaQueryWrapper<BusinessBalanceDetailLock>()
                .in(BusinessBalanceDetailLock::getNumber, numbers));

    }

    /**
     * 设置余额锁定的状态
     * @param number
     * @param status
     */
    default void setStatus(String number, Integer status){
        this.update(null, new LambdaUpdateWrapper<BusinessBalanceDetailLock>()
                        .set(BusinessBalanceDetailLock::getStatus, status)
                .eq(BusinessBalanceDetailLock::getNumber, number));
    }

    /**
     * 根据单号获取列表数据
     *
     * @param number
     * @return
     */
    default List<BusinessBalanceDetailLock> queryListByNumber(String number) {
        return this.selectList(new LambdaQueryWrapper<BusinessBalanceDetailLock>()
                .eq(BusinessBalanceDetailLock::getNumber, number));

    }

    /**
     * 获取视频订单提现记录
     */
    List<WithdrawDepositRecordVO> withdrawDepositRecord(@Param("dto") WithdrawDepositRecordDTO dto);
}




