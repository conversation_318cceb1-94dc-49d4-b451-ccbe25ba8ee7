package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailStatisticsVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail(商家余额详情表)】的数据库操作Mapper
 * @createDate 2024-12-25 16:59:33
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail
 */
public interface BusinessBalanceDetailMapper extends SuperMapper<BusinessBalanceDetail> {

    /**
     * 获取有效余额详情列表
     *
     * @param businessId
     * @return
     */
    List<BusinessBalanceDetail> getValidBusinessBalanceDetailList(Long businessId);

    /**
     * 根据单号列表 获取余额详情列表
     *
     * @param numbers
     * @return
     */
    default List<BusinessBalanceDetail> getDetailListByNumbers(List<String> numbers) {
        return this.selectList(new LambdaQueryWrapper<BusinessBalanceDetail>()
                .in(BusinessBalanceDetail::getNumber, numbers));

    }

    /**
     * 余额详情统计
     * @param businessId
     * @return
     */
    BusinessBalanceDetailStatisticsVO statistics(Long businessId);

    /**
     * 获取最后一条数据
     *
     * @return
     */
    default BusinessBalanceDetail getLastEntity() {
        return this.selectOne(new LambdaQueryWrapper<BusinessBalanceDetail>().orderByDesc(BusinessBalanceDetail::getId).last("limit 1"));
    }


}




