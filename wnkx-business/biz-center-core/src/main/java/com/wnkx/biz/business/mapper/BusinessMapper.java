package com.wnkx.biz.business.mapper;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ResidentBusinessDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.ResidentBusinessVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business(商家表)】的数据库操作Mapper
 * @createDate 2024-06-20 10:30:40
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.Business
 */
@Mapper
public interface BusinessMapper extends SuperMapper<Business> {

    /**
     * 统计商家数量
     *
     * @return
     */
    BusinessStatisticsVO businessStatistics();

    /**
     * 获取商家余额总数
     *
     * @return
     */
    BigDecimal businessBalanceTotal();

    /**
     * 获取商家数据
     *
     * @param id
     * @return
     */
    Business getBusinessForUpdate(Long id);

    /**
     * 获取商家列表数据
     *
     * @param ids
     * @return
     */
    List<Business> businessListForUpdate(@Param("ids") List<Long> ids);

    /**
     * 获取入驻会员列表
     *
     * @param dto
     * @return
     */
    List<ResidentBusinessVO> queryResidentBusiness(ResidentBusinessDTO dto);


    /**
     * 查询商家列表
     * @param dto
     * @return
     */
    List<BusinessDetailVO> queryBusinessList(BusinessDTO dto);

    default void setBusinessWechatUrlByMemberCode(String businessWechatUrl, String memberCode) {
        update(null, new LambdaUpdateWrapper<Business>()
                .set(Business::getBusinessWechatUrl, businessWechatUrl)
                .eq(Business::getMemberCode, memberCode));
    }

    default String getBusinessWechatUrlByMemberCode(String memberCode) {
        return selectOne(new LambdaQueryWrapper<Business>()
                .select(Business::getBusinessWechatUrl)
                .eq(Business::getMemberCode, memberCode)
                .last("limit 1")
        ).getBusinessWechatUrl();
    }

    String getBusinessMainAccountExternalUserId(Long businessId);

    default List<Business> getUnInitWechatBusinessList(){
        return selectList(new LambdaQueryWrapper<>(Business.class)
                .select(Business::getId)
                .select(Business::getMemberCode)
                .isNull(Business::getBusinessWechatUrl)
        );
    }

    /**
     * 根据商家id获取商家编码
     * @param businessId 商家id
     * @return
     */
    default String getmemberCodeByBusinessId(Long businessId){
        return selectOne(new LambdaQueryWrapper<Business>()
                .select(Business::getMemberCode)
                .eq(Business::getId, businessId)
                .last("limit 1")
        ).getMemberCode();
    }

    /**
     * 商家排单时 更新最近排单时间
     */
    default void updateRecentOrderTime(Long businessId) {
        update(null, new LambdaUpdateWrapper<Business>()
                .eq(Business::getId, businessId)
                .set(Business::getIsExistRecentOrder, StatusTypeEnum.YES.getCode())
                .set(Business::getRecentOrderTime, DateUtil.date())
        );
    }

    /**
     * 设置支付成功状态为成功（微信/支付宝支付成功、对公、全币种提交凭证）
     * @param businessId
     */
    default void updatePaySucceed(Long businessId) {
        update(null, new LambdaUpdateWrapper<Business>()
                .eq(Business::getId, businessId)
                .set(Business::getPaySucceed, StatusTypeEnum.YES.getCode())
        );
    }

    /**
     * 获取商家列表 可通过businessId进一步筛选
     */
    default List<Business> selectBusinessList(Long businessId) {
        return selectList(new LambdaQueryWrapper<Business>()
                .eq(ObjectUtil.isNotNull(businessId), Business::getId, businessId)
        );
    }

    default List<Business> getAllBusinessCodeList(){
        return selectList(new LambdaQueryWrapper<>(Business.class)
                .select(Business::getId)
                .select(Business::getMemberCode)
        );
    }

    List<BusinessOwnerAccountContactUserDTO> selectBusinessOwnerUserContactUserName();

    BusinessOwnerAccountContactUserDTO getBusinessOwnerUserContactUserNameByMemberCode(@Param("memberCode") String memberCode);
}
