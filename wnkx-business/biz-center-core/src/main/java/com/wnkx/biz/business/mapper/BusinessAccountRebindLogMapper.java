package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.enums.AuditStatusEnum;
import com.ruoyi.common.core.enums.StatusEnum;
import com.ruoyi.system.api.domain.entity.BusinessAccountRebindLog;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;

@Mapper
public interface BusinessAccountRebindLogMapper extends SuperMapper<BusinessAccountRebindLog> {


    /**
     * 解绑
     * @param businessAccount
     * @param bizUserId
     * @return
     */
    default BusinessAccountRebindLog selectRebindLog(BusinessAccount businessAccount, Long bizUserId) {
        return this.selectOne(Wrappers.lambdaQuery(BusinessAccountRebindLog.class)
                .eq(BusinessAccountRebindLog::getAuditStatus, AuditStatusEnum.APPROVE.getCode())
                .eq(BusinessAccountRebindLog::getAccount, businessAccount.getAccount())
                .eq(BusinessAccountRebindLog::getBizUserId, bizUserId)
                .and(wrapper -> wrapper
                .eq(BusinessAccountRebindLog::getStatus, StatusEnum.ENABLED.getCode())
                .or()
                .eq(BusinessAccountRebindLog::getStatus, StatusEnum.UN_ENABLED.getCode())));
    }

    default BusinessAccountRebindLog selectOneByApplyId(Long applyId) {
        return selectOne(Wrappers.lambdaQuery(BusinessAccountRebindLog.class).eq(BusinessAccountRebindLog::getApplyId, applyId));
    }

    /**
     * 设置日志为解绑状态
     * @param account
     * @param businessId
     * @param bizUserId
     * @return
     */
    default void unbind(String account,Long businessId, Long bizUserId) {
        this.update(new BusinessAccountRebindLog(), new LambdaUpdateWrapper<BusinessAccountRebindLog>()
                .set(BusinessAccountRebindLog::getStatus, 3)
                .set(BusinessAccountRebindLog::getUpdateTime, new Date())
                .eq(BusinessAccountRebindLog::getAccount, account)
                .eq(BusinessAccountRebindLog::getBusinessId, businessId)
                .eq(BusinessAccountRebindLog::getBizUserId, bizUserId)

        );

    }

    /**
     * 解绑
     */
    default void updateRebindLogToUnbind(BusinessAccount businessAccount, Long bizUserId) {
        update(null, new LambdaUpdateWrapper<BusinessAccountRebindLog>()
                .eq(BusinessAccountRebindLog::getAuditStatus, AuditStatusEnum.APPROVE.getCode())
                .eq(BusinessAccountRebindLog::getAccount, businessAccount.getAccount())
                .eq(BusinessAccountRebindLog::getBizUserId, bizUserId)
                .in(BusinessAccountRebindLog::getStatus, StatusEnum.ENABLED.getCode(), StatusEnum.UN_ENABLED.getCode())
                .set(BusinessAccountRebindLog::getStatus, 3)
        );
    }
}
