package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowSaveDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowValidListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceAuditFlowDetailExportVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_balance_audit_flow(余额提现审核表)】的数据库操作Service
* @createDate 2024-08-09 18:19:51
*/
public interface IBusinessBalanceAuditFlowService extends IService<BusinessBalanceAuditFlow> {


    /**
     * 获取余额提现审核列表
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlowVO> queryList(BusinessBalanceAuditFlowListDTO dto);


    /**
     * 获取提现明细导出
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlowDetailExportVO> queryExportList(BusinessBalanceAuditFlowListDTO dto);

    /**
     * 加载对应数据
     * @param list
     * @return
     */
    List<BusinessBalanceAuditFlowVO> load(List<BusinessBalanceAuditFlowVO> list);

    /**
     * 添加
     * @param dto
     * @return
     */
    BusinessBalanceAuditFlow addBusinessBalanceAuditFlow(BusinessBalanceAuditFlowSaveDTO dto);

    /**
     * 根据ID获取余额提现详情
     * @param id
     * @return
     */
    BusinessBalanceAuditFlowDetailVO getDetailById(Long id);


    /**
     * 获取统计数据*
     * @return
     */
    BusinessBalanceAuditFlowStatisticsVO getStatistics();

    /**
     * 获取有效余额提现审核数据
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlow> queryValidList(BusinessBalanceAuditFlowValidListDTO dto);

    void markNotice(Long id);
}
