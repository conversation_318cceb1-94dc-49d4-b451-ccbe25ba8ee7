package com.wnkx.biz.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelVisitFlow;
import com.wnkx.biz.channel.service.DistributionChannelVisitFlowService;
import com.wnkx.biz.channel.mapper.DistributionChannelVisitFlowMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_visit_flow(分销渠道访问流水数据)】的数据库操作Service实现
* @createDate 2025-03-04 10:59:09
*/
@Service
public class DistributionChannelVisitFlowServiceImpl extends ServiceImpl<DistributionChannelVisitFlowMapper, DistributionChannelVisitFlow>
    implements DistributionChannelVisitFlowService{

}




