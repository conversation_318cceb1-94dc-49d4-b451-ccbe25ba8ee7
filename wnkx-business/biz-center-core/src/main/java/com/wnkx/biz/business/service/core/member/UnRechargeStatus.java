package com.wnkx.biz.business.service.core.member;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.MemberStatusDto;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.business.service.core.BaseMemberStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 非会员
 * @create :2024-06-21 17:40
 **/
@Component
@RequiredArgsConstructor
public class UnRechargeStatus extends BaseMemberStatus {

    private final IBusinessService businessService;
    private final IBusinessAccountService businessAccountService;

    @Override
    public void recharge(MemberStatusDto memberStatusDto) {
        //生成会员编码
        String memberCode = businessAccountService.initMemberCode(memberStatusDto.getBusinessId());
        //需要校验会员编码是否存在
        BusinessDTO businessDTO = super.getBusinessDto(memberStatusDto);
        businessDTO.setMemberCode(memberCode);
        businessDTO.setMemberFirstTime(businessDTO.getMemberLastTime());
        businessDTO.setMemberFirstType(memberStatusDto.getChoosePackageType());
        businessDTO.setMemberValidity(getResultDate(businessDTO.getMemberLastTime(),memberStatusDto.getMonthNum(),memberStatusDto.getPresentedTime(), memberStatusDto.getPresentedTimeType()));
        businessService.update(businessDTO);
        businessService.initBusinessWechatRemark(memberStatusDto.getBusinessId(),memberCode);
        businessService.initBusinessWechatUrl(memberCode);
    }


    @Override
    public void preExpire(MemberStatusDto business) {
        throw new ServiceException("非会员无法更新状态为：[即将过期]状态");
    }

    @Override
    public void expire(MemberStatusDto business) {
        throw new ServiceException("非会员无法更新状态为：[过期]状态");
    }
}
