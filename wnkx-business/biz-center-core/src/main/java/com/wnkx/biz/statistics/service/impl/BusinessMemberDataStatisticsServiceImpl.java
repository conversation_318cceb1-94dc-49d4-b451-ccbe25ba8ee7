package com.wnkx.biz.statistics.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessMemberDataStatistics;
import com.wnkx.biz.statistics.mapper.BusinessMemberDataStatisticsMapper;
import com.wnkx.biz.statistics.service.IBusinessMemberDataStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【business_member_data_statistics(商家会员数据统计)】的数据库操作Service实现
 * @createDate 2025-06-17 17:01:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessMemberDataStatisticsServiceImpl extends ServiceImpl<BusinessMemberDataStatisticsMapper, BusinessMemberDataStatistics>
        implements IBusinessMemberDataStatisticsService {

}




