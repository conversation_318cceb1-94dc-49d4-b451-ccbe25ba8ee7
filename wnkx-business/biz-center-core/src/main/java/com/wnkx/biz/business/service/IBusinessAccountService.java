package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.*;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.*;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowAuditDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowValidListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceDetailLockInfoDTO;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.casus.BalancePayOutDTO;
import com.ruoyi.system.api.domain.entity.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.SaveBizUserVO;
import com.ruoyi.system.api.domain.vo.order.BusinessRemarkFlowVO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_account(商家账号表)】的数据库操作Service
 * @createDate 2024-06-20 10:30:41
 */
@Validated
public interface IBusinessAccountService extends IService<BusinessAccount> {

    /**
     * 查询单条账号数据(已绑定登录账号的商家账号)
     * ownerAccount、businessVO、businessAccountVOS数据无效
     *
     * @param dto
     * @return
     */
    BusinessAccountVO queryOne(BusinessAccountDTO dto);


    /**
     * 查询账号列表(已绑定登录账号的商家账号)
     * ownerAccount、businessVO、businessAccountVOS数据无效
     *
     * @param dto
     * @return
     */
    List<BusinessAccountVO> queryList(BusinessAccountDTO dto);

    /**
     * 查询账号列表(商家账号列表，不管是否绑定登录账号)
     * ownerAccount、businessVO、businessAccountVOS数据无效
     *
     * @param dto
     * @return
     */
    List<BusinessAccountVO> list(BusinessAccountDTO dto);

    /**
     * 根据unionId获取账号数据（如果登录账号未绑定商家账号，返回一个mock数据）
     *
     * @param unionid
     * @return
     */
    BusinessAccountVO getBusinessAccount(String unionid);

    /**
     * 手机号验证码登录
     *
     * @param dto
     * @return
     */
    PhoneLoginVO phoneLogin(PhoneLoginDTO dto);

    /**
     * 修改登录账号的登录时间
     *
     * @param businessAccount
     */
    void updateLoginTime(BusinessAccountVO businessAccount);

    /**
     * 生成二维码
     *
     * @param type
     * @param code
     * @return
     */
    QrCodeDTO generateQrcode(Integer type, String code);

    /**
     * 检查当前账号与手机号是否相同
     * * @param dto
     */
    void bizUserCheckPhone(CheckPhoneDTO dto);

    /**
     * 修改登录账号手机号
     *
     * @param dto
     */
    void updateBizUserPhone(UpdatePhoneDTO dto);

    /**
     * 检查手机号
     *
     * @param phone
     */
    void checkPhone(String phone);

    /**
     * 修改登录账号微信
     *
     * @param dto
     */
    String updateBizUserWeChat(UpdateWeChatDTO dto);

    /**
     * 修改登录账号名称
     *
     * @param dto
     */
    void bizUserUpdateName(UpdateNameDTO dto);

    /**
     * 修改账号数据(内部使用)
     *
     * @param dto
     */
    void update(BusinessAccountDTO dto);

    /**
     * 禁用账号（商家端使用）
     *
     * @param dto
     */
    void updateBusinessAccountStatus(@Valid BusinessAccountStatusDTO dto);

    /**
     * 设置用户ExternalUserid
     *
     * @param unionId        用户unionId
     * @param externalUserid 外部用户id
     */
    void setUserExternalUserid(String unionId, String externalUserid);

    /**
     * 下单用户列表(商家账号列表，不管是否绑定登录账号)
     *
     * @param dto
     * @return
     */
    List<BusinessAccountVO> orderUserList(BusinessAccountDTO dto);

    /**
     * 根据申请列表初始化子账号
     *
     * @param businessAccountApply
     * @return
     */
    BusinessAccount initBusinessSon(BusinessAccountApply businessAccountApply);

    /**
     * 初始化账号
     *
     * @param isOwner
     * @param ownerAccount
     * @param unionid
     * @return
     */
    String initAccount(Integer isOwner, String ownerAccount, String unionid);

    /**
     * 检查扫码用户是否正确
     *
     * @param ticket
     * @param account
     * @return
     */
    BusinessAccountVO checkAccount(String ticket, String account);

    /**
     * 获取账号详情
     *
     * @param dto
     * @return
     */
    BusinessAccountVO getBusinessAccountOne(BusinessAccountDTO dto);

    /**
     * 套餐类型
     *
     * @param packageType
     * @return
     */
    BusinessMemberActivity getBusinessMemberActivity(Integer packageType);


    /**
     * 获取账号数据列表
     *
     * @param dto
     * @return
     */
    List<BusinessAccountDetailVO> getBusinessAccountDetailVOs(BusinessAccountDetailDTO dto);

    /**
     * 根据账号数据补全账号信息
     *
     * @param businessAccount
     * @return
     */
    BusinessAccountVO getBusinessAccountVO(BusinessAccountVO businessAccount);

    /**
     * 填充数据
     *
     * @param businesses
     * @return
     */
    List<BusinessDetailVO> loadBusinessDetail(List<Business> businesses);

    List<BusinessDetailVO> loadBusinessVODetail(List<BusinessDetailVO> businessDetailVOS);

    /**
     * 获取导出列表
     *
     * @return
     */
    List<BusinessDetailExportVO> businessListExport(List<BusinessDetailVO> businessDetailVOS);

    /**
     * 刷新缓存
     */
    void refreshToken();

    /**
     * 获取加密手机号
     *
     * @param phone
     * @return
     */
    String getPhone(String phone);


    /**
     * 清除用户ExternalUserid
     *
     * @param externalUserId
     */
    void cleanUserExternalUserid(String externalUserId);

    /**
     * 换绑主账号
     *
     * @param dto
     */
    void exchangeBindOwner(UnBindOwnerDTO dto);

    /**
     * 发票信息管理(商家端使用)
     *
     * @param dto
     */
    void updateInvoice(@Valid BusinessInvoiceDTO dto);

    /**
     * 初始化会员code
     *
     * @param businessId
     * @return
     */
    String initMemberCode(Long businessId);

    /**
     * 更新Token
     *
     * @param businessIds
     * @param business
     */
    void updateLoginBusiness(List<Long> businessIds, BusinessAccountTokenDTO business);

    /**
     * 会员状态流转，如果使用余额则扣除余额
     *
     * @param dto
     */
    BusinessAccountVO flowMember(@Valid FlowMemberDto dto);

    /**
     * 解绑微信
     *
     * @param dto
     */
    void unbindLoginUser(UnbindAccountDTO dto);


    /**
     * 检查手机状态
     *
     * @param dto
     * @return
     */
    CheckPhoneVO checkPhoneStatus(CheckPhoneDTO dto);

    /**
     * 绑定账号
     *
     * @param dto
     */
    void bindingLoginUser(BindingAccountDTO dto);

    /**
     * 换绑微信
     *
     * @param dto
     */
    @Deprecated(since = "2025-01-16", forRemoval = true)
    void rebind(@Valid RebindDTO dto);

    /**
     * 新增登录账号
     *
     * @param dto
     * @return
     */
    SaveBizUserVO saveBizUser(BizUserSaveDTO dto);

    /**
     * 获取账号列表
     *
     * @param dto
     * @return
     */
    List<BizUserListVO> bizUserList(BizUserListDTO dto);


    /**
     * 获取登录账号详情列表
     *
     * @param dto
     * @return
     */
    List<BizUserDetailVO> bizUserDetailList(BizUserDetailListDTO dto);

    /**
     * 修改登录账号
     *
     * @param dto
     */
    void editBizUser(BizUserEditDTO dto);

    /**
     * 修改登录账号手机号
     *
     * @param dto
     */
    void editBizUserPhone(BizUserEditPhoneDTO dto);

    /**
     * 初始化商家
     *
     * @param bizUser
     * @return
     */
    Business initBusinessV1(@Validated BizUser bizUser);

    /**
     * 删除商家数据*
     *
     * @param businessId
     */
    void deleteBusiness(Long businessId);

    /**
     * 根据bizUserId获取登录账号数据
     *
     * @param bizUserId
     * @return
     */
    BusinessAccount getByBizUserId(Long bizUserId);

    List<String> businessManagerList(Integer type);


    List<BusinessSubAccountListVo> getSubAccountList(Long businessId);

    List<BusinessAccountDetailVO> getBizUserInfo(BusinessAccountDetailDTO dto);

    void editBizUserStatus(BizUserEditStatusDTO dto);

    /**
     * 商家回访-填写回访记录-回访账号下拉框
     */
    List<BusinessAccountVO> writeReturnVisitAccount(Long businessId);

    /**
     * ------------------------余额处理开始------------------------------
     * 获取商家总余额
     *
     * @return
     */
    BigDecimal businessBalanceTotal();

    /**
     * 发起提现
     *
     * @param dto
     */
    void balancePayOut(@Valid BalancePayOutDTO dto);

    /**
     * 余额提现审核*
     *
     * @param dto
     */
    void auditPayOut(@Valid BusinessBalanceAuditFlowAuditDTO dto);

    /**
     * 获取有效余额提现审核数据
     *
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(BusinessBalanceAuditFlowValidListDTO dto);

    /**
     * 获取有效余额锁定数据
     *
     * @param dto
     * @return
     */
    List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto);

    /**
     * 获取视频订单提现记录
     */
    List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto);
    /**
     * ------------------------余额处理结束------------------------------
     */

    /**
     * ------------------------商家处理开始-----------------------------
     */

    /**
     * 商家统计
     *
     * @return
     */
    BusinessStatisticsVO businessStatistics();

    /**
     * 获取入驻商家列表
     *
     * @param dto
     * @return
     */
    List<ResidentBusinessVO> queryResidentBusiness(ResidentBusinessDTO dto);

    /**
     * 入驻会员导出
     *
     * @param dto
     */
    List<ResidentBusinessExportVO> exportResidentBusinessList(ResidentBusinessDTO dto);

    /**
     * 获取商家余额列表
     *
     * @param dto
     * @return
     */
    List<BusinessBalanceVO> businessBalanceList(BusinessDTO dto);

    /**
     * 获取余额导出数据
     *
     * @param businessBalanceVOS
     * @return
     */
    List<BusinessBalanceExportVO> exportBusinessBalanceExportList(List<BusinessBalanceVO> businessBalanceVOS);

    /**
     * 填充数据
     *
     * @param businesses
     * @return
     */
    List<BusinessBalanceVO> loadBusinessAccount(List<Business> businesses);

    /**
     * 商家状态修改（运营端使用）
     *
     * @param dto
     */
    void updateBusinessStatus(@Valid BusinessStatusDTO dto);

    /**
     * 商家备注修改（运营端使用）
     *
     * @param dto
     */
    void updateBusinessRemark(@Valid BusinessRemarkDTO dto);

    /**
     * 获取商家备注流水（运营端使用）
     *
     * @param businessId
     * @return
     */
    List<BusinessRemarkFlowVO> getListByBusinessId(Long businessId);

    /**
     * 编辑商家数据（运营端使用）
     *
     * @param dto
     */
    void updateBusiness(@Valid EditBusinessDTO dto);

    /**
     * 修改会员时间
     *
     * @param dto
     */
    void updateMemberValidity(@Valid EditMemberValidityDTO dto);

    /**
     * 检查是否不可结算
     *
     * @param businessId
     * @return
     */
    CheckUnableSettlementVO checkUnableSettlement(Long businessId, boolean isInner);

    /**
     * 根据商家Id获取商家有效期列表
     *
     * @param businessId
     * @return
     */
    List<BusinessMemberValidityFlowVO> getBusinessMemberValidityFlowListByBusinessId(Long businessId);

    /**
     * 获取商家主账号换绑记录列表
     *
     * @param businessId
     * @return
     */
    List<BusinessOwnerFlowVO> getOwnerFlowListByBusinessId(Long businessId);

    /**
     * 获取商家有效数据列表
     *
     * @param dto
     * @return
     */
    List<BusinessMemberValidityFlowVO> getBusinessMemberValidFlowList(BusinessMemberValidityFlowDTO dto);

    /**
     * 获取商家有效数据关联订单
     *
     * @param businessId
     * @return
     */
    BusinessMemberValidityFlowVO getRelationOrder(Long businessId);

    /**
     * 获取商家有效数据导出数据
     *
     * @param dto
     * @return
     */
    List<BusinessMemberValidityFlowExportVO> getBusinessMemberValidFlowExportList(BusinessMemberValidityFlowDTO dto);

    /**
     * 修改商家名称（商家端使用）
     *
     * @param dto
     */
    void resetBusinessName(@Valid ResetBusinessNameDTO dto);

    /**
     * 修改商家规模
     *
     * @param dto
     */
    void resetBusinessScale(@Valid ResetBusinessScaleDTO dto);

    /**
     * 初始化商家名称、账号名称、商家规模
     *
     * @param dto
     */
    void initBusinessInfo(InitBusinessInfoDTO dto);


    /**
     * 批量修改关联人员（运营端）
     *
     * @param dto
     */
    void updateWaiter(@Valid UpdateWaiterDTO dto);


    /**
     * ------------------------商家处理结束-----------------------------
     */

    /**
     * 根据裂变种草id获取用户信息
     *
     * @param seedIds 种草官ID集合
     * @return 用户详细信息列表
     */
    List<BusinessAccountDetailVO> getUserMemberStatusBySeedId(Collection<String> seedIds);

    /**
     * 查询商家用户下拉框
     */
    List<BusinessAccountSelectVO> businessAccountSelect(BusinessAccountDetailDTO dto);
}
