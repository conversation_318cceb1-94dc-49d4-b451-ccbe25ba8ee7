package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ResidentBusinessDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.domain.vo.biz.business.ResidentBusinessVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business(商家表)】的数据库操作Service
 * @createDate 2024-06-20 10:30:40
 */
@Validated
public interface IBusinessService extends IService<Business> {

    /**
     * 获取商家vo数据
     * @param dto
     * @return
     */
    BusinessVO queryBusinessVo(BusinessDTO dto);

    /**
     * 获取商家余额信息
     * @param dto
     * @return
     */
    BusinessBalanceInfoVO getBusinessBalanceDetailVo(BusinessDTO dto);
    /**
     * 获取商家列表通用接口
     *
     * @param dto
     * @return
     */
    List<Business> queryList(BusinessDTO dto);

    /**
     * 查询商家列表
     * @param dto
     * @return
     */
    List<BusinessDetailVO> queryBusinessList(BusinessDTO dto);
    /**
     * 查询入驻会员列表
     * @param dto
     * @return
     */
    List<ResidentBusinessVO> queryResidentBusiness(ResidentBusinessDTO dto);

    /**
     * 查询单个商家
     *
     * @param dto
     * @return
     */
    Business queryOne(BusinessDTO dto);


    /**
     * 修改商家数据
     *
     * @param dto
     */
    void update(BusinessDTO dto);

    /**
     * 获取商家企业微信URl
     *
     * @param memberCode 商家编码
     */
    String getBusinessWechatUrlByMemberCode(String memberCode);

    /**
     * 初始化商家企业微信URl
     *
     * @param memberCode 商家编码
     */
    void initBusinessWechatUrl(String memberCode);

    /**
     * 初始化商家微信备注
     *
     * @param memberCode 商家编码
     */
    void initBusinessWechatRemark(Long businessId,String memberCode);

    /**
     * 获取商家主账号外部用户id
     *
     * @param businessId 商家id
     * @return
     */
    String getBusinessMainAccountExternalUserId(Long businessId);

    /**
     * 获取还未初始化企业微信的商家列表
     */
    List<Business> getUnInitWechatBusinessList();

    /**
     * 批量初始化商家微信url
     *
     * @param businessList 商家编码
     */
    void initBusinessWechatUrl(List<Business> businessList);

    /**
     * 根据商家id获取商家会员编码
     * @param businessId 商家id
     * @return
     */
    String getmemberCodeByBusinessId(Long businessId);

    /**
     * 商家排单时 更新最近排单时间 以及更新商家回访事件
     */
    void updateRecentOrderTime(Long businessId);

    /**
     * 流转待支付时间
     * @param businessId
     */
    void updatePaySucceed(Long businessId);

    /**
     * 定时更新回访表
     */
    void updateBusinessCallback(Long businessId);


    /**
     * 获取还未初始化企业微信的商家列表
     */
    List<Business> getAllBusinessCodeList();

    /**
     * 获取工作台-财务部 提现待审批、分销待结算
     * @return
     */
    WorkbenchVO getFinanceWorkbenchVo();

    List<BusinessOwnerAccountContactUserDTO> getBusinessOwnerUserContactUserName();

    BusinessOwnerAccountContactUserDTO getBusinessOwnerUserContactUserNameByMemberCode(String memberCode);

    void initBusinessWechatUrlBaseOnContactUser(List<BusinessOwnerAccountContactUserDTO> allBusinessList, HashMap<String, String> kvMap);
}
