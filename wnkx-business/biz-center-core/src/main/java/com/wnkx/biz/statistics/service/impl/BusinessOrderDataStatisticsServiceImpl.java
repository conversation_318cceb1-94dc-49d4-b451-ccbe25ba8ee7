package com.wnkx.biz.statistics.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessOrderDataStatistics;
import com.wnkx.biz.statistics.service.IBusinessOrderDataStatisticsService;
import com.wnkx.biz.statistics.mapper.BusinessOrderDataStatisticsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【business_order_data_statistics(商家排单数统计)】的数据库操作Service实现
 * @createDate 2025-06-17 17:01:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessOrderDataStatisticsServiceImpl extends ServiceImpl<BusinessOrderDataStatisticsMapper, BusinessOrderDataStatistics>
        implements IBusinessOrderDataStatisticsService {

}




