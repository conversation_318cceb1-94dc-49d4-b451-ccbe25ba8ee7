package com.wnkx.biz.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.channel.*;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.dto.system.EditMemberDiscountDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel(分销渠道信息表)】的数据库操作Service
 * @createDate 2024-09-24 17:02:50
 */
public interface IDistributionChannelService extends IService<DistributionChannel> {


    /**
     * 根据分销渠道Id获取分销渠道详情
     *
     * @param distributionChannelId
     * @return
     */
    DistributionChannelDetailVO getByDistributionChannelId(Long distributionChannelId);

    /**
     * 获取裂变详情
     * @param distributionChannelId
     * @return
     */
    FissionChannelVO getFissionChannelVOById(Long distributionChannelId);

    /**
     * 获取分销渠道列表
     *
     * @param dto
     * @return
     */
    List<DistributionChannelVO> queryList(DistributionChannelListDTO dto);

    /**
     * 查询裂变渠道列表
     * @param dto
     * @return
     */
    List<FissionChannelVO> queryFissionChannelList(DistributionChannelListDTO dto);

    /**
     * 填充数据
     * @param fissionChannelVOS
     */
    void fillFissionChannelVO(List<FissionChannelVO> fissionChannelVOS);
    /**
     * 获取分销渠道条件统计
     *
     * @param dto
     * @return
     */
    DistributionChannelStatisticsVO statistics(DistributionChannelListDTO dto);

    /**
     * 裂变统计条件统计
     * @param dto
     * @return
     */
    DistributionChannelStatisticsVO fissionStatistics(DistributionChannelListDTO dto);


    /**
     * 新增分销渠道
     *
     * @param dto
     * @return
     */
    SaveDistributionChannelVO saveDistribution(DistributionChancelSaveDTO dto);

    /**
     * 修改分销渠道
     *
     * @param dto
     */
    void editDistribution(DistributionChancelEditDTO dto);

    /**
     * 修改分销渠道状态
     *
     * @param dto
     */
    void updateStatus(DistributionStatusDTO dto);

    /**
     * 分销渠道统计
     *
     * @param dto
     * @return
     */
    DistributionChannelStatisticsVO statistics(DistributionChancelStatisticsDTO dto);

    /**
     * 根据种草码 获取分销渠道信息
     */
    DistributionChannelVO getDistributionChannelBySeedCode(String seedCode);

    /**
     * 根据种草码 获取分销渠道实体信息
     *
     * @param seedCode
     * @return
     */
    DistributionChannel getDistributionChannelEntityBySeedCode(String seedCode);

    /**
     * 保存分销渠道订单数据
     *
     * @param dto
     */
    void saveDistributionChannelOrder(DistributionChannelOrderDTO dto);

    /**
     * 保存渠道订单信息
     *
     * @param dto
     */
    void saveBatchDistributionChannelOrder(DistributionChannelOrderBatchDTO dto);


    /**
     * 根据专属链接获取企微地址
     *
     * @param linkCode
     * @return
     */
    String getWeChatUrlByLinkCode(String linkCode);

    /**
     * 根据专属链接获取分销渠道
     *
     * @param linkCode
     * @return
     */
    DistributionChannel getByLinkCode(String linkCode);

    /**
     * 获取渠道邀请数据列表
     *
     * @param dto
     * @return
     */
    List<ChannelInviteVO> inviteList(InviteListDTO dto);

    /**
     * 获取私密信息
     *
     * @param id
     * @return
     */
    ChannelPrivacyInfoVO privacyInfo(Long id);

    /**
     * 获取渠道注册列表
     *
     * @return
     */
    List<ChannelInviteVO> channelRegisterList();

    /**
     * 获取渠道详情数据
     *
     * @return
     */
    DistributionChannelInfoVO channelInfo();

    /**
     * 预览分销渠道海报
     */
    void previewPoster(Long id, HttpServletResponse response);

    /**
     * 预览裂变渠道海报
     * @param id
     * @param response
     */
    void previewFissionPoster(Long id, HttpServletResponse response);

    /**
     * 下载分销渠道海报
     */
    void downloadPoster(Long id, HttpServletResponse response);
    /**
     * 下载分销渠道海报
     */
    void downloadManagerPoster(Long id, HttpServletResponse response);

    /**
     * 下载裂变渠道海报
     * @param id
     * @param response
     */
    void downloadFissionManagerPoster(Long id, HttpServletResponse response);

    /**
     * 下载全部分销渠道海报
     */
    void downloadAllPoster(HttpServletResponse response);

//    /**
//     * 根据id列表下载渠道海报
//     * @param channelIds
//     * @param response
//     */
//    void downloadAllPosterByIds(List<Long> channelIds, HttpServletResponse response);

    /**
     * 根据ID列表下载裂变海报
     * @param channelIds
     * @param response
     */
    void downloadAllFissionPosterByIds(List<Long> channelIds, HttpServletResponse response);


    /**
     * 获取创建人列表
     * @param name
     * @return
     */
    List<SysUserVO> createUserList(String name);

    /**
     * 根据种草列表获取分销渠道
     * @param seedCodes
     * @return
     */
    List<DistributionChannel> queryListBySeedCodes(Collection<String> seedCodes);

//    /**
//     * 会员渠道折扣
//     * @return
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    FissionMemberDiscountVO getFissionMemberDiscount();

    /**
     * 获取列表折扣
     * @return
     */
    ChannelBrokeRageVO getFissionDiscountV1();

//    /**
//     * 修改裂变渠道折扣
//     * @param dto
//     * @return
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    void editFissionMemberDiscount(EditFissionMemberDiscountDTO dto);

    /**
     * 修改会员折扣
     * @param dto
     */
    void editMemberDiscount(EditMemberDiscountDTO dto);


    /**
     * 修改裂变渠道活动
     * @param dto
     */
    void editFissionMemberDiscountV1(EditFissionChannelDiscountDTO dto);


    List<BizUserListVO> inviteRegisterList();
}
