package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessOwnerFlowVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_owner_flow(商家主账号换绑记录)】的数据库操作Service
 * @createDate 2025-01-16 11:04:07
 */
public interface IBusinessOwnerFlowService extends IService<BusinessOwnerFlow> {

    /**
     * 根据商家ID获取主账号换绑记录列表
     *
     * @param businessId
     * @return
     */
    List<BusinessOwnerFlowVO> getListByBusinessId(Long businessId);

    /**
     * 保存商家主账号换绑记录
     *
     * @param businessOwnerFlow
     * @return
     */
    BusinessOwnerFlow saveEntity(BusinessOwnerFlow businessOwnerFlow);
}
