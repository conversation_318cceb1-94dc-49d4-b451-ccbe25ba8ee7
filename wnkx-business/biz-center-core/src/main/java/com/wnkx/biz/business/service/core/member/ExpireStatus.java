package com.wnkx.biz.business.service.core.member;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.MemberStatusDto;
import com.wnkx.biz.business.service.IBusinessService;
import com.wnkx.biz.business.service.core.BaseMemberStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description : 已过期
 * @create :2024-06-21 17:40
 **/
@Component
@RequiredArgsConstructor
public class ExpireStatus extends BaseMemberStatus {

    private final IBusinessService businessService;

    @Override
    public void recharge(MemberStatusDto memberStatusDto) {
        BusinessDTO businessDTO = super.getBusinessDto(memberStatusDto);
        businessDTO.setMemberValidity(getResultDate(businessDTO.getMemberLastTime(), memberStatusDto.getMonthNum(), memberStatusDto.getPresentedTime(), memberStatusDto.getPresentedTimeType()));
        businessService.update(businessDTO);
        businessService.initBusinessWechatRemark(memberStatusDto.getBusinessId(), memberStatusDto.getMemberCode());
    }


    @Override
    public void preExpire(MemberStatusDto business) {
        throw new ServiceException("已过期状态无法修改为：[即将过期]状态");
    }

    @Override
    public void expire(MemberStatusDto memberStatusDto) {
        throw new ServiceException("已过期状态无法修改为：[已过期]状态");
    }
}
