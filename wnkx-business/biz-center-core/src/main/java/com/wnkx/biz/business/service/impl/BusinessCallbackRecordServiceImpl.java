package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.ReturnVisitRecordDTO;
import com.ruoyi.system.api.domain.dto.biz.business.WriteReturnVisitDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackRecord;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackEventVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackRecordListVO;
import com.wnkx.biz.business.mapper.BusinessCallbackRecordMapper;
import com.wnkx.biz.business.service.BusinessCallbackEventService;
import com.wnkx.biz.business.service.BusinessCallbackRecordService;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.model.service.BizResourceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessCallbackRecordServiceImpl extends ServiceImpl<BusinessCallbackRecordMapper, BusinessCallbackRecord> implements BusinessCallbackRecordService {


    private final BizResourceService resourceService;
    private final IBusinessAccountService businessAccountService;
    private final BusinessCallbackEventService businessCallbackEventService;

    /**
     * 商家回访-回访记录
     */
    @Override
    public List<BusinessCallbackRecordListVO> returnVisitRecord(ReturnVisitRecordDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bcr.write_time", OrderByDto.DIRECTION.DESC);
        orderByDto.setField("bcr.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);

        List<BusinessCallbackRecordListVO> businessCallbackRecordListVOS = baseMapper.returnVisitRecord(dto);
        if (CollUtil.isEmpty(businessCallbackRecordListVOS)) {
            return businessCallbackRecordListVOS;
        }

        //  回访记录图片
        Set<String> resourceStrIds = businessCallbackRecordListVOS.stream().map(BusinessCallbackRecordListVO::getResourceId).filter(ObjectUtil::isNotNull).collect(Collectors.toSet());
        Map<Long, BizResource> resourceMap = resourceService.getResourceMapByIds(StringUtils.splitToLong(resourceStrIds, StrPool.COMMA));

        //  对应事件
        Set<Long> callbackIds = businessCallbackRecordListVOS.stream().map(BusinessCallbackRecordListVO::getCallbackId).collect(Collectors.toSet());
        List<BusinessCallbackEvent> businessCallbackEvents = businessCallbackEventService.selectListByCallbackIds(callbackIds);
        Map<Long, List<BusinessCallbackEvent>> businessCallbackEventMap = businessCallbackEvents.stream().collect(Collectors.groupingBy(BusinessCallbackEvent::getCallbackId));

        for (BusinessCallbackRecordListVO businessCallbackRecordListVO : businessCallbackRecordListVOS) {
            //  设置回访记录图片
            if (CharSequenceUtil.isNotBlank(businessCallbackRecordListVO.getResourceId())) {
                List<Long> resourceIds = StringUtils.splitToLong(businessCallbackRecordListVO.getResourceId(), StrPool.COMMA);
                List<String> objectKeys = new ArrayList<>();

                for (Long resourceId : resourceIds) {
                    objectKeys.add(resourceMap.getOrDefault(resourceId, new BizResource()).getObjectKey());
                }
                businessCallbackRecordListVO.setObjectKeys(objectKeys);
            }

            businessCallbackRecordListVO.setBusinessCallbackEventVOS(BeanUtil.copyToList(businessCallbackEventMap.get(businessCallbackRecordListVO.getCallbackId()), BusinessCallbackEventVO.class));
        }

        return businessCallbackRecordListVOS;
    }

    /**
     * 商家回访-回访记录-回访账号下拉框
     */
    @Override
    public List<BusinessCallbackRecord> returnVisitAccount(Long businessId) {
        List<BusinessCallbackRecord> businessCallbackRecords = baseMapper.returnVisitAccount(businessId);
        return businessCallbackRecords.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                record -> new AbstractMap.SimpleEntry<>(record.getAccountIsOwnerAccount(), record.getAccountName()), // 按 accountIsOwnerAccount 和 accountName 组合去重
                                record -> record,
                                (existing, replacement) -> existing, // 保留第一个出现的
                                LinkedHashMap::new // 保持原顺序
                        ),
                        map -> map.values().stream() // 获取去重后的值
                                .sorted(Comparator.comparing(BusinessCallbackRecord::getAccountIsOwnerAccount, Comparator.reverseOrder())) // 根据 accountIsOwnerAccount 降序排序
                                .collect(Collectors.toList()) // 转回 List
                ));
    }

    /**
     * 新增商家回访记录
     */
    @Override
    public void addBusinessCallbackRecord(WriteReturnVisitDTO dto) {
        BusinessAccountVO businessAccountVO = businessAccountService.queryOne(BusinessAccountDTO.builder().id(dto.getAccountId()).build());
        Assert.notNull(businessAccountVO, "回访账号不存在~");

        BusinessCallbackRecord businessCallbackRecord = new BusinessCallbackRecord();
        businessCallbackRecord.setBusinessId(dto.getBusinessId());
        businessCallbackRecord.setCallbackId(dto.getId());
        businessCallbackRecord.setAccountBizUserId(businessAccountVO.getBizUserId());
        businessCallbackRecord.setAccountId(businessAccountVO.getId());
        businessCallbackRecord.setAccountIsOwnerAccount(businessAccountVO.getIsOwnerAccount());
        businessCallbackRecord.setAccountName(businessAccountVO.getName());
        businessCallbackRecord.setAccountNickName(businessAccountVO.getNickName());
        businessCallbackRecord.setFeedbackType(CharSequenceUtil.join(StrPool.COMMA, dto.getFeedbackType()));
        businessCallbackRecord.setCallbackContent(dto.getCallbackContent());

        if (CollUtil.isNotEmpty(dto.getObjectKeys())) {
            List<Long> resourceIds = resourceService.saveBatchBizResourceReturnIds(dto.getObjectKeys());
            businessCallbackRecord.setResourceId(CharSequenceUtil.join(StrPool.COMMA, resourceIds));
        }

        businessCallbackRecord.setWriteTime(dto.getWriteTime());
        businessCallbackRecord.setWriteBy(SecurityUtils.getUsername());
        businessCallbackRecord.setWriteById(SecurityUtils.getUserId());
        baseMapper.insert(businessCallbackRecord);
    }
}
