package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelOrder;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_order(分销渠道订单表)】的数据库操作Service
 * @createDate 2024-12-09 16:15:59
 */
public interface IDistributionChannelOrderService extends IService<DistributionChannelOrder> {

    /**
     * 根据今日渠道订单信息
     *
     * @return
     */
    List<DistributionChannelOrder> getTodayDistributionChannelOrder();
}
