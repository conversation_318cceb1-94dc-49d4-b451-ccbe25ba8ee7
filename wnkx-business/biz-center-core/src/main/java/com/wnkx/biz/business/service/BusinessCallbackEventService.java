package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
public interface BusinessCallbackEventService extends IService<BusinessCallbackEvent> {

    /**
     * 根据回访ID查询回访事件
     */
    List<BusinessCallbackEvent> selectListByCallbackIds(Collection<Long> callbackIds);

    /**
     * 根据回访ID查询回访事件
     */
    List<BusinessCallbackEvent> selectListByCallbackId(Long callbackId);

    /**
     * 根据回访ID删除回访事件
     */
    void deleteByCallbackIds(List<Long> deleteCallbackIds);
}
