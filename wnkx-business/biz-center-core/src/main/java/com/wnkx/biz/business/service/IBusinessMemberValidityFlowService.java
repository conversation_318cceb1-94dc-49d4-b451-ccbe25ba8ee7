package com.wnkx.biz.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessMemberValidityFlowVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_member_validity_flow(商家会员有效期修改流水)】的数据库操作Service
* @createDate 2024-10-14 10:21:18
*/
public interface IBusinessMemberValidityFlowService extends IService<BusinessMemberValidityFlow> {

    /**
     * 根据商家Id获取商家有效期列表
     * @param dto
     * @return
     */
    List<BusinessMemberValidityFlowVO> queryList(BusinessMemberValidityFlowDTO dto);

    /**
     *获取关联订单数据
     * @param businessId
     * @return
     */
    BusinessMemberValidityFlowVO getRelationOrder(Long businessId);
}
