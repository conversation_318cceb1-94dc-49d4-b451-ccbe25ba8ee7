package com.wnkx.biz.business.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessOperLog;
import com.ruoyi.system.api.model.LoginUser;
import com.wnkx.biz.business.mapper.BusinessOperLogMapper;
import com.wnkx.biz.business.service.IBusinessOperLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【business_oper_log(商家信息操作日志)】的数据库操作Service实现
* @createDate 2024-09-20 10:26:28
*/
@Service
@Slf4j
public class BusinessOperLogServiceImpl extends ServiceImpl<BusinessOperLogMapper, BusinessOperLog>
    implements IBusinessOperLogService {

    @Override
    @Async
    public void saveLog(Business originalBusiness, Business resultBusiness) {
        try {
            if (UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType())){
                SysUser sysUser = ((LoginUser) SecurityUtils.getLoginUser()).getSysUser();
                baseMapper.insert(BusinessOperLog.builder()
                                .nickName(sysUser.getNickName())
                                .userName(sysUser.getUserName())
                                .userId(sysUser.getUserId())
                                .originalBusiness(JSON.toJSONString(originalBusiness))
                                .resultBusiness(JSON.toJSONString(resultBusiness))
                        .build());

            }
        } catch (Exception e) {
            log.warn("originalBusiness = {}-----------", JSON.toJSONString(originalBusiness));
            log.warn("resultBusiness = {}-----------", JSON.toJSONString(resultBusiness));
            log.error("添加商家操作日志失败：{}", e);
        }
    }
}




