package com.wnkx.biz.business.service.impl;
import java.math.BigDecimal;
import java.util.Date;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.BalanceSourceTypeEnum;
import com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowSaveDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.BusinessBalanceAuditFlowValidListDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceAuditFlowDetailExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.PayoutBusinessBalanceDetailVO;
import com.wnkx.biz.business.mapper.BusinessBalanceAuditFlowMapper;
import com.wnkx.biz.business.mapper.BusinessBalanceDetailMapper;
import com.wnkx.biz.business.mapper.BusinessBalancePrepayMapper;
import com.wnkx.biz.business.service.IBusinessAccountService;
import com.wnkx.biz.business.service.IBusinessBalanceAuditFlowService;
import com.wnkx.biz.business.service.IBusinessBalanceDetailLockService;
import com.wnkx.biz.model.service.BizResourceService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_audit_flow(余额提现审核表)】的数据库操作Service实现
 * @createDate 2024-08-09 18:19:51
 */
@Service
@RequiredArgsConstructor
public class BusinessBalanceAuditFlowServiceImpl extends ServiceImpl<BusinessBalanceAuditFlowMapper, BusinessBalanceAuditFlow>
        implements IBusinessBalanceAuditFlowService {

    private final RemoteService remoteService;

    private final IBusinessAccountService businessAccountService;

    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;

    private final IBusinessBalanceDetailLockService businessBalanceDetailLockService;

    private final BizResourceService bizResourceService;

    private final BusinessBalancePrepayMapper businessBalancePrepayMapper;

    private final BusinessBalanceDetailMapper businessBalanceDetailMapper;

    @Override
    public List<BusinessBalanceAuditFlowVO> queryList(BusinessBalanceAuditFlowListDTO dto) {
        //查询用户列表
        fillAndIsNoData(dto);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        //查询商家列表
        List<BusinessBalanceAuditFlowVO> businessBalanceAuditFlowVOS;
        if (dto.getFilterNotice() > 0) {
            businessBalanceAuditFlowVOS = baseMapper.queryList(dto, SecurityUtils.getUserId(), SecurityUtils.currentUserIsAdmin());
        } else {
            businessBalanceAuditFlowVOS = baseMapper.queryList(dto, null, false);
        }
        load(businessBalanceAuditFlowVOS);
        return businessBalanceAuditFlowVOS;
    }

    @Override
    public List<BusinessBalanceAuditFlowDetailExportVO> queryExportList(BusinessBalanceAuditFlowListDTO dto) {
        fillAndIsNoData(dto);

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessBalanceAuditFlowDetailExportVO> businessBalanceAuditFlowDetailExportVOS = baseMapper.queryExportList(dto);
        loadExportList(businessBalanceAuditFlowDetailExportVOS);
        return businessBalanceAuditFlowDetailExportVOS;
    }

    @Override
    public List<BusinessBalanceAuditFlowVO> load(List<BusinessBalanceAuditFlowVO> list) {
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        Set<Long> businessIds = new HashSet<>();
        List<Long> createUserIds = new ArrayList<>();
        List<String> withdrawNumberList = new ArrayList<>();
        for (BusinessBalanceAuditFlowVO item : list) {
            createUserIds.add(item.getCreateUserId());
            createUserIds.add(item.getAuditUserId());
            createUserIds.add(item.getBusinessWaiterId());
            businessIds.add(item.getBusinessId());
            withdrawNumberList.add(item.getWithdrawNumber());
        }
        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(createUserIds);
        List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
        Map<Long, String> userMap = new HashMap<>();
        Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userList)) {
            userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getUserName));
        }
        BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
        businessAccountDetailDTO.setBusinessIds(businessIds);
        List<BusinessAccountDetailVO> businessAccountDetailVOs = businessAccountService.getBusinessAccountDetailVOs(businessAccountDetailDTO);
        List<BusinessBalanceDetailLock> businessBalanceDetailLockVOS = businessBalanceDetailLockService.queryListByNumberList(withdrawNumberList);
        Map<String, List<BusinessBalanceDetailLock>> businessBalanceDetailLockVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(businessAccountDetailVOs)) {
            businessMap = businessAccountDetailVOs.stream().filter(item -> StatusTypeEnum.YES.getCode().equals(item.getIsOwnerAccount())).collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity(), (v1, v2) -> v1));
        }
        if (CollUtil.isNotEmpty(businessBalanceDetailLockVOS)) {
            businessBalanceDetailLockVOMap = businessBalanceDetailLockVOS.stream().collect(Collectors.groupingBy(BusinessBalanceDetailLock::getNumber));
        }

        for (BusinessBalanceAuditFlowVO item : list) {
            item.setCreateUserName(Optional.ofNullable(userMap.get(item.getCreateUserId())).orElse(""));
            item.setAuditUserName(Optional.ofNullable(userMap.get(item.getAuditUserId())).orElse(""));
            item.setBusinessAccountDetailVO(Optional.ofNullable(businessMap.get(item.getBusinessId())).orElse(new BusinessAccountDetailVO()));
            item.setBusinessServiceName(Optional.ofNullable(userMap.get(item.getBusinessWaiterId())).orElse(""));
            List<BusinessBalanceDetailLock> businessBalanceDetailLocks = businessBalanceDetailLockVOMap.get(item.getWithdrawNumber());
            if (CollUtil.isNotEmpty(businessBalanceDetailLocks)) {
                List<String> numbers = new ArrayList<>();
                for (BusinessBalanceDetailLock lockItem : businessBalanceDetailLocks) {
                    if (List.of(BalanceSourceTypeEnum.PREPAY_INCOME.getCode(), BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()).contains(lockItem.getOrigin())) {
                        numbers.add(lockItem.getBalanceNumber());
                    } else {
                        numbers.add(lockItem.getVideoCode());
                    }
                }
                item.setNumbers(numbers);
            }
        }
        return list;
    }

    public List<BusinessBalanceAuditFlowDetailExportVO> loadExportList(List<BusinessBalanceAuditFlowDetailExportVO> list){
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        Set<Long> businessIds = new HashSet<>();
        List<Long> createUserIds = new ArrayList<>();
        List<String> withdrawNumberList = new ArrayList<>();
        for (BusinessBalanceAuditFlowDetailExportVO item : list) {
            item.setUseBalance(Optional.ofNullable(item.getUseBalance()).orElse(BigDecimal.ZERO));
            item.setPayOutAmount(Optional.ofNullable(item.getPayOutAmount()).orElse(BigDecimal.ZERO));
            createUserIds.add(item.getCreateUserId());
            createUserIds.add(item.getAuditUserId());
            createUserIds.add(item.getBusinessWaiterId());
            businessIds.add(item.getBusinessId());
            withdrawNumberList.add(item.getWithdrawNumber());
        }
        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(createUserIds);
        List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
        Map<Long, String> userMap = new HashMap<>();
        Map<Long, BusinessAccountDetailVO> businessMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userList)) {
            userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getUserName));
        }
        BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
        businessAccountDetailDTO.setBusinessIds(businessIds);
        List<BusinessAccountDetailVO> businessAccountDetailVOs = businessAccountService.getBusinessAccountDetailVOs(businessAccountDetailDTO);
        List<BusinessBalanceDetailLock> businessBalanceDetailLockVOS = businessBalanceDetailLockService.queryListByNumberList(withdrawNumberList);
        Map<String, List<BusinessBalanceDetailLock>> businessBalanceDetailLockVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(businessAccountDetailVOs)) {
            businessMap = businessAccountDetailVOs.stream().filter(item -> StatusTypeEnum.YES.getCode().equals(item.getIsOwnerAccount())).collect(Collectors.toMap(BusinessAccountDetailVO::getBusinessId, Function.identity(), (v1, v2) -> v1));
        }
        if (CollUtil.isNotEmpty(businessBalanceDetailLockVOS)) {
            businessBalanceDetailLockVOMap = businessBalanceDetailLockVOS.stream().collect(Collectors.groupingBy(BusinessBalanceDetailLock::getNumber));
        }

        for (BusinessBalanceAuditFlowDetailExportVO item : list) {
            item.setCreateUserName(Optional.ofNullable(userMap.get(item.getCreateUserId())).orElse(""));
            item.setAuditUserName(Optional.ofNullable(userMap.get(item.getAuditUserId())).orElse(""));
            item.setBusinessAccountDetailVO(Optional.ofNullable(businessMap.get(item.getBusinessId())).orElse(new BusinessAccountDetailVO()));
            item.setBusinessServiceName(Optional.ofNullable(userMap.get(item.getBusinessWaiterId())).orElse(""));
            List<BusinessBalanceDetailLock> businessBalanceDetailLocks = businessBalanceDetailLockVOMap.get(item.getWithdrawNumber());
            item.setTotalBalance(item.getUseBalance().add(item.getPayOutAmount()));
            if (CollUtil.isNotEmpty(businessBalanceDetailLocks)) {
                List<String> numbers = new ArrayList<>();
                for (BusinessBalanceDetailLock lockItem : businessBalanceDetailLocks) {
                    if (List.of(BalanceSourceTypeEnum.PREPAY_INCOME.getCode(), BalanceSourceTypeEnum.ONLINE_RECHARGE.getCode()).contains(lockItem.getOrigin())) {
                        numbers.add(lockItem.getBalanceNumber());
                    } else {
                        numbers.add(lockItem.getVideoCode());
                    }
                }
                item.setNumbers(numbers);
            }
        }
        return list;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBalanceAuditFlow addBusinessBalanceAuditFlow(BusinessBalanceAuditFlowSaveDTO dto) {
        BusinessBalanceAuditFlow businessBalanceAuditFlow = BeanUtil.copyProperties(dto, BusinessBalanceAuditFlow.class);
        businessBalanceAuditFlow.setCreateUserId(SecurityUtils.getUserId());
        businessBalanceAuditFlow.setCreateTime(new Date());
        baseMapper.insert(businessBalanceAuditFlow);
        baseMapper.generateWithdrawNumber(businessBalanceAuditFlow.getId());
        businessBalanceAuditFlow.setWithdrawNumber(String.format("TX%05d", businessBalanceAuditFlow.getId()));
        return businessBalanceAuditFlow;
    }

    @Override
    public BusinessBalanceAuditFlowDetailVO getDetailById(Long id) {
        BusinessBalanceAuditFlow businessBalanceAuditFlow = this.getById(id);
        BusinessBalanceAuditFlowDetailVO businessBalanceAuditFlowDetailVO = BeanUtil.copyProperties(businessBalanceAuditFlow, BusinessBalanceAuditFlowDetailVO.class);
        if (StringUtils.isNull(businessBalanceAuditFlowDetailVO)) {
            return null;
        }
        if (StrUtil.isNotBlank(businessBalanceAuditFlow.getPayoutResourceUrl())) {
            List<Long> resourceIds = StringUtils.splitToLong(businessBalanceAuditFlow.getPayoutResourceUrl(), StrUtil.COMMA);
            List<BizResource> bizResources = bizResourceService.selectListByIds(resourceIds);
            List<String> payoutResourceUrls = bizResources.stream().map(BizResource::getObjectKey).collect(Collectors.toList());
            businessBalanceAuditFlowDetailVO.setPayoutResourceUrlList(payoutResourceUrls);
        }
        List<BusinessBalanceDetailLock> businessBalanceDetailLockList = businessBalanceDetailLockService.queryListByNumberList(Arrays.asList(businessBalanceAuditFlow.getWithdrawNumber()));
        Map<String, BusinessBalancePrepay> prepayMap = new HashMap<>();
        List<String> prepayNums = businessBalanceDetailLockList.stream().filter(bbd -> StrUtil.isNotBlank(bbd.getPrepayNum()))
                .map(BusinessBalanceDetailLock::getPrepayNum).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(prepayNums)) {
            List<BusinessBalancePrepay> prepayList = businessBalancePrepayMapper.selectPrepayListByOriginNumbers(prepayNums);
            prepayMap = prepayList.stream().collect(Collectors.toMap(BusinessBalancePrepay::getPrepayNum, pl -> pl));
        }
        Map<String, OrderVideoRefund> balanceToRefundMap = new HashMap<>();
        List<String> balanceNums = businessBalanceDetailLockList.stream().filter(bbd -> bbd.getBalanceNumber().startsWith("TK"))
                .map(BusinessBalanceDetailLock::getBalanceNumber).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(balanceNums)) {
            List<BusinessBalanceDetail> balanceDetailList = businessBalanceDetailMapper.getDetailListByNumbers(balanceNums);

            Map<String, BusinessBalanceDetail> balanceDetailMap = balanceDetailList.stream()
                    .collect(Collectors.toMap(BusinessBalanceDetail::getNumber, Function.identity()));

            List<String> originNums = balanceDetailList.stream().map(BusinessBalanceDetail::getOriginNumber).collect(Collectors.toList());
            List<OrderVideoRefund> orderVideoRefundList = remoteService.getOrderVideoRefundList(originNums);
            Map<String, OrderVideoRefund> orderVideoRefundMap = orderVideoRefundList.stream()
                    .collect(Collectors.toMap(OrderVideoRefund::getRefundNum, Function.identity()));
            for (Map.Entry<String, BusinessBalanceDetail> entry : balanceDetailMap.entrySet()) {
                String balanceNumber = entry.getKey();
                String originNumber = entry.getValue().getOriginNumber();
                OrderVideoRefund refund = orderVideoRefundMap.get(originNumber);
                if (refund != null) {
                    balanceToRefundMap.put(balanceNumber, refund);
                }
            }
        }
        if (CollUtil.isNotEmpty(businessBalanceDetailLockList)) {
            List<PayoutBusinessBalanceDetailVO> list = new ArrayList<>();
            for (BusinessBalanceDetailLock item : businessBalanceDetailLockList) {
                PayoutBusinessBalanceDetailVO vo = new PayoutBusinessBalanceDetailVO();
                vo.setBusinessId(businessBalanceAuditFlow.getBusinessId());
                vo.setNumber(item.getBalanceNumber());
                vo.setVideoCode(item.getVideoCode());
                vo.setOrigin(item.getOrigin());
                vo.setUseBalance(item.getUseBalance());
                vo.setValidBalance(item.getPayOutAmount());
                vo.setCreateTime(item.getBalanceCreateTime());
                vo.setCreateOrderUserName(item.getCreateOrderUserName());
                if (item.getBalanceNumber().startsWith("TK")) {
                    OrderVideoRefund orderVideoRefund = balanceToRefundMap.get(item.getBalanceNumber());
                    if (null != orderVideoRefund) {
                        vo.setRemark(orderVideoRefund.getRefundCause());
                    }
                }else {
                    BusinessBalancePrepay businessBalancePrepay = prepayMap.get(item.getPrepayNum());
                    if (null != businessBalancePrepay) {
                        vo.setRemark(businessBalancePrepay.getApplyRemark());
                        vo.setContainPresentedAmount(businessBalancePrepay.getContainPresentedAmount());
                    }
                }
                list.add(vo);
            }
            businessBalanceAuditFlowDetailVO.setBusinessBalanceDetails(list);
        }

        List<Long> sysUserIds = new ArrayList<>();
        if (StringUtils.isNotNull(businessBalanceAuditFlowDetailVO.getAuditUserId())) {
            sysUserIds.add(businessBalanceAuditFlowDetailVO.getAuditUserId());
            sysUserIds.add(businessBalanceAuditFlowDetailVO.getCreateUserId());
        }
        SysUserListDTO sysUserListDTO = new SysUserListDTO();
        sysUserListDTO.setUserId(sysUserIds);
        List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
        if (CollUtil.isNotEmpty(userList)) {
            Map<Long, String> userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getUserName));
            businessBalanceAuditFlowDetailVO.setAuditUserName(Optional.ofNullable(userMap.get(businessBalanceAuditFlowDetailVO.getAuditUserId())).orElse(""));
            businessBalanceAuditFlowDetailVO.setCreateUserName(Optional.ofNullable(userMap.get(businessBalanceAuditFlowDetailVO.getCreateUserId())).orElse(""));
        }
        return businessBalanceAuditFlowDetailVO;
    }

    @Override
    public BusinessBalanceAuditFlowStatisticsVO getStatistics() {
        return baseMapper.getStatistics();
    }

    @Override
    public List<BusinessBalanceAuditFlow> queryValidList(BusinessBalanceAuditFlowValidListDTO dto) {
        return baseMapper.queryValidList(dto);
    }

    @Override
    public void markNotice(Long id) {
        baseMapper.markNotice(id);
    }

    /**
     * 是否不存在数据
     *
     * @param dto
     * @return
     */
    private Boolean fillAndIsNoData(BusinessBalanceAuditFlowListDTO dto) {
        if (CollUtil.isEmpty(dto.getAuditStatus())) {
            dto.setAuditStatus(BusinessBalanceAuditStatusEnum.getAudit());
        }
        if (StringUtils.isNotBlank(dto.getSearchNameMemberCodeAccount())) {
            CompletableFuture<List<Long>> userIdsFuture = CompletableFuture.supplyAsync(() -> {
                SysUserListDTO sysUserListDTO = new SysUserListDTO();
                sysUserListDTO.setUserName(dto.getSearchNameMemberCodeAccount());
                List<SysUser> userList = remoteService.getUserList(sysUserListDTO);
                return userList.stream().map(SysUser::getUserId).collect(Collectors.toList());
            }, asyncPoolTaskExecutor);

            CompletableFuture<List<Long>> businessIdsFuture = CompletableFuture.supplyAsync(() -> {
                BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
                businessAccountDetailDTO.setSearchNameMemberCodeAccount(dto.getSearchNameMemberCodeAccount());
                List<BusinessAccountDetailVO> businessAccountDetailVOs = businessAccountService.getBusinessAccountDetailVOs(businessAccountDetailDTO);
                if (CollUtil.isEmpty(businessAccountDetailVOs)) {
                    return Collections.emptyList();
                }
                return businessAccountDetailVOs.stream().map(BusinessAccountDetailVO::getBusinessId).collect(Collectors.toList());
            }, asyncPoolTaskExecutor);
            try {
                List<Long> userIds = userIdsFuture.get();
                if (CollUtil.isNotEmpty(userIds)) {
                    dto.setCreateUserIds(userIds);
                }
            } catch (Exception e) {
                log.error("获取获取用户失败：{}", e);
            }
            try {
                List<Long> businessIds = businessIdsFuture.get();
                if (CollUtil.isNotEmpty(businessIds)) {
                    dto.setBusinessIds(businessIds);
                }
            } catch (Exception e) {
                log.error("获取获取商家失败：{}", e);
            }
            if (CollUtil.isNotEmpty(dto.getCreateUserIds()) || CollUtil.isNotEmpty(dto.getBusinessIds())) {
                return false;
            }
            return true;
        } else {
            return false;
        }
    }
}








