package com.wnkx.biz.statistics.mapper;

import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessMemberDataStatistics;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberExpireCountVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberRechargeCountVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.MemberTrendVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_member_data_statistics(商家会员数据统计)】的数据库操作Mapper
 * @createDate 2025-06-17 17:01:52
 * @Entity mygen.domain.BusinessMemberDataStatistics
 */
public interface BusinessMemberDataStatisticsMapper extends SuperMapper<BusinessMemberDataStatistics> {

    /**
     * 会员类型分析
     * @return
     */
    List<PieChartVO> getBusinessMemberTypeAnalysis();

    /**
     * 会员退款单数占比
     * @param from
     * @param to
     * @return
     */
    List<PieChartVO> getBusinessExitAnalysis(@Param("from") Date from, @Param("to") Date to);


    /**
     * 会员来源
     * @param from
     * @param to
     * @return
     */
    List<PieChartVO> getBusinessSourceAnalysis(@Param("from") Date from, @Param("to") Date to);

    /**
     * 会员趋势
     * @param from
     * @param to
     * @param dateFormat  日期转化
     * @return
     */
    List<MemberTrendVO> getMemberTrendVO(@Param("from") Date from, @Param("to") Date to, @Param("dateFormat") String dateFormat);

    /**
     * 昨日会员充值数据(新会员/续费会员)
     * @return
     */
    MemberRechargeCountVO getYesterdayMemberRechargeCountVO();

    /**
     * 已到期会员数
     * @return
     */
    MemberExpireCountVO getOverMemberCount();

    /**
     * 获取退会数量
     * @param from
     * @param to
     * @return
     */
    Long getExitMemberCount(@Param("from") Date from, @Param("to") Date to);


}




