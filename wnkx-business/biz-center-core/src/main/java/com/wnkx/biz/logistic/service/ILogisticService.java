package com.wnkx.biz.logistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.entity.Logistic;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.ruoyi.system.api.domain.vo.LogisticVO;

import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.List;

/**
 * 物流信息Service接口
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
public interface ILogisticService extends IService<Logistic>
{
    /**
     * 注册物流单号
     */
    void register(Collection<String> numbers);

    /**
     * 接收API推送的物流信息
     *
     * @param request 请求
     */
    void notify(HttpServletRequest request);

    /**
     * 通过物流单号查询物流信息
     *
     * @return 物流信息
     */
    List<LogisticVO> selectListByNumbers(Collection<String> numbers);

    /**
     * 通过条件查询物流单号
     */
    Collection<String> getNumbersByCondition(LogisticListDTO dto);

    /**
     * 通过物流单号获取最新的物流信息
     */
    List<LogisticInfoVO> getLastLogisticInfo(Collection<String> number);

    /**
     * 通过物流单号获取物流信息
     */
    LogisticVO getLogisticInfo(String number);
}