package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.dto.biz.channel.fission.MemberSeedRecordWithdrawalDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.fission.WithdrawalListDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.biz.channel.fission.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【member_seed_record_withdrawal(会员种草提现)】的数据库操作Service
* @createDate 2025-05-15 09:15:45
*/
public interface MemberSeedRecordWithdrawalService extends IService<MemberSeedRecordWithdrawal> {


    /**
     * 获取提现列表
     * @param channelId
     * @return
     */
    List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalListByChannelId(Long channelId);


    /**
     * 获取裂变拉新结算列表
     * @param dto
     * @return
     */
    List<MemberSeedRecordWithdrawalVO> queryMemberSeedRecordWithdrawalList(WithdrawalListDTO dto);


    /**
     * 获取裂变拉新结算列表 导出数据
     * @param dto
     * @return
     */
    List<MemberSeedRecordWithdrawalExportVO> queryMemberSeedRecordWithdrawalListExport(WithdrawalListDTO dto);

    /**
     * 获取裂变结算记录
     * @param dto
     * @return
     */
    List<FissionSettleRecordVO> queryFissionSettleRecordList(WithdrawalListDTO dto);


    /**
     * 获取裂变结算记录 导出数据
     * @param dto
     * @return
     */
    List<FissionSettleRecordExportVO> queryFissionSettleRecordListExport(WithdrawalListDTO dto);

    /**
     * 根据提现id获取提现详情数据
     * @param id
     * @return
     */
    MemberSeedRecordWithdrawalVO getMemberSeedRecordWithdrawalDetail(Long id);

    /**
     * 获取历史数据
     * @param accountType
     * @param channelId
     * @return
     */
    MemberSeedRecordWithdrawalVO getLastWithdrawalDetailByAccountType(Integer accountType, Long channelId);


    /**
     * 初始化提现数据
     * @param dto
     * @return
     */
    MemberSeedRecordWithdrawal initEntity(@Validated MemberSeedRecordWithdrawalDTO dto);

    /**
     * 初始化提现单号
     *
     * @param channelId
     * @return
     */
    String initWithdrawalNum(Long channelId);

    /**
     * 获取裂变拉新结算统计
     * @return
     */
    FissionCountStatisticsVO getFissionCountStatisticsVO();
}
