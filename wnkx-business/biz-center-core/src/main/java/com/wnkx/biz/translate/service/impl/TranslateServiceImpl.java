package com.wnkx.biz.translate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.enums.TranslateEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.system.api.domain.dto.YoudaoResponseDTO;
import com.ruoyi.system.api.domain.entity.biz.translate.TranslationCache;
import com.ruoyi.system.api.domain.vo.biz.translate.TranslateVO;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateBatchRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateBatchResponse;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateResponse;
import com.wnkx.biz.translate.service.TranslateService;
import com.wnkx.biz.translate.service.TranslationCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 翻译服务
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TranslateServiceImpl implements TranslateService {

    private final TmtClient tmtClient;
    private final OkHttpClient okHttpClient;
    private final ThreadPoolTaskExecutor asyncPoolTaskExecutor;

    @Value("${youdao.gateway:''}")
    private String youdaoUrl;

    @Value("${youdao.appKey:''}")
    private String appKey;

    @Value("${youdao.appSecret:''}")
    private String appSecret;

    @Value("${youdao.maxLength: 5000}")
    private Integer maxLength;

    private final TranslationCacheService translationCacheService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public TranslateVO translate(String text, Integer language) {
        TranslateVO result = getTranslate(text);
        if (StatusTypeEnum.NO.getCode().equals(result.getIsFromTable()) && StrUtil.isNotBlank(result.getTargetText())) {
            TranslationCache translationCache = new TranslationCache();
            translationCache.setSourceText(text);
            translationCache.setTranslatedText(result.getTargetText());
            translationCache.setSourceSha256(result.getSha256Hex());
            translationCacheService.save(translationCache);
        }
        return result;
    }

    public TranslateVO getTranslate(String text) {
        Assert.notNull(text, "翻译内容不能为空");
        Assert.isTrue(text.length() <= 8000, "翻译内容不能超过8000字符");

        String sha256Hex = DigestUtils.sha256Hex(text);
        TranslateVO result = new TranslateVO();
        result.setSha256Hex(sha256Hex);
        result.setOriginText(text);
        //sha256加密

        TranslationCache bySourceSha256 = translationCacheService.getBySourceSha256(sha256Hex);
        if (ObjectUtil.isNotNull(bySourceSha256)) {
            result.setIsFromTable(StatusTypeEnum.YES.getCode());
            result.setTargetText(bySourceSha256.getTranslatedText());
            return result;
        }
        result.setIsFromTable(StatusTypeEnum.NO.getCode());

        if (StringUtils.isNotBlank(text) && text.length() > maxLength) {
            String translateFirstStr = StringUtils.safeTruncate(text, maxLength);
            //翻译第一句
            TranslateVO translateFirst = youdaoTranslate(translateFirstStr);
            Assert.notNull(translateFirst, "翻译失败");
            if (StringUtils.isBlank(translateFirst.getTargetText())) {
                result.setIsFromTable(StatusTypeEnum.YES.getCode());
            }

            String translateSecondStr = text.substring(translateFirstStr.length());
            TranslateVO translateSecond = youdaoTranslate(translateSecondStr);
            Assert.notNull(translateSecond, "翻译失败");
            if (StringUtils.isBlank(translateSecond.getTargetText())) {
                result.setIsFromTable(StatusTypeEnum.YES.getCode());
            }

            result.setTargetText(translateFirst.getTargetText() + translateSecond.getTargetText());
        } else {
            TranslateVO translate = youdaoTranslate(text);
            Assert.notNull(translate, "翻译失败");
            if (StringUtils.isBlank(translate.getTargetText())) {
                result.setIsFromTable(StatusTypeEnum.YES.getCode());
            }
            result.setTargetText(translate.getTargetText());
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> translate(List<String> textList, Integer language) {
        return youdaoTranslateBatch(textList);
    }

    public List<String> youdaoTranslateBatch(List<String> textList) {
        List<CompletableFuture<TranslateVO>> futures = textList.stream()
                .map(text -> CompletableFuture.supplyAsync(() -> getTranslate(text), asyncPoolTaskExecutor))
                .collect(Collectors.toList());

        // 等待所有任务完成并收集结果
        List<TranslateVO> collect = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        //加入数据库数据
        List<TranslationCache> translationCacheList = new ArrayList<>();
        List<String> sha256HexList = new ArrayList<>();
        for (TranslateVO item : collect) {
            if (StatusTypeEnum.NO.getCode().equals(item.getIsFromTable()) && !sha256HexList.contains(item.getSha256Hex())) {
                sha256HexList.add(item.getSha256Hex());
                TranslationCache translationCache = new TranslationCache();
                translationCache.setSourceText(item.getOriginText());
                translationCache.setTranslatedText(item.getTargetText());
                translationCache.setSourceSha256(item.getSha256Hex());
                translationCacheList.add(translationCache);
            }
        }
        if (CollUtil.isNotEmpty(translationCacheList)){
            translationCacheService.saveBatch(translationCacheList);
        }

        return collect.stream().map(TranslateVO::getTargetText).collect(Collectors.toList());
    }

    private TranslateVO youdaoTranslate(String text) {
        String response = requestForHttp(getPayload(text));
        return new TranslateVO(text, response);
    }

    Map<String, String> getPayload(String text) {
        Map<String, String> params = new HashMap<>();
        String salt = IdUtil.fastSimpleUUID();
        params.put("from", "auto");
        params.put("to", "zh-CHS");
        params.put("signType", "v3");
        String curtime = String.valueOf(System.currentTimeMillis() / 1000);
        params.put("curtime", curtime);
        String signStr = appKey + truncate(text) + salt + curtime + appSecret;
        String sign = getDigest(signStr);
        params.put("appKey", appKey);
        params.put("q", text);
        params.put("salt", salt);
        params.put("sign", sign);
        return params;
    }


    public String requestForHttp(Map<String, String> params) {
        FormBody.Builder formBuilder = new FormBody.Builder(StandardCharsets.UTF_8);
        for (Map.Entry<String, String> entry : params.entrySet()) {
            formBuilder.add(entry.getKey(), entry.getValue());
        }

        Request request = new Request.Builder()
                .url(youdaoUrl)
                .post(formBuilder.build())
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) return "";
            String json = response.body().string();
            List<String> translation = JSONUtil.toBean(json, YoudaoResponseDTO.class).getTranslation();
            if (CollUtil.isNotEmpty(translation)) {
                return translation.stream().collect(Collectors.joining(","));
            }
            log.info("翻译失败请求参数，{}", params.get("q"));
            log.info("翻译失败，{}", json);
            return "";
        } catch (Exception e) {
            log.info("翻译失败，{}", e.getMessage());
            return "";
        }
    }

    private String getDigest(String string) {
        if (string == null) {
            return null;
        }
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
        byte[] btInput = string.getBytes(StandardCharsets.UTF_8);
        try {
            MessageDigest mdInst = MessageDigest.getInstance("SHA-256");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
    }

    public static String truncate(String q) {
        if (q == null) {
            return null;
        }
        int len = q.length();
        return len <= 20 ? q : (q.substring(0, 10) + len + q.substring(len - 10, len));
    }

    private TranslateVO tencentTranslate(String text, Integer language) {
        final TextTranslateRequest textTranslateRequest = new TextTranslateRequest();
        textTranslateRequest.setSourceText(text);
        textTranslateRequest.setSource("auto");
        textTranslateRequest.setTarget(TranslateEnum.getLabel(language));
        textTranslateRequest.setProjectId(0L);
        try {
            final TextTranslateResponse textTranslateResponse = tmtClient.TextTranslate(textTranslateRequest);
            return new TranslateVO(textTranslateResponse.getSource(), textTranslateResponse.getTargetText());
        } catch (TencentCloudSDKException e) {
            log.info("翻译失败{}:{}", e.getErrorCode(), e.getMessage());
            throw new ServiceException("翻译失败");
        }
    }

    private List<String> tencentTranslateBatch(List<String> textList, Integer language) {
        final TextTranslateBatchRequest textTranslateBatchRequest = new TextTranslateBatchRequest();
        textTranslateBatchRequest.setSourceTextList(textList.toArray(new String[0]));
        textTranslateBatchRequest.setSource("auto");
        textTranslateBatchRequest.setTarget(TranslateEnum.getLabel(language));
        textTranslateBatchRequest.setProjectId(0L);
        try {
            final TextTranslateBatchResponse textTranslateBatchResponse = tmtClient.TextTranslateBatch(textTranslateBatchRequest);
            return List.of(textTranslateBatchResponse.getTargetTextList());
        } catch (TencentCloudSDKException e) {
            log.info("翻译失败{}:{}", e.getErrorCode(), e.getMessage());
            throw new ServiceException("翻译失败");
        }
    }


}

