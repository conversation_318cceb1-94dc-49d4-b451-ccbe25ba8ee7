package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessOwnerFlowVO;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_owner_flow(商家主账号换绑记录)】的数据库操作Mapper
 * @createDate 2025-01-16 11:04:07
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow
 */
public interface BusinessOwnerFlowMapper extends SuperMapper<BusinessOwnerFlow> {

    /**
     * 根据商家id查询换绑记录
     * @param businessId
     * @return
     */
    List<BusinessOwnerFlowVO> getListByBusinessId(Long businessId);
}




