package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.OrderConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.config.OrderPayProperties;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.SysDictData;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayDetailVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayExportVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.OrderPayeeAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.prepay.PrepayUnPayVO;
import com.ruoyi.system.api.domain.vo.order.RealTimeExchangeRateVO;
import com.wnkx.biz.business.mapper.BusinessBalancePrepayMapper;
import com.wnkx.biz.business.service.IBusinessBalancePrepayService;
import com.wnkx.biz.config.PrepayConfig;
import com.wnkx.biz.model.service.BizResourceService;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_prepay(商家预付表)】的数据库操作Service实现
 * @createDate 2024-11-04 14:32:59
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessBalancePrepayServiceImpl extends ServiceImpl<BusinessBalancePrepayMapper, BusinessBalancePrepay>
        implements IBusinessBalancePrepayService {

    private final BizResourceService bizResourceService;
    private final PrepayConfig prepayConfig;
    private final OrderPayProperties orderPayProperties;
    private final RemoteService remoteService;
    private final RedisService redisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBalancePrepay insertBusinessBalancePrepay(BusinessBalancePrepayDTO businessBalancePrepay) {
        Assert.isTrue(redisService.getLock(CacheConstants.RECHARGE_PREPAY_NUM_LOCK_KEY, 20L), "系统繁忙，请重试~！");
        try {
            BusinessBalancePrepay entity = BeanUtil.copyProperties(businessBalancePrepay, BusinessBalancePrepay.class);
            List<Long> resourceIds = bizResourceService.saveBatchBizResourceReturnIds(businessBalancePrepay.getResourceIds());
            entity.setResourceId(StrUtil.join(StrUtil.COMMA, resourceIds));
            entity.setCreateById(SecurityUtils.getUserId());
            entity.setCreateBy(SecurityUtils.getUsername());
            entity.setPrepayNum(getPrepayNum());
            entity.setPayNum(IdUtils.createOrderNum(OrderConstant.NORMAL_ORDER_PAY_NUM_PREFIX_ZFDD, 4));
            entity.setTaxPoint(BigDecimal.ZERO);
            entity.setPayAmount(entity.getAmount().subtract(businessBalancePrepay.getContainPresentedAmount()));

            entity.setSubmitCredentialTime(new Date());
            entity.setStatus(OrderStatusEnum.UN_CHECK.getCode());
            entity.setCurrentExchangeRate(getCurrentExchange().getRealTimeExchangeRate());
            BigDecimal payAmountDollar = entity.getPayAmount().divide(entity.getCurrentExchangeRate(), 2, RoundingMode.DOWN);
            if (PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(businessBalancePrepay.getPayType())){
                Assert.isTrue(payAmountDollar.compareTo(businessBalancePrepay.getPayAmountDollar()) == 0, "数据已更新，请刷新页面");
            }
            entity.setPayAmountDollar(payAmountDollar);


            this.save(entity);
            if (ObjectUtil.isNotNull(businessBalancePrepay.getAccountId())){
                remoteService.saveOrderPayeeAccount(OrderPayAccountDTO.builder()
                        .orderNum(entity.getPrepayNum())
                        .payeeAccountConfigInfoId(businessBalancePrepay.getAccountId())
                        .accountType(businessBalancePrepay.getPayType()).build());
            }
            return entity;
        } finally {
            redisService.releaseLock(CacheConstants.RECHARGE_PREPAY_NUM_LOCK_KEY);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBalancePrepay initBusinessBalancePrepay(OnlineBusinessBalancePrepayDTO businessBalancePrepay) {
        Assert.isTrue(UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()), "只有商家端才能创建钱包线上订单");
        Assert.isTrue(redisService.getLock(CacheConstants.RECHARGE_PREPAY_NUM_LOCK_KEY, 20L), "系统繁忙，请重试~！");
        try {
            BusinessBalancePrepay entity = new BusinessBalancePrepay();
            entity.setBusinessId(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
            entity.setOrderType(OrderTypeEnum.ONLINE_RECHARGE.getCode());
            entity.setPrepayNum(getPrepayNum());
            entity.setCurrentExchangeRate(getCurrentExchange().getRealTimeExchangeRate());
            entity.setPayNum(IdUtils.createOrderNum(OrderConstant.NORMAL_ORDER_PAY_NUM_PREFIX_ZFDD, 4));
            //计算赠送金额
            entity.setContainPresentedAmount(new BigDecimal(getContainPresentedAmount(businessBalancePrepay.getAmount())));

            entity.setTaxPoint(BigDecimal.ZERO);
            entity.setPayAmount(new BigDecimal(businessBalancePrepay.getAmount()) );
            entity.setPayAmountDollar(entity.getPayAmount().divide(entity.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
            entity.setAmount(entity.getPayAmount().add(entity.getContainPresentedAmount()));
            entity.setCreateById(SecurityUtils.getUserId());
            entity.setCreateBy(SecurityUtils.getUsername());
            this.save(entity);
            return entity;
        } finally {
            redisService.releaseLock(CacheConstants.RECHARGE_PREPAY_NUM_LOCK_KEY);
        }
    }

    public Long getContainPresentedAmount(Long amount) {
        if (ObjectUtil.isNull(amount) || amount.compareTo(prepayConfig.getMinRecharge()) < 0) {
            return 0L;
        }
        Long containPresentedAmount = 0L;
        //剩余金额
        Long surplusAmount = amount;
        while (surplusAmount.compareTo(prepayConfig.getMinRecharge()) >= 0){
            if (surplusAmount.compareTo(prepayConfig.getMaxRecharge()) >= 0){
                containPresentedAmount += prepayConfig.getMaxRechargePresented();
                surplusAmount -= prepayConfig.getMaxRecharge();
            }else {
                containPresentedAmount += (surplusAmount/prepayConfig.getMinRecharge() * prepayConfig.getMinRechargePresented());
                surplusAmount = 0L;
            }
        }
        return containPresentedAmount;
    }

    private BigDecimal getPayAmount(BusinessBalancePrepay entity) {
        if (ObjectUtil.isNull(entity.getAmount())) {
            return BigDecimal.ZERO;
        }
        BigDecimal taxPoint = orderPayProperties.getTaxPoint();
        if (ObjectUtil.isNull(taxPoint)) {
            return entity.getAmount();
        }
        if (PrepayPayTypeEnum.PUBLIC.getCode().equals(entity.getPayType())) {
            BigDecimal taxPointCost = entity.getAmount().multiply(taxPoint.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).setScale(2, RoundingMode.DOWN);
            return entity.getAmount().add(taxPointCost);
        } else {
            return entity.getAmount();
        }
    }

    @Override
    public List<BusinessBalancePrepayVO> queryList(BusinessBalancePrepayListDTO dto) {

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbp.audit_status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("bbp.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.queryList(dto);
    }

    @Override
    public List<BusinessBalancePrepayVO> innerQueryList(BusinessBalancePrepayListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbp.audit_status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("bbp.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.innerQueryList(dto);
    }

    @Override
    public List<BusinessBalancePrepayExportVO> exportBusinessBalancePrepayList(BusinessBalancePrepayListDTO dto) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bbp.audit_status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("bbp.id", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessBalancePrepayVO> businessBalancePrepayVOS = baseMapper.queryList(dto);
        if (CollUtil.isEmpty(businessBalancePrepayVOS)) {
            return Collections.emptyList();
        }
        List<SysDictData> dictTypeList = remoteService.selectDictDataByType("sys_money_type");
        Map<String, String> dictTypeMap = dictTypeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictTypeMap.put(CurrencyEnum.CNY.getCode().toString(), CurrencyEnum.CNY.getLabel());
        List<BusinessBalancePrepayExportVO> businessBalancePrepayExportVOS = BeanUtil.copyToList(businessBalancePrepayVOS, BusinessBalancePrepayExportVO.class);

        for (BusinessBalancePrepayExportVO item : businessBalancePrepayExportVOS) {
            item.setCurrencyString(dictTypeMap.get(Convert.toStr(item.getCurrency())));
        }
        return businessBalancePrepayExportVOS;
    }

    @Override
    public void load(List<BusinessBalancePrepayVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> orderNums = list.stream().map(BusinessBalancePrepayVO::getPrepayNum).collect(Collectors.toList());
        List<OrderPayeeAccount> orderPayeeAccounts = remoteService.queryOrderPayeeAccountListByOrderNums(orderNums);
        if (CollUtil.isEmpty(orderPayeeAccounts)) {
            return;
        }
        Map<String, OrderPayeeAccount> orderPayeeAccountMap = orderPayeeAccounts.stream().collect(Collectors.toMap(OrderPayeeAccount::getOrderNum, p -> p));
        for (BusinessBalancePrepayVO item : list) {
            if (ObjectUtil.isNull(orderPayeeAccountMap.get(item.getPrepayNum()))) {
                continue;
            }
            item.setOrderPayeeAccountVO(BeanUtil.copyProperties(orderPayeeAccountMap.get(item.getPrepayNum()), OrderPayeeAccountVO.class));
        }
    }

    @Override
    public BusinessBalancePrepay auditBusinessBalancePrepay(AuditBusinessBalancePrepayDTO dto) {
        BusinessBalancePrepay businessBalancePrepayByTable = this.getById(dto.getId());
        BusinessBalancePrepay businessBalancePrepay = checkAuditPrepay(dto, businessBalancePrepayByTable);
        baseMapper.updateById(businessBalancePrepay);
        businessBalancePrepay.setPrepayNum(businessBalancePrepayByTable.getPrepayNum());
        businessBalancePrepay.setPayAmount(businessBalancePrepayByTable.getPayAmount());
        businessBalancePrepay.setPayType(businessBalancePrepayByTable.getPayType());
        businessBalancePrepay.setPayTypeDetail(businessBalancePrepayByTable.getPayTypeDetail());
        businessBalancePrepay.setOrderType(businessBalancePrepayByTable.getOrderType());
        return businessBalancePrepay;
    }

    private BusinessBalancePrepay checkAuditPrepay(AuditBusinessBalancePrepayDTO dto, BusinessBalancePrepay entity) {
        Assert.notNull(entity, "预付审核数据不存在！");
        Assert.isTrue(entity.getBusinessId().equals(dto.getBusinessId()), "数据库商家数据与请求不一致！");
        Assert.isTrue(BusinessPrepayAuditStatusEnum.PRE_APPROVE.getCode().equals(entity.getAuditStatus()), "只有待审核状态能够进行审核处理！");

        BusinessBalancePrepay businessBalancePrepay = BeanUtil.copyProperties(dto, BusinessBalancePrepay.class);
        if (BusinessPrepayAuditStatusEnum.APPROVE.getCode().equals(dto.getAuditStatus())) {
            Assert.notNull(dto.getRealAmount(), "[预付款增加金额]不能为空");
            Assert.notNull(dto.getRealPayAmount(), "[实际支付金额]不能为空");
            if (PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(entity.getPayType())){
                Assert.notNull(dto.getRealPayAmountCurrency(), "[预付款增加金额]不能为空");
                Assert.notNull(dto.getCurrency(), "[币种]不能为空");
            }else {
                businessBalancePrepay.setRealPayAmountCurrency(null);
                businessBalancePrepay.setCurrency(null);
            }
            Assert.notNull(dto.getPayTime(), "[支付时间]不能为空");
//            Assert.isTrue(entity.getAmount().compareTo(dto.getRealAmount()) == 0, "实付金额需要与申请增加金额一致");//11.29不校验
            businessBalancePrepay.setRejectCause(null);
            businessBalancePrepay.setStatus(OrderStatusEnum.FINISHED.getCode());
        } else if (BusinessPrepayAuditStatusEnum.CANCEL.getCode().equals(dto.getAuditStatus())) {
            Assert.notNull(dto.getRejectCause(), "[拒绝原因]不能为空");
            businessBalancePrepay.setRealAmount(null);
            businessBalancePrepay.setRealPayAmount(null);
            businessBalancePrepay.setRealPayAmountCurrency(null);
            businessBalancePrepay.setCurrency(null);
            businessBalancePrepay.setPayTime(null);
            businessBalancePrepay.setRemark(null);
            businessBalancePrepay.setStatus(OrderStatusEnum.TRADE_CLOSE.getCode());
        } else {
            throw new ServiceException("审核状态有误！");
        }
        businessBalancePrepay.setAuditUserId(SecurityUtils.getUserId());
        businessBalancePrepay.setAuditUserName(SecurityUtils.getUsername());
        businessBalancePrepay.setAuditTime(new Date());
        return businessBalancePrepay;
    }


    @Override
    public BusinessBalancePrepayDetailVO getDetailById(Long id) {
        BusinessBalancePrepay entity = this.getById(id);
        if (ObjectUtil.isNull(entity)) {
            return null;
        }
        BusinessBalancePrepayDetailVO businessBalancePrepayDetailVO = BeanUtil.copyProperties(entity, BusinessBalancePrepayDetailVO.class);
        if (StrUtil.isNotBlank(entity.getResourceId())) {
            businessBalancePrepayDetailVO.setResources(bizResourceService.selectObjectKeyListByIds(StringUtils.splitToLong(entity.getResourceId(), StrUtil.COMMA)));
        }
        List<OrderPayeeAccount> orderPayeeAccounts = remoteService.queryOrderPayeeAccountListByOrderNums(Arrays.asList(entity.getPrepayNum()));
        if (CollUtil.isNotEmpty(orderPayeeAccounts)) {
            businessBalancePrepayDetailVO.setOrderPayeeAccountVO(BeanUtil.copyProperties(orderPayeeAccounts.get(0), OrderPayeeAccountVO.class));

        }

        return businessBalancePrepayDetailVO;
    }

    @Override
    public BusinessBalancePrepayStatisticsVO getStatistics() {
        return baseMapper.getStatistics();
    }

    @Override
    public List<BusinessBalancePrepay> onlineBusinessBalancePrepay() {
        Assert.isTrue(UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()), "只有商家端才能创建钱包线上订单");
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("status", OrderByDto.DIRECTION.ASC);
        orderByDto.setField("creat_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.getOnlineList(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitCredential(OnlineRechargeSubmitCredentialDTO dto) {
        BusinessBalancePrepay businessBalancePrepay = baseMapper.getByPreNum(dto.getOrderNum());
        Assert.notNull(businessBalancePrepay, "订单不存在~");
        //非代付 需要校验支付类型是否一致
        if (StatusTypeEnum.NO.getCode().equals(dto.getIsAnother())){
            Assert.isTrue(businessBalancePrepay.getPayType().equals(dto.getPayType()), "支付类型不一致~");
        }
        Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(businessBalancePrepay.getOrderType()), "订单类型错误~");
        Assert.isNull(businessBalancePrepay.getPayTime(), "订单已支付~");
        Assert.isNull(businessBalancePrepay.getSubmitCredentialTime(), "订单已提交审核~");
        Assert.isTrue(PrepayPayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())
                        || PrepayPayTypeEnum.PUBLIC.getCode().equals(dto.getPayType()),
                "支付方式只能是全币种或者对公");
        if (PayTypeEnum.FULL_CURRENCY.getCode().equals(dto.getPayType())) {
            Assert.isTrue(businessBalancePrepay.getPayAmountDollar().compareTo(orderPayProperties.getFullCurrencyUpperLimit()) >= 0, "应支付金额少于" + orderPayProperties.getFullCurrencyUpperLimit() + "USD，不支持使用全币种支付");
        }
        List<Long> resourceIds = bizResourceService.saveBatchBizResourceReturnIds(dto.getResourceIds());

        BusinessBalancePrepay updateEntity = new BusinessBalancePrepay();
        updateEntity.setId(businessBalancePrepay.getId());
        updateEntity.setResourceId(StrUtil.join(StrUtil.COMMA, resourceIds));
        updateEntity.setSubmitCredentialTime(new Date());
        updateEntity.setPayType(dto.getPayType());
        updateEntity.setPayTypeDetail(dto.getPayTypeDetail());
        updateEntity.setPayAccount(dto.getPayAccount());
        updateEntity.setStatus(OrderStatusEnum.UN_CHECK.getCode());
        baseMapper.updateById(updateEntity);

        if (ObjectUtil.isNotNull(businessBalancePrepay.getAccountId()) && StatusTypeEnum.NO.getCode().equals(dto.getIsAnother())){
            remoteService.saveOrderPayeeAccount(OrderPayAccountDTO.builder()
                    .orderNum(businessBalancePrepay.getPrepayNum())
                    .payeeAccountConfigInfoId(businessBalancePrepay.getAccountId())
                    .accountType(businessBalancePrepay.getPayType()).build());
        }
    }

    @Override
    public PrepayUnPayVO getOnlineUnPay(Long businessId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("creat_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        List<BusinessBalancePrepay> onlineUnPay = Optional.ofNullable(baseMapper.getOnlineUnPay(businessId)).orElse(Collections.emptyList());
        return PrepayUnPayVO.builder()
                .unPayOnlineList(onlineUnPay)
                .unPayNum(onlineUnPay.size())
                .build();
    }

    @Override
    public BusinessBalancePrepay getByPreNum(String preNum) {
        return baseMapper.getByPreNum(preNum);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOnlineOrder(Long id) {
        BusinessBalancePrepay businessBalancePrepay = baseMapper.selectById(id);
        Assert.notNull(businessBalancePrepay, "订单不存在~");
        Assert.isTrue(OrderStatusEnum.UN_PAY.getCode().equals(businessBalancePrepay.getStatus()), "订单状态需要是待支付~");
        Assert.isTrue(OrderTypeEnum.ONLINE_RECHARGE.getCode().equals(businessBalancePrepay.getOrderType()), "订单类型需要是线上钱包充值~");
        baseMapper.cancelOnlineOrder(id);
        remoteService.closeAllOrder(Arrays.asList(businessBalancePrepay.getPrepayNum()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAppId(PrepayUpdateAppIdDTO dto) {
        baseMapper.updateAppId(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessBalancePrepay updateOrderPayStatus(PrepayUpdatePayStatusDTO dto) {
        BusinessBalancePrepay businessBalancePrepayByTable = baseMapper.selectPrepayListByOriginNumbers(Arrays.asList(dto.getPrepayNum())).get(0);
        Assert.notNull(businessBalancePrepayByTable, "预付审核数据不存在！");
        Assert.isTrue(BusinessPrepayAuditStatusEnum.PRE_APPROVE.getCode().equals(businessBalancePrepayByTable.getAuditStatus()), "只有待审核状态能够进行审核处理！");
        if (Boolean.TRUE.toString().equals(dto.getDebuggerEnable())) {
            if (new BigDecimal("0.01").compareTo(dto.getTotalAmount()) != 0) {
                log.error("测试情况 回调金额非0.01错误，订单号：{},回调金额：{}", businessBalancePrepayByTable.getPrepayNum(), dto.getTotalAmount());
                throw new ServiceException("测试情况 回调金额非0.01错误！");
            }
        } else if (businessBalancePrepayByTable.getPayAmount().compareTo(dto.getTotalAmount()) != 0) {
            log.error("订单应付金额与回调金额不一致，订单号：{},应付金额：{},回调金额：{}", businessBalancePrepayByTable.getPrepayNum(), businessBalancePrepayByTable.getPayAmount(), dto.getTotalAmount());
            throw new ServiceException("订单应付金额与回调金额不一致！");
        }

        BusinessBalancePrepay businessBalancePrepay = BeanUtil.copyProperties(businessBalancePrepayByTable, BusinessBalancePrepay.class);
        businessBalancePrepay.setStatus(OrderStatusEnum.FINISHED.getCode());
        businessBalancePrepay.setPayType(dto.getPayType());
        businessBalancePrepay.setRealAmount(businessBalancePrepayByTable.getAmount());
        businessBalancePrepay.setRealPayAmount(businessBalancePrepayByTable.getPayAmount());
        businessBalancePrepay.setPayTime(dto.getPayTime());
        businessBalancePrepay.setAuditStatus(BusinessPrepayAuditStatusEnum.APPROVE.getCode());

        baseMapper.updateById(businessBalancePrepay);
        return businessBalancePrepay;
    }


    @Override
    public List<BusinessBalancePrepay> getCloseOnlineOrders(Date beginTime, Date endTime) {
        return baseMapper.getCloseOnlineOrders(beginTime, endTime);
    }

    @Override
    public RealTimeExchangeRateVO getCurrentExchange() {
        if (hasCachePrice()) {
            return getCachePrice();
        }
        return RealTimeExchangeRateVO.builder().realTimeExchangeRate(new BigDecimal("7.2345")).isDefault(true).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchFieldNullToNull(Collection<String> orderNums) {
        baseMapper.updateBatchFieldNullToNull(orderNums);
    }

    public String getPrepayNum() {
        long prepay = ObjectUtil.isNotNull(prepayConfig.getInitPrepayNum()) ? prepayConfig.getInitPrepayNum() : 100000L;
        //获取最新的一条体检号数据
        Long id = 0L;
        BusinessBalancePrepay businessBalancePrepay = baseMapper.getLastEntity();
        if (businessBalancePrepay != null) {
            /**数据库主键id*/
            id = businessBalancePrepay.getId();
        }
        /**序列主键 + 最后一条数据的id + 1*/
        prepay = prepay + id + 1;
        return getPrepayPrefix() + Convert.toStr(prepay).substring(1);
    }

    private String getPrepayPrefix() {
        //读取config配置peis.apply.pecodePrefix
        String prepayPrefix = prepayConfig.getPrefix();
        return StrUtil.isNotEmpty(prepayPrefix) ? prepayPrefix : "YF";
    }

    private boolean hasCachePrice() {
        return redisService.hasKey(CacheConstants.EXCHANGE_RATE_KEY);
    }

    private RealTimeExchangeRateVO getCachePrice() {
        return RealTimeExchangeRateVO.builder().realTimeExchangeRate(redisService.getCacheObject(CacheConstants.EXCHANGE_RATE_KEY)).build();
    }
}




