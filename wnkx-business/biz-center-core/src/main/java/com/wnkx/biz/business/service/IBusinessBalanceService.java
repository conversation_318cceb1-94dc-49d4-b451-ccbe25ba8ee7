package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.dto.order.casus.BalancePayOutDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商家余额处理接口
 * @createDate 2024-11-04 14:32:59
 */
public interface IBusinessBalanceService {

    /**
     * 获取商家总余额
     *
     * @return
     */
    BigDecimal businessBalanceTotal();

    /**
     * 修改商家余额
     *
     * @param dto
     */
    void updateBusinessBalance(@Valid BusinessBalanceDTO dto);

    /**
     * 发起提现
     *
     * @param dto
     */
    void balancePayOut(@Valid BalancePayOutDTO dto);

    /**
     * 余额提现审核*
     *
     * @param dto
     */
    void auditPayOut(@Valid BusinessBalanceAuditFlowAuditDTO dto);


    /**
     * 预付款审核
     * @param dto
     */
    void auditBusinessBalancePrepay(@Valid AuditBusinessBalancePrepayDTO dto);

    /**
     * 支付成功更新状态
     * @param dto
     * @return
     */
    BusinessBalancePrepay updateOrderPayStatus(PrepayUpdatePayStatusDTO dto);


    /**
     * 获取有效余额提现审核数据
     * @param dto
     * @return
     */
    List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(BusinessBalanceAuditFlowValidListDTO dto);


    /**
     * 获取有效余额锁定数据
     * @param dto
     * @return
     */
    List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto);

    /**
     * 获取视频订单提现记录
     */
    List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto);

}
