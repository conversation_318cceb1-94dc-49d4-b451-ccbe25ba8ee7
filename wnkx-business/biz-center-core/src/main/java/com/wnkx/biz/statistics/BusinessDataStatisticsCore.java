package com.wnkx.biz.statistics;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.statistics.BusinessExitEnum;
import com.ruoyi.common.core.enums.statistics.BusinessMemberTypeLabelEnum;
import com.ruoyi.common.core.enums.statistics.BusinessOrderLabelEnum;
import com.ruoyi.common.core.enums.statistics.BusinessRechargeTypeLabelEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessMemberDataStatistics;
import com.ruoyi.system.api.domain.entity.biz.statistics.BusinessOrderDataStatistics;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.member.*;
import com.wnkx.biz.business.mapper.BusinessMapper;
import com.wnkx.biz.statistics.mapper.BusinessMemberDataStatisticsMapper;
import com.wnkx.biz.statistics.mapper.BusinessOrderDataStatisticsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员数据数据统计
 * @create :2025-06-18 11:12
 **/
@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessDataStatisticsCore {
    private final BusinessOrderDataStatisticsMapper businessOrderDataStatisticsMapper;
    private final BusinessMemberDataStatisticsMapper businessMemberDataStatisticsMapper;
    private final BusinessMapper businessMapper;

    /**
     * 会员排单数占比统计
     * @param startTime
     * @param endTime
     * @return
     */
    public BusinessAnalysisVO getBusinessOrderAnalysisVO(Date startTime, Date endTime){
        List<PieChartVO> businessOrderAnalysisVO = businessOrderDataStatisticsMapper.getBusinessOrderAnalysisVO(startTime, endTime);
        BusinessOrderDataStatistics businessOrderDataStatistics = businessOrderDataStatisticsMapper.selectOne(new LambdaQueryWrapper<BusinessOrderDataStatistics>().last("ORDER BY id DESC limit 1"));
        BusinessAnalysisVO init = new BusinessAnalysisVO().init(businessOrderAnalysisVO, Arrays.stream(BusinessOrderLabelEnum.values()).map(BusinessOrderLabelEnum::getLabel).collect(Collectors.toList()));
        if (ObjectUtil.isNotNull(businessOrderDataStatistics)){
            init.setUpdateTime(businessOrderDataStatistics.getUpdateTime());
        }
        return init;
    }

    /**
     * 会员类型分析
     * @return
     */
    public BusinessAnalysisVO getBusinessMemberTypeAnalysisVO(){
        List<PieChartVO> businessOrderAnalysisVO = businessMemberDataStatisticsMapper.getBusinessMemberTypeAnalysis();
        BusinessAnalysisVO init = new BusinessAnalysisVO().init(businessOrderAnalysisVO, Arrays.stream(BusinessMemberTypeLabelEnum.values())
                .map(BusinessMemberTypeLabelEnum::getLabel).collect(Collectors.toList()));
        init.setUpdateTime(new Date());
        return init;
    }
    /**
     * 会员退款单数占比
     * @return
     */
    public BusinessAnalysisVO getBusinessExitAnalysis(Date startTime, Date endTime){
        List<PieChartVO> businessOrderAnalysisVO = businessMemberDataStatisticsMapper.getBusinessExitAnalysis(startTime, endTime);
        BusinessAnalysisVO init = new BusinessAnalysisVO().init(businessOrderAnalysisVO, Arrays.stream(BusinessExitEnum.values())
                .map(BusinessExitEnum::getLabel).collect(Collectors.toList()));
        init.setUpdateTime(new Date());
        return init;
    }
    /**
     * 会员来源分析
     * @return
     */
    public BusinessAnalysisVO getBusinessSourceAnalysis(Date startTime, Date endTime){
        List<PieChartVO> businessOrderAnalysisVO = businessMemberDataStatisticsMapper.getBusinessSourceAnalysis(startTime, endTime);
        BusinessAnalysisVO init = new BusinessAnalysisVO().init(businessOrderAnalysisVO, Arrays.stream(BusinessRechargeTypeLabelEnum.values())
                .map(BusinessRechargeTypeLabelEnum::getLabel).collect(Collectors.toList()));
        init.setUpdateTime(new Date());
        return init;
    }

    /**
     * 会员排单数统计
     *
     * @param type 1-日 2-月 3-年
     * @return
     */
    public MemberTypeOrderCountResultVO getMemberTypeOrderCountVO(Date startTime, Date endTime, Integer type) {
        MemberTypeOrderCountResultVO memberTypeOrderCountResultVO = new MemberTypeOrderCountResultVO();
        BusinessOrderDataStatistics businessOrderDataStatistics = Optional.ofNullable(businessOrderDataStatisticsMapper.selectOne(new LambdaQueryWrapper<BusinessOrderDataStatistics>()
                .last("order by create_time desc limit 1"))).orElse(new BusinessOrderDataStatistics());
        memberTypeOrderCountResultVO.setUpdateTime(businessOrderDataStatistics.getCreateTime());

        String dateFormat = "'%c.%d'";
        if (ObjectUtil.isNull(type)) {
            type = 1;
        }
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            endTime = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
            if (type == 1){
                startTime = DateUtils.getStartOfDay(DateUtils.addDays(endTime, -6));
            }else if (type == 2) {
                startTime = DateUtils.getStartOfDay(DateUtils.addDays(endTime, -29));
            } else if (type == 3) {
                startTime = DateUtils.getStartOfDay(DateUtils.addMonths(endTime, -11));
                dateFormat = "'%Y.%c'";
            }
        }else {
            if (DateUtils.getDatePoorDay(endTime, startTime) > 90) {
                dateFormat = "'%Y.%c'";
                type = 3;
            } else if (DateUtils.getDatePoorDay(endTime, startTime) > 10) {
                type = 2;
            }
        }
        List<MemberTypeOrderCountVO> result = initMemberTypeOrderCountVO(startTime, endTime, type);
        List<MemberTypeOrderCountVO> memberTypeOrderCountVOs = businessOrderDataStatisticsMapper.getMemberTypeOrderCountVO(startTime, endTime, dateFormat);
        if (CollUtil.isEmpty(memberTypeOrderCountVOs)) {
            memberTypeOrderCountResultVO.setMemberTypeOrderCountVOS(result);
            return memberTypeOrderCountResultVO;
        }

        Map<String, MemberTypeOrderCountVO> memberTypeOrderCountVOMap = memberTypeOrderCountVOs.stream().collect(Collectors.toMap(MemberTypeOrderCountVO::getRecordTime, Function.identity()));
        for (MemberTypeOrderCountVO item : result) {
            MemberTypeOrderCountVO memberTypeOrderCountVO = memberTypeOrderCountVOMap.get(item.getRecordTime());
            if (ObjectUtil.isNull(memberTypeOrderCountVO)) {
                continue;
            }
            item.setOldMemberOrderCount(memberTypeOrderCountVO.getOldMemberOrderCount());
            item.setNewMemberOrderCount(memberTypeOrderCountVO.getNewMemberOrderCount());
            item.setTotalOrderCount(item.getOldMemberOrderCount() + item.getNewMemberOrderCount());
        }
        memberTypeOrderCountResultVO.setMemberTypeOrderCountVOS(result);
        return memberTypeOrderCountResultVO;
    }

    /**
     * 会员趋势
     * @param type
     * @return
     */
    public MemberTrendListVO getMemberTrendVO(Date startTime, Date endTime, Integer type){
        MemberTrendListVO memberTrendListVO = MemberTrendListVO.init();
        BusinessMemberDataStatistics businessMemberDataStatistics = Optional.ofNullable(businessMemberDataStatisticsMapper.selectOne(new LambdaQueryWrapper<BusinessMemberDataStatistics>()
                .last("order by create_time desc limit 1"))).orElse(new BusinessMemberDataStatistics());
        memberTrendListVO.setUpdateTime(businessMemberDataStatistics.getCreateTime());
        String dateFormat = "'%c.%d'";
        if (ObjectUtil.isNull(type)) {
            type = 1;
        }
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            endTime = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
            if (type == 1){
                startTime = DateUtils.getStartOfDay(DateUtils.addDays(endTime, -6));
            }else if (type == 2) {
                startTime = DateUtils.getStartOfDay(DateUtils.addDays(endTime, -29));
            } else if (type == 3) {
                startTime = DateUtils.getStartOfDay(DateUtils.addMonths(endTime, -11));
                dateFormat = "'%Y.%c'";
            }
        }else {
            if (DateUtils.getDatePoorDay(endTime, startTime) > 90) {
                dateFormat = "'%Y.%c'";
                type = 3;
            } else if (DateUtils.getDatePoorDay(endTime, startTime) > 10) {
                type = 2;
            }
        }
        List<MemberTrendVO> result = initMemberTrendVO(startTime, endTime, type);
        List<MemberTrendVO> memberTrendVOList = businessMemberDataStatisticsMapper.getMemberTrendVO(startTime, endTime, dateFormat);
        if (CollUtil.isEmpty(memberTrendVOList)) {
            loadMemberTrendVO(memberTrendListVO, result);
            return memberTrendListVO;
        }

        Map<String, MemberTrendVO> memberTrendVOMap = memberTrendVOList.stream().collect(Collectors.toMap(MemberTrendVO::getRecordTime, Function.identity()));
        for (MemberTrendVO item :result){
            MemberTrendVO memberTrend = memberTrendVOMap.get(item.getRecordTime());
            if (ObjectUtil.isNull(memberTrend)){
                continue;
            }
            item.setMemberCount(memberTrend.getMemberCount());
            item.setRenewMemberCount(memberTrend.getRenewMemberCount());
            item.setExpireMemberCount(memberTrend.getExpireMemberCount());
            item.setExitMemberCount(memberTrend.getExitMemberCount());
            item.setRechargeMemberCount(memberTrend.getMemberCount() + memberTrend.getRenewMemberCount());
        }
        loadMemberTrendVO(memberTrendListVO, result);
        return memberTrendListVO;
    }

    private void loadMemberTrendVO(MemberTrendListVO memberTrendListVO, List<MemberTrendVO> result) {
        for (MemberTrendVO item : result) {
            memberTrendListVO.getDateArray().add(item.getRecordTime());
            memberTrendListVO.getMemberCountArray().add(item.getMemberCount());
            memberTrendListVO.getExitMemberCountArray().add(item.getExitMemberCount());
            memberTrendListVO.getExpireMemberCountArray().add(item.getExpireMemberCount());
            memberTrendListVO.getRenewMemberCountArray().add(item.getRenewMemberCount());
            memberTrendListVO.getRechargeMemberCountArray().add(item.getRechargeMemberCount());
        }
    }

    /**
     * 会员充值数据
     * @return
     */
    public MemberRechargeCountVO getMemberRechargeCountVO(){
        MemberRechargeCountVO result = MemberRechargeCountVO.init();
        Long memberCount = Optional.ofNullable(businessMapper.selectCount(new LambdaQueryWrapper<Business>()
                .in(Business::getMemberStatus, List.of(MemberTypeEnum.RECHARGE.getCode(), MemberTypeEnum.NO_EXPIRE.getCode())))).orElse(0L);
        result.setMemberCount(memberCount);

        MemberRechargeCountVO rechargeCountVO = businessMemberDataStatisticsMapper.getYesterdayMemberRechargeCountVO();
        if (ObjectUtil.isNotNull(rechargeCountVO)){
            result.setYesterdayMemberCount(Optional.ofNullable(rechargeCountVO.getYesterdayMemberCount()).orElse(0L));
            result.setYesterdayRenewMemberCount(Optional.ofNullable(rechargeCountVO.getYesterdayRenewMemberCount()).orElse(0L));
        }
        Date endTime = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
        Date startTime = DateUtils.getStartOfDay(endTime);
        Long exitMemberCount = Optional.ofNullable(businessMemberDataStatisticsMapper.getExitMemberCount(startTime, endTime)).orElse(0L);

        result.setYesterdayExitMemberCount(exitMemberCount);
        return result;
    }
    /**
     * 会员过期数据
     * @return
     */
    public MemberExpireCountVO getMemberExpireCountVO(){
        MemberExpireCountVO result = MemberExpireCountVO.init();
        Long overMemberCount = Optional.ofNullable(businessMapper.selectCount(new LambdaQueryWrapper<Business>()
                .eq(Business::getMemberStatus, MemberTypeEnum.EXPIRE.getCode()))).orElse(0L);
        result.setOverMemberCount(overMemberCount);
        Date endTime = DateUtils.getEndOfDay(DateUtils.addDays(new Date(), -1));
        Date startTime = DateUtils.getStartOfDay(DateUtils.addDays(endTime, -29));
        List<BusinessMemberDataStatistics> memberDataStatistics = businessMemberDataStatisticsMapper.selectList(new LambdaQueryWrapper<BusinessMemberDataStatistics>()
                .between(BusinessMemberDataStatistics::getRecordTime, startTime, endTime));
        if (CollUtil.isEmpty(memberDataStatistics)){
            return result;
        }
        Long noExpireMemberCount = 0L;

        for (BusinessMemberDataStatistics item : memberDataStatistics){
            if (ObjectUtil.isNotNull(item.getRecordTime())) {
                if (LocalDate.now().minusDays(1).equals(item.getRecordTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())){
                    result.setYesterdayUpdateTime(Optional.ofNullable(item.getCreateTime()).orElse(new Date()));
                    result.setYesterdayExpireMemberCount(Optional.ofNullable(item.getExpireMemberCount()).orElse(0L));
                }
                noExpireMemberCount += Optional.ofNullable(item.getExpireMemberCount()).orElse(0L);
            }
        }
        result.setNoExpireMemberCount(noExpireMemberCount);
        result.setUpdateTime(new Date());

        return result;
    }

    private List<MemberTypeOrderCountVO> initMemberTypeOrderCountVO(Date startTime, Date endTime,Integer type) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        String dateFormat = "M.d";
        if (ObjectUtil.isNotNull(type) && type == 3){
            dateFormat = "yyyy.M";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        List<MemberTypeOrderCountVO> resultList = new ArrayList<>();
        while (!calendar.getTime().after(endTime)) {
            resultList.add(MemberTypeOrderCountVO.init(sdf.format(calendar.getTime())));
            if (ObjectUtil.isNotNull(type) && type == 3){
                calendar.add(Calendar.MONTH, 1);
            }else {
                calendar.add(Calendar.DATE, 1);
            }

        }
        return resultList;
    }

    private List<MemberTrendVO> initMemberTrendVO(Date startTime, Date endTime,Integer type){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        String dateFormat = "M.dd";
        if (ObjectUtil.isNotNull(type) && type == 3){
            dateFormat = "yyyy.M";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        List<MemberTrendVO> resultList = new ArrayList<>();
        while (!calendar.getTime().after(endTime)) {
            resultList.add(MemberTrendVO.init(sdf.format(calendar.getTime())));
            if (ObjectUtil.isNotNull(type) && type == 3){
                calendar.add(Calendar.MONTH, 1);
            }else {
                calendar.add(Calendar.DATE, 1);
            }

        }
        return resultList;
    }
}
