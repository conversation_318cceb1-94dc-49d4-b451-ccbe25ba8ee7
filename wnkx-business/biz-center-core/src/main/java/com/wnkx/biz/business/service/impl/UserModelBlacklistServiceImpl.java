package com.wnkx.biz.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.order.ClearVideoCartIntentionModelDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.biz.model.UserModelBlacklist;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.wnkx.biz.business.mapper.UserModelBlacklistMapper;
import com.wnkx.biz.business.service.IBusinessAccountCollectModelService;
import com.wnkx.biz.business.service.IUserModelBlacklistService;
import com.wnkx.biz.model.mapper.ModelMapper;
import com.wnkx.biz.remote.RemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【user_model_blacklist(模特黑名单)】的数据库操作Service实现
 * @createDate 2025-01-09 10:46:49
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserModelBlacklistServiceImpl extends ServiceImpl<UserModelBlacklistMapper, UserModelBlacklist>
        implements IUserModelBlacklistService {
    private final RedisService redisService;
    private final RemoteService remoteService;
    private final ModelMapper modelMapper;
    private final IBusinessAccountCollectModelService businessAccountCollectModelService;


    /**
     * 获取拉黑该模特的商家ID
     */
    @Override
    public List<Long> getBlackModelBusinessIdsByModelId(Long modelId) {
        return baseMapper.getBlackModelBusinessIdsByModelId(modelId);
    }

    /**
     * 获取当前用户拉黑模特列表
     */
    @Override
    public List<UserModelBlacklist> selectBlackModelListByBizUserId() {
        return baseMapper.selectBlackModelListByBizUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void blackModel(Long modelId) {
        Assert.isTrue(UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()), "当前用户类型不可拉黑模特~");
        Long bizUserId = SecurityUtils.getLoginBusinessUser().getBizUserId();
        Assert.notNull(bizUserId, "用户账号不能为空~");
        try {
            lock(bizUserId);
            Assert.isFalse(baseMapper.hasBlacklist(bizUserId, modelId), "已拉黑模特，请勿重复拉黑~");
            baseMapper.insert(UserModelBlacklist.builder().modelId(modelId).bizUserId(bizUserId).build());
            //处理模特拉黑数
            updateModel(modelId, 1);
            businessAccountCollectModelService.clearUserModel(bizUserId, modelId);
            remoteService.clearVideoCartIntentionModelId(ClearVideoCartIntentionModelDTO.builder().bizUserId(bizUserId).intentionModelId(modelId).build());
        } finally {
            redisService.releaseLock(CacheConstants.BIZ_USER_BLACK_MODEL_KEY + bizUserId);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelBlackModel(Long modelId) {
        Assert.isTrue(UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType()), "当前用户类型不可取消拉黑模特~");
        Long bizUserId = SecurityUtils.getLoginBusinessUser().getBizUserId();
        Assert.notNull(bizUserId, "用户账号不能为空~");
        try {
            lock(bizUserId);
            Assert.isTrue(baseMapper.hasBlacklist(bizUserId, modelId), "账号未拉黑模特~");
            baseMapper.deleteBlacklist(bizUserId, modelId);
            //清楚购物车意向模特

            //处理模特拉黑数
            updateModel(modelId, -1);
        } finally {
            redisService.releaseLock(CacheConstants.BIZ_USER_BLACK_MODEL_KEY + bizUserId);
        }
    }

    @Override
    public List<UserBlackModelVO> userBlackModelList(Long bizUserId) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("umb.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        return baseMapper.userBlackModelList(bizUserId);
    }

    @Override
    public List<UserBlackModelVO> userAllBlackModelList(Long bizUserId) {
        List<UserBlackModelVO> blackModelVOList = baseMapper.userBlackModelList(bizUserId);
        return CollUtil.isNotEmpty(blackModelVOList) ? blackModelVOList : new ArrayList<>();
    }

    @Override
    public List<UserModelBlacklist> userBlackModelListByModelIds(List<Long> modelIds) {
        return baseMapper.userBlackModelListByModelIds(modelIds);
    }

    private void updateModel(Long modelId, Integer count) {
        Model model = modelMapper.selectById(modelId);
        Assert.notNull(model, "模特不存在~");
        Model updateModel = new Model();
        updateModel.setId(modelId);
        updateModel.setBlacklistCount(model.getBlacklistCount() + count);
        modelMapper.updateById(updateModel);
    }

    private void lock(Long bizUserId) {
        Assert.isTrue(redisService.getLock(CacheConstants.BIZ_USER_BLACK_MODEL_KEY + bizUserId, 30L), "请勿频繁操作~");
    }
}




