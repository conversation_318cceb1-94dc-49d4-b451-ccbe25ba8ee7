package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail_flow(商家余额详情流水表)】的数据库操作Service
 * @createDate 2024-12-25 16:59:33
 */
public interface IBusinessBalanceDetailFlowService extends IService<BusinessBalanceDetailFlow> {

    /**
     * 根据视频编码获取列表
     * @param videoCode
     * @return
     */
    List<BusinessBalanceDetailFlow> getListByVideoCode(String videoCode);

    /**
     * 根据余额流水ID列表获取列表
     * @param balanceFlowIds
     * @return
     */
    List<BusinessBalanceDetailFlow> getListByBalanceFlowIds(List<Long> balanceFlowIds);
}
