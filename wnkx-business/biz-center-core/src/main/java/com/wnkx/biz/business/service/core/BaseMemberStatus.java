package com.wnkx.biz.business.service.core;


import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.enums.MemberTypeEnum;
import com.ruoyi.common.core.enums.PresentedTimeTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.MemberStatusDto;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :会员状态父类
 * @create :2024-06-21 17:35
 **/
@Slf4j
public abstract class BaseMemberStatus {


    /**
     * 充值
     * @param memberStatusDto
     */
    public abstract void recharge(MemberStatusDto memberStatusDto);

    /**
     * 预过期
     * @param memberStatusDto
     */
    public abstract void preExpire(MemberStatusDto memberStatusDto);

    /**
     * 已过期
     * @param memberStatusDto
     */
    public abstract void expire(MemberStatusDto memberStatusDto);

    public BusinessDTO getBusinessDto(MemberStatusDto memberStatusDto) {
        BusinessDTO businessDTO = new BusinessDTO();
        businessDTO.setId(memberStatusDto.getBusinessId());
        businessDTO.setMemberType(StatusTypeEnum.YES.getCode());
        businessDTO.setMemberStatus(MemberTypeEnum.RECHARGE.getCode());
        businessDTO.setMemberLastTime(new Date());
        businessDTO.setMemberPackageType(memberStatusDto.getChoosePackageType());
        businessDTO.setRechargeCount((ObjectUtil.isNull(memberStatusDto.getRechargeCount()) ? 0 : memberStatusDto.getRechargeCount()) +1);
        return businessDTO;
    }

    public Date getResultDate(Date originDate,Integer month, Integer presentedTime, Integer presentedTimeType){
        Date resultDate = DateUtils.addMonths(originDate, month);
        if (presentedTime.compareTo(0) == 0) {
            return resultDate;
        }
        if (PresentedTimeTypeEnum.DAY.getCode().equals(presentedTimeType)){
            return DateUtils.addDays(resultDate, presentedTime);
        }else if (PresentedTimeTypeEnum.MONTH.getCode().equals(presentedTimeType)){
            return DateUtils.addMonths(resultDate, presentedTime);
        }else if (PresentedTimeTypeEnum.YEAR.getCode().equals(presentedTimeType)){
            return DateUtils.addYears(resultDate, presentedTime);
        }else {
            log.error("加赠时间类型错误：{}", presentedTimeType);
        }

        return resultDate;
    }

    public Date getOriginDate(Date resultDate,Integer month, Integer presentedTime, Integer presentedTimeType){
        if (presentedTime.compareTo(0) == 0) {
            return DateUtils.addMonths(resultDate, -month);
        }
        if (PresentedTimeTypeEnum.DAY.getCode().equals(presentedTimeType)){
            return DateUtils.addMonths(DateUtils.addDays(resultDate, -presentedTime), -month);
        }else if (PresentedTimeTypeEnum.MONTH.getCode().equals(presentedTimeType)){
            return DateUtils.addMonths(DateUtils.addMonths(resultDate, -presentedTime), -month);
        }else if (PresentedTimeTypeEnum.YEAR.getCode().equals(presentedTimeType)){
            return DateUtils.addMonths(DateUtils.addYears(resultDate, -presentedTime), -month);
        }else {
            log.error("加赠时间类型错误：{}", presentedTimeType);
        }
        return DateUtils.addMonths(resultDate, -month);
    }
}
