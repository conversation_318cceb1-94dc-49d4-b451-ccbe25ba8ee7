package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.order.BusinessRemarkFlow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_remark_flow(商家备注流水)】的数据库操作Mapper
* @createDate 2024-12-17 17:01:19
* @Entity com.ruoyi.system.api.domain.entity.order.BusinessRemarkFlow
*/
public interface BusinessRemarkFlowMapper extends SuperMapper<BusinessRemarkFlow> {

    /**
     * 根据商家ID获取商家备注流水列表
     * @param businessId
     * @return
     */
    default List<BusinessRemarkFlow> getListByBusinessId(Long businessId){
        return this.selectList(new LambdaQueryWrapper<BusinessRemarkFlow>()
                .eq(BusinessRemarkFlow::getBusinessId, businessId));
    }
    /**
     * 根据商家ID获取商家备注流水列表
     * @param businessIds
     * @return
     */
    default List<BusinessRemarkFlow> getListByBusinessIds(List<Long> businessIds){
        return this.selectList(new LambdaQueryWrapper<BusinessRemarkFlow>()
                .in(BusinessRemarkFlow::getBusinessId, businessIds));
    }
}




