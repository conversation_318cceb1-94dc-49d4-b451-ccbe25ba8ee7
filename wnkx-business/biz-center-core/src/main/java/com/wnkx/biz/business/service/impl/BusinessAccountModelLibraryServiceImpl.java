package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.constant.HttpStatus;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.enums.ModelStatusEnum;
import com.ruoyi.common.core.enums.ModelTagEnum;
import com.ruoyi.common.core.enums.ModelVideoResourceTypeEnum;
import com.ruoyi.common.core.enums.StatusTypeEnum;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountCollectModel;
import com.ruoyi.system.api.domain.entity.biz.common.BizResource;
import com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.UserBlackModelVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelFamilyVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelTagVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelVO;
import com.wnkx.biz.business.mapper.BusinessAccountModelLibraryMapper;
import com.wnkx.biz.business.service.IBusinessAccountCollectModelService;
import com.wnkx.biz.business.service.IBusinessAccountModelLibraryService;
import com.wnkx.biz.business.service.IUserModelBlacklistService;
import com.wnkx.biz.model.service.BizResourceService;
import com.wnkx.biz.model.service.IModelService;
import com.wnkx.biz.model.service.IModelTagService;
import com.wnkx.biz.model.service.IModelVideoResourceService;
import com.wnkx.biz.remote.RemoteService;
import com.wnkx.biz.tag.service.ITagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 模特商家端信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024/5/27 11:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessAccountModelLibraryServiceImpl implements IBusinessAccountModelLibraryService {

    private final BusinessAccountModelLibraryMapper businessAccountModelLibraryMapper;
    /**
     * 模特案例资源服务
     */
    private final IModelVideoResourceService modelResourceService;
    /**
     * 商家收藏模特服务
     */
    private final IBusinessAccountCollectModelService businessAccountCollectModelService;
    /**
     * 模特服务
     */
    private final IModelService modelService;
    private final IModelTagService modelTagService;
    private final ITagService tagService;
    private final RemoteService remoteService;
    private final BizResourceService bizResourceService;
    private final IUserModelBlacklistService userModelBlacklistService;


    /**
     * 商家端-模特库-我的收藏-收藏模特列表
     *
     * @param businessAccountCollectModelDTO 包含查询条件和分页信息的数据传输对象
     * @return 返回收藏的模特列表，如果用户没有收藏任何模特则返回空列表
     */
    @Override
    public List<BusinessAccountCollectModelVO> selectBusinessAccountCollectModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO) {
        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("bacm.create_time", OrderByDto.DIRECTION.DESC);
        PageUtils.startPage(orderByDto);
        if (ObjectUtil.isNull(businessAccountCollectModelDTO.getNeedAllModel())){
            businessAccountCollectModelDTO.setNeedAllModel(StatusTypeEnum.NO.getCode());
        }
        List<BusinessAccountCollectModelVO> list = businessAccountModelLibraryMapper.selectBusinessAccountCollectModelListByCondition(businessAccountCollectModelDTO,ModelTagEnum.CATEGORY.getCode(),ModelTagEnum.TAG.getCode(), SecurityUtils.getBizUserId());

        assembleData(list);
        return list;
    }


    /**
     * 商家端-模特库-我的收藏-收藏 or 取消收藏
     */
    @Override
    public void collectModel(Long id) {
        try {
            LambdaQueryWrapper<BusinessAccountCollectModel> qw = new LambdaQueryWrapper<>();
            qw.eq(BusinessAccountCollectModel::getBizUserId, SecurityUtils.getBizUserId());
            qw.eq(BusinessAccountCollectModel::getModelId, id);

            // 判断是否已收藏
            List<BusinessAccountCollectModel> list = businessAccountCollectModelService.list(qw);
            if (CollUtil.isNotEmpty(list)) {
                // 已收藏则取消收藏
                businessAccountCollectModelService.remove(qw);
            } else {
                // 未收藏则进行收藏
                BusinessAccountCollectModel mc = new BusinessAccountCollectModel();
                mc.setBizUserId(SecurityUtils.getBizUserId());
                mc.setModelId(id);
                businessAccountCollectModelService.save(mc);
            }
        } catch (Exception e) {
            log.error("收藏操作失败", e);
            throw new ServiceException("收藏操作失败" + e.getMessage(), HttpStatus.ERROR);
        }
    }

    /**
     * 商家端-模特库-模特列表-获取模特信息详细信息。
     *
     * @param id 模特的唯一标识符。
     * @return ModelMerchantVO 模特的详细信息对象。如果找不到对应模特信息，则返回一个空的ModelMerchantVO对象。
     */
    @Override
    public BusinessAccountCollectModelVO getModelInfo(Long id, String keyword, List<Long> specialtyCategory) {
        ModelVO modelVO = modelService.selectModelById(id);
        BusinessAccountCollectModelVO businessAccountCollectModelVO = BeanUtil.copyProperties(modelVO, BusinessAccountCollectModelVO.class);
        businessAccountCollectModelVO.setLoginAccount("");
        boolean exists = businessAccountCollectModelService.lambdaQuery()
                .eq(BusinessAccountCollectModel::getBizUserId, SecurityUtils.getBizUserId())
                .eq(BusinessAccountCollectModel::getModelId, id)
                .exists();

        businessAccountCollectModelVO.setCollect(exists);

        List<ModelFamilyVO> modelFamilyVOS = modelService.selectModelFamilyByFamilyId(modelVO.getFamilyId());
        businessAccountCollectModelVO.setModelFamilys(modelFamilyVOS);

        businessAccountCollectModelVO.getVideoCase().addAll(businessAccountCollectModelVO.getAmazonVideo());
        businessAccountCollectModelVO.getVideoCase().addAll(businessAccountCollectModelVO.getTiktokVideo());
        if (ObjectUtil.isNotNull(modelVO) && CollUtil.isNotEmpty(modelVO.getPersons())){
            businessAccountCollectModelVO.setIssueUserName(modelVO.getPersons().get(0).getName());
        }

        if (CharSequenceUtil.isNotBlank(keyword)) {
            for (ModelTagVO item : businessAccountCollectModelVO.getTags()) {
                String name = item.getName();
                item.setHighlight(name != null && name.toLowerCase().contains(keyword.toLowerCase()));
            }
            businessAccountCollectModelVO.getTags().sort(Comparator.comparing(ModelTagVO::isHighlight).reversed());
            for (ModelTagVO item : businessAccountCollectModelVO.getSpecialtyCategory()) {
                String name = item.getName();
                item.setHighlight(name != null && name.toLowerCase().contains(keyword.toLowerCase()));
            }
            businessAccountCollectModelVO.getSpecialtyCategory().sort(Comparator.comparing(ModelTagVO::isHighlight).reversed());
            for (ModelVideoResource item : businessAccountCollectModelVO.getVideoCase()) {
                String name = item.getName();
                item.setHighlight(name != null && name.toLowerCase().contains(keyword.toLowerCase()));
            }
            businessAccountCollectModelVO.getVideoCase().sort(Comparator.comparing(ModelVideoResource::isHighlight).reversed());
        }
        if (CollUtil.isNotEmpty(specialtyCategory)) {
            for (ModelTagVO item : businessAccountCollectModelVO.getSpecialtyCategory()) {
                Long dictId = item.getId();
                item.setHighlight(dictId != null && specialtyCategory.contains(dictId));
            }
            businessAccountCollectModelVO.getSpecialtyCategory().sort(Comparator.comparing(ModelTagVO::isHighlight).reversed());
        }
        return businessAccountCollectModelVO;
    }

    @Override
    public BusinessAccountCollectModelVO getReferenceModelInfo(Long id, List<Long> specialtyCategory) {
        ModelVO modelVO = modelService.selectModelById(id);
        BusinessAccountCollectModelVO businessAccountCollectModelVO = BeanUtil.copyProperties(modelVO, BusinessAccountCollectModelVO.class);
        businessAccountCollectModelVO.setCollect(Boolean.FALSE);
        List<ModelFamilyVO> modelFamilyVOS = modelService.selectModelFamilyByFamilyId(modelVO.getFamilyId());
        businessAccountCollectModelVO.setModelFamilys(modelFamilyVOS);

        if (CollUtil.isNotEmpty(specialtyCategory)) {
            for (ModelTagVO item : businessAccountCollectModelVO.getSpecialtyCategory()) {
                Long dictId = item.getId();
                item.setHighlight(dictId != null && specialtyCategory.contains(dictId));
            }
            businessAccountCollectModelVO.getSpecialtyCategory().sort(Comparator.comparing(ModelTagVO::isHighlight).reversed());
        }
        return businessAccountCollectModelVO;
    }

    /**
     * 商家端-模特库-模特列表
     *
     * @param businessAccountCollectModelDTO 模特查询条件
     * @return 返回商家端模特信息列表
     */
    @Override
    public List<BusinessAccountCollectModelVO> selectBusinessAccountModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO) {
        businessAccountCollectModelDTO.init();
        //  获取有逾期未反馈素材和无法接单的模特
        List<Long> cannotModel = remoteService.checkModelOverdueVideo(new ArrayList<>());
        if (UserTypeConstants.USER.equals(SecurityUtils.getLoginUserType())){
            //获取拉黑模特数据
            List<UserBlackModelVO> userAllBlackModelList = userModelBlacklistService.userAllBlackModelList(SecurityUtils.getBizUserId());
            if (CollUtil.isNotEmpty(userAllBlackModelList)){
                cannotModel.addAll(userAllBlackModelList.stream().map(UserBlackModelVO::getModelId).collect(Collectors.toList()));
            }
        }
        businessAccountCollectModelDTO.setCannotModel(cannotModel);
        PageUtils.startPage();
        // 通过mapper层查询符合条件的模特信息
        List<BusinessAccountCollectModelVO> list = businessAccountModelLibraryMapper.selectBusinessAccountModelListByCondition
                (businessAccountCollectModelDTO,ModelTagEnum.CATEGORY.getCode(),ModelTagEnum.TAG.getCode(), SecurityUtils.getBizUserId());
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        assembleData(list);
        return list;
    }

    @Override
    public List<Long> modelIsSlotOrNo(BusinessAccountCollectModelDTO dto) {
        return businessAccountModelLibraryMapper.selectModelStatusByModelIds(dto);
    }

    /**
     * 模特关联资源
     *
     * @param modelIds 模特id
     */
    private Map<Long, List<ModelVideoResource>> getModelVideoResourceMapByModelIds(List<Long> modelIds) {
        List<ModelVideoResource> modelVideoResourceList = modelResourceService.selectListByModelIds(modelIds);

        if (CollUtil.isEmpty(modelVideoResourceList)) {
            return new HashMap<>();
        }
        return modelVideoResourceList.stream().collect(Collectors.groupingBy(ModelVideoResource::getModelId));
    }

    /**
     * 组装响应数据
     *
     * @param list
     */
    private void assembleData(List<BusinessAccountCollectModelVO> list) {
        //  获取关联标签
        List<Long> modelId = list.stream().map(BusinessAccountCollectModelVO::getId).collect(Collectors.toList());
        Map<Long, List<ModelTagVO>> modelTagVOS = modelTagService.getModelTagVOMap(modelId);

        List<String> livePicIds = list.stream().map(BusinessAccountCollectModelVO::getLivePicId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> bizResourceIds = StringUtils.splitToLong(livePicIds, StrUtil.COMMA);
        Map<Long, BizResource> bizResourceMap = bizResourceService.getResourceMapByIds(bizResourceIds);

        // 查询模特关联的资源信息
        Map<Long, List<ModelVideoResource>> modelResourceMap = getModelVideoResourceMapByModelIds(modelId);

        // 遍历处理每个模特信息，设置关联的资源数据
        for (BusinessAccountCollectModelVO merchantVO : list) {
            List<ModelVideoResource> modelVideoResource = modelResourceMap.get(merchantVO.getId());
            if (CollUtil.isNotEmpty(modelVideoResource)) {
                merchantVO.setAmazonVideo(modelVideoResource.stream().filter(item -> ModelVideoResourceTypeEnum.AMAZON_VIDEO.getCode().equals(item.getType())).sorted(Comparator.comparingInt(ModelVideoResource::getSort).reversed()).collect(Collectors.toList()));
                merchantVO.setTiktokVideo(modelVideoResource.stream().filter(item -> ModelVideoResourceTypeEnum.TIKTOK_VIDEO.getCode().equals(item.getType())).sorted(Comparator.comparingInt(ModelVideoResource::getSort).reversed()).collect(Collectors.toList()));
            }
            if (StrUtil.isNotBlank(merchantVO.getLivePicId())) {
                for (String livePicId : StrUtil.split(merchantVO.getLivePicId(), StrUtil.COMMA)) {
                    merchantVO.getLifePhoto().add(bizResourceMap.getOrDefault(Convert.toLong(livePicId), new BizResource()).getObjectKey());
                }
            }

            merchantVO.echo(modelTagVOS.get(merchantVO.getId()));

            if (!Objects.equals(merchantVO.getStatus(), ModelStatusEnum.NORMAL.getCode())) {
                merchantVO.setIsNoSlot(Boolean.TRUE);
            }
        }

        //暂无档期的往后排
        list.sort(Comparator.comparing(BusinessAccountCollectModelVO::getIsNoSlot));

    }
}
