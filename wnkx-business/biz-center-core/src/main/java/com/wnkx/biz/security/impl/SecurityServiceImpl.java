package com.wnkx.biz.security.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.constant.Constants;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.SysSecurityService;
import com.ruoyi.system.api.domain.entity.biz.business.BizUser;
import com.ruoyi.system.api.domain.vo.SmsVo;
import com.wnkx.biz.business.service.IBizUserService;
import com.wnkx.biz.security.SecurityService;
import com.wnkx.biz.sms.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 图片验证码与短信验证码
 *
 * <AUTHOR>
 * @date 2024/8/28
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SecurityServiceImpl implements SecurityService {
    private final SmsService smsService;
    private final RedisService redisService;
    private final IBizUserService bizUserService;
    private final SysSecurityService sysSecurityService;

    @Override
    public void sendCode(SmsVo smsVo) {
        sendThirdPartSms(smsVo);
    }

    @Override
    public void registerSendCode(SmsVo smsVo) {
        //校验手机号对应数据是否已绑定
        BizUser bizUser = bizUserService.getByPhone(smsVo.getPhoneNum());
        if (ObjectUtil.isNotNull(bizUser) && StrUtil.isNotBlank(bizUser.getUnionid()) && StrUtil.isNotBlank(smsVo.getTicket())) {
            //登录账号已绑定微信
            throw new Biz200Exception(Biz200Exception.ERROR_IN_DATA, ErrorConstants.PHONE_REGISTER);
        }

        sendThirdPartSms(smsVo);
    }

    public void sendThirdPartSms(SmsVo smsVo) {
        final Integer sendCodeCount = smsService.getSendCodeCount(smsVo.getPhoneNum());

        if (sendCodeCount != null && sendCodeCount >= 3) {
            log.debug("验证码获取次数过多，已自动锁定，请10分钟后尝试");
            throw new Biz200Exception(Biz200Exception.ERROR_IN_DATA, ErrorConstants.SEND_CODE_TO_MORE);
        } else {
            if (sysSecurityService.isOpenSmsSecurity() || (sendCodeCount != null && sendCodeCount >= 2)) {
                log.debug("发送验证码次数超过限制，需要图片验证");
                if (StringUtils.isEmpty(smsVo.getUuid())) {
                    throw new Biz200Exception(Biz200Exception.ERROR_IN_DATA, ErrorConstants.SEND_CODE_PIC_CAPTCHA);
                }
            }
            if (StringUtils.isNotBlank(smsVo.getUuid())){
                checkCaptcha(smsVo.getCode(), smsVo.getUuid());
            }
        }
        smsService.sendCode(smsVo.getPhoneNum());
    }

    /**
     * 校验验证码
     *
     * @param code 验证码
     * @param uuid 唯一标识
     */
    public void checkCaptcha(String code, String uuid) {
        if (StringUtils.isEmpty(code)) {
            throw new ServiceException("验证码不能为空");
        }
        if (StringUtils.isEmpty(uuid)) {
            throw new ServiceException("验证码已失效");
        }
        String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisService.getCacheObject(verifyKey);
        redisService.deleteObject(verifyKey);

        if (!code.equalsIgnoreCase(captcha)) {
            throw new ServiceException("验证码错误");
        }
    }


}
