package com.wnkx.biz.channel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wnkx.biz.channel.service.MemberSeedRecordRelevanceService;
import com.wnkx.biz.channel.mapper.MemberSeedRecordRelevanceMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【member_seed_record_relevance(会员种草提现_种草记录关联表)】的数据库操作Service实现
* @createDate 2025-05-15 09:15:45
*/
@Service
@RequiredArgsConstructor
@Slf4j
public class MemberSeedRecordRelevanceServiceImpl extends ServiceImpl<MemberSeedRecordRelevanceMapper, MemberSeedRecordRelevance>
    implements MemberSeedRecordRelevanceService{

}




