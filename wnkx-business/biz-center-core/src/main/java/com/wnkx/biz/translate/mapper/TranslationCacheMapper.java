package com.wnkx.biz.translate.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.translate.TranslationCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【translation_cache】的数据库操作Mapper
 * @createDate 2025-03-17 09:05:13
 * @Entity com.ruoyi.system.api.domain.entity.biz.translate.TranslationCache
 */
public interface TranslationCacheMapper extends BaseMapper<TranslationCache> {

    /**
     * 根据加密结果查询翻译数据
     *
     * @param sourceSha256
     * @return
     */
    default TranslationCache getBySourceSha256(String sourceSha256) {
        return selectOne(new LambdaQueryWrapper<TranslationCache>().eq(TranslationCache::getSourceSha256, sourceSha256));
    }

    /**
     * 根据加密结果获取翻译数据列表
     * @param sourceSha256List
     * @return
     */
    default List<TranslationCache> getBySourceSha256List(List<String> sourceSha256List) {
        return selectList(new LambdaQueryWrapper<TranslationCache>().in(TranslationCache::getSourceSha256, sourceSha256List));
    }
}




