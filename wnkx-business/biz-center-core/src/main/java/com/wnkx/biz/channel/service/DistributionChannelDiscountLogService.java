package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.dto.biz.channel.fission.EditFissionChannelDiscountDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_discount_log(渠道折扣日志)】的数据库操作Service
* @createDate 2025-05-15 09:15:45
*/
public interface DistributionChannelDiscountLogService extends IService<DistributionChannelDiscountLog> {

    /**
     * 填充日志
     * @param dto
     */
    void saveDistributionChannelDiscountLog(EditFissionChannelDiscountDTO dto);

    /**
     * 根据渠道ID获取折扣日志
     * @param channelId
     * @return
     */
    List<DistributionChannelDiscountLog> queryListByChannelId(Long channelId);
}
