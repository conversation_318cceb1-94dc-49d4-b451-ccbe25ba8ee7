package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessMemberValidityFlowVO;
import com.wnkx.biz.business.mapper.BusinessMemberValidityFlowMapper;
import com.wnkx.biz.business.service.IBusinessMemberValidityFlowService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【business_member_validity_flow(商家会员有效期修改流水)】的数据库操作Service实现
* @createDate 2024-10-14 10:21:18
*/
@Service
public class BusinessMemberValidityFlowServiceImpl extends ServiceImpl<BusinessMemberValidityFlowMapper, BusinessMemberValidityFlow>
    implements IBusinessMemberValidityFlowService {

    @Override
    public List<BusinessMemberValidityFlowVO> queryList(BusinessMemberValidityFlowDTO dto) {
        return baseMapper.queryList(dto);
    }

    @Override
    public BusinessMemberValidityFlowVO getRelationOrder(Long businessId) {
        return BeanUtil.copyProperties(baseMapper.getRelationOrder(businessId), BusinessMemberValidityFlowVO.class);
    }
}




