package com.wnkx.biz.business.service;

import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountCollectModelDTO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountCollectModelVO;

import java.util.List;

/**
 * 模特商家端Service接口
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
public interface IBusinessAccountModelLibraryService {

    /**
     * 商家端-模特库-模特列表
     */
    List<BusinessAccountCollectModelVO> selectBusinessAccountModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO);

    /**
     * 商家端-模特库-模特列表-获取模特信息详细信息
     */
    BusinessAccountCollectModelVO getModelInfo(Long id, String keyword, List<Long> specialtyCategory);

    /**
     * 获取模特详情信息（无需登录）
     * @param id
     * @return
     */
    BusinessAccountCollectModelVO getReferenceModelInfo(Long id, List<Long> specialtyCategory);

    /**
     * 商家端-模特库-我的收藏-收藏 or 取消收藏
     */
    void collectModel(Long id);

    /**
     * 商家端-模特库-我的收藏-收藏模特列表
     */
    List<BusinessAccountCollectModelVO> selectBusinessAccountCollectModelListByCondition(BusinessAccountCollectModelDTO businessAccountCollectModelDTO);

    List<Long> modelIsSlotOrNo(BusinessAccountCollectModelDTO dto);
}
