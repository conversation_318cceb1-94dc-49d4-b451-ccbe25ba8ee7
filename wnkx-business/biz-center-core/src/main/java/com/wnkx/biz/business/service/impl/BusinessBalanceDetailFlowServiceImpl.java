package com.wnkx.biz.business.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.wnkx.biz.business.service.IBusinessBalanceDetailFlowService;
import com.wnkx.biz.business.mapper.BusinessBalanceDetailFlowMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail_flow(商家余额详情流水表)】的数据库操作Service实现
 * @createDate 2024-12-25 16:59:33
 */
@Service
public class BusinessBalanceDetailFlowServiceImpl extends ServiceImpl<BusinessBalanceDetailFlowMapper, BusinessBalanceDetailFlow>
        implements IBusinessBalanceDetailFlowService {

    @Override
    public List<BusinessBalanceDetailFlow> getListByVideoCode(String videoCode) {
        return baseMapper.getListByVideoCode(videoCode);
    }

    @Override
    public List<BusinessBalanceDetailFlow> getListByBalanceFlowIds(List<Long> balanceFlowIds) {
        return baseMapper.getListByBalanceFlowIds(balanceFlowIds);
    }
}




