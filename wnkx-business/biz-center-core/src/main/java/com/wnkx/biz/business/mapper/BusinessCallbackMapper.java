package com.wnkx.biz.business.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.BusinessCallbackStatusEnum;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessCallbackListDTO;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallback;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackListVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackStatusCountVO;
import com.wnkx.db.mapper.SuperMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:27
 */
@Mapper
public interface BusinessCallbackMapper extends SuperMapper<BusinessCallback> {

    /**
     * 商家回访-回访列表
     */
    List<BusinessCallbackListVO> selectCallbackListByCondition(@Param("dto") BusinessCallbackListDTO dto);

    /**
     * 获取回访中记录
     */
    default BusinessCallback getInTheReturnVisitByBusinessId(Long businessId) {
        return selectOne(new LambdaQueryWrapper<BusinessCallback>()
                .eq(BusinessCallback::getBusinessId, businessId)
                .eq(BusinessCallback::getStatus, BusinessCallbackStatusEnum.IN_THE_RETURN_VISIT.getCode())
        );
    }

    /**
     * 获取商家回访记录
     */
    default List<BusinessCallback> selectListByBusinessId(Long businessId) {
        return selectList(new LambdaQueryWrapper<BusinessCallback>()
                .eq(ObjectUtil.isNotNull(businessId), BusinessCallback::getBusinessId, businessId)
        );
    }

    /**
     * 商家回访-回访状态数统计
     */
    BusinessCallbackStatusCountVO returnVisitStatusCount();
}
