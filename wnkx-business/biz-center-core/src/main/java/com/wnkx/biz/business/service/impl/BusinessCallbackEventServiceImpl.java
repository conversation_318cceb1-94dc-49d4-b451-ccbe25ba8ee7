package com.wnkx.biz.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessCallbackEvent;
import com.wnkx.biz.business.mapper.BusinessCallbackEventMapper;
import com.wnkx.biz.business.service.BusinessCallbackEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:28
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessCallbackEventServiceImpl extends ServiceImpl<BusinessCallbackEventMapper, BusinessCallbackEvent> implements BusinessCallbackEventService {

    /**
     * 根据回访ID删除回访事件
     */
    @Override
    public void deleteByCallbackIds(List<Long> deleteCallbackIds) {
        if (CollUtil.isEmpty(deleteCallbackIds)) {
            return;
        }
        baseMapper.deleteByCallbackIds(deleteCallbackIds);
    }

    /**
     * 根据回访ID查询回访事件
     */
    @Override
    public List<BusinessCallbackEvent> selectListByCallbackId(Long callbackId) {
        return baseMapper.selectListByCallbackIds(List.of(callbackId));
    }

    /**
     * 根据回访ID查询回访事件
     */
    @Override
    public List<BusinessCallbackEvent> selectListByCallbackIds(Collection<Long> callbackIds) {
        return baseMapper.selectListByCallbackIds(callbackIds);
    }
}
