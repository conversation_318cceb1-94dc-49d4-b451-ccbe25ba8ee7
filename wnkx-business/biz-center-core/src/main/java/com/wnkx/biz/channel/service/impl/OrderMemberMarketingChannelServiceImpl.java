package com.wnkx.biz.channel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.dto.order.OrderMemberChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel;
import com.wnkx.biz.channel.service.IOrderMemberMarketingChannelService;
import com.wnkx.biz.channel.mapper.OrderMemberMarketingChannelMapper;
import com.wnkx.biz.core.ChannelCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
* <AUTHOR>
* @description 针对表【order_member_marketing_channel(会员市场渠道记录表)】的数据库操作Service实现
* @createDate 2024-12-06 14:56:21
*/
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderMemberMarketingChannelServiceImpl extends ServiceImpl<OrderMemberMarketingChannelMapper, OrderMemberMarketingChannel>
    implements IOrderMemberMarketingChannelService {

    private final ChannelCore channelCore;

    @Override
    public void saveOrderMemberMarketingChannel(OrderMemberChannelDTO dto) {
        MarketingChannel marketingChannel = channelCore.getMarketingChannelByBizUserId(dto.getBizUserId());
        if (ObjectUtil.isNull(marketingChannel)){
            return;
        }
        OrderMemberMarketingChannel orderMemberChannel = BeanUtil.copyProperties(dto, OrderMemberMarketingChannel.class);
        orderMemberChannel.setChannelId(marketingChannel.getId());
        orderMemberChannel.setChannelName(marketingChannel.getMarketingChannelName());
        orderMemberChannel.setMarketingPlatform(marketingChannel.getMarketingPlatform());
        orderMemberChannel.setCreateTime(DateUtil.date());
        baseMapper.insert(orderMemberChannel);
    }
}




