package com.wnkx.biz.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.entity.order.BusinessRemarkFlow;
import com.ruoyi.system.api.domain.vo.order.BusinessRemarkFlowVO;
import com.ruoyi.system.api.domain.vo.order.OrderVideoChangeLogInfoVO;
import com.wnkx.biz.business.service.IBusinessRemarkFlowService;
import com.wnkx.biz.business.mapper.BusinessRemarkFlowMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【business_remark_flow(商家备注流水)】的数据库操作Service实现
* @createDate 2024-12-17 17:01:19
*/
@Service
public class BusinessRemarkFlowServiceImpl extends ServiceImpl<BusinessRemarkFlowMapper, BusinessRemarkFlow>
    implements IBusinessRemarkFlowService {

    @Override
    public List<BusinessRemarkFlowVO> getListByBusinessId(Long businessId) {
        return BeanUtil.copyToList(baseMapper.getListByBusinessId(businessId), BusinessRemarkFlowVO.class);
    }

    @Override
    public Map<Long, List<BusinessRemarkFlow>> getRemarkFlowMap(List<Long> businessIds) {
        if (CollUtil.isEmpty(businessIds)){
            return Collections.emptyMap();
        }
        List<BusinessRemarkFlow> listByBusinessIds = baseMapper.getListByBusinessIds(businessIds);
        if (CollUtil.isEmpty(listByBusinessIds)){
            return Collections.emptyMap();
        }
        return listByBusinessIds.stream().collect(Collectors.groupingBy(BusinessRemarkFlow::getBusinessId));
    }
}




