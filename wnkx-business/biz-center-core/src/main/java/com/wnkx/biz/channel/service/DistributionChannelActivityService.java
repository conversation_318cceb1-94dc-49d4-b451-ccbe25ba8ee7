package com.wnkx.biz.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivity;
import com.ruoyi.system.api.domain.vo.biz.channel.ChannelBrokeRageVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【distribution_channel_activity(渠道活动表)】的数据库操作Service
 * @createDate 2024-12-02 14:27:22
 */
public interface DistributionChannelActivityService extends IService<DistributionChannelActivity> {

    /**
     * 查询渠道活动列表
     * @param dto 查询条件
     * @return 渠道活动列表
     */
    List<ChannelActivityDTO> list(ChannelActivityDTO dto);

    /**
     * 保存渠道活动
     * @param dto  渠道活动信息
     */
    void saveChannelActivity(ChannelActivityEditDTO dto);

    /**
     * 编辑渠道活动
     * @param dto 渠道活动信息
     */
    void editChannelActivity(ChannelActivityEditDTO dto);

    /**
     * 获取单个渠道活动信息
     * @param id 渠道活动id
     * @return 渠道活动信息
     */
    ChannelActivityEditDTO getActivityInfo(Long id);
//
//    /**
//     * 获取渠道折扣
//     * @param code 种草码
//     * @return 折扣
//     */
//    @Deprecated(since = "于5/12~5/16周开发计划 更新", forRemoval = true)
//    BigDecimal getDiscount(String code);

    /**
     * 获取渠道折扣
     * @param code
     * @return
     */
    ChannelBrokeRageVO getChannelDiscount(String code);

    /**
     * 获取渠道活动列表
     * @param channelName 渠道名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param activityId 活动id
     * @return 渠道活动列表
     */
    List<ChannelActivityInfoDTO> getActivityChannelList(String channelName, Date startTime, Date endTime, Long activityId);

    /**
     * 下载活动海报
     */
    void download(Long id, HttpServletResponse response);

    ChannelActivityDTO getLatestChannelDiscount(Long channelId);
}
