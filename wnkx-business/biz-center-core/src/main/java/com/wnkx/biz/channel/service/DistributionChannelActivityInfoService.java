package com.wnkx.biz.channel.service;

import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivityInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【distribution_channel_activity_info(渠道活动关联表)】的数据库操作Service
* @createDate 2024-12-02 14:27:22
*/
public interface DistributionChannelActivityInfoService extends IService<DistributionChannelActivityInfo> {
    void save(List<Long> channelIds, Long activityId);

    void updateActivity(ChannelActivityEditDTO dto);

    List<ChannelActivityInfoDTO> getChannelInfo(Long id);

    List<ChannelActivityInfoDTO> getChannelInfo(String channelName,Long id);
}
