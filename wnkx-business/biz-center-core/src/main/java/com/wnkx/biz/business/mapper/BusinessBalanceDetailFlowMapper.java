package com.wnkx.biz.business.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow;
import com.wnkx.db.mapper.SuperMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【business_balance_detail_flow(商家余额详情流水表)】的数据库操作Mapper
 * @createDate 2024-12-25 16:59:33
 * @Entity com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow
 */
public interface BusinessBalanceDetailFlowMapper extends SuperMapper<BusinessBalanceDetailFlow> {

    /**
     * 根据余额流水ID列表获取列表
     * @param balanceFlowIds
     * @return
     */
    default List<BusinessBalanceDetailFlow> getListByBalanceFlowIds(List<Long> balanceFlowIds) {
        return this.selectList(new LambdaQueryWrapper<BusinessBalanceDetailFlow>()
                .in(BusinessBalanceDetailFlow::getBalanceFlowId, balanceFlowIds));
    }

    /**
     * 根据视频编码获取列表
     *
     * @param videoCode
     * @return
     */
    default List<BusinessBalanceDetailFlow> getListByVideoCode(String videoCode) {
        return this.selectList(new LambdaQueryWrapper<BusinessBalanceDetailFlow>()
                .eq(BusinessBalanceDetailFlow::getVideoCode, videoCode));

    }
}




