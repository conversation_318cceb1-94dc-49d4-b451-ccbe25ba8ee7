package com.wnkx.biz.business.service.core;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountTokenDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.MemberStatusDto;
import com.ruoyi.system.api.domain.entity.biz.business.*;
import com.ruoyi.system.api.domain.entity.biz.channel.BizUserChannel;
import com.ruoyi.system.api.domain.entity.biz.channel.BusinessChannel;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.wnkx.biz.business.service.*;
import com.wnkx.biz.channel.mapper.BizUserChannelMapper;
import com.wnkx.biz.channel.mapper.BusinessChannelMapper;
import com.wnkx.biz.core.SystemCore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.util.*;


/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-21 17:37
 **/
@Service
@RequiredArgsConstructor
@Validated
@Slf4j
public class MemberContext {

    protected static final Map<Integer, String> MEMBER_STATUS_NAME_MAP;

    static {
        MEMBER_STATUS_NAME_MAP = new HashMap<>();
        MEMBER_STATUS_NAME_MAP.put(MemberTypeEnum.NO_RECHARGE.getCode(), "unRechargeStatus");
        MEMBER_STATUS_NAME_MAP.put(MemberTypeEnum.RECHARGE.getCode(), "rechargeStatus");
        MEMBER_STATUS_NAME_MAP.put(MemberTypeEnum.NO_EXPIRE.getCode(), "unExpireStatus");
        MEMBER_STATUS_NAME_MAP.put(MemberTypeEnum.EXPIRE.getCode(), "expireStatus");
    }

    private final Map<String, BaseMemberStatus> memberStatusMap;

    private final IBusinessService businessService;

    private final SystemCore systemCore;

    private final IBizUserService bizUserService;

    private final IBusinessAccountService businessAccountService;

    private final IBusinessMemberValidityFlowService businessMemberValidityFlowService;

    private final BizUserChannelMapper bizUserChannelMapper;

    private final BusinessChannelMapper businessChannelMapper;


    /**
     * 会员状态流转
     *
     * @param memberStatusDto
     */
    @Transactional(rollbackFor = Exception.class)
    public BusinessAccountVO flowMember(@Valid MemberStatusDto memberStatusDto) {
        BusinessDTO dto = new BusinessDTO();
        dto.setId(memberStatusDto.getBusinessId());
        Business business = businessService.queryOne(dto);
        if (ObjectUtil.isNull(business)) {
            Assert.isTrue(MemberTypeEnum.RECHARGE.getCode().equals(memberStatusDto.getChooseMemberStatus()), "商家数据不存在！");
            //初始化商家
            BizUser bizUser = bizUserService.getById(memberStatusDto.getBizUserId());
            if (ObjectUtil.isNull(bizUser)) {
                throw new ServiceException("登录账号数据不存在！");
            }
            business = businessAccountService.initBusinessV1(bizUser);
            business.setMemberStatus(MemberTypeEnum.NO_RECHARGE.getCode());
            //设置商家id
            BizUserChannel bizUserChannel = bizUserChannelMapper.getUserChannelOne(bizUser.getId());
            bizUserChannel.setBusinessId(business.getId());
            bizUserChannelMapper.updateById(bizUserChannel);
            businessChannelMapper.insert(BeanUtil.copyProperties(bizUserChannel, BusinessChannel.class,"id"));
        }
        BeanUtil.copyProperties(business, memberStatusDto);
        memberStatusDto.setBusinessId(business.getId());
        BaseMemberStatus baseMemberStatus = memberStatusMap.get(MEMBER_STATUS_NAME_MAP.get(business.getMemberStatus()));
        Integer presentedTime = 0;
        Integer presentedTimeType = PresentedTimeTypeEnum.MONTH.getCode();
        //根据选择的会员状态调用方法
        if (MemberTypeEnum.RECHARGE.getCode().equals(memberStatusDto.getChooseMemberStatus())) {
            if (ObjectUtil.isNull(memberStatusDto.getChoosePackageType())) {
                throw new ServiceException("充值时套餐类型不能为空！");
            }
            Integer monthNum = PackageTypeEnum.getMonthByCode(memberStatusDto.getChoosePackageType());
            memberStatusDto.setMonthNum(monthNum);
            if (ObjectUtil.isNotNull(memberStatusDto.getPresentedTime())) {
                presentedTime = memberStatusDto.getPresentedTime();
            }
            if (ObjectUtil.isNotNull(memberStatusDto.getPresentedTimeType())){
                presentedTimeType = memberStatusDto.getPresentedTimeType();
            }
            memberStatusDto.setPresentedTime(presentedTime);
            memberStatusDto.setPresentedTimeType(presentedTimeType);
            //充值状态
            baseMemberStatus.recharge(memberStatusDto);
            businessService.updateBusinessCallback(business.getId());
        } else if (MemberTypeEnum.NO_EXPIRE.getCode().equals(memberStatusDto.getChooseMemberStatus())) {
            //即将过期
            baseMemberStatus.preExpire(memberStatusDto);
        } else if (MemberTypeEnum.EXPIRE.getCode().equals(memberStatusDto.getChooseMemberStatus())) {
            //过期
            baseMemberStatus.expire(memberStatusDto);
        } else {
            throw new ServiceException("选择状态有误，不存在该状态");
        }
        BusinessAccountVO businessAccountVO = businessAccountService.queryOne(BusinessAccountDTO.builder().bizUserId(memberStatusDto.getBizUserId()).build());

        businessAccountVO.setBusinessVO(BeanUtil.copyProperties(businessService.getById(business.getId()), BusinessVO.class));
        businessAccountService.updateLoginBusiness(Arrays.asList(memberStatusDto.getBusinessId()), getBusinessAccountTokenDTO(businessAccountVO));

        //添加会员有效期修改流水
        if (MemberTypeEnum.RECHARGE.getCode().equals(memberStatusDto.getChooseMemberStatus())) {
            BusinessMemberValidityFlow businessMemberValidityFlow = new BusinessMemberValidityFlow();
            try {
                Date endOfDayIgnoreMillisecond = DateUtils.getEndOfDayIgnoreMillisecond(businessAccountVO.getBusinessVO().getMemberValidity());
                businessMemberValidityFlow = BusinessMemberValidityFlow.builder()
                        .businessId(business.getId())
                        .resultMemberValidity(endOfDayIgnoreMillisecond)
                        .originMemberValidity(baseMemberStatus.getOriginDate(businessAccountVO.getBusinessVO().getMemberValidity(), memberStatusDto.getMonthNum(), memberStatusDto.getPresentedTime(), memberStatusDto.getPresentedTimeType()))
                        .presentedTime(presentedTime)
                        .presentedTimeType(presentedTimeType)
                        .type(MemberValidTypeEnum.USER.getCode())
                        .rechargeCount((ObjectUtil.isNull(business.getRechargeCount()) ? 0 : memberStatusDto.getRechargeCount()) +1)
                        .memberPackageType(memberStatusDto.getChoosePackageType())
                        .orderNum(memberStatusDto.getOrderNum())
                        .realPayAmount(memberStatusDto.getRealPayAmount())
                        .realPayAmountCurrency(memberStatusDto.getRealPayAmountCurrency())
                        .currency(memberStatusDto.getCurrency())
                        .remark("")
                        .createBy(StrUtil.isBlank(SecurityUtils.getUsername()) ? "商家购买" : SecurityUtils.getUsername())
                        .createById(Optional.ofNullable(SecurityUtils.getUserId()).orElse(0L))
                        .build();
                businessMemberValidityFlowService.save(businessMemberValidityFlow);
            } catch (Exception e) {
                log.error("添加余额流水失败：{}", businessMemberValidityFlow.toString());
            }
        }
        return businessAccountVO;
    }


    private BusinessAccountTokenDTO getBusinessAccountTokenDTO(BusinessAccountVO vo) {
        BusinessAccountTokenDTO businessAccountTokenDTO = new BusinessAccountTokenDTO();
        businessAccountTokenDTO.setName(vo.getName());
        businessAccountTokenDTO.setIsProxy(vo.getBusinessVO().getIsProxy());
        businessAccountTokenDTO.setCustomerType(vo.getBusinessVO().getCustomerType());
        businessAccountTokenDTO.setWaiterId(vo.getBusinessVO().getWaiterId());
        businessAccountTokenDTO.setMemberCode(vo.getBusinessVO().getMemberCode());
        businessAccountTokenDTO.setMemberType(vo.getBusinessVO().getMemberType());
        businessAccountTokenDTO.setMemberStatus(vo.getBusinessVO().getMemberStatus());
        businessAccountTokenDTO.setMemberPackageType(vo.getBusinessVO().getMemberPackageType());
        businessAccountTokenDTO.setMemberFirstTime(vo.getBusinessVO().getMemberFirstTime());
        businessAccountTokenDTO.setMemberFirstType(vo.getBusinessVO().getMemberFirstType());
        businessAccountTokenDTO.setMemberLastTime(vo.getBusinessVO().getMemberLastTime());
        businessAccountTokenDTO.setMemberValidity(vo.getBusinessVO().getMemberValidity());
        return businessAccountTokenDTO;
    }
}
