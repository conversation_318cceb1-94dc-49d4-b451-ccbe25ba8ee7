package com.wnkx.biz.logistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.enums.LogisticMainStatus;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.entity.LogisticInfo;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;
import com.wnkx.biz.logistic.mapper.LogisticInfoMapper;
import com.wnkx.biz.logistic.service.LogisticInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 物流信息详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
@RequiredArgsConstructor
public class LogisticInfoServiceImpl extends ServiceImpl<LogisticInfoMapper, LogisticInfo> implements LogisticInfoService {


    /**
     * 通过物流单号获取最新的物流信息
     */
    @Override
    public List<LogisticInfoVO> getLastLogisticInfo(Collection<String> numbers) {
        List<LogisticInfoVO> list = baseMapper.getLastLogisticInfo(numbers);

        list.forEach(item -> {
                    item.setMainStatusSketch(LogisticMainStatus.getSketchByLabel(item.getMainStatus()));
                    item.setSubStatusSketch(LogisticMainStatus.LogisticSubStatus.getSketchByLabel(item.getSubStatus()));
                }
        );
        return list;
    }

    @Override
    public Collection<String> getNumbersByCondition(LogisticListDTO dto) {
        return baseMapper.getNumbersByCondition(dto);
    }

    /**
     * 通过物流单号删除物流信息
     *
     * @param number 物流单号
     */
    @Override
    public void removeByNumber(String number) {
        baseMapper.removeByNumber(number);
    }

    /**
     * 通过物流单号查询物流信息
     *
     * @param number 物流单号
     * @return 物流信息
     */
    @Override
    public List<LogisticInfo> selectListByNumber(Collection<String> number) {
        return baseMapper.selectListByNumber(number);
    }
}
