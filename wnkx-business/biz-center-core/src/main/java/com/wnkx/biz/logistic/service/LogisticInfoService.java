package com.wnkx.biz.logistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.logistic.LogisticListDTO;
import com.ruoyi.system.api.domain.entity.LogisticInfo;
import com.ruoyi.system.api.domain.vo.LogisticInfoVO;

import java.util.Collection;
import java.util.List;

/**
 * 物流信息详情Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface LogisticInfoService extends IService<LogisticInfo>
{

    /**
     * 通过物流单号查询物流信息
     *
     * @param number 物流单号
     * @return 物流信息
     */
    List<LogisticInfo> selectListByNumber(Collection<String> number);

    /**
     * 通过物流单号删除物流信息
     *
     * @param number 物流单号
     */
    void removeByNumber(String number);


    Collection<String> getNumbersByCondition(LogisticListDTO dto);

    /**
     * 通过物流单号获取最新的物流信息
     */
    List<LogisticInfoVO> getLastLogisticInfo(Collection<String> numbers);
}
