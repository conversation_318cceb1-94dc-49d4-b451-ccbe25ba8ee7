package com.wnkx.biz.channel.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.channel.InviteListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.MarketingChannelListDTO;
import com.ruoyi.system.api.domain.dto.biz.channel.VisitMarketingChannelDTO;
import com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.biz.channel.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【marketing_channel(市场渠道信息表)】的数据库操作Service
 * @createDate 2024-09-24 17:03:37
 */
public interface IMarketingChannelService extends IService<MarketingChannel> {

    /**
     * 查询市场渠道列表
     */
    List<MarketingChannelListVO> marketingChannelListByCondition(MarketingChannelListDTO listDTO);

    /**
     * 新增市场渠道
     */
    void saveMarketingChannel(MarketingChannelDTO dto);

    /**
     * 查询市场渠道详情
     */
    MarketingChannelDetailVO getMarketingChannelDetail(Long id);

    /**
     * 编辑市场渠道
     */
    void editMarketingChannel(MarketingChannelDTO dto);

    /**
     * 下载物料
     */
    DownloadMaterialVO downloadMaterial(Long id);

    /**
     * 通过专属链接code获取专属企微二维码
     */
    QrcodeByDedicatedLinkCodeVO getQrcodeByDedicatedLinkCode(String dedicatedLinkCode);

    /**
     * 导出市场渠道列表
     */
    void exportMarketingChannelList(MarketingChannelListDTO listDTO, HttpServletResponse response);

    /**
     * 分销渠道统计
     *
     * @param listDTO
     * @return
     */
    MarketingChannelStatisticsVO marketingChannelStatistics(MarketingChannelListDTO listDTO);

    /**
     * 获取创建人列表
     *
     * @param name
     * @return
     */
    List<SysUserVO> createUserList(String name);


    /**
     * 邀请记录
     *
     * @param dto
     * @return
     */
    List<ChannelInviteVO> getChannelInviteList(InviteListDTO dto);

    /**
     * 获取邀请记录统计
     *
     * @param dto
     * @return
     */
    ChannelInviteStatisticsVO getChannelInviteStatistics(InviteListDTO dto);

    /**
     * 导出邀请记录列表数据
     *
     * @param dto
     * @return
     */
    List<ChannelInviteExportVO> exportChannelInviteList(InviteListDTO dto);

    /**
     * 记录访问
     *
     * @param dto
     */
    void visit(VisitMarketingChannelDTO dto);

    /**
     * 刷新独立访客数
     */
    void refreshUniqueVisitor();
}
