<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.wechat.mapper.WeChatExternalUserMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.wechat.WeChatExternalUser" id="WeChatExternalUserResult">
        <result property="id" column="id"/>
        <result property="externalUserid" column="external_userid"/>
        <result property="unionid" column="unionid"/>
        <result property="name" column="name"/>
        <result property="position" column="position"/>
        <result property="avatar" column="avatar"/>
        <result property="corpName" column="corp_name"/>
        <result property="corpFullName" column="corp_full_name"/>
        <result property="type" column="type"/>
        <result property="gender" column="gender"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectWeChatExternalUserVo">
        select id,
               external_userid,
               unionid,
               name,
               position,
               avatar,
               corp_name,
               corp_full_name,
               type,
               gender,
               status,
               create_time,
               update_time
        from we_chat_external_user
    </sql>
    <select id="todayContactUser" resultType="com.ruoyi.system.api.domain.dto.biz.wechat.ContactUserCountDTO">
        with sel_data as (
            SELECT connect_user_id as contact_user_id
                          FROM we_chat_external_user wceu
                          where wceu.create_time > CURDATE()
                            and connect_user_id is not null
                          order by id desc
                          )
        select contact_user_id, count(*) add_count
        from sel_data
        group by contact_user_id
        order by add_count asc
    </select>
</mapper>