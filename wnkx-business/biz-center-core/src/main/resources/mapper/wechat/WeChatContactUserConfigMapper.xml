<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.wechat.mapper.WeChatContactUserConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactUserConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="contactUserName" column="contact_user_name" jdbcType="VARCHAR"/>
            <result property="contactUserId" column="contact_user_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,contact_user_name,contact_user_id,
        create_time,update_time
    </sql>
    <select id="getAcquisitionByContactUserId" resultType="java.lang.String">
        SELECT config_url from we_chat_contact_user_config_info wccuci WHERE wccuci.url_type = ${@<EMAIL>}
                                                                and wccuci.config_id = #{contactUserId}
    </select>
</mapper>
