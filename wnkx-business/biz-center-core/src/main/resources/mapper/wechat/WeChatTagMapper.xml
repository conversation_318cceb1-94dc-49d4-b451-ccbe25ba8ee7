<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.wechat.mapper.WeChatTagMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.wechat.WeChatTag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="VARCHAR"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="tagId" column="tag_id" jdbcType="VARCHAR"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,group_id,group_name,
        tag_id,tag_name,create_time
    </sql>
</mapper>
