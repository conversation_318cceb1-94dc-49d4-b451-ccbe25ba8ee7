<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.wechat.mapper.WeChatContactConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.wechat.WeChatContactConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="configId" column="config_id" jdbcType="VARCHAR"/>
            <result property="qrCode" column="qr_code" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,config_id,qr_code,
        create_time,update_time
    </sql>
</mapper>
