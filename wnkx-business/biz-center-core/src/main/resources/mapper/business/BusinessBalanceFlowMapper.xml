<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalanceFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <id property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
        <result property="refundNum" column="refund_num" jdbcType="VARCHAR"/>
        <result property="videoCode" column="video_code" jdbcType="VARCHAR"/>
        <result property="balance" column="balance" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="origin" column="origin" jdbcType="INTEGER"/>
        <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,business_id,order_num,refund_num,
        video_code,balance,amount,
        type,origin,order_time,create_user_id,
        create_user_name,create_time,update_user_id,
        update_user_name,update_time
    </sql>

    <select id="queryList" resultType="com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceFlow">
        select
        bbf.id,bbf.business_id,bbf.flow_order_no,bbf.order_num,bbf.refund_num,bbf.withdraw_number,bbf.prepay_num,bbf.video_code,bbf.video_id,bbf.balance,bbf.amount,bbf.type,bbf.origin,bbf.order_time,bbf.create_user_id,bbf.create_user_name,bbf.create_time,bbf.update_user_id,bbf.update_user_name,bbf.update_time
        from business_balance_flow bbf
        <if test="keyword != null and keyword != ''">
            left join business_balance_detail_flow bbdf on bbdf.balance_flow_id = bbf.id
        </if>
        <where>
            <if test="keyword != null and keyword != ''">
                and (
                bbdf.balance_number LIKE CONCAT('%', #{keyword}, '%')
                or bbf.order_num LIKE CONCAT('%', #{keyword}, '%')
                or bbf.refund_num LIKE CONCAT('%', #{keyword}, '%')
                or bbf.video_code LIKE CONCAT('%', #{keyword}, '%')
                or bbf.prepay_num LIKE CONCAT('%', #{keyword}, '%')
                or bbf.withdraw_number LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="businessId != null">
                AND bbf.business_id = #{businessId}
            </if>
            <if test="orderNum != null and orderNum != ''">
                AND (
                bbf.order_num LIKE CONCAT('%', #{orderNum}, '%')
                or bbf.prepay_num LIKE CONCAT('%', #{orderNum}, '%')
                )
            </if>
            <if test="refundNum != null and refundNum != ''">
                AND bbf.refund_num LIKE CONCAT('%', #{refundNum}, '%')
            </if>
            <if test="videoCode != null and videoCode != ''">
                AND bbf.video_code LIKE CONCAT('%', #{videoCode}, '%')
            </if>
            <if test="businessIds != null and businessIds.size() != 0">
                AND bbf.business_id IN
                <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                    #{businessId}
                </foreach>
            </if>
            <if test="origins != null and origins.size() != 0">
                AND bbf.origin IN
                <foreach collection="origins" item="origin" open="(" separator="," close=")">
                    #{origin}
                </foreach>
            </if>
            <if test="orderTimeBegin != null and orderTimeEnd != null">
                AND bbf.order_time BETWEEN #{orderTimeBegin} AND #{orderTimeEnd}
            </if>
            <if test="origin != null">
                AND bbf.origin = #{origin}
            </if>
            <if test="type != null">
                AND bbf.type = #{type}
            </if>
        </where>
        group by bbf.id,bbf.business_id,bbf.flow_order_no,bbf.order_num,bbf.refund_num,bbf.withdraw_number,bbf.prepay_num,bbf.video_code,bbf.video_id,bbf.balance,bbf.amount,bbf.type,bbf.origin,bbf.order_time,bbf.create_user_id,bbf.create_user_name,bbf.create_time,bbf.update_user_id,bbf.update_user_name,bbf.update_time
    </select>

    <select id="statistics" resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceFlowStatisticsVO">
        select
        business_id,
        SUM(CASE WHEN origin in(7,8) THEN amount ELSE 0 END) AS prepayAmountTotal,
        SUM(CASE WHEN origin in(4,5) THEN amount ELSE 0 END) AS balancePayTotal,
        SUM(CASE WHEN origin in(1,2,3) THEN amount ELSE 0 END) AS payoutTotal,
        SUM(CASE WHEN origin = 6 THEN amount ELSE 0 END) AS withdrawTotal
        from business_balance_flow
        <where>
            <if test="businessIds != null and businessIds.size() != 0">
                and business_id in
                <foreach collection="businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by business_id
    </select>

    <select id="getBusinessBalanceDetailFlowExports" resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailFlowExportVO">
        SELECT
            bbf.order_time AS createTime,
            b.member_code,
            bbd.number,
            bbd.origin_number,
            bbd.video_code originCode,
            bbd.order_time,
            bbd.pay_type,
            bbd.number_type,
            bbf.origin,
            bbdf.number useOrderNum,
            bbdf.video_code useVideoCode,
            bbdf.type detailFlowType,
            bbdf.use_balance useBalance

        FROM
            business_balance_detail bbd
                INNER JOIN business_balance_detail_flow bbdf ON bbd.number = bbdf.balance_number
                INNER JOIN business b ON b.id = bbd.business_id
                LEFT JOIN business_balance_flow bbf ON bbf.id = bbdf.balance_flow_id
        <where>
            <if test="orderTimeBegin != null and orderTimeEnd != null">
                bbf.order_time between #{orderTimeBegin} AND #{orderTimeEnd}
            </if>
        </where>
    </select>
</mapper>
