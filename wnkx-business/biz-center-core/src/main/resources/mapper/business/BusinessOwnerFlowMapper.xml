<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessOwnerFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessOwnerFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="originAccountId" column="origin_account_id" jdbcType="BIGINT"/>
            <result property="originAccountNickName" column="origin_account_nick_name" jdbcType="VARCHAR"/>
            <result property="originAccountName" column="origin_account_name" jdbcType="VARCHAR"/>
            <result property="accountId" column="account_id" jdbcType="BIGINT"/>
            <result property="accountNickName" column="account_nick_name" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_id,origin_account_id,
        origin_account_nick_name,origin_account_name,account_id,
        account_nick_name,account_name,create_by_id,
        create_by,create_time,update_time
    </sql>

    <select id="getListByBusinessId" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessOwnerFlowVO">
        select b.name businessName,
               b.member_code,
               bof.id,
               bof.business_id,
               bof.origin_account_id,
               bof.origin_account_nick_name,
               bof.origin_account_name,
               bof.account_id,
               bof.account_nick_name,
               bof.account_name,
               bof.create_by_id,
               bof.create_by,
               bof.create_time,
               bof.update_time
        from business_owner_flow bof
                 left join business b on b.id = bof.business_id
        where bof.business_id = #{businessId}
    </select>
</mapper>
