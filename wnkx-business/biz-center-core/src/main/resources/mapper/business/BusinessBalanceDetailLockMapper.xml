<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalanceDetailLockMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailLock">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="number" column="number" jdbcType="VARCHAR"/>
            <result property="balanceDetailId" column="balance_detail_id" jdbcType="BIGINT"/>
            <result property="useBalance" column="use_balance" jdbcType="DECIMAL"/>
            <result property="payOutAmount" column="pay_out_amount" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,number,balance_detail_id,
        use_balance,pay_out_amount,status,
        create_time,update_time
    </sql>
<!--    获取视频订单提现记录-->
    <select id="withdrawDepositRecord"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO">
        select
            bbdl.prepay_num,
            bbdl.origin,
            bbdl.video_code,
            bbdl.status,
            bbdl.pay_out_amount,
            bbdl.create_time,
            bbaf.audit_time
        from business_balance_detail_lock bbdl
            join business_balance_audit_flow bbaf on bbaf.withdraw_number = bbdl.number
        <where>
            <if test="dto.status != null and dto.status.size() > 0 ">
                AND bbdl.status IN
                <foreach collection="dto.status" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.videoCodes != null and dto.videoCodes.size() > 0 ">
                AND bbdl.video_code IN
                <foreach collection="dto.videoCodes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.prepayNums != null and dto.prepayNums.size() > 0 ">
                AND bbdl.prepay_num IN
                <foreach collection="dto.prepayNums" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.videoCodePrepayNums != null and dto.videoCodePrepayNums.size() > 0 ">
                AND (
                bbdl.prepay_num IN
                <foreach collection="dto.videoCodePrepayNums" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                    or
                bbdl.video_code IN
                <foreach collection="dto.videoCodePrepayNums" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                )
            </if>

            AND bbdl.origin != ${@com.ruoyi.common.core.enums.BalanceSourceTypeEnum@PREPAY_INCOME.getCode}

            <if test="dto.businessId != null ">
                AND bbaf.business_id = #{dto.businessId}
            </if>

            <if test="dto.businessIds != null and dto.businessIds.size() > 0 ">
                AND bbaf.business_id IN
                <foreach collection="dto.businessIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryValidList"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO">
        select bbdl.id,
               bbdl.number,
               bbdl.video_code,
               bbdl.prepay_num,
               bbdl.balance_number,
               bbdl.balance_detail_id,
               bbdl.use_balance,
               bbdl.pay_out_amount,
               bbdl.status,
               bbdl.origin,
               bbdl.balance_create_time,
               bbdl.create_order_user_name,
               bbdl.create_order_user_nick_name,
               bbaf.audit_time,
               bbaf.remark auditRemark,
               bbaf.withdraw_way,
               bbaf.amount,
               bbaf.real_amount,
               bbd.origin_number,
               bbd.number_type,
               b.id businessId,
               b.name businessName,
               b.member_code merchantCode
        from 	business_balance_audit_flow bbaf
                INNER JOIN business_balance_detail_lock bbdl ON bbdl.number = bbaf.withdraw_number
                inner join business_balance_detail bbd on bbd.id = bbdl.balance_detail_id
                left join business b on b.id = bbaf.business_id
        <where>
            bbaf.audit_status = ${@<EMAIL>}
            <if test="dto.auditStartTime != null and dto.auditEndTime != null">
                and bbaf.audit_time between #{dto.auditStartTime} and #{dto.auditEndTime}
            </if>
            <if test="dto.videoCodes != null and dto.videoCodes.size() > 0">
                    and bbdl.video_code in
            <foreach collection="dto.videoCodes" item="item" open="(" separator="," close=")">#{item}</foreach>
            </if>
        </where>
    </select>
</mapper>
