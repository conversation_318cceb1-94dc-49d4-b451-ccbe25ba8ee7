<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalancePrepayMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="amount" column="amount" jdbcType="DECIMAL"/>
            <result property="realAmount" column="real_amount" jdbcType="DECIMAL"/>
            <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
            <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="BOOLEAN"/>
            <result property="auditUserId" column="audit_user_id" jdbcType="BIGINT"/>
            <result property="auditUserName" column="audit_user_name" jdbcType="VARCHAR"/>
            <result property="rejectCause" column="reject_cause" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="creatTime" column="creat_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_id,amount,
        real_amount,pay_time,resource_id,
        audit_status,audit_status,audit_user_id,audit_user_name,
        reject_cause,remark,create_by_id,
        create_by,creat_time
    </sql>

    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO">
        select
            bbp.id,bbp.business_id,bbp.account_id,bbp.pay_account,bbp.prepay_num,bbp.amount,bbp.contain_presented_amount,bbp.real_amount,
            bbp.pay_num,bbp.order_type,bbp.status,bbp.pay_user_id,bbp.alipay_pay_app_id,bbp.wechat_pay_app_id,bbp.submit_credential_time,bbp.close_order_time,
            bbp.pay_amount,bbp.real_pay_amount,bbp.real_pay_amount_currency,bbp.currency,bbp.current_exchange_rate,
            bbp.pay_type,bbp.pay_type_detail,bbp.pay_time,bbp.audit_time,bbp.audit_user_id,bbp.audit_user_name,bbp.apply_remark,bbp.pay_time,
            bbp.resource_id,bbp.audit_status,bbp.audit_time,bbp.audit_user_id,bbp.audit_user_name,bbp.reject_cause,bbp.remark,
            bbp.create_by_id,bbp.create_by,bbp.creat_time,
            b.owner_account,b.name businessName,b.is_proxy,b.create_time businessRegisterTime, bu.nick_name,b.member_code
        from business_balance_prepay bbp
        inner join business b on b.id = bbp.business_id
        left join business_account ba on ba.account = b.owner_account
        left join biz_user bu on bu.id = ba.biz_user_id
        <where>
            bbp.submit_credential_time is not null
            <if test="keyword != null and keyword != ''">
                and (
                bbp.prepay_num like concat('%', #{keyword}, '%')
                or bu.nick_name like concat('%', #{keyword}, '%')
                or b.name like concat('%', #{keyword}, '%')
                or b.member_code like concat('%', #{keyword}, '%')
                or bbp.create_by like concat('%', #{keyword}, '%')
                )
            </if>
            <if test="auditStatus != null">
                and bbp.audit_status = #{auditStatus}
            </if>

            <if test="orderType != null">
                and bbp.order_type = #{orderType}
            </if>

            <if test="prepayNums != null and prepayNums.size() > 0 ">
                AND bbp.prepay_num IN
                <foreach item="item" index="index" collection="prepayNums" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="payTypeDetails != null and payTypeDetails.size() > 0 ">
                AND bbp.pay_type_detail IN
                <foreach item="item" index="index" collection="payTypeDetails" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>
    <select id="innerQueryList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayVO">
        select
        bbp.id,bbp.business_id,bbp.account_id,bbp.pay_account,bbp.prepay_num,bbp.amount,bbp.contain_presented_amount,bbp.real_amount,bbp.pay_amount,bbp.real_pay_amount,bbp.real_pay_amount_currency,bbp.currency,bbp.current_exchange_rate,
        bbp.pay_type,bbp.pay_type_detail,bbp.pay_time,bbp.audit_time,bbp.audit_user_id,bbp.audit_user_name,bbp.apply_remark,bbp.pay_time,bbp.resource_id,bbp.audit_status,bbp.audit_time,bbp.audit_user_id,bbp.audit_user_name,bbp.reject_cause,bbp.remark,bbp.create_by_id,bbp.create_by,bbp.creat_time,
               bbp.pay_num,bbp.order_type,bbp.status,bbp.pay_user_id,bbp.alipay_pay_app_id,bbp.wechat_pay_app_id,bbp.submit_credential_time,bbp.close_order_time,
               b.owner_account,b.name businessName,b.is_proxy,b.create_time businessRegisterTime, bu.nick_name,b.member_code
        from business_balance_prepay bbp
        inner join business b on b.id = bbp.business_id
        left join business_account ba on ba.account = b.owner_account
        left join biz_user bu on bu.id = ba.biz_user_id
        <where>
            <if test="keyword != null and keyword != ''">
                and (
                bbp.prepay_num like concat('%', #{keyword}, '%')
                or bu.nick_name like concat('%', #{keyword}, '%')
                or b.name like concat('%', #{keyword}, '%')
                or b.member_code like concat('%', #{keyword}, '%')
                or bbp.create_by like concat('%', #{keyword}, '%')
                )
            </if>
            <if test="auditStatus != null">
                and bbp.audit_status = #{auditStatus}
            </if>

            <if test="orderType != null">
                and bbp.order_type = #{orderType}
            </if>

            <if test="prepayNums != null and prepayNums.size() > 0 ">
                AND bbp.prepay_num IN
                <foreach item="item" index="index" collection="prepayNums" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <select id="getStatistics" resultType = "com.ruoyi.system.api.domain.vo.biz.business.BusinessBalancePrepayStatisticsVO">
        SELECT
            sum(CASE WHEN audit_status = 0 THEN 1 ELSE 0 END) preApproveNum,
            sum(CASE WHEN audit_status = 1 THEN 1 ELSE 0 END) approveNum,
            sum(CASE WHEN audit_status = 2 THEN 1 ELSE 0 END) cancelNum
        FROM
            business_balance_prepay
        where submit_credential_time is not null
    </select>
</mapper>
