<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessMemberValidityFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberValidityFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="originMemberValidity" column="origin_member_validity" jdbcType="TIMESTAMP"/>
            <result property="resultMemberValidity" column="result_member_validity" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,origin_member_validity,result_member_validity,
        remark,create_by_id,create_by,
        create_time
    </sql>
    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessMemberValidityFlowVO">
        select
        bmvf.id,bmvf.business_id,bmvf.order_num,bmvf.origin_member_validity,bmvf.result_member_validity,bmvf.presented_time,bmvf.presented_time_type,bmvf.type,bmvf.member_package_type,bmvf.change_reason_type,bmvf.remark,bmvf.create_by_id,bmvf.create_by,bmvf.create_time
        ,bmvf.currency,bmvf.real_pay_amount,bmvf.real_pay_amount_currency,bmvf.order_num
        ,b.member_code,b.member_status
        from business_member_validity_flow bmvf
        left join business b on b.id = bmvf.business_id
        <where>
            <if test="businessId != null">
                and bmvf.business_id = #{businessId}
            </if>
            <if test="changeReasonType != null">
                and bmvf.change_reason_type = #{changeReasonType}
            </if>
            <if test="type != null">
                and bmvf.type = #{type}
            </if>
            <if test="businessIds != null and businessIds.size() != 0 ">
                and bmvf.business_id in
                <foreach collection="businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>
</mapper>
