<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessMemberActivityMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="memberPackageType" column="member_package_type" jdbcType="BOOLEAN"/>
            <result property="presentedTime" column="presented_time" jdbcType="TINYINT"/>
            <result property="startTime" column="start_time" jdbcType="TINYINT"/>
            <result property="endTime" column="end_time" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,member_package_type,presented_time,
        start_time,end_time,status,
        create_by_id,create_by,create_time,
        update_by_id,update_by,update_time
    </sql>
</mapper>
