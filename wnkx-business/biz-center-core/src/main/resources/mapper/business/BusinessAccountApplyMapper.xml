<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessAccountApplyMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccountApply">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ownerAccount" column="owner_account" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
            <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
            <result property="externalUserId" column="external_user_id" jdbcType="VARCHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="BOOLEAN"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,owner_account,name,
        nick_name,pic,unionid,
        external_user_id,audit_status,audit_time,
        create_time
    </sql>

    <select id="list" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountApplyVO">
        select
        <include refid="Base_Column_List"/>
        from business_account_apply
        <where>
            <if test="ownerAccount != null and ownerAccount != ''"> and owner_account = #{ownerAccount}</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="nickName != null and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="auditStatus != null"> and audit_status =#{auditStatus}</if>
            <if test="unionid != null and unionid !=''"> and unionid =#{unionid}</if>
            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and audit_status in
                <foreach item="item" index="index" collection="auditStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="businessAccountApplyNum" resultType="Integer">
        select count(1) from business_account_apply
        <where>
            <if test="ownerAccount != null and ownerAccount != ''"> and owner_account = #{ownerAccount}</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="nickName != null and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="auditStatus != null"> and audit_status =#{auditStatus}</if>
            <if test="auditStatusList != null and auditStatusList.size() != 0">
                and audit_status in
                <foreach item="item" index="index" collection="auditStatusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSubAccountList"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessSubAccountListVo">
        SELECT
            barl.id,
            barl.name,
            barl.nick_name,
            barl.phone,
            barl.connect_user_name,
            barl.create_time,
            barl.audit_time,
            barl.status,
            barl.audit_status
        FROM business_account_rebind_log barl
        WHERE barl.business_id = #{businessId}
    </select>
    <select id="selectApplyAndRebindLog"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountRebindLogVo">
        SELECT
            baa.biz_user_id,
            baa.business_id,
            baa.create_time,
            baa.audit_time,
            baa.audit_status,
            CASE
                WHEN baa.audit_status = 1 THEN ba.status
                ELSE NULL
                END AS status,
            CASE
                WHEN baa.audit_status = 1 THEN ba.account
                ELSE NULL
                END AS account,
            baa.name,
            baa.nick_name,
            bu.phone,
            bu.connect_user_name
        FROM business_account_apply baa
                 LEFT JOIN business_account ba ON baa.biz_user_id = ba.biz_user_id
                 INNER JOIN biz_user bu ON bu.id = baa.biz_user_id
        WHERE baa.audit_status = 0
        UNION ALL
        SELECT
            ba.biz_user_id,
            ba.business_id,
            ba.create_time,
            ba.create_time AS audit_time,
            1 AS audit_status,
            ba.status,
            ba.account,
            bu.name,
            bu.nick_name,
            bu.phone,
            bu.connect_user_name
        FROM business_account ba
                 INNER JOIN biz_user bu ON ba.biz_user_id = bu.id
        WHERE ba.biz_user_id IS NOT NULL
          AND ba.biz_user_id NOT IN (
            SELECT baa.biz_user_id
            FROM business_account_apply baa
            WHERE baa.audit_status = 0
        ) AND ba.is_owner_account = 0
        ORDER BY create_time DESC
    </select>
</mapper>
