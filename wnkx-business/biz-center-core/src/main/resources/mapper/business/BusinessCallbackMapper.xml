<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessCallbackMapper">

    <!--    商家回访-回访列表-->
    <select id="selectCallbackListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackListVO">
        SELECT
            bc.id,
            bc.business_id,
            bc.`status`,
            b.`name`,
            b.member_code,
            b.scale,
            b.waiter_id,
            b.member_first_time,
            b.member_validity,
            bc.mark_time,
            bc.write_time
        FROM
            business_callback bc
                JOIN ( SELECT callback_id, MAX( callback_time ) AS callback_time FROM business_callback_event GROUP BY callback_id ) bce ON bce.callback_id = bc.id
                LEFT JOIN business b ON b.id = bc.business_id
        <where>
            <if test="dto.id != null ">
                AND bc.id = #{dto.id}
            </if>

            <if test="dto.status != null ">
                AND bc.status = #{dto.status}
            </if>

            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                    b.name LIKE CONCAT('%', #{dto.keyword}, '%')
                    OR b.member_code LIKE CONCAT('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.event != null and dto.event.size() > 0 ">
                AND bc.id IN (
                SELECT
                    callback_id
                FROM
                    business_callback_event
                WHERE
                    callback_event IN
                    <foreach collection="dto.event" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>
        </where>
    </select>

    <!--    商家回访-回访状态数统计-->
    <select id="returnVisitStatusCount"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackStatusCountVO">
        SELECT
            COUNT(CASE WHEN bc.status = ${@com.ruoyi.common.core.enums.BusinessCallbackStatusEnum@WAIT_FOR_RETURN_VISIT.getCode} THEN 1 END) AS waitForReturnVisitCount,
            COUNT(CASE WHEN bc.status = ${@com.ruoyi.common.core.enums.BusinessCallbackStatusEnum@IN_THE_RETURN_VISIT.getCode} THEN 1 END) AS inTheReturnVisitCount,
            COUNT(CASE WHEN bc.status = ${@com.ruoyi.common.core.enums.BusinessCallbackStatusEnum@ALREADY_VISITED.getCode} THEN 1 END) AS alreadyVisitedCount
        FROM
            business_callback bc
                JOIN (SELECT DISTINCT callback_id FROM business_callback_event) bce ON bc.id = bce.callback_id
    </select>
</mapper>
