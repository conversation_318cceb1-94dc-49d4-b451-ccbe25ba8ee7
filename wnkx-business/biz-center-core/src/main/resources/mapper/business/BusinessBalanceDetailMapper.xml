<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalanceDetailMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="number" column="number" jdbcType="VARCHAR"/>
        <result property="videoCode" column="video_code" jdbcType="VARCHAR"/>
        <result property="numberType" column="number_type" jdbcType="BOOLEAN"/>
        <result property="origin" column="origin" jdbcType="BOOLEAN"/>
        <result property="balance" column="balance" jdbcType="DECIMAL"/>
        <result property="useBalance" column="use_balance" jdbcType="DECIMAL"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="creat_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getValidBusinessBalanceDetailList"
            resultType="com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetail">
        select bbd.id,
               bbd.business_id,
               bbd.number,
               bbd.origin_number,
               bbd.video_code,
               bbd.number_type,
               bbd.origin,
               bbd.balance,
               bbd.use_balance,
               bbd.lock_balance,
               bbd.valid_balance,
               bbd.create_order_user_name,
               bbd.create_order_user_nick_name,
               bbd.create_by_id,
               bbd.create_by,
               bbd.create_time,
               bbd.update_time
        from business_balance_detail bbd
        where bbd.business_id = #{businessId}
          and valid_balance &gt; 0
    </select>
    <select id="statistics"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailStatisticsVO">
        select
            sum(bbd.balance) balanceTotal,
            sum(bbd.use_balance) useBalanceTotal
        from business_balance_detail bbd
        where bbd.business_id = #{businessId}
    </select>

</mapper>
