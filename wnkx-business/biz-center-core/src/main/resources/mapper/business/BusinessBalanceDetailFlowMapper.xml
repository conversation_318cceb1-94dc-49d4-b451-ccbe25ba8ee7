<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalanceDetailFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.balance.BusinessBalanceDetailFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="balanceFlowId" column="balance_flow_id" jdbcType="BIGINT"/>
            <result property="balanceNumber" column="balance_number" jdbcType="VARCHAR"/>
            <result property="useBalance" column="use_balance" jdbcType="DECIMAL"/>
            <result property="type" column="type" jdbcType="BOOLEAN"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="creat_time" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>
