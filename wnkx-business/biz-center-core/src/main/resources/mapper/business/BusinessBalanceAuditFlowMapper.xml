<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessBalanceAuditFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="realAmount" column="real_amount" jdbcType="DECIMAL"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="resourceUrl" column="resource_url" jdbcType="BIGINT"/>
        <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
        <result property="auditUserId" column="audit_user_id" jdbcType="BIGINT"/>
        <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="withdrawNumber" column="withdraw_number" jdbcType="VARCHAR"/>
        <result property="applyRemark" column="apply_remark" jdbcType="VARCHAR"/>
        <result property="withdrawWay" column="withdraw_number" jdbcType="INTEGER"/>
        <result property="notifyStatus" column="notify_status" jdbcType="INTEGER"/>
        <result property="notifyTime" column="notify_time" jdbcType="TIMESTAMP"/>


    </resultMap>

    <sql id="Base_Column_List">
        id
        ,business_id,amount,
        real_amount,pay_time,resource_url,
        audit_status,audit_user_id,audit_time,
        remark,create_user_id,create_time,withdraw_number,apply_remark,withdraw_way,notify_status,notify_time
    </sql>
    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowVO">
        select baf.id
        ,business_id,amount,
        real_amount,pay_time,resource_url,
        audit_status,audit_user_id,audit_time,
        baf.remark,baf.create_user_id,baf.create_time,withdraw_number,apply_remark,withdraw_way,notify_status,notify_time,b.waiter_id business_waiter_id
        from business_balance_audit_flow baf
            left join business b on b.id = baf.business_id
        <if test="dto.searchNameMemberCodeAccount != null and dto.searchNameMemberCodeAccount != ''">
            left join business_balance_detail_lock badl on badl.number = baf.withdraw_number
        </if>
        <where>
            <if test="dto.auditStatus != null and dto.auditStatus.size() != 0">and audit_status in
                <foreach collection="dto.auditStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.noticeStatus != null">and notify_status = #{dto.noticeStatus}</if>
            <if test="!currentUserIsAdmin and bizUserId !=null">
                and (
                business_id in (select id from business b WHERE waiter_id = #{bizUserId})
                or baf.create_user_id = #{bizUserId}
                or baf.audit_user_id = #{bizUserId}
                )
            </if>
            <if test="currentUserIsAdmin != null and bizUserId !=null">
                and audit_status = 1
            </if>
            <if test="dto.startTimeBegin != null and dto.startTimeEnd != null">
                and baf.create_time BETWEEN #{dto.startTimeBegin} AND #{dto.startTimeEnd}
            </if>
            <if test="dto.auditTimeBegin != null and dto.auditTimeEnd != null">
                and baf.audit_time BETWEEN #{dto.auditTimeBegin} AND #{dto.auditTimeEnd}
            </if>
            <if test="dto.searchNameMemberCodeAccount != null and dto.searchNameMemberCodeAccount != ''">
                and (
                baf.withdraw_number like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                or badl.video_code like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                or (badl.balance_number like concat('%', #{dto.searchNameMemberCodeAccount}, '%') and badl.video_code = '')
                <if test="dto.businessIds != null and dto.businessIds.size() != 0 ">
                    or business_id in
                    <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.createUserIds != null and dto.createUserIds.size() != 0 ">
                    or
                    baf.create_user_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    baf.audit_user_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    b.waiter_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
        </where>
        group by
                baf.id,
                business_id,amount,
                real_amount,
                pay_time,
                resource_url,
                audit_status,
                audit_user_id,
                audit_time,
                baf.remark,
                 baf.create_user_id,
                 baf.create_time,
                 withdraw_number,
                 apply_remark,
                 withdraw_way,
                 notify_status,
                 notify_time,
                 b.waiter_id
        order by baf.create_time desc
    </select>

    <select id="queryExportList" resultType="com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceAuditFlowDetailExportVO">
        select
        baf.withdraw_number,
        badl.balance_number,
        badl.video_code,
        badl.use_balance,
        badl.pay_out_amount,
        badl.pay_out_amount,
        baf.id,
        baf.business_id,
        baf.amount,
        baf.real_amount,
        baf.audit_status,
        baf.audit_user_id,
        baf.audit_time,
        baf.remark,
        baf.create_user_id,
        baf.create_time,
        baf.apply_remark,
        baf.withdraw_way,
        baf.notify_status,
        baf.notify_time,
        b.waiter_id business_waiter_id
        from business_balance_audit_flow baf
                 left join business b on b.id = baf.business_id
                 left join business_balance_detail_lock badl on badl.number = baf.withdraw_number
        <where>
            <if test="dto.auditStatus != null and dto.auditStatus.size() != 0">and audit_status in
                <foreach collection="dto.auditStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.noticeStatus != null">and notify_status = #{dto.noticeStatus}</if>
            <if test="dto.startTimeBegin != null and dto.startTimeEnd != null">
                and baf.create_time BETWEEN #{dto.startTimeBegin} AND #{dto.startTimeEnd}
            </if>
            <if test="dto.auditTimeBegin != null and dto.auditTimeEnd != null">
                and baf.audit_time BETWEEN #{dto.auditTimeBegin} AND #{dto.auditTimeEnd}
            </if>
            <if test="dto.searchNameMemberCodeAccount != null and dto.searchNameMemberCodeAccount != ''">
                and (
                baf.withdraw_number like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                or badl.video_code like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                or (badl.balance_number like concat('%', #{dto.searchNameMemberCodeAccount}, '%') and badl.video_code = '')
                <if test="dto.businessIds != null and dto.businessIds.size() != 0 ">
                    or business_id in
                    <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.createUserIds != null and dto.createUserIds.size() != 0 ">
                    or
                    baf.create_user_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    baf.audit_user_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or
                    b.waiter_id in
                    <foreach collection="dto.createUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
        </where>

    </select>

    <select id="queryValidList" resultType="com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow">
        select
        <include refid="Base_Column_List"/>
        from business_balance_audit_flow
        <where>
            <if test="auditStatus != null">and audit_status = #{auditStatus}</if>
            <if test="businessIds != null and businessIds.size() != 0 ">
                and business_id in
                <foreach collection="businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getStatistics" resultType = "com.ruoyi.system.api.domain.vo.biz.business.BusinessBalanceAuditFlowStatisticsVO">
        SELECT
            sum(CASE WHEN audit_status = 0 THEN 1 ELSE 0 END) preApproveNum,
            sum(CASE WHEN audit_status = 1 THEN 1 ELSE 0 END) approveNum,
            sum(CASE WHEN audit_status = 2 THEN 1 ELSE 0 END) cancelNum
        FROM
            business_balance_audit_flow
    </select>

    <select id="getBusinessLockAmount" resultType="java.math.BigDecimal">
        select
            sum(amount)
        from business_balance_audit_flow
        where audit_status = ${@com.ruoyi.common.core.enums.BusinessBalanceAuditStatusEnum@PRE_APPROVE.getcode}
        and business_id = #{businessId}
    </select>

    <select id="getPayoutAmountStatistics" resultType = "com.ruoyi.system.api.domain.vo.biz.business.PayoutAmountStatisticsVO">
        select
        SUM(CASE WHEN audit_status = 0 THEN amount ELSE 0 END) AS preApproveAmount,
        SUM(CASE WHEN audit_status = 1 THEN amount ELSE 0 END) AS approveAmount,
        SUM(CASE WHEN audit_status = 2 THEN amount ELSE 0 END) AS cancelAmount
        from business_balance_audit_flow
        <where>
            <if test="businessId != null">
                and business_id = #{businessId}
            </if>
        </where>
    </select>

</mapper>
