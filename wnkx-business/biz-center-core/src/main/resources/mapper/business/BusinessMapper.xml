<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.Business">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="ownerAccount" column="owner_account" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="isProxy" column="is_proxy" jdbcType="INTEGER"/>
            <result property="phoneVisible" column="phone_visible" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="customerType" column="customer_type" jdbcType="INTEGER"/>
            <result property="balance" column="balance" jdbcType="DECIMAL"/>
            <result property="isBalanceLock" column="is_balance_lock" jdbcType="INTEGER"/>
            <result property="waiterId" column="waiter_id" jdbcType="BIGINT"/>
            <result property="invoiceTitleType" column="invoice_title_type" jdbcType="INTEGER"/>
            <result property="invoiceTitle" column="invoice_title" jdbcType="VARCHAR"/>
            <result property="invoiceDutyParagraph" column="invoice_duty_paragraph" jdbcType="VARCHAR"/>
            <result property="invoiceContent" column="invoice_content" jdbcType="VARCHAR"/>
            <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
            <result property="memberType" column="member_type" jdbcType="INTEGER"/>
            <result property="memberStatus" column="member_status" jdbcType="INTEGER"/>
            <result property="memberPackageType" column="member_package_type" jdbcType="INTEGER"/>
            <result property="memberFirstTime" column="member_first_time" jdbcType="TIMESTAMP"/>
            <result property="memberFirstType" column="member_first_type" jdbcType="INTEGER"/>
            <result property="memberLastTime" column="member_last_time" jdbcType="TIMESTAMP"/>
            <result property="memberValidity" column="member_validity" jdbcType="TIMESTAMP"/>
            <result property="createUserId" column="create_user_id" jdbcType="BIGINT"/>
            <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="update_user_id" jdbcType="BIGINT"/>
            <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="businessWechatUrl" column="business_wechat_url" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, owner_account, name, is_proxy, phone_visible, status, customer_type, balance, waiter_id, invoice_title_type, invoice_title, invoice_duty_paragraph,
        invoice_content, member_code, member_type, member_status, member_package_type, member_first_time, member_last_time, member_validity,
        is_exist_recent_order, create_user_id, create_user_name, create_time, update_user_id, update_user_name, update_time, business_wechat_url
    </sql>

    <select id="businessStatistics" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessStatisticsVO">
        SELECT
            count(owner_account) as 'businessTotal',
            count(member_status = 3 or null) as 'expireMemberTotal',
            count(member_type = 1 or null) as 'memberTotal'
        FROM `business`;
    </select>

    <select id="businessBalanceTotal" resultType = "java.math.BigDecimal">
        select sum(balance)
        from business
    </select>


    <select id="getBusinessForUpdate" resultType="com.ruoyi.system.api.domain.entity.biz.business.Business">
        select * from business where id = #{id}
        for update
    </select>
    <select id="businessListForUpdate" resultType="com.ruoyi.system.api.domain.entity.biz.business.Business">
        select * from business where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        for update
    </select>

    <select id="queryResidentBusiness" resultType="com.ruoyi.system.api.domain.vo.biz.business.ResidentBusinessVO">
        select
        b.id,
        b.owner_account,
        b.name,
        bu.create_time,
        b.is_proxy,
        b.balance,
        b.use_balance,
        b.waiter_id,
        b.member_code,
        b.member_type,
        b.member_status,
        b.member_package_type,
        b.member_first_time,
        b.member_first_type,
        b.member_last_time,
        b.member_validity
        from business b
        left join business_account ba on b.owner_account = ba.account
        left join biz_user bu on bu.id = ba.biz_user_id
        <where>
            b.member_code != ''
            <if test="searchName != null and searchName != ''">
                and (
                b.owner_account like concat('%', #{searchName}, '%')
                or b.name like concat('%', #{searchName}, '%')
                or b.member_code like concat('%', #{searchName}, '%')
                )
            </if>
            <if test="businessCreateBegin != null and businessCreateEnd != null">
                and b.create_time BETWEEN #{businessCreateBegin} AND #{businessCreateEnd}
            </if>
            <if test="memberValidityBegin != null and memberValidityEnd != null">
                and b.member_validity BETWEEN #{memberValidityBegin} AND #{memberValidityEnd}
            </if>
            <if test="waiterId != null">and b.waiter_id = #{waiterId}</if>
            <if test="memberStatus != null">and b.member_status = #{memberStatus}</if>
        </where>
    </select>

    <select id="queryBusinessList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessDetailVO">
        SELECT b.*,buc.register_channel_type,buc.register_channel_id,dc.channel_name,bu.connect_user_name
        <if test="orderCountMap != null and orderByType != null">
            <!-- 动态注入 waits 数据 -->
            ,CASE
            <foreach collection="orderCountMap" item="orderCount" index="businessId" separator=" " >
                WHEN b.id = ${businessId} THEN ${orderCount}
            </foreach>
            ELSE 0
            END AS order_num
        </if>
        FROM business b
        left join business_account ba on ba.account = b.owner_account
        left  join biz_user bu on bu.id = ba.biz_user_id
        left join biz_user_channel buc on buc.biz_user_id = ba.biz_user_id
        left join distribution_channel dc on dc.seed_code =b.seed_code
        <where>
            <if test="isProxy != null">AND b.is_proxy = #{isProxy}</if>
            <if test="connectUserName != null and connectUserName != ''">AND bu.connect_user_name = #{connectUserName}</if>
            <if test="waiterId != null">AND b.waiter_id = #{waiterId}</if>
            <if test="memberType != null">AND b.member_type = #{memberType}</if>
            <if test="status != null">AND b.status = #{status}</if>
            <if test="customerType != null">AND b.customer_type = #{customerType}</if>
            <if test="businessIdentifier != null">AND b.business_identifier = #{businessIdentifier}</if>
            <if test="memberStatus != null">AND b.member_status = #{memberStatus}</if>
            <if test="memberCode != null">AND b.member_code LIKE CONCAT('%',#{memberCode},'%')</if>
            <if test="isExistRecentOrder != null">AND b.is_exist_recent_order = #{isExistRecentOrder}</if>
            <if test="searchName != null">
                AND (
                b.member_code LIKE CONCAT('%',#{searchName},'%')
                OR b.name LIKE CONCAT('%',#{searchName},'%')
                )
            </if>
            <if test="businessCreateBegin != null and businessCreateEnd != null">
                AND b.create_time BETWEEN #{businessCreateBegin} AND #{businessCreateEnd}
            </if>
            <if test="memberValidityBegin != null and memberValidityEnd != null">
                AND b.member_validity BETWEEN #{memberValidityBegin} AND #{memberValidityEnd}
            </if>
            <choose>
                <when test="isAssignWaiter == 1">AND b.waiter_id IS NOT NULL</when>
                <when test="isAssignWaiter == 0">AND b.waiter_id IS NULL</when>
            </choose>
            <if test="ownerAccount != null">AND b.owner_account LIKE CONCAT('%',#{ownerAccount},'%')</if>
            <if test="name != null">AND b.name LIKE CONCAT('%',#{name},'%')</if>
            <if test="accounts != null and accounts.size() != 0">AND b.owner_account IN
                <foreach collection="accounts" item="account" open="(" separator="," close=")">
                    #{account}
                </foreach>
            </if>
            <if test="memberStatusList != null and memberStatusList.size() != 0">AND b.member_status IN
                <foreach collection="memberStatusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="businessIds != null and businessIds.size() != 0">AND b.id IN
                <foreach collection="businessIds" item="businessId" open="(" separator="," close=")">
                    #{businessId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBusinessMainAccountExternalUserId" resultType="java.lang.String">
        select external_user_id from business b
                                     inner join business_account ba on b.owner_account = ba.account
                                     INNER join (select id,external_user_id,account_type from biz_user ) bu on bu.id = ba.biz_user_id
                WHERE b.id =#{businessId} and bu.account_type =1
        LIMIT 1
    </select>
    <select id="selectBusinessOwnerUserContactUserName"
            resultType="com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO">
        SELECT b.member_code as memberCode, bu.id as bizUserId, bu.connect_user_name as contactUserName, wceu.connect_user_id as contactUserId
        from business b
                 LEFT JOIN business_account ba on b.owner_account = ba.account
                 LEFT JOIN biz_user bu on ba.biz_user_id = bu.id
                 LEFT JOIN we_chat_external_user wceu on wceu.external_userid  = bu.external_user_id
    </select>
    <select id="getBusinessOwnerUserContactUserNameByMemberCode"
            resultType="com.ruoyi.system.api.domain.dto.biz.business.BusinessOwnerAccountContactUserDTO">
        SELECT b.member_code as memberCode, bu.id as bizUserId, bu.connect_user_name as contactUserName, wceu.connect_user_id as contactUserId
        from business b
                 LEFT JOIN business_account ba on b.owner_account = ba.account
                 LEFT JOIN biz_user bu on ba.biz_user_id = bu.id
                 LEFT JOIN we_chat_external_user wceu on wceu.external_userid  = bu.external_user_id
        where b.member_code = #{memberCode}
    </select>


</mapper>
