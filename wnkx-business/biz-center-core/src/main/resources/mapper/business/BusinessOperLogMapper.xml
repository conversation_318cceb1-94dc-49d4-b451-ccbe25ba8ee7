<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessOperLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessOperLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="originalBusiness" column="original_business" jdbcType="OTHER"/>
            <result property="resultBusiness" column="result_business" jdbcType="OTHER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,user_name,
        nick_name,original_business,result_business,
        create_time
    </sql>
</mapper>
