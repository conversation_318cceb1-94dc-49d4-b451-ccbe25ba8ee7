<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessCallbackRecordMapper">


    <select id="returnVisitRecord"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessCallbackRecordListVO">
        SELECT
            bcr.id,
            bcr.callback_id,
            bcr.account_is_owner_account,
            bcr.account_name,
            bcr.account_nick_name,
            bcr.feedback_type,
            bcr.callback_content,
            bcr.resource_id,
            bcr.write_time,
            bcr.write_by
        FROM
            business_callback_record bcr
                LEFT JOIN business_callback bc ON bc.id = bcr.callback_id
        <where>
            bcr.business_id = #{dto.businessId}

            <if test="dto.event != null and dto.event.size() > 0 ">
                AND bc.id IN (
                SELECT
                    callback_id
                FROM
                    business_callback_event
                WHERE
                    callback_event IN
                    <foreach collection="dto.event" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                )
            </if>

            <if test="dto.accountName != null and dto.accountName.size() > 0 ">
                AND bcr.account_name IN
                <foreach collection="dto.accountName" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.feedbackType != null and dto.feedbackType.size() > 0">
                AND (
                <foreach item="item" index="index" collection="dto.feedbackType" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, bcr.feedback_type)
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>
