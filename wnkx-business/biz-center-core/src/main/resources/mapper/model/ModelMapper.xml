<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.Model" id="ModelResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="birthday" column="birthday"/>
        <result property="zipcode" column="zipcode"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="city" column="city"/>
        <result property="state" column="state"/>
        <result property="modelPic" column="model_pic"/>
        <result property="livePic" column="live_pic"/>
        <result property="status" column="status"/>
        <result property="platform" column="platform"/>
        <result property="type" column="type"/>
        <result property="cooperation" column="cooperation"/>
        <result property="nation" column="nation"/>
        <result property="recipient" column="recipient"/>
    </resultMap>

    <sql id="selectModelColumns">
        m.id, m.model_pic, m.is_family_model, m.is_initiator, m.family_id,
        m.model_family_relationship, m.join_family_time, m.name, m.sex,
        m.sex_dict, m.type, m.type_dict, m.age_group, m.age_group_dict,
        m.birthday, m.age, m.nation, m.nation_dict, m.recipient, m.state,
        m.city, m.zipcode, m.detail_address, m.platform, m.platform_dict,
        m.cooperation, m.acceptability, m.commission_unit, m.commission,
        m.status, m.status_time,m.status_explain, m.live_pic, m.amazon_video as amazon_video_id, m.tiktok_video as tiktok_video_id,
        m.top_time, m.sort, m.overtime_rate, m.after_sale_rate, m.blacklist_count,
        m.create_by, m.create_time, m.update_by, m.update_time,
        m.cancel_cooperation_type, m.cancel_cooperation_sub_type,
        m.phone, m.sync_msg, m.is_show, m.about,m.developer_id,m.have_snail_pic
    </sql>
    <sql id="modelColumns">
        m.id,ma.account,ma.login_account,ma.last_login_time, m.model_pic, m.is_family_model, m.is_initiator, m.family_id, m.model_family_relationship,m.sex, m.birthday,m.name, m.age, m.nation, m.type, m.cooperation,m.age_group,
        m.platform, m.STATUS,m.status_time,m.status_explain, m.create_by, m.create_time, m.top_time,m.sort,m.recipient,m.city,m.state,m.detail_address,m.zipcode,m.acceptability,
        m.commission_unit,m.commission,m.live_pic as live_pic_id,m.amazon_video as amazon_video_id ,m.tiktok_video as tiktok_video_id,m.overtime_rate,m.after_sale_rate,m.phone,
        m.cancel_cooperation_type, m.cancel_cooperation_sub_type,m.blacklist_count,m.is_show,m.about,m.cooperation_score,m.developer_id,m.have_snail_pic
    </sql>
    <sql id="modelCondition">
        <where>
            <if test="dto.keyword != null and dto.keyword != ''">
                and (
                <!-- m.name like concat('%', #{dto.keyword}, '%') -->
                <!-- or ma.account like concat('%', #{dto.keyword}, '%') -->
                <!-- <if test="dto.persons != null and dto.persons.size() != 0"> -->
                <!--     <choose> -->
                <!--         <when test="dto.includeFamily == 1 "> -->
                <!--             and -->
                <!--         </when> -->
                <!--         <otherwise> -->
                <!--             or -->
                <!--         </otherwise> -->
                <!--     </choose> -->
                <!--      m.id in ( -->
                <!--     SELECT model_id from model_person where user_id in -->
                <!--     <foreach item="item" index="index" collection="dto.persons" open="(" separator="," close=")"> -->
                <!--         #{item} -->
                <!--     </foreach> -->
                <!--     ) -->
                <!-- </if> -->
                <!-- or  -->
                    m.id IN (
                            SELECT
                                model_id
                            FROM
                                model_tag
                            WHERE
                                dict_id IN (
                                    SELECT t.id FROM tag t WHERE t.name LIKE concat('%', #{dto.keyword}, '%')
                                ) OR dict_name LIKE concat('%', #{dto.keyword}, '%')
                    )
                )
            </if>
            <if test="dto.persons != null and dto.persons.size() != 0 ">
                and ( mp.user_id in
                <foreach item="item" index="index" collection="dto.persons" open="(" separator="," close=")">
                    #{item}
                </foreach>
                    OR m.developer_id IN
                    <foreach item="item" index="index" collection="dto.persons" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
            </if>
            <if test="dto.familyId != null">
                and m.family_id = #{dto.familyId}
            </if>
            <if test="dto.filterModelId != null">
                and m.id &lt;&gt; #{dto.filterModelId}
            </if>
            <if test="dto.familyIds != null and dto.familyIds.size() > 0">
                and m.family_id in
                <foreach item="item" index="index" collection="dto.familyIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.familyModelIds != null and dto.familyModelIds.size() > 0">
                and m.family_id in
                (select DISTINCT family_id from model where id in
                <foreach item="item" index="index" collection="dto.familyModelIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="dto.includeFamilyIds != null and dto.includeFamilyIds.size() > 0 and dto.includeFamily == 1">
                or m.family_id in
                <foreach item="item" index="index" collection="dto.includeFamilyIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.isInitiator != null">
                and m.is_initiator = #{dto.isInitiator}
            </if>
            <if test="dto.isFamilyModel != null">
                and m.is_family_model = #{dto.isFamilyModel}
            </if>
            <if test="dto.id != null  and dto.id.size() != 0">
                and m.id in
                <foreach item="item" collection="dto.id" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.name != null  and dto.name != ''">
                and m.name like concat('%', #{dto.name}, '%')
            </if>

            <if test="dto.account != null  and dto.account != ''">
                and ma.account like concat('%', #{dto.account}, '%')
            </if>

            <if test="dto.platform != null  and dto.platform.size() != 0">
                and
                <foreach item="item" index="index" collection="dto.platform" open="(" separator=" OR " close=")">
                    FIND_IN_SET(#{item}, platform)
                </foreach>
            </if>

            <if test="dto.nation != null and dto.nation.size() != 0">
                and m.nation in
                <foreach item="item" index="index" collection="dto.nation" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.sex != null and dto.sex.size() != 0">
                and m.sex in
                <foreach item="item" index="index" collection="dto.sex" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.ageGroup != null and dto.ageGroup.size() != 0">
                and m.age_group in
                <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.specialtyCategory != null  and dto.specialtyCategory.size() != 0">
                and m.id in (
                SELECT model_id from model_tag where dict_id in
                <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.type != null  and dto.type.size() != 0">
                and m.type in
                <foreach item="item" index="index" collection="dto.type" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size() != 0">
                and m.status in
                <foreach item="item" index="index" collection="dto.status" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.cooperation != null and dto.cooperation.size() != 0 ">
                and m.cooperation in
                <foreach item="item" index="index" collection="dto.cooperation" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.isShow != null and dto.isShow.size() > 0">
                and m.is_show in
                <foreach item="item" index="index" collection="dto.isShow" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.createTimeBegin != null and dto.createTimeEnd != null ">
                AND m.create_time BETWEEN #{dto.createTimeBegin} AND #{dto.createTimeEnd}
            </if>

            <if test="dto.haveSnailPics != null and dto.haveSnailPics.size() > 0 ">
                AND m.have_snail_pic IN
                <foreach item="item" index="index" collection="dto.haveSnailPics" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>

    <select id="selectModelListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelVO">
        SELECT <include refid="modelColumns"/>
        , (CASE WHEN m.cooperation = 1 THEN 3
        WHEN m.cooperation = 2 THEN 2
        WHEN m.cooperation = 0 THEN 1 else 0 end) AS cooperationSort
        <if test="dto.waitsMap != null and dto.waitsSort != null and dto.waitsSort != ''">
            <!-- 动态注入 waits 数据 -->
            ,CASE
            <foreach collection="dto.waitsMap" item="waits" index="modelId" separator=" " >
                WHEN m.id = ${modelId} THEN ${waits}
            </foreach>
            ELSE 0
            END AS waits
        </if>
        <if test="dto.toBeConfirmMap != null and dto.toBeConfirmSort != null and dto.toBeConfirmSort != ''">
            <!-- 动态注入 toBeConfirm 数据 -->
            ,CASE
            <foreach collection="dto.toBeConfirmMap" item="toBeConfirm" index="modelId" separator=" " >
                WHEN m.id = ${modelId} THEN ${toBeConfirm}
            </foreach>
            ELSE 0
            END AS toBeConfirm
        </if>
        FROM
        model m
        LEFT JOIN model_account ma on ma.model_id = m.id
        LEFT JOIN model_person mp on m.id = mp.model_id
        <include refid="modelCondition"/>
        ORDER BY
        <if test="dto.waitsMap != null and dto.waitsSort != null and dto.waitsSort != ''">
            waits ${dto.waitsSort},
        </if>
        <if test="dto.toBeConfirmMap != null and dto.toBeConfirmSort != null and dto.toBeConfirmSort != ''">
            toBeConfirm ${dto.toBeConfirmSort},
        </if>
        m.status_sort ASC ,m.cooperation_sort DESC ,m.update_time DESC ,m.id ASC
    </select>

    <select id="selectModelFamilyListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelVO">
        SELECT <include refid="modelColumns"/>
        <if test="dto.waitsMap != null and dto.waitsSort != null and dto.waitsSort != ''">
            <!-- 动态注入 waits 数据 -->
            ,CASE
            <foreach collection="dto.waitsMap" item="waits" index="modelId" separator=" " >
                WHEN m.id = ${modelId} THEN ${waits}
            </foreach>
            ELSE 0
            END AS waits
        </if>
        FROM
        model m
        LEFT JOIN model_account ma on ma.model_id = m.id
        <include refid="modelCondition"/>
    </select>

    <select id="getFamilyCount" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelFamilyCountVO">
        select family_id,
               count(family_id) as count
        from model
        <where>
            is_family_model = ${@<EMAIL>}
        </where>
        group by family_id
    </select>

    <select id="selectModelFamilyByFamilyId" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelFamilyVO">
        select
            m.id,ma.account,m.model_pic, m.is_initiator, m.family_id, m.model_family_relationship,m.sex,m.name,  m.nation, m.type, m.cooperation, m.cooperation_score,m.age_group,
            m.platform, m.STATUS
        FROM model m
                 LEFT JOIN model_account ma on ma.model_id = m.id
        where m.family_id = #{familyId}
    </select>

    <select id="selectModelAndRelevanceById" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelVO">
        SELECT <include refid="modelColumns"/>
        FROM model m
            LEFT JOIN model_account ma on ma.model_id = m.id
        WHERE
            m.id = #{id}
    </select>

    <select id="queryBlacklistModel"  resultType="com.ruoyi.system.api.domain.entity.Model" >
        SELECT
        m.id,
        m.NAME
        FROM user_model_blacklist umb
        left join model m on m.id = umb.model_id
        WHERE
        umb.biz_user_id = #{dto.bizUserId}
        <if test="dto.modelId != null and dto.modelId.size() !=0">
            AND m.id IN
            <foreach item="item" collection="dto.modelId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryModelBlackListUserVO" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.ModelBlackListUserVO">
        select
            umb.model_id,
            umb.biz_user_id,
            bu.name,
            bu.nick_name,
            bu.pic
        FROM user_model_blacklist umb
        left join biz_user bu on bu.id = umb.biz_user_id
        WHERE
        umb.model_id = #{modelId}
    </select>


    <select id="queryCannotAcceptList" resultType="com.ruoyi.system.api.domain.entity.Model">
        SELECT
            m.id,
            m.NAME
        FROM
            model m
        WHERE
            (m.`status` != '0' OR m.is_show != 1 )
            <if test="modelId != null and modelId.size() !=0">
                AND m.id IN
                <foreach item="item" collection="modelId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="addPreselectModelList" resultType="com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO">
        SELECT
            m.id,m.`name`, ma.account,m.sex,m.cooperation,m.age_group, m.model_pic,m.platform, m.type, m.commission_unit,m.commission,
            m.nation,m.acceptability,m.update_time,m.after_sale_rate,m.overtime_rate
            , (CASE WHEN m.cooperation = 1 THEN 3
                WHEN m.cooperation = 2 THEN 2
                WHEN m.cooperation = 0 THEN 1 else 0 end) AS cooperationSort
            <if test="dto.waitsMap != null and dto.waitsMap.size() > 0">
                <!-- 动态注入 waits 数据 -->
                ,CASE
                    <foreach collection="dto.waitsMap" item="waits" index="modelId" separator=" " >
                        WHEN m.id = ${modelId} THEN ${waits}
                    </foreach>
                    ELSE 0
                END AS waits
            </if>
        FROM
            model m
                    LEFT JOIN model_account ma ON ma.model_id = m.id
        <where>
            m.`status` = '0'
            AND m.is_show = 1
            AND m.nation = #{dto.nation}
            AND FIND_IN_SET( #{dto.platform}, m.platform )
            AND m.type in
            <foreach item="item" index="index" collection="dto.type" open="(" separator="," close=")">
                #{item}
            </foreach>

            <if test="dto.keyword != null and dto.keyword != ''">
            AND (
                    m.name LIKE concat('%', #{dto.keyword}, '%')
                    OR ma.account LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.modelIds != null and dto.modelIds.size() != 0">
                AND m.id in
                <foreach item="item" index="index" collection="dto.modelIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.account !=null and dto.account != ''">
                AND ma.account LIKE concat('%', #{dto.account}, '%')
            </if>

            <if test="dto.name !=null and dto.name != ''">
                AND m.name LIKE concat('%', #{dto.name}, '%')
            </if>

            <if test="dto.ageGroup !=null and dto.ageGroup.size()!=0">
                AND m.age_group in
                <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.specialtyCategory !=null and dto.specialtyCategory.size() != 0">
                AND m.id IN (
                    SELECT
                        mt.model_id
                    FROM
                        model_tag mt
                    WHERE
                        mt.dict_id IN
                            <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                )
            </if>

            <if test="dto.modelTag !=null and dto.modelTag.size() != 0">
                AND m.id IN (
                    SELECT
                        mt.model_id
                    FROM
                        model_tag mt
                    WHERE
                        mt.dict_id IN
                            <foreach item="item" index="index" collection="dto.modelTag" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                )
            </if>

            <if test="dto.cannotModel !=null and dto.cannotModel.size() != 0">
                AND m.id NOT IN
                    <foreach item="item" index="index" collection="dto.cannotModel" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>

            <if test="dto.sex != null and dto.sex.size() > 0">
                AND m.sex IN
                    <foreach item="item" index="index" collection="dto.sex" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>

            <if test="dto.carry != null">
                <choose>
                    <when test="dto.mainCarryModel != null and dto.mainCarryModel.size() > 0">
                        AND m.id
                            <if test="dto.carry == false">
                                NOT
                            </if>
                                    IN
                                    <foreach item="item" index="index" collection="dto.mainCarryModel" open="(" separator="," close=")">
                                        #{item}
                                    </foreach>
                    </when>
                    <when test="dto.carry == true">
                        AND m.id = -1
                    </when>
                </choose>
            </if>

            <if test="dto.normalPreselectModelIds != null and dto.normalPreselectModelIds.size() > 0">
                AND m.id NOT IN
                    <foreach item="item" index="index" collection="dto.normalPreselectModelIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="dto.cooperation != null">
                and m.cooperation = #{dto.cooperation}
            </if>
        </where>
        ORDER BY
        <if test="dto.waitsMap != null and dto.waitsSort != null and dto.waitsSort != ''">
            waits ${dto.waitsSort},
        </if>
        cooperationSort DESC,
        m.update_time DESC,
        m.id ASC
    </select>
    <select id="queryLikeModelList" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelVO">
        SELECT m.id
        FROM
            model m
                INNER JOIN model_account ma on ma.model_id = m.id
        <where>
            <if test="dto.name != null and dto.name != ''">
                OR m.name LIKE concat('%', #{dto.name}, '%')
            </if>
            <if test="dto.account != null and dto.account != ''">
                OR ma.account LIKE concat('%', #{dto.account}, '%')
            </if>
        </where>
    </select>
    <!--    置顶模特-->
    <update id="top">
        UPDATE model
        SET top_time = IF(top_time IS NULL, NOW(), NULL)
        WHERE id = #{id}
    </update>
    <select id="modelStartTravelList" resultType="com.ruoyi.system.api.domain.entity.Model">
        SELECT <include refid="selectModelColumns"/>
            from model m
        WHERE
            m.id IN (
            SELECT
                mt.model_id
            FROM
                model_travel mt
            WHERE
                mt.start_time &lt;= #{beginOfDay} AND mt.end_time &gt;= #{beginOfDay}
            )
          AND m.`status` = 0
    </select>
    <select id="modelEndTravelList" resultType="com.ruoyi.system.api.domain.entity.Model">
        SELECT <include refid="selectModelColumns"/>
            from model m
        WHERE m.id IN (SELECT mt.model_id
                     FROM model_travel mt
                     WHERE mt.end_time &lt;= #{beginOfDay})
          AND m.`status` = 2
    </select>
    <select id="editOrderChangeModelList" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelVO">
        SELECT
        <include refid="modelColumns"/>
        FROM
        model m
        LEFT JOIN model_account ma on ma.model_id = m.id
        <where>
            FIND_IN_SET(#{dto.platform}, m.platform)
            and m.nation = #{dto.nation}
            and m.`status` = '0'
            and m.is_show = 1
            <if test="dto.type != null">and m.type = #{dto.type}</if>
            <if test="dto.name != null  and dto.name != ''">
                and m.name like concat('%', #{dto.name}, '%')
            </if>

            <if test="dto.account != null  and dto.account != ''">
                and ma.account like concat('%', #{dto.account}, '%')
            </if>

            <if test="dto.sex != null and dto.sex.size() != 0">
                and m.sex in
                <foreach item="item" index="index" collection="dto.sex" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.tags != null  and dto.tags.size() != 0">
                and m.id in (
                SELECT model_id from model_tag where dict_id in
                <foreach item="item" index="index" collection="dto.tags" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.specialtyCategory != null  and dto.specialtyCategory.size() != 0">
                and m.id in (
                SELECT model_id from model_tag where dict_id in
                <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>

            <if test="dto.ageGroup != null and dto.ageGroup.size() != 0">
                and m.age_group in
                <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.cannotModel !=null and dto.cannotModel.size() != 0">
                AND m.id NOT IN
                <foreach item="item" index="index" collection="dto.cannotModel" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY

        m.top_time DESC,
        m.create_time ASC
    </select>
    <select id="referenceList" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelListSimpleVO">
        select m.model_pic,
                m.id,
               (CASE WHEN m.cooperation = 1 THEN 3
        WHEN m.cooperation = 2 THEN 2
        WHEN m.cooperation = 0 THEN 1 else 0 end) AS cooperationSort,
            m.name, m.sex, m.type, m.nation,m.`status`
        from model m
        <where>
            m.is_show = 1
            <if test="dto.platform !=null and dto.platform.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.platform" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.platform)
                </foreach>
                )
            </if>

            <if test="dto.nation !=null and dto.nation.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.nation" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.nation)
                </foreach>
                )
            </if>

            <if test="dto.type !=null and dto.type.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.type" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.type)
                </foreach>
                )
            </if>

            <if test="dto.sex !=null and dto.sex.size() != 0">
                AND (
                <foreach item="item" index="index" collection="dto.sex" open="" separator=" OR " close="">
                    FIND_IN_SET(#{item}, m.sex)
                </foreach>
                )
            </if>

            <if test="dto.ageGroup !=null and dto.ageGroup.size()!=0">
                AND m.age_group in
                <foreach item="item" index="index" collection="dto.ageGroup" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.specialtyCategory !=null and dto.specialtyCategory.size() != 0">
                AND m.id IN (
                    SELECT
                        mt.model_id
                    FROM
                        model_tag mt
                    WHERE
                        mt.dict_id IN
                        <foreach item="item" index="index" collection="dto.specialtyCategory" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                )
            </if>

            <if test="dto.cannotModel !=null and dto.cannotModel.size() != 0">
                AND m.id NOT IN
                <foreach item="item" index="index" collection="dto.cannotModel" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        m.status_sort,
        m.cooperation_sort DESC,
        m.update_time DESC,
        m.id ASC
        limit 50
    </select>

    <select id="getModelIdByRelationships" resultType="java.lang.Long">
        select distinct family_id
        from model
        where model_family_relationship in
        <foreach item="item" index="index" collection="modelFamilyRelationships" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectEnglishCustomerServiceModelData"
            resultType="com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO">
        SELECT
            mp.user_id AS customerServiceId,
            COUNT(*) AS modelCount,
            COUNT( CASE WHEN m.`status` IN (
                                            ${@<EMAIL>},
                                            ${@<EMAIL>}
                                           ) THEN 1 END ) AS modelCooperationCount,
            COUNT( CASE WHEN m.`status` = ${@<EMAIL>} THEN 1 END ) AS modelNormalCount,
            COUNT( CASE WHEN m.`status` = ${@<EMAIL>} THEN 1 END ) AS modelTravelCount
        FROM
            model_person mp
                JOIN model m ON m.id = mp.model_id
        GROUP BY
            mp.user_id
    </select>

    <update id="updateModelInfoFieldNullToNull">
        UPDATE model
        SET
            model_pic = #{model.modelPic},
            is_family_model = #{model.isFamilyModel},
            is_initiator = #{model.isInitiator},
            family_id = #{model.familyId},
            model_family_relationship = #{model.modelFamilyRelationship},
            join_family_time = #{model.joinFamilyTime},
            name = #{model.name},
            sex = #{model.sex},
            sex_dict = #{model.sexDict},
            type = #{model.type},
            type_dict = #{model.typeDict},
            age_group = #{model.ageGroup},
            age_group_dict = #{model.ageGroupDict},
            birthday = #{model.birthday},
            age = #{model.age},
            nation = #{model.nation},
            nation_dict = #{model.nationDict},
            recipient = #{model.recipient},
            state = #{model.state},
            city = #{model.city},
            zipcode = #{model.zipcode},
            detail_address = #{model.detailAddress},
            platform = #{model.platform},
            platform_dict = #{model.platformDict},
            cooperation = #{model.cooperation},
            acceptability = #{model.acceptability},
            commission_unit = #{model.commissionUnit},
            commission = #{model.commission},
            status = #{model.status},
            status_time = #{model.statusTime},
            status_explain = #{model.statusExplain},
            live_pic = #{model.livePic},
            amazon_video = #{model.amazonVideoId},
            tiktok_video = #{model.tiktokVideoId},
            top_time = #{model.topTime},
            sort = #{model.sort},
            overtime_rate = #{model.overtimeRate},
            after_sale_rate = #{model.afterSaleRate},
            blacklist_count = #{model.blacklistCount},
            create_by = #{model.createBy},
            create_time = #{model.createTime},
            update_by = #{model.updateBy},
            update_time = #{model.updateTime},
            cancel_cooperation_type = #{model.cancelCooperationType},
            cancel_cooperation_sub_type = #{model.cancelCooperationSubType},
            phone = #{model.phone},
            sync_msg = #{model.syncMsg},
            is_show = #{model.isShow},
            about = #{model.about},
            developer_id = #{model.developerId},
            have_snail_pic = #{model.haveSnailPic},
            video_last_update_time = #{model.videoLastUpdateTime},
            tag_last_update_time = #{model.tagLastUpdateTime},
            category_last_update_time = #{model.categoryLastUpdateTime}
        WHERE id = #{model.id}
    </update>

    <!-- 查询模特姓名和账户名组合列表（用于下拉筛选） -->
    <select id="selectModelNameAccountOptions" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelNameAccountVO">
        SELECT CONCAT(m.name, '(', ma.account, ')') AS nameAccount, m.create_time AS createTime, m.id AS id
        FROM model m inner JOIN model_account ma ON m.id = ma.model_id
        ORDER BY m.create_time DESC
    </select>
    <select id="modelSelect" resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO">
        SELECT
            m.id,
            ma.account,
            m.name
        FROM
            model m
                LEFT JOIN model_account ma ON m.id = ma.model_id
                LEFT JOIN model_person mp on m.id = mp.model_id
        <where>
            <if test="dto.id != null  and dto.id.size() != 0">
                AND m.id IN
                <foreach item="item" collection="dto.id" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.name != null  and dto.name != ''">
                AND m.name LIKE CONCAT('%', #{dto.name}, '%')
            </if>

            <if test="dto.persons != null and dto.persons.size() != 0 ">
                AND ( mp.user_id IN
                <foreach item="item" index="index" collection="dto.persons" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR m.developer_id IN
                <foreach item="item" index="index" collection="dto.persons" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
        </where>
    </select>
</mapper>