<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelTagMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.model.ModelTag" id="ModelTagResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="dictId"    column="dict_id"    />
    </resultMap>

    <sql id="selectModelTagVo">
        select id, model_id, dict_id from model_tag
    </sql>
</mapper>