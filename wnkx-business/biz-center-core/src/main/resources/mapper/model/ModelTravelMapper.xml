<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelTravelMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.model.ModelTravel" id="ModelTravelResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectModelTravelVo">
        select id, model_id, start_time, end_time, remark, create_time, update_time from model_travel
    </sql>
</mapper>