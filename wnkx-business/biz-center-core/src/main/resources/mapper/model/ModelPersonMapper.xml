<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelPersonMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.ModelPerson" id="ModelPersonResult">
        <result property="id"    column="id"    />
        <result property="modelId"    column="model_id"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectModelPersonVo">
        select id, model_id, user_id, create_by, create_time, update_by, update_time from model_person
    </sql>
    <select id="getEnglishCustomerServiceAddedModelCounts"
            resultType="com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo">
        SELECT
            mp.user_id AS customerServiceId,
            COUNT(*) AS addedCount
        FROM
            model_person mp
                JOIN model m ON m.id = mp.model_id
        WHERE
            DATE_FORMAT(mp.create_time,'%Y-%m')  = #{date}
        GROUP BY
            mp.user_id;
    </select>
    <select id="getUserId" resultType="java.lang.Long">
        SELECT
            DISTINCT back_user_id
        FROM
            (
                SELECT user_id AS back_user_id
                FROM model_person
                WHERE user_id IS NOT NULL

                UNION ALL

                SELECT developer_id AS back_user_id
                FROM model
                WHERE developer_id IS NOT NULL
            )t
    </select>
</mapper>