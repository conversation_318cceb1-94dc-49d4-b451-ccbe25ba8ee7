<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelChangeRecordMapper">

    <sql id="getFinalOustModelIdsByDateSql">
        WITH latest AS (
            SELECT
                model_id,
                operate_detail,
                ROW_NUMBER() OVER (
                    PARTITION BY model_id
                    ORDER BY operate_time DESC
                    ) AS rn
            FROM model_change_record
            WHERE
                operate_type = ${@com.ruoyi.common.core.enums.ModelChangeTypeEnum@CHANGE_STATUS.getcode}
                AND DATE_FORMAT(operate_time, ${dateFormat}) = #{date}
        ),
             earliest AS (
                 SELECT
                     model_id,
                     state_before_change,
                     ROW_NUMBER() OVER (
                         PARTITION BY model_id
                         ORDER BY operate_time ASC
                         ) AS rn
                 FROM model_change_record
                 WHERE
                     operate_type = 3
                   AND DATE_FORMAT(operate_time, ${dateFormat}) = #{date}
             )
        SELECT
            l.model_id
        FROM latest l
                 JOIN earliest e ON l.model_id = e.model_id
        WHERE l.rn = 1
          AND (
            l.operate_detail LIKE '%取消合作%'
                OR l.operate_detail LIKE '%暂停合作%'
            )
          AND e.rn = 1
          AND e.state_before_change IN (
                                        ${@<EMAIL>},
                                        ${@<EMAIL>}
                                        )
    </sql>

    <select id="getFinalOustModelIdsByDate" resultType="java.lang.Long">
        <include refid="getFinalOustModelIdsByDateSql"/>
    </select>
    <select id="getEnglishCustomerServiceOustModelCounts"
            resultType="com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo">
        SELECT
            mp.user_id AS customerServiceId,
            COUNT(*) AS oustCount
        FROM (
                <include refid="getFinalOustModelIdsByDateSql"/>
             )AS lo
            JOIN model_person mp ON mp.model_id = lo.model_id
        GROUP BY mp.user_id
    </select>
</mapper>