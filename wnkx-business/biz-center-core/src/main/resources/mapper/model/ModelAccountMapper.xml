<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelAccountMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.model.ModelAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="modelId" column="model_id" jdbcType="BIGINT"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="loginAccount" column="login_account" jdbcType="VARCHAR"/>
            <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,model_id,account,login_account,
        last_login_time,create_time,update_time
    </sql>
</mapper>
