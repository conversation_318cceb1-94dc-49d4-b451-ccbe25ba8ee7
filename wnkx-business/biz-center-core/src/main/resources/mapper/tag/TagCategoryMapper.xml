<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.tag.mapper.TagCategoryMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.tag.TagCategory" id="TagCategoryResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="path"    column="path"    />
        <result property="name"    column="name"    />
        <result property="sort"    column="sort"    />
    </resultMap>

    <sql id="selectTagCategoryVo">
        select id, parent_id, path, name, sort, create_time, update_time from tag_category
    </sql>
</mapper>