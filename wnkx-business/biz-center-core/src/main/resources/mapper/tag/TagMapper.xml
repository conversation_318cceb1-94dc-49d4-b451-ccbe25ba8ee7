<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.tag.mapper.TagMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.tag.Tag" id="TagResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="name"    column="name"    />
        <result property="englishName"    column="english_name"    />
        <result property="path"    column="path"    />
        <result property="categoryId"    column="category_id"    />
        <result property="sort"    column="sort"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectTagVo">
        select id,parent_id, name,  path, category_id, sort, create_time, update_time, status, remark, create_by, update_by from tag
    </sql>

    <select id="stair" resultType="com.ruoyi.system.api.domain.vo.TagListVO">
        <include refid="selectTagVo"/>
        WHERE
            LENGTH( path ) - LENGTH(
            REPLACE ( path, '/', '' )) &lt;= 2
            AND category_id = #{categoryId}
    </select>
    <select id="rank" resultType="com.ruoyi.system.api.domain.vo.TagListVO">
        <include refid="selectTagVo"/>
        WHERE
            LENGTH( path ) - LENGTH(
            REPLACE ( path, '/', '' )) = #{rank}
            AND category_id = #{categoryId}
            AND status = 0
        ORDER BY sort
    </select>
    <select id="rankUpslope" resultType="com.ruoyi.system.api.domain.vo.TagListVO">
        <include refid="selectTagVo"/>
        WHERE
            LENGTH( path ) - LENGTH(
            REPLACE ( path, '/', '' )) &lt;= #{rank}
            AND category_id = #{categoryId}
            AND status = 0
        ORDER BY create_time desc
    </select>
</mapper>