<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.page.mapper.PageConfigMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.page.PageConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="platform" column="platform" jdbcType="INTEGER"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,type,platform,
        content,create_time
    </sql>

    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.page.PageConfigVO">
        select id,name,type,platform,create_time,user_perms
        from page_config
        <where>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="id != null "> and id = #{id}</if>
            <if test="type != null "> and type = #{type}</if>
        </where>
    </select>
</mapper>
