<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.amazon.mapper.AmazonGoodsPicMapper">

    <insert id="insertAmazonGoodsPic">
        INSERT INTO
            amazon_goods_pic ( goods_id, object_key, product_name,product_chinese_name, spec_info )
                SELECT
                      #{goodsId},
                      #{objectKey},
                      #{productName},
                      #{productChineseName},
                      #{specInfo}
                FROM
                    DUAL
                WHERE
                    NOT EXISTS ( SELECT 1 FROM amazon_goods_pic WHERE goods_id = #{goodsId} OR object_key = #{objectKey} );
    </insert>
</mapper>
