<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.casus.mapper.CasusVideoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.casus.CasusVideo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="BOOLEAN"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,pic,
        link,platform,create_by,
        create_id,create_time,update_by,
        update_id,update_time
    </sql>
</mapper>
