<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.casus.mapper.GroupVideoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.casus.GroupVideo">
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="videoId" column="video_id" jdbcType="BIGINT"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        group_id,video_id,sort,
        create_by,create_id,create_time
    </sql>
</mapper>
