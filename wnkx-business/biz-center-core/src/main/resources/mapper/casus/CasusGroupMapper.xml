<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.casus.mapper.CasusGroupMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.casus.CasusGroup">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="platform" column="platform" jdbcType="BOOLEAN"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createId" column="create_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateId" column="update_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,platform,
        create_by,create_id,create_time,
        update_by,update_id,update_time
    </sql>

    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.order.casus.CasusGroupVO">
        select cg.id,
               cg.name,
               cg.platform,
               count(gv.group_id) as videoCount
        from casus_group cg
        left join group_video gv on cg.id = gv.group_id
        <where>
            <if test="ids != null and ids.size() != 0">
                and cg.id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null">and cg.id = #{id}</if>
            <if test="platform != null">and cg.platform = #{platform}</if>
            <if test="name != null  and name != ''"> and cg.name like concat('%', #{name}, '%')</if>
        </where>
        group by cg.id,
                 cg.name,
                 cg.platform

    </select>

    <select id="queryGroupsVideoList" resultType="com.ruoyi.system.api.domain.vo.order.casus.GroupVideoVO">
        select
            gv.group_id,
            cv.id as videoId,
            cv.name as videoName,
            cv.pic as videoPic,
            cv.link as videoLink,
            gv.create_time as joinTime,
            gv.sort
        from group_video gv
        inner join casus_video cv on gv.video_id = cv.id
        <where>
            <if test="groupId != null">and gv.group_id = #{groupId}</if>
            <if test="videoId != null">and gv.video_id = #{videoId}</if>
            <if test="videoName != null  and videoName != ''"> and cv.name like concat('%', #{videoName}, '%')</if>
        </where>
    </select>

    <select id="queryAddGroupsVideoList" resultType="com.ruoyi.system.api.domain.vo.order.casus.GroupAddVideoVO">
        SELECT
            cv.id,
            cv.name,
            cv.pic
        FROM casus_video cv
        left join (select * from group_video where group_id = #{groupId}) as a on a.video_id = cv.id
        <where>
            a.video_id is null
            and cv.platform = #{platform}
            <if test="id != null">and cv.id = #{id}</if>
            <if test="name != null  and name != ''"> and cv.name like concat('%', #{name}, '%')</if>
        </where>
    </select>
</mapper>
