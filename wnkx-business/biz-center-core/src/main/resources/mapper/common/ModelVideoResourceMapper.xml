<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelVideoResourceMapper">

    <resultMap type="com.ruoyi.system.api.domain.entity.biz.model.ModelVideoResource" id="ResourceResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="videoUrl"    column="video_url"    />
        <result property="picUri"    column="pic_uri"    />
    </resultMap>

    <sql id="selectResourceVo">
        select id, name, url, link_id, link_type, create_time, update_time from model_video_resource
    </sql>
</mapper>