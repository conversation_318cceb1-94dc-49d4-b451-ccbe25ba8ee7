<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.common.mapper.TextMapper">

    <sql id="selectTextVO">
        select tt.id,tt.name,tt.remark,tt.type,tt.sort,tt.status,tt.can_delete,tt.create_time,tt.update_time from text_table tt
    </sql>

    <select id="selectListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.common.TextListVO">
        <include refid="selectTextVO"/>
        <where>
            <if test="dto.id != null">
                and tt.id like concat('%',#{dto.id},'%')
            </if>
            <if test="dto.type != null">
                and tt.type = #{dto.type}
            </if>
            <if test="dto.name != null">
                and tt.name like concat('%',#{dto.name},'%')
            </if>
        </where>
    </select>

    <select id="selectHelpListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.common.TextHelpListVO">
        <include refid="selectTextVO"/>
        <where>
            tt.type in(${@com.ruoyi.common.core.enums.TextTypeEnum@HELP_COMMON_PROBLEM.getcode},${@com.ruoyi.common.core.enums.TextTypeEnum@HELP_NOVICE_GUIDE.getcode})
            <if test="dto.type != null">
                and tt.type = #{dto.type}
            </if>
            <if test="dto.name != null">
                and tt.name like concat('%',#{dto.name},'%')
            </if>
            <if test="dto.status != null">
                and tt.status = #{dto.status}
            </if>
        </where>
    </select>

    <select id="selectUserHelpListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.common.TextHelpVO">
        select tt.id,tt.name,tt.content,tt.type,tt.sort,tt.status,tt.can_delete,tt.create_time,tt.update_time from text_table tt
        <where>
            tt.type in(${@com.ruoyi.common.core.enums.TextTypeEnum@HELP_COMMON_PROBLEM.getcode},${@com.ruoyi.common.core.enums.TextTypeEnum@HELP_NOVICE_GUIDE.getcode})
            <if test="dto.type != null">
                and tt.type = #{dto.type}
            </if>
            <if test="dto.status != null">
                and tt.status = #{dto.status}
            </if>
            <if test="dto.name != null">
                and tt.name like concat('%',#{dto.name},'%')
            </if>
        </where>
    </select>
</mapper>