<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.DistributionChannelActivityMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="activityName" column="activity_name" jdbcType="VARCHAR"/>
            <result property="discount" column="discount" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

<!--    已结束-0 已暂停-1 未开始-2 进行中-3-->
    <sql id ="status_c">
        CASE
        WHEN NOW() &lt; `start_time` THEN 2
        WHEN NOW() BETWEEN `start_time` AND `end_time` THEN `status`
        WHEN NOW() &gt; `end_time` THEN 0
        END AS `status`
    </sql>
    <sql id="Base_Column_List">
        id,activity_name,discount,
        CASE
        WHEN NOW() &lt; `start_time` THEN 2
        WHEN NOW() BETWEEN `start_time` AND `end_time` THEN `status`
        WHEN NOW() &gt; `end_time` THEN 0
        END AS `status`,
            start_time,end_time,
        create_by,create_by_id,create_time,
        update_by,update_by_id,update_time,
        `type`
    </sql>
    <sql id="time">
        ((start_time &lt;NOW()  AND end_time &gt; NOW()) OR (start_time BETWEEN NOW() AND NOW()) OR (end_time BETWEEN NOW() AND NOW())
    </sql>
    <select id="channelList" resultType="com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO">
        select * from (select<include refid="Base_Column_List"/>,IF(channel_count IS NULL, 0, channel_count) as channel_count from
        distribution_channel_activity dca LEFT JOIN (SELECT activity_id, count(*) AS channel_count FROM
        distribution_channel_activity_info GROUP BY activity_id) detail ON detail.activity_id = dca.id
        <where>
            <if test="dto.activityName != null and dto.activityName != ''">
                and activity_name like concat('%',#{dto.activityName},'%')
            </if>
        </where>
        ) detail
        <where>
            <if test="dto.status != null">
                and status = #{dto.status}
            </if>
        </where>
        order by status desc,create_time desc
    </select>
    <select id="getChannelActivityBaseInfo"
            resultType="com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityEditDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `distribution_channel_activity`
        <where>
            id IN
            <foreach close=")" collection="ids" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
        order by id desc
    </select>
    <select id="getChannelActivityList"
            resultType="com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO">
        select * from (select  id,activity_name,discount,
        CASE
        WHEN #{startTime} &gt; `end_time` THEN 0
        WHEN #{endTime} &lt; `start_time` THEN 2
        ELSE `status`
        END AS `status`,
        start_time,end_time,
        create_by,create_by_id,create_time,
        update_by,update_by_id,update_time,`type` FROM
        `distribution_channel_activity`
        ) base
        <where>
            status = 3
            and (
                ( `type` =0) or
                ( `type` =1 and id in (SELECT activity_id from distribution_channel_activity_info dcai WHERE channel_id = #{channelId})) or
                ( `type` =2 and id not in (SELECT activity_id from distribution_channel_activity_info dcai WHERE channel_id = #{channelId}))
            )
            <if test="activityId != null">
                and id != #{activityId}
            </if>
        </where>
    </select>
    <select id="getMinDiscount" resultType="java.math.BigDecimal">
        select min(discount) from distribution_channel_activity dca where (<include refid="time"/>) and status=3) and
        (
            (`type` = 0) or
            (`type` =1 and id in (SELECT DISTINCT activity_id from distribution_channel_activity_info dcai WHERE channel_id in (SELECT id from distribution_channel WHERE id = #{id} and status = 0))) or
            (`type` =2 and id not in ((SELECT DISTINCT activity_id from distribution_channel_activity_info dcai WHERE channel_id in (SELECT id from distribution_channel WHERE id = #{id} and status = 0))))
        )
    </select>
    <select id="getOtherChannelCount" resultType="java.lang.Long">
        select count(id) from distribution_channel dc where dc.id not in (select channel_id from distribution_channel_activity_info where  activity_id = #{id}) and status =0 and dc.channel_type = ${@<EMAIL>}
    </select>
    <select id="getPartChannelCount" resultType="java.lang.Long">
        select count(id) from distribution_channel dc where dc.id in (select channel_id from distribution_channel_activity_info where  activity_id = #{id}) and status =0 and dc.channel_type = ${@<EMAIL>}
    </select>
    <select id="getLatestChannelDiscount"
            resultType="com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityDTO">
        select activity_name,discount,status,start_time,end_time from distribution_channel_activity dca where (<include refid="time"/>) and status=3) and
        (
            (`type` = 0) or
            (`type` =1 and id in (SELECT DISTINCT activity_id from distribution_channel_activity_info dcai WHERE channel_id in (SELECT id from distribution_channel WHERE id = #{id} and status = 0))) or
            (`type` =2 and id not in ((SELECT DISTINCT activity_id from distribution_channel_activity_info dcai WHERE channel_id in (SELECT id from distribution_channel WHERE id = #{id} and status = 0))))
        ) ORDER by discount , create_time desc limit 1
    </select>
</mapper>
