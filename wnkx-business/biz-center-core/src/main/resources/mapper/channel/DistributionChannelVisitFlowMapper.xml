<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.DistributionChannelVisitFlowMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelVisitFlow">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
            <result property="uniqueVisitor" column="unique_visitor" jdbcType="INTEGER"/>
            <result property="pageView" column="page_view" jdbcType="INTEGER"/>
            <result property="bounceRate" column="bounce_rate" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel_id,unique_visitor,
        page_view,bounce_rate,create_time
    </sql>
    <select id="getSumVisit" resultType="java.lang.Long">
        SELECT SUM(dcvf.unique_visitor)
        from distribution_channel_visit_flow dcvf
        LEFT JOIN distribution_channel dc ON dcvf.channel_id = dc.id
        where dc.channel_type = #{channelType}
    </select>
</mapper>
