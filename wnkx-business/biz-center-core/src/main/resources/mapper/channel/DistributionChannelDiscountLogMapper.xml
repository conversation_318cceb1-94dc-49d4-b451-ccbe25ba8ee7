<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.DistributionChannelDiscountLogMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelDiscountLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channelType" column="channel_type" jdbcType="INTEGER"/>
            <result property="memberDiscountType" column="member_discount_type" jdbcType="INTEGER"/>
            <result property="memberDiscount" column="member_discount" jdbcType="DECIMAL"/>
            <result property="settleDiscountType" column="settle_discount_type" jdbcType="INTEGER"/>
            <result property="settleDiscount" column="settle_discount" jdbcType="DECIMAL"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel_type,member_discount_type,
        member_discount,settle_discount_type,settle_discount,
        start_time,end_time,create_time
    </sql>
</mapper>
