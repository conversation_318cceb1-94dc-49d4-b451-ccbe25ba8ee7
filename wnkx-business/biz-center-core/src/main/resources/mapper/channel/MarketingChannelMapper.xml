<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.MarketingChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="marketingPlatform" column="marketing_platform" jdbcType="TINYINT"/>
        <result property="marketingChannelName" column="marketing_channel_name" jdbcType="VARCHAR"/>
        <result property="landingForm" column="landing_form" jdbcType="TINYINT"/>
        <result property="dedicatedLinkCode" column="dedicated_link_code" jdbcType="VARCHAR"/>
        <result property="weChatUrl" column="we_chat_url" jdbcType="VARCHAR"/>
        <result property="tagId" column="tag_id" jdbcType="VARCHAR"/>
        <result property="uniqueVisitor" column="unique_visitor" jdbcType="INTEGER"/>
        <result property="pageView" column="page_view" jdbcType="INTEGER"/>
        <result property="bounceRate" column="bounce_rate" jdbcType="DECIMAL"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createId" column="create_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
       select mc.id,mc.marketing_platform,mc.marketing_channel_name,mc.landing_form,
              mc.dedicated_link_code,mc.we_chat_url,mc.tag_id,
              mc.unique_visitor,mc.page_view,mc.bounce_rate,
              mc.status,mc.remark,mc.create_by,mc.create_id,mc.create_time
        from marketing_channel mc
    </sql>
    <sql id="queryChannelTable">
        FROM
        marketing_channel mc
        LEFT JOIN (SELECT
        channel_id,
        sum(real_pay_amount) real_pay_amount,
        count(1) member_num
        FROM
        order_member_marketing_channel
        <if test="dto.startTime != null and dto.endTime != null">
            where create_time BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        GROUP BY
        channel_id) ommc ON ommc.channel_id = mc.id
        left join (
        select
        count(1) register_num,
        register_channel_id channelId
        from biz_user_channel
        where
        register_channel_type = ${@<EMAIL>}
        <if test="dto.startTime != null and dto.endTime != null">
            and register_time BETWEEN #{dto.startTime} AND #{dto.endTime}
        </if>
        group by register_channel_id
        ) register ON register.channelId = mc.id
        left join (
        select
        sum(unique_visitor) unique_visitor,
        channel_id
        from marketing_channel_visit_flow
        <where>
            <if test="dto.startTime != null and dto.endTime != null">
                and create_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
        </where>
        group by channel_id
        ) visit ON visit.channel_id = mc.id
        LEFT JOIN (
            select channel_id,count(1) as wechat_count from we_chat_external_user WHERE channel_type =  ${@<EMAIL>}
            <if test="dto.startTime != null and dto.endTime != null">
                and create_time BETWEEN #{dto.startTime} AND #{dto.endTime}
            </if>
             group by channel_id
        )wceu_c on wceu_c.channel_id = mc.id
    </sql>

    <sql id="sqlwhereSearch">
        <where>
            <if test="dto.marketingChannelName != null and dto.marketingChannelName != ''">
                AND mc.marketing_channel_name LIKE CONCAT('%',#{dto.marketingChannelName},'%')
            </if>
            <if test="dto.marketingPlatform != null and dto.marketingPlatform.size > 0">
                AND mc.marketing_platform IN
                <foreach collection="dto.marketingPlatform" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.userIds != null and dto.userIds.size() != 0">
                AND mc.create_id in
                <foreach collection="dto.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.id != null and dto.id != ''">
                AND mc.id = #{dto.id}
            </if>
        </where>
    </sql>
    <select id="marketingChannelListByCondition" resultType="com.ruoyi.system.api.domain.vo.biz.channel.MarketingChannelListVO">
        SELECT
        mc.id,
        mc.marketing_platform,
        mc.marketing_channel_name,
        mc.landing_form,
        mc.create_by,
        mc.create_time,
        mc.dedicated_link_code,
        visit.unique_visitor,
        ommc.member_num,
        ommc.real_pay_amount,
        register.register_num newRegistrationCount,
        wceu_c.wechat_count
        <include refid="queryChannelTable"/>
        <include refid="sqlwhereSearch"/>
    </select>

    <select id="marketingChannelStatistics" resultType="com.ruoyi.system.api.domain.vo.biz.channel.MarketingChannelStatisticsVO">
        select
            sum(visit.unique_visitor) unique_visitor,
            sum(register.register_num) newRegistrationCount,
            sum(wceu_c.wechat_count) wechat_count,
            sum(ommc.real_pay_amount) real_pay_amount,
            sum(ommc.member_num) member_num
        <include refid="queryChannelTable"/>
        <include refid="sqlwhereSearch"/>
    </select>
    <select id="createUserList" resultType="com.ruoyi.system.api.domain.vo.SysUserVO">
        select
               distinct
            create_id userId,
            create_by userName
            from marketing_channel
        <where>
            <if test="name != null and name != ''">
                create_by like CONCAT('%',#{name},'%')
            </if>
        </where>
    </select>

    <select id="getMarketingChannelByBizUserId" resultType="com.ruoyi.system.api.domain.entity.biz.channel.MarketingChannel">
        select mc.*
        from biz_user_channel buc
        inner join marketing_channel mc on buc.register_channel_id = mc.id and buc.register_channel_type = ${@<EMAIL>}
        where biz_user_id = #{bizUserId}
    </select>


    <sql id="queryChannelInvite">
        from biz_user_channel buc
        inner join biz_user bu on buc.biz_user_id = bu.id
        left join order_member_marketing_channel omc on bu.id = omc.biz_user_id
        <where>
            register_channel_type = ${@<EMAIL>}
            and register_channel_id = #{channelId}
            <if test="keyword != null and keyword != ''">
                and (bu.phone like concat('%', #{keyword}, '%')
                or bu.nick_name like concat('%', #{keyword}, '%'))
            </if>
            <if test="memberPackageType != null and memberPackageType != 3 and memberPackageType != 4">
                and omc.memberType = #{memberPackageType} and t.memberChannelId = #{channelId}
            </if>
            <if test="memberPackageType != null and memberPackageType == 3">
                and (omc.memberChannelId != #{channelId})
            </if>
            <if test="memberPackageType != null and memberPackageType == 4">
                and (omc.memberType is null)
            </if>
            <if test="memberStartTime != null and memberEndTime != null">
                and omc.create_time BETWEEN #{memberStartTime} AND #{memberEndTime}
            </if>
            <if test="registerStartTime != null and registerEndTime != null">
                and buc.register_time BETWEEN #{registerStartTime} AND #{registerEndTime}
            </if>
        </where>
    </sql>
    <select id="getChannelInviteList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO">
        select bu.id bizUserId,
        bu.phone,
        bu.nick_name,
        buc.add_wechat_time,
        buc.register_channel_id,
        buc.wechat_channel_id,
        buc.wechat_channel_type,
        buc.register_time,
        bu.account_type,
        omc.member_package_type memberType,
        omc.channel_id memberChannelId,
        omc.real_pay_amount,
        omc.create_time,
        buc.register_channel_type
        <include refid="queryChannelInvite"/>
    </select>

    <select id="getChannelInviteStatistics" resultType="com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteStatisticsVO">
        select
            count(bu.id) registerNum,
            count(omc.id) memberNum,
            sum(omc.real_pay_amount) realPayAmount
        <include refid="queryChannelInvite"/>
    </select>

    <select id="refreshUniqueVisitor">
        update marketing_channel mc
            join (
            select
            channel_id,
            sum(unique_visitor) unique_visitor
            from marketing_channel_visit_flow
            group by channel_id
            )mcvf on mc.id = mcvf.channel_id
            SET mc.unique_visitor = mcvf.unique_visitor;
    </select>
</mapper>
