<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.OrderMemberMarketingChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
            <result property="marketingPlatform" column="marketing_platform" jdbcType="BOOLEAN"/>
            <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="businessName" column="business_name" jdbcType="VARCHAR"/>
            <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
            <result property="bizUserNickName" column="biz_user_nick_name" jdbcType="VARCHAR"/>
            <result property="bizUserPhone" column="biz_user_phone" jdbcType="VARCHAR"/>
            <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
            <result property="memberPackageType" column="member_package_type" jdbcType="BOOLEAN"/>
            <result property="realPayAmount" column="real_pay_amount" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,channel_id,marketing_platform,
        channel_name,business_id,business_name,
        biz_user_id,biz_user_nick_name,biz_user_phone,
        member_code,member_package_type,real_pay_amount,
        remark,create_time
    </sql>

    <select id="getInitOrderMemberMarketingChannel" resultType="com.ruoyi.system.api.domain.entity.biz.channel.OrderMemberMarketingChannel">
        select
        mc.id channel_id,
        mc.marketing_platform marketing_platform,
        mc.marketing_channel_name channel_name,
        b.id business_id,
        b.`name` business_name,
        bu.id biz_user_id,
        bu.nick_name biz_user_nick_name,
        bu.phone biz_user_phone,
        b.member_code,
        b.member_first_type member_package_type,
        bmvf.order_num orderNum,
        0.0 real_pay_amount,
        "" remark,
        b.member_first_time create_time
        from business b
        INNER JOIN business_account ba on b.owner_account = ba.account
        INNER JOIN biz_user bu on bu.id = ba.biz_user_id
        INNER JOIN biz_user_channel buc on buc.biz_user_id = ba.biz_user_id
        INNER JOIN marketing_channel mc on mc.id = buc.register_channel_id
        left join(
        SELECT
        t.business_id,
        t.order_num
        FROM
        business_member_validity_flow t
        JOIN ( SELECT business_id, MIN( origin_member_validity ) AS max_origin_member_validity FROM business_member_validity_flow WHERE order_num &lt;&gt; "" GROUP BY business_id ) tm ON t.business_id = tm.business_id
        AND t.origin_member_validity = tm.max_origin_member_validity
        ) bmvf on bmvf.business_id = b.id

        where buc.register_channel_type = 1 and b.member_first_type is not null and b.seed_code = "";
    </select>
</mapper>
