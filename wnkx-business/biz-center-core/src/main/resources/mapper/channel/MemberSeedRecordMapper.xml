<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.MemberSeedRecordMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
            <result property="channelType" column="channel_type" jdbcType="BOOLEAN"/>
            <result property="channelBizUserId" column="channel_biz_user_id" jdbcType="BIGINT"/>
            <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
            <result property="channelPhone" column="channel_phone" jdbcType="VARCHAR"/>
            <result property="channelSeedId" column="channel_seed_id" jdbcType="VARCHAR"/>
            <result property="seedCode" column="seed_code" jdbcType="VARCHAR"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="businessName" column="business_name" jdbcType="VARCHAR"/>
            <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
            <result property="bizUserNickName" column="biz_user_nick_name" jdbcType="VARCHAR"/>
            <result property="bizUserPhone" column="biz_user_phone" jdbcType="VARCHAR"/>
            <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
            <result property="memberPackageType" column="member_package_type" jdbcType="BOOLEAN"/>
            <result property="orderNum" column="order_num" jdbcType="VARCHAR"/>
            <result property="realPayAmount" column="real_pay_amount" jdbcType="DECIMAL"/>
            <result property="currency" column="currency" jdbcType="BOOLEAN"/>
            <result property="realPayAmountCurrency" column="real_pay_amount_currency" jdbcType="DECIMAL"/>
            <result property="payType" column="pay_type" jdbcType="BOOLEAN"/>
            <result property="settleType" column="settle_type" jdbcType="BOOLEAN"/>
            <result property="settleRage" column="settle_rage" jdbcType="DECIMAL"/>
            <result property="seedCodeDiscount" column="seed_code_discount" jdbcType="DECIMAL"/>
            <result property="settleAmount" column="settle_amount" jdbcType="DECIMAL"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getFissionStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionAmountStatisticsVO">
        select
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_DEPOSIT.getCode} THEN settle_amount ELSE 0.0 END ) pendingDepositAmount,
            sum(CASE WHEN status in (${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode},
                                     ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode},
                                     ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}) THEN settle_amount ELSE 0.0 END ) canWithdrawAmount,
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode} THEN settle_amount ELSE 0.0 END ) underReviewAmount,
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode} THEN settle_amount ELSE 0.0 END ) pendingTransferAmount,
            sum(CASE WHEN status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) withdrawSuccessAmount
        from member_seed_record
        <where>
        <if test="channelId != null">
           and channel_id = #{channelId}
        </if>
        </where>
    </select>
    <select id="getFissionCountStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionCountStatisticsVO">
        select
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_DEPOSIT.getCode} THEN 1 ELSE 0 END) pendingDepositCount,
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode} THEN 1 ELSE 0 END) pendingWithdrawalCount,
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode} THEN 1 ELSE 0 END) underReviewCount,
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode} THEN 1 ELSE 0 END) pendingTransferCount,
        sum(CASE WHEN status = ${@<EMAIL>} THEN 1 ELSE 0 END) withdrawSuccessCount,
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode} THEN 1 ELSE 0 END) reviewRejectedCount,
        sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode} THEN 1 ELSE 0 END) transferExceptionCount,
        count(1) totalCount
        from member_seed_record
        <where>
        <if test="channelId != null">
            channel_id = #{channelId}
        </if>
        </where>
    </select>
    <select id="memberSeedRecordList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordVO">
        select a.*
        from (select
                  msr.*,
                  msrw.withdrawal_num,
                  msrw.audit_time,
                  msrw.audit_remark as auditRemark ,
                  msrw.audit_user_name as auditUserName,
                  msrw.payout_time ,
                  msrw.withdrawal_time,
                  msrw.withdrawal_remark as withdrawalRemark,
                  msrw.withdrawal_user_name as withdrawalUserName,
                  msrw.create_time as withdrawal_create_time
              from member_seed_record msr
                       inner join (
                           select
                               member_seed_record_id,
                               max(member_seed_record_withdrawal_id) as member_seed_record_withdrawal_id
                           from member_seed_record_relevance
                           group by member_seed_record_id
                  ) msrr on msrr.member_seed_record_id = msr.id
                       inner join member_seed_record_withdrawal msrw on msrr.member_seed_record_withdrawal_id = msrw.id
              where msr.channel_id = #{channelId}
              union All
              select
                  msr.*,
                  null as withdrawalNum,
                  null as audit_time,
                  null as auditRemark,
                  null as auditUserName,
                  null as payout_time,
                  null as withdrawal_time,
                  null as withdrawalRemark,
                  null as withdrawalUserName,
                  msr.create_time as withdrawal_create_time
              from member_seed_record msr
              where msr.channel_id = #{channelId} and
                      msr.status in(${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_DEPOSIT.getCode}
                      ,${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode}
                      ,${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@DEPOSIT_FAILED.getCode}
                      ,${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UN_NEED_TRANSFERRED.getCode}
                      )) a
    </select>

    <select id = "canWithdrawAmount" resultType="java.math.BigDecimal">
          select
            sum(settle_amount) settleAmount
          from member_seed_record
          where channel_id = #{channelId} and status in(
                                                        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode},
                                                        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode},
                                                        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}
              )
    </select>


    <select id="queryOrderMemberChannelList"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO">
        select msr.id,
        msr.channel_id,
        msr.channel_name,
        msr.channel_phone,
        msr.seed_code,
        msr.business_id,
        b.name businessName,
        msr.business_id,
        msr.biz_user_id,
        msr.biz_user_nick_name,
        msr.biz_user_phone,
        msr.member_code,
        msr.member_package_type,
        msr.real_pay_amount,
        msr.real_pay_amount_currency,
        msr.currency,
        msr.pay_type,
        msr.settle_type,
        msr.settle_rage,
        msr.settle_amount,
        msr.settle_amount as real_settle_amount,
        msrw.withdrawal_time as settle_time,
        msr.status as settle_status,
        msrw.resource_url as settle_resource_url,
        msrw.withdrawal_user_name as settle_user_name,
        msrw.withdrawal_user_id as settle_user_id,
        msrw.payout_time as payout_time,
        msr.create_time
        from member_seed_record msr
        left join business b on msr.business_id = b.id
        left join (
        select
        member_seed_record_id,
        max(member_seed_record_withdrawal_id) as member_seed_record_withdrawal_id
        from member_seed_record_relevance
        group by member_seed_record_id
        ) msrr on msrr.member_seed_record_id = msr.id
        left join member_seed_record_withdrawal msrw on msrr.member_seed_record_withdrawal_id = msrw.id
        <where>
            <if test="dto.isFiltrationZero != null and dto.isFiltrationZero == 1">and msr.settle_amount > 0</if>
            <if test="dto.memberPackageType != null and dto.memberPackageType != 3">
                and msr.member_package_type = #{dto.memberPackageType}
            </if>
            <if test="dto.memberPackageType != null and dto.memberPackageType == 3">
                and msr.member_package_type is null
            </if>
            <if test="dto.channelType != null">
                and msr.channel_type = #{dto.channelType}
            </if>

            <if test="dto.settleStatus != null and dto.settleStatus.size() > 0 ">
                AND msr.status IN
                <foreach collection="dto.settleStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.businessIds != null and dto.businessIds.size() > 0 ">
                AND msr.business_id IN
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.channelId != null and dto.channelId != ''">
                AND msr.channel_id = #{dto.channelId}
            </if>
            <if test="dto.channelIds != null and dto.channelIds.size() > 0">
                and msr.channel_id IN
                <foreach collection="dto.channelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                msr.channel_name LIKE concat('%', #{dto.keyword}, '%')
                OR msr.channel_phone LIKE concat('%', #{dto.keyword}, '%')
                OR msr.member_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                OR msr.biz_user_nick_name LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>
            <if test="dto.withdrawalTimeStart != null and dto.withdrawalTimeEnd != null">
                AND msrw.withdrawal_time BETWEEN #{dto.withdrawalTimeStart} AND #{dto.withdrawalTimeEnd} AND msrw.status != ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}
            </if>
        </where>
    </select>
</mapper>
