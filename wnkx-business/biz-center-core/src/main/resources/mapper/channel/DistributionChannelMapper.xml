<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.DistributionChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannel">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
        <result property="brokeRage" column="broke_rage" jdbcType="DECIMAL"/>
        <result property="seedCode" column="seed_code" jdbcType="VARCHAR"/>
        <result property="dedicatedLinkCode" column="dedicated_link_code" jdbcType="VARCHAR"/>
        <result property="weChatUrl" column="we_chat_url" jdbcType="VARCHAR"/>
        <result property="tagId" column="tag_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="queryChannelTable">
        distribution_channel dc
        LEFT JOIN (SELECT
        channel_id,
        sum(real_pay_amount) real_pay_amount,
        sum(CASE WHEN status in (${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}) THEN settle_amount ELSE 0.0 END ) un_settle_amount,
        sum( CASE WHEN status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) settle_amount,
        sum( CASE WHEN status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) real_settle_amount,
        count(1) member_num
        FROM
        member_seed_record
        where channel_type = ${@<EMAIL>} and status <![CDATA[ <> ]]> ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@DEPOSIT_FAILED.getCode}
        <if test="startTime != null and endTime != null">
            and create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY
        channel_id ) t ON t.channel_id = dc.id
        LEFT JOIN (SELECT
        seed_code,
        sum(real_pay_amount) real_pay_amount
        FROM
        distribution_channel_order
        <if test="startTime != null and endTime != null">
            where pay_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY
        seed_code) dco ON dco.seed_code = dc.seed_code
        left join (
        select
        count(*) register_num,
        register_channel_id channelId
        from biz_user_channel
        where
        register_channel_type = ${@<EMAIL>}
        <if test="startTime != null and endTime != null">
            and register_time BETWEEN #{startTime} AND #{endTime}
        </if>
        group by register_channel_id
        ) register ON register.channelId = dc.id
        left join (
        select
        count(*) wechat_num,
        channel_id
        from we_chat_external_user
        where
        channel_type = ${@<EMAIL>}
        <if test="startTime != null and endTime != null">
            and create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        group by channel_id
        ) wechat ON wechat.channel_id = dc.id
        left join (
        select
        sum(unique_visitor) unique_visitor,
        channel_id
        from distribution_channel_visit_flow
        <where>
            <if test="startTime != null and endTime != null">
                and create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        group by channel_id
        ) visit ON visit.channel_id = dc.id
    </sql>
    <sql id="queryFissionChannelTable">
        distribution_channel dc
        left join biz_user bu on bu.id = dc.biz_user_id
        LEFT JOIN (SELECT
        channel_id,
        sum( real_pay_amount ) real_pay_amount,
        sum( CASE WHEN status in (${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}) THEN settle_amount ELSE 0.0 END ) un_settle_amount,
        sum( CASE WHEN status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) settle_amount,
        sum( CASE WHEN status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) real_settle_amount,
        count(1) member_num
        FROM
        member_seed_record
        where channel_type = ${@<EMAIL>} and status <![CDATA[ <> ]]> ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@DEPOSIT_FAILED.getCode}
        <if test="startTime != null and endTime != null">
            and create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY
        channel_id ) t ON t.channel_id = dc.id
        LEFT JOIN (SELECT
        seed_code,
        sum(real_pay_amount) real_pay_amount
        FROM
        distribution_channel_order
        <if test="startTime != null and endTime != null">
            where pay_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY
        seed_code) dco ON dco.seed_code = dc.seed_code
        left join (
        select
        count(*) register_num,
        register_channel_id channelId
        from biz_user_channel
        where
        register_channel_type = ${@<EMAIL>}
        <if test="startTime != null and endTime != null">
            and register_time BETWEEN #{startTime} AND #{endTime}
        </if>
        group by register_channel_id
        ) register ON register.channelId = dc.id
        left join (
        select
        count(*) wechat_num,
        channel_id
        from we_chat_external_user
        where
        channel_type = ${@<EMAIL>}
        <if test="startTime != null and endTime != null">
            and create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        group by channel_id
        ) wechat ON wechat.channel_id = dc.id
    </sql>
    <sql id="sqlwhereSearch">
        <where>
            <if test="keyword != null and keyword != ''">
                and (UPPER(dc.channel_name) like concat('%',#{keyword}, '%')
                or UPPER(dc.poster_name) like concat('%', #{keyword}, '%')
                or dc.seed_code like concat('%', #{keyword}, '%')
                or dc.phone like concat('%', #{keyword}, '%'))
            </if>
            <if test="channelType != null">
                and dc.channel_type = #{channelType}
            </if>
            <if test="createId != null">
                and dc.create_id = #{createId}
            </if>
        </where>
    </sql>
    <sql id="sqlWhereFissionSearch">
        <where>
            <if test="keyword != null and keyword != ''">
                and (dc.seed_code like concat('%', #{keyword}, '%')
                <if test="channelIds != null and channelIds.size() > 0">
                    or dc.id IN
                    <foreach collection="channelIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="memberStatus != null and bizUserIds != null and bizUserIds.size() > 0">
                and dc.biz_user_id in
                <foreach collection="bizUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="channelType != null">
                and dc.channel_type = #{channelType}
            </if>
            <if test="channelStatus != null">
                and dc.status = #{channelStatus}
            </if>
           <if test="accountType != null">
                and bu.account_type = #{accountType}
            </if>
<!--            <if test="isSettle != null and isSettle == 1">-->
<!--                and t.settleStatus > 0-->
<!--            </if>-->
<!--            <if test="isSettle != null and isSettle == 0">-->
<!--                and (t.settleStatus = 0 or t.settleStatus is null)-->
<!--            </if>-->
            <if test="createId != null">
                and dc.create_id = #{createId}
            </if>
        </where>
    </sql>
    <select id="getByDistributionChannelId" resultType="com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelDetailVO">
        select
            dc.id,
            dc.channel_name,
            dc.poster_name,
            dc.biz_user_id,
            dc.phone,
            dc.remark,
            dc.settle_discount_type,
            dc.broke_rage,
            dc.create_time,
            dc.create_by,
            dc.seed_id,
            dc.status,
            dc.seed_code,
            dc.disable_time,
            dc.dedicated_link_code dedicatedLink,
            dc.we_chat_url
        from distribution_channel dc
        where dc.id = #{distributionChannelId}
    </select>
    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO">
        select dc.id,
               dc.seed_code,
        dc.channel_name,
        dc.poster_name,
        dc.STATUS,
        dc.phone,
        dc.settle_discount_type,
        dc.broke_rage,
        dc.remark,
        dc.create_time,
        dc.create_by,
--         if(t.settleStatus> 0, 1, 0) isSettle,
        dco.real_pay_amount,
        t.un_settle_amount,
        t.member_num,
        t.settle_amount,
        t.real_settle_amount,
        t.real_pay_amount memberOrderAmount,
        register.register_num,
        wechat.wechat_num addWeChatNum,
        visit.unique_visitor
        FROM
        <include refid="queryChannelTable"/>
        <include refid="sqlwhereSearch"/>
    </select>
    <select id="queryFissionChannelList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.FissionChannelVO">
        select dc.id,
        dc.seed_id,
        dc.seed_code,
        dc.biz_user_id,
        dc.channel_name,
        dc.channel_type,
        dc.STATUS,
        dc.disable_time,
        dc.phone,
        dc.broke_rage,
        dc.remark,
        dc.create_time,
        dc.create_by,
--         if(t.settleStatus> 0, 1, 0) isSettle,
        dco.real_pay_amount,
        t.un_settle_amount,
        t.member_num,
        t.settle_amount,
        t.real_settle_amount,
        register.register_num,
        wechat.wechat_num addWeChatNum
        FROM
        <include refid="queryFissionChannelTable"/>
        <include refid="sqlWhereFissionSearch"/>
    </select>
    <select id="distributionChannelStatistics" resultType="com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelStatisticsVO">
        select
            sum(register.register_num) registerNum,
            sum(t.member_num) memberNum,
            sum(wechat.wechat_num) addWeChatNum,
            sum(t.real_pay_amount) memberAmount
        FROM
        <include refid="queryChannelTable"/>
        <include refid="sqlwhereSearch"/>

    </select>

    <select id="fissionChannelStatistics" resultType="com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelStatisticsVO">
        select
        sum(register.register_num) registerNum,
        sum(t.member_num) memberNum,
        sum(wechat.wechat_num) addWeChatNum
        FROM
        <include refid="queryFissionChannelTable"/>
        <include refid="sqlWhereFissionSearch"/>
    </select>

    <select id="getMemberSeedRecordStatisticTotal" resultType="com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO">
        SELECT
        count(1) memberNum,
        sum( CASE WHEN t.status in (${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_WITHDRAWAL.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode},
        ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode}) THEN settle_amount ELSE 0.0 END ) unSettleAmount,
        sum( CASE WHEN t.status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) settleAmount,
        sum( CASE WHEN t.status = ${@<EMAIL>} THEN settle_amount ELSE 0.0 END ) realSettleAmount
        FROM
        member_seed_record t
        inner JOIN distribution_channel dc on t.channel_id = dc.id
        <where>
            dc.channel_type = #{channelType} and t.status <![CDATA[ <> ]]> ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@DEPOSIT_FAILED.getCode}
            <if test="startTime != null and endTime != null">
                and t.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="getChannelRegisterList" resultType="com.ruoyi.system.api.domain.vo.biz.channel.ChannelInviteVO">
            select bu.id bizUserId,
                bu.phone,
                bu.nick_name,
                bu.pic,
                buc.add_wechat_time,
                buc.wechat_channel_type,
                buc.register_time,
                buc.register_channel_type
            from distribution_channel dc
            inner join biz_user_channel buc on dc.id = buc.register_channel_id and buc.register_channel_type = dc.channel_type
            left join biz_user bu on bu.id = buc.biz_user_id
            where
                dc.id = #{id}
    </select>

    <select id="getDistributionChannelBySeedCode" resultType="com.ruoyi.system.api.domain.vo.biz.channel.DistributionChannelVO">
        select
            dc.id,
            dc.phone,
            dc.biz_user_id,
            dc.channel_type,
            dc.channel_name,
            dc.seed_code,
            dc.seed_id,
            dc.dedicated_link_code,
            dc.we_chat_url,
            dc.tag_id,
            dc.settle_discount_type,
            dc.broke_rage
        from distribution_channel dc
        where dc.seed_code = #{seedCode} AND dc.status = 0
    </select>


    <select id="createUserList" resultType="com.ruoyi.system.api.domain.vo.SysUserVO">
        select
               distinct
        create_id userId,
        create_by userName
        from distribution_channel
        <where>
            channel_type = ${@<EMAIL>}
            <if test="name != null and name != ''">
                create_by like CONCAT('%',#{name},'%')
            </if>
        </where>
    </select>
</mapper>
