<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.OrderMemberChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.order.OrderMemberChannel">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="channelName" column="channel_name" jdbcType="VARCHAR"/>
        <result property="channelPhone" column="channel_phone" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="businessName" column="business_name" jdbcType="VARCHAR"/>
        <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
        <result property="bizUserNickName" column="biz_user_nick_name" jdbcType="VARCHAR"/>
        <result property="bizUserPhone" column="biz_user_phone" jdbcType="VARCHAR"/>
        <result property="memberCode" column="member_code" jdbcType="VARCHAR"/>
        <result property="memberPackageType" column="member_package_type" jdbcType="INTEGER"/>
        <result property="settleRage" column="settle_rage" jdbcType="DECIMAL"/>
        <result property="settleAmount" column="settle_amount" jdbcType="DECIMAL"/>
        <result property="settleTime" column="settle_time" jdbcType="TIMESTAMP"/>
        <result property="settleStatus" column="settle_status" jdbcType="INTEGER"/>
        <result property="settleResourceUrl" column="settle_resource_url" jdbcType="VARCHAR"/>
        <result property="settleUserName" column="settle_user_name" jdbcType="VARCHAR"/>
        <result property="settleUserId" column="settle_user_id" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        select omc.id,
               omc.channel_id,
               omc.channel_name,
               omc.channel_phone,
               omc.seed_code,
               omc.business_id,
               b.name businessName,
               omc.business_id,
               omc.biz_user_id,
               omc.biz_user_nick_name,
               omc.biz_user_phone,
               omc.member_code,
               omc.member_package_type,
               omc.real_pay_amount,
               omc.real_pay_amount_currency,
               omc.currency,
               omc.pay_type,
               omc.settle_rage,
               omc.real_settle_amount,
               omc.settle_amount,
               omc.settle_time,
               omc.settle_status,
               omc.settle_resource_url,
               omc.settle_user_name,
               omc.settle_user_id,
               omc.remark,
               omc.create_time
        from order_member_channel omc
                 left join business b on b.id = omc.business_id
    </sql>
    <select id="memberChannelListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderMemberChannelListVO">
        <include refid="Base_Column_List"/>
        <where>
            <if test="dto.isFiltrationZero != null and dto.isFiltrationZero == 1">and omc.settle_amount > 0</if>

            <if test="dto.memberPackageType != null and dto.memberPackageType != 3">
                and omc.member_package_type = #{dto.memberPackageType}
            </if>
            <if test="dto.memberPackageType != null and dto.memberPackageType == 3">
                and omc.member_package_type is null
            </if>
            <if test="dto.channelType != null">
                and omc.channel_type = #{dto.channelType}
            </if>

            <if test="dto.settleStatus != null and dto.settleStatus.size() > 0 ">
                AND omc.settle_status IN
                <foreach collection="dto.settleStatus" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.businessIds != null and dto.businessIds.size() > 0 ">
                AND omc.business_id IN
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.channelId != null and dto.channelId != ''">
                AND omc.channel_id = #{dto.channelId}
            </if>
            <if test="dto.channelIds != null and dto.channelIds.size() > 0">
                and omc.channel_id IN
                <foreach collection="dto.channelIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                omc.channel_id LIKE concat('%', #{dto.keyword}, '%')
                OR omc.channel_name LIKE concat('%', #{dto.keyword}, '%')
                OR omc.channel_phone LIKE concat('%', #{dto.keyword}, '%')
                OR omc.member_code LIKE concat('%', UCASE(#{dto.keyword}), '%')
                OR omc.biz_user_nick_name LIKE concat('%', #{dto.keyword}, '%')
                OR b.name LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <select id="getMemberChannelStatisticTotal" resultType="com.ruoyi.system.api.domain.vo.order.OrderMemberChannelStatisticVO">
        SELECT
            count( 1 ) memberNum,
            sum( CASE WHEN t.settle_status = 1 THEN t.real_settle_amount ELSE 0.0 END ) realSettleAmount,
            sum( CASE WHEN t.settle_status = 1 THEN t.settle_amount ELSE 0.0 END ) settleAmount,
            sum( CASE WHEN t.settle_status = 0 THEN t.settle_amount ELSE 0.0 END ) unSettleAmount
        FROM
            order_member_channel t
        inner JOIN distribution_channel dc on t.channel_id = dc.id
        <where>
            dc.channel_type = #{channelType}
            <if test="startTime != null and endTime != null">
                and t.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
    <select id="memberChannelDetailById"
            resultType="com.ruoyi.system.api.domain.entity.order.OrderMemberChannel">
        <include refid="Base_Column_List"/>
        where omc.id = #{id}
    </select>
</mapper>
