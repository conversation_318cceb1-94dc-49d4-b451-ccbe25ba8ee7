<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.MemberSeedRecordRelevanceMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordRelevance">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="memberSeedRecordId" column="member_seed_record_id" jdbcType="BIGINT"/>
            <result property="memberSeedRecordWithdrawalId" column="member_seed_record_withdrawal_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,member_seed_record_id,member_seed_record_withdrawal_id,
        create_time
    </sql>


    <select id="getMemberSeedRecordIdsByWithdrawalIds" resultType="java.lang.Long">
        select
        msrr.member_seed_record_id
        from member_seed_record_relevance msrr
        where msrr.member_seed_record_withdrawal_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>
</mapper>
