<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.BusinessChannelMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.BusinessChannel">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="bizUserId" column="biz_user_id" jdbcType="BIGINT"/>
            <result property="registerChannelType" column="register_channel_type" jdbcType="BOOLEAN"/>
            <result property="registerChannelId" column="register_channel_id" jdbcType="BIGINT"/>
            <result property="registerTime" column="register_time" jdbcType="TIMESTAMP"/>
            <result property="wechatChannelType" column="wechat_channel_type" jdbcType="BOOLEAN"/>
            <result property="wechatChannelId" column="wechat_channel_id" jdbcType="BIGINT"/>
            <result property="addWechatTime" column="add_wechat_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,business_id,biz_user_id,
        register_channel_type,register_channel_id,register_time,
        wechat_channel_type,wechat_channel_id,add_wechat_time
    </sql>

    <select id="selectBusinessChannelByBusinessId" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO">
        SELECT
        bc.register_channel_type,
        bc.wechat_channel_type,
        bc.wechat_channel_id,
        bc.business_id,
        bc.add_wechat_time
        FROM business_channel bc
        <where>
            <if test="dto.ids != null and dto.ids.size() > 0">
                bc.business_id IN
                <foreach collection="dto.ids" item="item" index="index"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
