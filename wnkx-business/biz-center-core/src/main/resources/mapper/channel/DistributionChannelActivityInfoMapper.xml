<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.DistributionChannelActivityInfoMapper">


    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.DistributionChannelActivityInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="activityId" column="activity_id" jdbcType="BIGINT"/>
        <result property="channelId" column="channel_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,activity_id,channel_id,
        create_time,update_time
    </sql>
    <select id="getActivityChannelInfo"
            resultType="com.ruoyi.system.api.domain.dto.biz.channel.ChannelActivityInfoDTO">
        SELECT id, channel_name,poster_name, broke_rage,seed_code,we_chat_url,`status`
        from distribution_channel dc
        <where>
            dc.channel_type = ${@<EMAIL>}
            <if test="channelName != null and channelName != ''">
                and channel_name like concat('%',#{channelName},'%')
            </if>
            <if test="activityId !=null ">
                and id in (select channel_id from distribution_channel_activity_info dcai WHERE activity_id = #{activityId})
            </if>
            <if test="!showDisableChannel">
                and status = 0
            </if>
        </where> order by id desc
    </select>
</mapper>