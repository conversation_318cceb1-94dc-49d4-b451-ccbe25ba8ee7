<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.channel.mapper.MemberSeedRecordWithdrawalMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.channel.member.MemberSeedRecordWithdrawal">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="withdrawalNum" column="withdrawal_num" jdbcType="VARCHAR"/>
            <result property="channelSeedId" column="channel_seed_id" jdbcType="VARCHAR"/>
            <result property="settleAmount" column="settle_amount" jdbcType="DECIMAL"/>
            <result property="withdrawalAccountType" column="withdrawal_account_type" jdbcType="BOOLEAN"/>
            <result property="payeeName" column="payee_name" jdbcType="VARCHAR"/>
            <result property="payeePhone" column="payee_phone" jdbcType="VARCHAR"/>
            <result property="payeeIdentityCard" column="payee_identity_card" jdbcType="VARCHAR"/>
            <result property="payeeAccount" column="payee_account" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="BOOLEAN"/>
            <result property="auditTime" column="audit_time" jdbcType="TIMESTAMP"/>
            <result property="auditRemark" column="audit_remark" jdbcType="VARCHAR"/>
            <result property="auditUserId" column="audit_user_id" jdbcType="BIGINT"/>
            <result property="auditUserName" column="audit_user_name" jdbcType="VARCHAR"/>
            <result property="withdrawalTime" column="withdrawal_time" jdbcType="TIMESTAMP"/>
            <result property="withdrawalRemark" column="withdrawal_remark" jdbcType="VARCHAR"/>
            <result property="withdrawalUserId" column="withdrawal_user_id" jdbcType="BIGINT"/>
            <result property="withdrawalUserName" column="withdrawal_user_name" jdbcType="VARCHAR"/>
            <result property="payAccount" column="pay_account" jdbcType="VARCHAR"/>
            <result property="resourceUrl" column="resource_url" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="sqlwhereSearch">
        msrw.settle_amount > 0
        <if test="dto.keyword != null and dto.keyword != '' ">
            AND (
            msr.order_num LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
            OR msrw.withdrawal_num LIKE CONCAT('%', UCASE(#{dto.keyword}), '%')
            OR msrw.channel_seed_id LIKE CONCAT('%', #{dto.keyword}, '%')
            OR msr.channel_name LIKE CONCAT('%', #{dto.keyword}, '%')
            OR msr.member_code = #{dto.keyword}
            <if test="dto.keywordBizUserIds != null and dto.keywordBizUserIds.size() >0">
                or msr.channel_biz_user_id in
                <foreach collection="dto.keywordBizUserIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        <if test="dto.memberSeedRecordWithdrawalStatus != null">and msrw.status = #{dto.memberSeedRecordWithdrawalStatus}</if>
        <if test="dto.withdrawalAccountType != null">and msrw.withdrawal_account_type = #{dto.withdrawalAccountType}</if>
        <if test="dto.channelType != null">and msr.channel_type = #{dto.channelType}</if>
        <if test="dto.memberSeedRecordWithdrawalStatusList != null and dto.memberSeedRecordWithdrawalStatusList.size() >0 ">and msrw.status in
            <foreach item="item" index="index" collection="dto.memberSeedRecordWithdrawalStatusList" open="(" separator="," close=")">
                #{item}
            </foreach></if>
        <if test="dto.startTime != null and dto.endTime != null">and msrw.create_time BETWEEN #{dto.startTime} AND #{dto.endTime}</if>
        <if test="dto.memberPackageType != null">and msr.member_package_type = #{dto.memberPackageType}</if>
    </sql>

    <select id="queryMemberSeedRecordWithdrawalListByChannelId" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordWithdrawalVO">
        SELECT
            *
        from member_seed_record_withdrawal
        where channel_id = #{channelId}
    </select>

    <select id = "getMemberSeedRecordWithdrawalDetail"  resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordWithdrawalVO">
        SELECT
            msrw.*,
            dc.channel_name,
            dc.seed_code,
            dc.poster_name
        from member_seed_record_withdrawal msrw
                 inner join distribution_channel dc on dc.id = msrw.channel_id
        where msrw.id = #{id}
    </select>
    <select id="queryMemberSeedRecordWithdrawalList"
            resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordWithdrawalVO">
        select
        msrw.*,
        group_concat(msr.order_num) as orderNumStr,
        max(msr.channel_name) as channelName,
        sum(msr.settle_amount) settleAmountGroup
        from member_seed_record_withdrawal msrw
        inner join member_seed_record_relevance msrr on msrr.member_seed_record_withdrawal_id = msrw.id
        inner join member_seed_record msr on msrr.member_seed_record_id = msr.id
        <where>
            <include refid="sqlwhereSearch"/>
        </where>
        group by msrw.id
    </select>
    <select id="queryMemberSeedRecordWithdrawalListExport" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.MemberSeedRecordWithdrawalVO">
        select
            msrw.*,
            msr.order_num,
            msr.member_code,
            msr.create_time as recordCreateTime,
            msr.settle_type,
            msr.channel_name,
            msr.settle_rage,
            msr.settle_amount as orderSettleAmount
        from member_seed_record_withdrawal msrw
                 inner join member_seed_record_relevance msrr on msrr.member_seed_record_withdrawal_id = msrw.id
                 inner join member_seed_record msr on msrr.member_seed_record_id = msr.id

        <where>
            <include refid="sqlwhereSearch"/>

        </where>
    </select>
    <select id="queryFissionSettleRecordList"
            resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionSettleRecordVO">
        select
            msrw.*,
            msr.order_num orderNumStr,
            b.name inviteBusinessName,
            msr.member_code inviteMemberCode,
            msrr.id relevanceId,
            msr.biz_user_nick_name inviteBizUserNickName,
            msr.settle_type settleType,
            msr.settle_rage settleRage,
            msr.channel_name channelName,
            msr.settle_amount orderSettleAmount
        from member_seed_record_withdrawal msrw
        inner join member_seed_record_relevance msrr on msrr.member_seed_record_withdrawal_id = msrw.id
        inner join member_seed_record msr on msrr.member_seed_record_id = msr.id
        left join business b on b.id = msr.business_id
        <where>
            msrw.status = ${@<EMAIL>}
            <if test="dto.keyword != null and dto.keyword != '' ">
                AND (
                msr.order_num LIKE CONCAT('%', #{dto.keyword}, '%')
                OR UCASE(b.name) LIKE CONCAT('%', #{dto.keyword}, '%')
                OR msr.member_code LIKE CONCAT('%', #{dto.keyword}, '%')
                OR UCASE(msr.biz_user_nick_name) LIKE CONCAT('%', #{dto.keyword}, '%')
                OR msrw.withdrawal_num LIKE CONCAT('%', #{dto.keyword}, '%')
                OR msrw.channel_seed_id LIKE CONCAT('%', #{dto.keyword}, '%')
                OR UCASE(msr.channel_name) LIKE CONCAT('%', #{dto.keyword}, '%')
                <if test="dto.keywordBizUserIds != null and dto.keywordBizUserIds.size() >0 != 0">
                    or msr.channel_biz_user_id in
                    <foreach collection="dto.keywordBizUserIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )
            </if>
            <if test="dto.settleType != null">and msr.settle_type = #{dto.settleType}</if>
            <if test="dto.withdrawalAccountType != null">and msrw.withdrawal_account_type = #{dto.withdrawalAccountType}</if>
            <if test="dto.channelType != null">and msr.channel_type = #{dto.channelType}</if>
            <if test="dto.memberSeedRecordWithdrawalStatus != null">and msrw.status = #{dto.memberSeedRecordWithdrawalStatus}</if>
            <if test="dto.startTime != null and dto.endTime != null">and msrw.create_time BETWEEN #{dto.startTime} AND #{dto.endTime}</if>
            <if test="dto.settleStartTime != null and dto.settleEndTime != null">and msrw.withdrawal_time BETWEEN #{dto.settleStartTime} AND #{dto.settleEndTime}</if>
            <if test="dto.memberPackageType != null">and msr.member_package_type = #{dto.memberPackageType}</if>
        </where>
    </select>

    <select id="getFissionCountStatisticsVO" resultType="com.ruoyi.system.api.domain.vo.biz.channel.fission.FissionCountStatisticsVO">
        select
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@UNDER_REVIEW.getCode} THEN 1 ELSE 0 END) underReviewCount,
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@PENDING_TRANSFER.getCode} THEN 1 ELSE 0 END) pendingTransferCount,
            sum(CASE WHEN status = ${@<EMAIL>} THEN 1 ELSE 0 END) withdrawSuccessCount,
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@REVIEW_REJECTED.getCode} THEN 1 ELSE 0 END) reviewRejectedCount,
            sum(CASE WHEN status = ${@com.ruoyi.common.core.enums.MemberSeedRecordStatusEnum@TRANSFER_EXCEPTION.getCode} THEN 1 ELSE 0 END) transferExceptionCount,
            count(1) totalCount
        from member_seed_record_withdrawal
        where settle_amount > 0
    </select>
</mapper>
