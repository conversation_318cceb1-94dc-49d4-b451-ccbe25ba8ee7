SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int(5) DEFAULT NULL,
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `create_time` datetime(0) DEFAULT NULL,
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `update_time` datetime(0) DEFAULT NULL,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-05-13 10:03:01', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-05-13 10:03:01', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-light', 'Y', 'admin', '2024-05-13 10:03:01', 'admin', '2024-07-06 09:50:12', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-05-13 10:03:01', '', NULL, '是否开启注册用户功能（true开启，false关闭）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '部门名称',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '部门表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '润一', 0, '老板', '15888888888', '<EMAIL>', '0', NULL, '0', 'admin', '2024-06-07 18:49:43', 'admin', '2024-07-16 19:05:25');
INSERT INTO `sys_dept` VALUES (101, 100, '0,100', '厦门分公司', 1, '老板', '15888888888', '<EMAIL>', '0', NULL, '0', 'admin', '2024-06-07 18:49:43', 'admin', '2024-07-16 19:05:31');

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int(4) DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字典数据表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (100, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (101, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (102, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (103, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (104, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (105, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (106, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (107, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (108, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (109, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (110, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (111, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (112, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (113, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (114, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (115, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (116, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (117, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (118, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (119, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (120, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (121, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (122, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (123, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (124, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (125, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (126, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (127, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-05-13 10:03:00', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (128, 6, '英国', '1', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:07', 'admin', '2024-07-11 11:53:15', NULL);
INSERT INTO `sys_dict_data` VALUES (129, 1, '加拿大', '2', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:18', 'admin', '2024-05-22 14:54:23', NULL);
INSERT INTO `sys_dict_data` VALUES (130, 2, '德国', '3', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:29', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (131, 3, '法国', '4', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:37', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (132, 4, '意大利', '5', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (133, 5, '西班牙', '6', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:50', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (134, 0, '美国', '7', 'biz_nation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 14:54:58', 'admin', '2024-07-11 11:53:04', NULL);
INSERT INTO `sys_dict_data` VALUES (135, 0, '启用', '0', 'biz_status', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:09:44', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (136, 0, '禁用', '1', 'biz_status', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:09:52', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (137, 0, 'Amazon', '0', 'biz_model_platform', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:11:01', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (138, 0, 'TikTok', '1', 'biz_model_platform', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:11:10', 'admin', '2024-07-11 11:53:59', NULL);
INSERT INTO `sys_dict_data` VALUES (139, 1, '影响者', '0', 'biz_model_type', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:11:56', 'admin', '2024-07-11 11:52:13', NULL);
INSERT INTO `sys_dict_data` VALUES (140, 0, '素人', '1', 'biz_model_type', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:12:01', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (141, 0, '都属于', '3', 'biz_model_type', NULL, 'default', 'N', '1', 'admin', '2024-05-22 15:12:07', 'admin', '2024-05-24 16:42:55', NULL);
INSERT INTO `sys_dict_data` VALUES (142, 0, '一般模特', '0', 'biz_model_cooperation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:13:42', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (143, 1, '优质模特', '1', 'biz_model_cooperation', NULL, 'default', 'N', '0', 'admin', '2024-05-22 15:13:47', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (144, 0, '模特ID', 'modelId', 'biz_model_option', NULL, 'default', 'N', '0', 'admin', '2024-05-23 19:01:28', 'admin', '2024-07-09 18:56:52', '模特ID');
INSERT INTO `sys_dict_data` VALUES (145, 1, '模特姓名', 'name', 'biz_model_option', NULL, 'default', 'N', '0', 'admin', '2024-05-23 19:01:49', '', NULL, '模特姓名');
INSERT INTO `sys_dict_data` VALUES (146, 1, '其他', '2', 'biz_model_platform', NULL, 'default', 'N', '0', 'admin', '2024-05-27 10:42:01', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (147, 0, '婴幼儿', '1', 'biz_model_ageGroup', NULL, 'default', 'N', '0', 'admin', '2024-05-28 15:58:54', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (148, 1, '儿童', '2', 'biz_model_ageGroup', NULL, 'default', 'N', '0', 'admin', '2024-05-28 15:59:03', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (149, 2, '成年人', '3', 'biz_model_ageGroup', NULL, 'default', 'N', '0', 'admin', '2024-05-28 15:59:14', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (150, 3, '老年人', '4', 'biz_model_ageGroup', NULL, 'default', 'N', '0', 'admin', '2024-05-28 15:59:25', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (151, 0, '正常', '0', 'biz_model_status', NULL, 'default', 'N', '0', 'admin', '2024-05-28 16:11:56', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (152, 1, '暂停', '1', 'biz_model_status', NULL, 'default', 'N', '0', 'admin', '2024-05-28 16:12:03', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (153, 2, '取消合作', '3', 'biz_model_status', NULL, 'default', 'N', '0', 'admin', '2024-05-28 16:12:09', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '字典类型表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (100, '用户性别', 'sys_user_sex', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (101, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (102, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (103, '任务状态', 'sys_job_status', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (104, '任务分组', 'sys_job_group', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (105, '系统是否', 'sys_yes_no', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (106, '通知类型', 'sys_notice_type', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (107, '通知状态', 'sys_notice_status', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (108, '操作类型', 'sys_oper_type', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (109, '系统状态', 'sys_common_status', '0', 'admin', '2024-05-13 10:02:59', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (110, '国家', 'biz_nation', '0', 'admin', '2024-05-22 14:46:29', '', NULL, '国家字典');
INSERT INTO `sys_dict_type` VALUES (111, '启用禁用状态', 'biz_status', '0', 'admin', '2024-05-22 15:08:10', 'admin', '2024-05-22 15:09:35', '启用禁用状态');
INSERT INTO `sys_dict_type` VALUES (112, '模特合作平台', 'biz_model_platform', '0', 'admin', '2024-05-22 15:10:51', 'admin', '2024-05-22 15:13:06', '模特合作平台');
INSERT INTO `sys_dict_type` VALUES (113, '模特类型', 'biz_model_type', '0', 'admin', '2024-05-22 15:11:43', '', NULL, '模特类型');
INSERT INTO `sys_dict_type` VALUES (114, '模特合作深度', 'biz_model_cooperation', '0', 'admin', '2024-05-22 15:13:23', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (115, '模特列表搜索下拉框', 'biz_model_option', '0', 'admin', '2024-05-23 19:00:41', '', NULL, '模特列表搜索下拉框');
INSERT INTO `sys_dict_type` VALUES (116, '模特年龄层', 'biz_model_ageGroup', '0', 'admin', '2024-05-28 15:58:21', '', NULL, '模特年龄层');
INSERT INTO `sys_dict_type` VALUES (117, '模特状态', 'biz_model_status', '0', 'admin', '2024-05-28 16:11:41', '', NULL, '模特状态');

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '用户账号',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '账号类型（0=内部端,1=用户端，2=模特端）',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '登录IP地址',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '提示信息',
  `access_time` datetime(0) DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '系统访问记录' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '菜单名称',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '路由参数',
  `is_frame` int(1) DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int(1) DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32082 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '菜单权限表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-05-13 10:02:51', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-05-13 10:02:51', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-05-13 10:02:51', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '账号管理', 1, 1, 'user', 'system/user/index', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-05-13 10:02:51', 'admin', '2024-05-20 15:49:42', '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-05-13 10:02:51', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-05-13 10:02:51', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '组织结构管理', 1, 4, 'dept', 'system/dept/index', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-05-13 10:02:51', 'admin', '2024-05-20 18:08:45', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-05-13 10:02:51', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-05-13 10:02:51', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2024-05-13 10:02:51', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', 1, 0, 'C', '0', '1', 'system:notice:list', 'message', 'admin', '2024-05-13 10:02:51', 'admin', '2024-07-03 16:11:00', '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-05-13 10:02:51', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-05-13 10:02:51', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (114, '表单构建', 3, 1, 'build', 'tool/build/index', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-05-13 10:02:51', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (115, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2024-05-13 10:02:51', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'system/operlog/index', '', 1, 0, 'C', '0', '0', 'system:operlog:list', 'form', 'admin', '2024-05-13 10:02:51', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'system/logininfor/index', '', 1, 0, 'C', '0', '0', 'system:logininfor:list', 'logininfor', 'admin', '2024-05-13 10:02:51', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1001, '用户查询', 100, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-05-13 10:02:51', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户新增', 100, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-05-13 10:02:51', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户修改', 100, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户删除', 100, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导出', 100, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '用户导入', 100, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '重置密码', 100, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色查询', 101, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色新增', 101, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色修改', 101, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色删除', 101, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '角色导出', 101, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单查询', 102, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单新增', 102, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单修改', 102, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '菜单删除', 102, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门查询', 103, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门新增', 103, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门修改', 103, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '部门删除', 103, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位查询', 104, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位新增', 104, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位修改', 104, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位删除', 104, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '岗位导出', 104, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典查询', 105, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典新增', 105, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典修改', 105, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典删除', 105, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '字典导出', 105, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数查询', 106, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数新增', 106, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数修改', 106, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数删除', 106, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '参数导出', 106, 5, '#', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告查询', 107, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告新增', 107, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告修改', 107, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-05-13 10:02:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '公告删除', 107, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作查询', 500, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:query', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '操作删除', 500, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:remove', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '日志导出', 500, 4, '#', '', '', 1, 0, 'F', '0', '0', 'system:operlog:export', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录查询', 501, 1, '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:query', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '登录删除', 501, 2, '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:remove', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '日志导出', 501, 3, '#', '', '', 1, 0, 'F', '0', '0', 'system:logininfor:export', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 115, 1, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 115, 2, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 115, 3, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 115, 2, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 115, 4, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 115, 5, '#', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-05-13 10:02:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32000, '模特管理', 0, 5, 'model', NULL, NULL, 1, 0, 'M', '0', '0', '', 'user', 'admin', '2024-05-21 11:23:09', 'admin', '2024-06-20 11:40:44', '');
INSERT INTO `sys_menu` VALUES (32001, '模特列表', 32000, 0, 'modelManage', 'model/modelManage/index', NULL, 1, 1, 'C', '0', '0', 'model:manage:list', 'peoples', 'admin', '2024-05-21 11:25:10', 'admin', '2024-06-20 11:41:08', '');
INSERT INTO `sys_menu` VALUES (32002, '新增', 32001, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'model:manage:add', '#', 'admin', '2024-05-21 14:13:57', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32003, '修改', 32001, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'model:manage:edit', '#', 'admin', '2024-05-21 14:14:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32004, '导出', 32001, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'model:manage:export', '#', 'admin', '2024-05-21 14:14:55', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32007, '订单管理', 0, 8, 'order', NULL, NULL, 1, 0, 'M', '0', '0', '', 'list', 'admin', '2024-06-07 10:00:01', 'admin', '2024-07-15 15:37:20', '');
INSERT INTO `sys_menu` VALUES (32010, '财务管理', 0, 9, '/finance', NULL, NULL, 1, 0, 'M', '0', '0', '', 'money', 'admin', '2024-06-15 09:28:31', 'admin', '2024-07-02 18:54:23', '');
INSERT INTO `sys_menu` VALUES (32011, '应收审批', 32010, 1, 'receivableApprove', 'finance/receivableApprove/index', NULL, 1, 1, 'C', '0', '0', 'finance:receivable:approve', 'form', 'admin', '2024-06-15 09:34:43', 'admin', '2024-07-02 18:54:32', '');
INSERT INTO `sys_menu` VALUES (32012, '发票管理', 32010, 2, 'invoice', 'finance/invoice/index', NULL, 1, 1, 'C', '0', '0', 'finance:invoice:list', 'clipboard', 'admin', '2024-06-15 09:36:49', 'admin', '2024-07-02 18:54:40', '');
INSERT INTO `sys_menu` VALUES (32013, '审核', 32011, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'receivable:approve:audit', '#', 'admin', '2024-06-15 09:40:48', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32014, '确认发票', 32012, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:invoice:edit', '#', 'admin', '2024-06-18 17:04:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32015, '上传发票', 32012, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:invoice:upload', '#', 'admin', '2024-06-18 17:04:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32016, '导出', 32012, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:invoice:export', '#', 'admin', '2024-06-18 17:45:54', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32017, '商家管理', 0, 7, 'merchant', NULL, NULL, 1, 0, 'M', '0', '0', '', 'user', 'admin', '2024-06-19 18:02:32', 'admin', '2024-07-03 11:57:26', '');
INSERT INTO `sys_menu` VALUES (32018, '商家列表', 32017, 1, 'info/list', 'merchant/list/index', NULL, 1, 1, 'C', '0', '0', 'merchant:manage:list', 'peoples', 'admin', '2024-06-19 18:04:23', 'admin', '2024-07-03 18:58:34', '');
INSERT INTO `sys_menu` VALUES (32019, '新增', 32018, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'merchant:list:add', '#', 'admin', '2024-06-19 18:05:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32020, '编辑', 32018, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'merchant:list:edit', '#', 'admin', '2024-06-19 18:05:53', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32021, '订单收支明细', 32010, 3, 'incomeInfo', 'finance/incomeInfo/index', NULL, 1, 1, 'C', '0', '0', 'finance:incomeInfo:list', 'money', 'admin', '2024-06-20 09:49:31', 'admin', '2024-07-03 09:54:13', '');
INSERT INTO `sys_menu` VALUES (32022, '退款审批', 32010, 4, 'refundApproval', 'finance/refundApproval/index', NULL, 1, 1, 'C', '0', '0', 'finance:refundApproval:list', 'skill', 'admin', '2024-06-21 11:40:24', 'admin', '2024-07-02 18:54:52', '');
INSERT INTO `sys_menu` VALUES (32024, 'demo', 3, 4, 'demo', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'code', 'admin', '2024-06-24 11:13:40', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32025, '裁剪弹窗案例', 32024, 0, 'demo/cropper-dialog', 'demo/cropper-demo', NULL, 1, 1, 'C', '0', '0', 'demo:cropper:dialog', 'code', 'admin', '2024-06-24 11:15:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32026, '商家余额', 32017, 2, 'balance', 'merchant/balance/index', NULL, 1, 1, 'C', '0', '0', 'merchant:balance:list', 'money', 'admin', '2024-06-25 10:24:51', 'admin', '2024-07-03 11:46:18', '');
INSERT INTO `sys_menu` VALUES (32027, '任务单', 0, 10, '/task', NULL, NULL, 1, 0, 'M', '0', '0', '', 'skill', 'admin', '2024-06-25 11:40:49', 'admin', '2024-07-02 11:05:11', '');
INSERT INTO `sys_menu` VALUES (32028, '工单列表', 32027, 1, 'workOrder', 'task/index', NULL, 1, 1, 'C', '0', '0', 'task:list', 'list', 'admin', '2024-06-25 11:43:46', 'admin', '2024-07-15 15:45:03', '');
INSERT INTO `sys_menu` VALUES (32029, '导出', 32021, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:orderRevenue:export', '#', 'admin', '2024-06-25 15:15:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32030, '导出', 32022, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:refundApproval:export', '#', 'admin', '2024-06-25 15:19:26', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32032, '导出', 32026, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'merchant:balance:export', '#', 'admin', '2024-06-25 15:29:27', 'admin', '2024-06-25 15:29:47', '');
INSERT INTO `sys_menu` VALUES (32036, '标签管理', 0, 6, 'label/index', 'label/index', NULL, 1, 1, 'C', '0', '0', 'tag:tag:list', 'date', 'admin', '2024-06-25 22:45:36', 'dwy', '2024-07-15 18:20:23', '');
INSERT INTO `sys_menu` VALUES (32037, '新增一级标签', 32036, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'label:add', '#', 'admin', '2024-06-25 22:46:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32039, '订单列表', 32007, 0, 'list', 'order/list/index', NULL, 1, 0, 'C', '0', '0', 'order:manage:list', 'form', 'admin', '2024-07-02 15:35:03', 'admin', '2024-07-15 15:19:24', '');
INSERT INTO `sys_menu` VALUES (32040, '批量提交预选模特', 32039, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:batch-submit', '#', 'admin', '2024-07-02 17:41:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32041, '取消订单', 32039, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:cancel', '#', 'admin', '2024-07-02 17:43:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32042, '去审核', 32039, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:audit', '#', 'admin', '2024-07-02 17:50:56', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32043, '申请退款', 32039, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:refund', '#', 'admin', '2024-07-02 17:51:20', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32044, '预选管理', 32039, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:preselection', '#', 'admin', '2024-07-02 17:52:30', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32045, '确认提交', 32039, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:submit', '#', 'admin', '2024-07-02 17:53:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32046, '匹配反馈情况', 32039, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:case', '#', 'admin', '2024-07-02 17:55:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32047, '标记订单', 32039, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:flag', '#', 'admin', '2024-07-02 17:56:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32048, '填写物流单号', 32039, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:shipments', '#', 'admin', '2024-07-02 17:57:25', 'admin', '2024-07-02 18:44:53', '');
INSERT INTO `sys_menu` VALUES (32049, '反馈素材', 32039, 9, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:feedbackMaterial', '#', 'admin', '2024-07-02 18:06:51', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32050, '下载反馈素材', 32039, 10, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:feedbackMaterial-download', '#', 'admin', '2024-07-02 18:07:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32051, '驳回反馈素材', 32039, 11, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:feedbackMaterial-reject', '#', 'admin', '2024-07-02 18:08:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32052, '创建工单', 32039, 12, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:create-work-order', '#', 'admin', '2024-07-02 18:09:49', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32053, '上传视频', 32039, 13, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:upload', '#', 'admin', '2024-07-02 18:10:44', 'admin', '2024-07-02 18:44:11', '');
INSERT INTO `sys_menu` VALUES (32054, '上传产品图', 32039, 14, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:upload-productPic', '#', 'admin', '2024-07-03 14:52:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32055, '导出', 32039, 15, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:export', '#', 'admin', '2024-07-06 14:13:45', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32056, '备注', 32039, 16, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:remark', '#', 'admin', '2024-07-06 16:01:01', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32057, '会员订单列表', 32007, 1, 'member', 'order/vip/index', NULL, 1, 1, 'C', '0', '0', 'order:vip:list', 'form', 'admin', '2024-07-06 18:14:36', 'admin', '2024-07-15 15:19:29', '');
INSERT INTO `sys_menu` VALUES (32058, '取消订单', 32057, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:member:cancel', '#', 'admin', '2024-07-08 11:40:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32059, '订单详情', 32007, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:details', '#', 'admin', '2024-07-08 11:47:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32060, '模特反馈素材', 32039, 10, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:modelFeedbackMaterial', '#', 'admin', '2024-07-11 19:41:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32061, '案例管理', 0, 11, 'case', NULL, NULL, 1, 0, 'M', '0', '0', '', 'table', 'admin', '2024-07-15 13:36:27', 'admin', '2024-07-15 13:36:55', '');
INSERT INTO `sys_menu` VALUES (32062, '视频管理', 32061, 1, 'video', 'case/video/index', NULL, 1, 1, 'C', '0', '0', 'case:video:list', 'client', 'admin', '2024-07-15 13:41:06', 'admin', '2024-07-15 15:19:47', '');
INSERT INTO `sys_menu` VALUES (32063, '分组管理', 32061, 2, 'group', 'case/group/index', NULL, 1, 1, 'C', '0', '0', 'case:group:list', 'tree', 'admin', '2024-07-15 13:42:11', 'admin', '2024-07-15 15:19:52', '');
INSERT INTO `sys_menu` VALUES (32064, '编辑', 32062, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:video:edit', '#', 'admin', '2024-07-15 14:54:26', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32065, '删除', 32062, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:video:delete', '#', 'admin', '2024-07-15 14:55:08', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32066, '新增', 32062, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:video:add', '#', 'admin', '2024-07-15 14:55:21', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32067, '新增', 32063, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:add', '#', 'admin', '2024-07-15 17:17:06', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32068, '编辑', 32063, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:edit', '#', 'admin', '2024-07-15 17:17:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32069, '删除', 32063, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:delete', '#', 'admin', '2024-07-15 17:17:32', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32070, '视频管理', 32063, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:manage', '#', 'admin', '2024-07-15 17:22:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32071, '编辑订单', 32039, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'order:manage:edit', '#', 'admin', '2024-07-16 10:05:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32072, '移除分组', 32063, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:remove', '#', 'admin', '2024-07-16 11:01:32', 'admin', '2024-07-16 11:01:43', '');
INSERT INTO `sys_menu` VALUES (32073, '排序更改', 32063, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:sort', '#', 'admin', '2024-07-16 11:02:42', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32074, '添加视频', 32063, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'case:group:add-video', '#', 'admin', '2024-07-16 11:04:05', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32075, '明细', 32026, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'merchant:balance:details', '#', 'admin', '2024-07-16 17:50:19', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32076, '余额提现', 32026, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'merchant:balance:withdraw', '#', 'admin', '2024-07-16 17:50:52', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32077, '同意', 32022, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:refundApproval:yes', '#', 'admin', '2024-07-16 17:57:09', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32078, '拒绝', 32022, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'finance:refundApproval:no', '#', 'admin', '2024-07-16 17:57:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32079, '取消工单', 32028, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'task:work:cancel', '#', 'admin', '2024-07-16 17:59:17', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32080, '重新打开', 32028, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'task:work:open', '#', 'admin', '2024-07-16 18:00:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (32081, '标记处理', 32028, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'task:work:markers', '#', 'admin', '2024-07-16 18:01:04', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int(4) NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '通知公告表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '模块标题',
  `business_type` int(2) DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求方式',
  `operator_type` int(1) DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '返回参数',
  `status` int(1) DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime(0) DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`oper_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '操作日志记录' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '岗位名称',
  `post_sort` int(4) NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '岗位信息表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'default', '默认岗位', 0, '0', 'admin', '2024-07-16 18:52:21', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'default' COMMENT '角色权限字符串',
  `role_sort` int(4) NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建者电话',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新者电话',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色信息表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '16544885522', '2024-05-13 10:02:50', '', NULL, NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '2', 1, 1, '0', '0', 'admin', '16544885522', '2024-05-13 10:02:50', 'admin', NULL, '2024-07-15 15:57:59', '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色和部门关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (2, 100);
INSERT INTO `sys_role_dept` VALUES (2, 101);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '角色和菜单关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '手机号码',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime(0) DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户信息表' ROW_FORMAT = Compact;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 101, 'admin', 'admin', '00', '<EMAIL>', '15888888888', NULL, '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '\\**********', '2024-07-17 00:11:21', 'admin', '2024-05-13 10:02:49', 'admin', '2024-07-17 00:11:22', 'admin');

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `post_id` bigint(20) NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户与岗位关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '用户和角色关联表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
