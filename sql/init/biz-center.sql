SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for business
-- ----------------------------
DROP TABLE IF EXISTS `business`;
CREATE TABLE `business`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '公司ID',
  `owner_account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商家主账号（FK：business_account.account）',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商家名称',
  `is_proxy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为代理(0:否,1:是)',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '账号状态（0正常 1禁用）',
  `customer_type` tinyint(1) DEFAULT 0 COMMENT '客户类型 （0-一般客户 1-重要客户）',
  `balance` decimal(12, 1) NOT NULL DEFAULT 0.0 COMMENT '帐号余额',
  `is_balance_lock` tinyint(1) NOT NULL DEFAULT 0 COMMENT '余额是否锁定（0-不锁定，1-锁定）',
  `waiter_id` bigint(20) DEFAULT NULL COMMENT '对接客服  FK：sys_user.user_id',
  `invoice_title_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '抬头类型  0-企业',
  `invoice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票抬头',
  `invoice_duty_paragraph` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '税号',
  `invoice_content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '发票内容',
  `member_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '会员编码',
  `member_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '会员类型: 0-非会员，1-会员',
  `member_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '会员状态：0-非会员1-正常，2-即将过期，3-已过期',
  `member_package_type` tinyint(1) DEFAULT NULL COMMENT '套餐类型：0-980套餐，1-1980套餐',
  `member_first_time` datetime(0) DEFAULT NULL COMMENT '会员首次购买时间',
  `member_first_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '首次购买套餐类型：0-980套餐，1-1980套餐',
  `member_last_time` datetime(0) DEFAULT NULL COMMENT '会员最近购买时间',
  `member_validity` datetime(0) DEFAULT NULL COMMENT '会员有效期',
  `is_exist_recent_order` tinyint(1) NOT NULL DEFAULT 0 COMMENT '近30天是否存在订单 0-不存在 1-存在',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人id FK: sys_user.user_id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人名称： FK: sys_user.user_name',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人id	FK: sys_user.user_id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人名称  FK: sys_user.user_name',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_owner_account`(`owner_account`) USING BTREE,
  INDEX `uk_member_code`(`member_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商家表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for business_account
-- ----------------------------
DROP TABLE IF EXISTS `business_account`;
CREATE TABLE `business_account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账号',
  `password` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `business_id` bigint(20) NOT NULL COMMENT '商家id FK: business.id',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '名称',
  `nick_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '微信昵称',
  `pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '头像',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'unionId',
  `external_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'ExternalUserID企业微信外部联系人id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '账号状态（0正常 1禁用 2被删除）',
  `last_login_time` datetime(0) DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0-未删除，1-已删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_account`(`account`) USING BTREE,
  UNIQUE INDEX `uk_unionid`(`unionid`) USING BTREE,
  UNIQUE INDEX `uk_external_user_id`(`external_user_id`) USING BTREE,
  INDEX `ck_business_id`(`business_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商家账号表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for business_account_collect_model
-- ----------------------------
DROP TABLE IF EXISTS `business_account_collect_model`;
CREATE TABLE `business_account_collect_model`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_id` bigint(20) DEFAULT NULL COMMENT '商家子账号id',
  `model_id` bigint(20) DEFAULT NULL COMMENT '模特id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '商家子账号收藏模特表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for business_balance_flow
-- ----------------------------
DROP TABLE IF EXISTS `business_balance_flow`;
CREATE TABLE `business_balance_flow`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_id` bigint(20) NOT NULL COMMENT '商家ID',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '订单号',
  `refund_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '退款单号',
  `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '视频编码',
  `balance` decimal(10, 2) NOT NULL COMMENT '帐号余额',
  `amount` decimal(12, 1) NOT NULL COMMENT '订单金额（单位：￥）',
  `type` tinyint(1) NOT NULL COMMENT '订单类型(0-收入、1-支出)',
  `origin` tinyint(1) NOT NULL COMMENT '订单来源(1-补偿订单收入、2-取消订单收入、3-取消选配收入、4.视频订单支出、5.会员订单支出)',
  `order_time` datetime(0) NOT NULL COMMENT '交易时间',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人id FK: business.id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人名称： FK: business.name',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人id	FK:  business.id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人名称  FK: business.name',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '余额流水表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for casus_group
-- ----------------------------
DROP TABLE IF EXISTS `casus_group`;
CREATE TABLE `casus_group`  (
  `id` bigint(16) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组名称',
  `platform` tinyint(1) NOT NULL COMMENT '平台类型：0:Amazon,1:tiktok,2:其他',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '创建人',
  `create_id` bigint(16) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '修改人',
  `update_id` bigint(16) DEFAULT NULL COMMENT '修改人id',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '案例分组表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for casus_video
-- ----------------------------
DROP TABLE IF EXISTS `casus_video`;
CREATE TABLE `casus_video`  (
  `id` bigint(16) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频名称',
  `pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '封面图片',
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频链接',
  `platform` tinyint(1) NOT NULL COMMENT '平台类型：0:Amazon,1:tiktok,2:其他',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '创建人',
  `create_id` bigint(16) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '修改人',
  `update_id` bigint(16) DEFAULT NULL COMMENT '修改人id',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '案例视频表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for document_remark
-- ----------------------------
DROP TABLE IF EXISTS `document_remark`;
CREATE TABLE `document_remark`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `document_id` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单据id',
  `document_type` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '单据类型（1:订单、2:工单、3:售后单……）',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注内容',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '单据备注表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for group_video
-- ----------------------------
DROP TABLE IF EXISTS `group_video`;
CREATE TABLE `group_video`  (
  `group_id` bigint(16) NOT NULL COMMENT '分组ID',
  `video_id` bigint(16) NOT NULL COMMENT '视频ID',
  `sort` int(8) NOT NULL DEFAULT 0 COMMENT '排序',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '创建人',
  `create_id` bigint(16) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '分组视频关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for logistic
-- ----------------------------
DROP TABLE IF EXISTS `logistic`;
CREATE TABLE `logistic`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号',
  `event` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '物流跟踪状态（TRACKING_UPDATED:持续更新,TRACKING_STOPPED:停止跟踪）',
  `carrier` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商名称',
  `carrier_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商代码',
  `carrier_alias` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商别名',
  `carrier_tel` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商联系电话',
  `carrier_homepage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商官网',
  `carrier_country` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输商所属国家',
  `sign_time` datetime(0) DEFAULT NULL COMMENT '签收时间',
  `sa_country` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_国家或地区（大写）',
  `sa_state` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_州、省',
  `sa_city` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_城市',
  `sa_street` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_街道',
  `sa_postal_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_邮编',
  `sa_longitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_经度',
  `sa_latitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件地址_纬度',
  `ra_country` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_国家或地区（大写）',
  `ra_state` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_州、省',
  `ra_city` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_城市',
  `ra_street` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_街道',
  `ra_postal_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_邮编',
  `ra_longitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_经度',
  `ra_latitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件地址_纬度',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '物流信息表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for logistic_info
-- ----------------------------
DROP TABLE IF EXISTS `logistic_info`;
CREATE TABLE `logistic_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '事件描述',
  `main_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '物流主状态',
  `sub_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '物流子状态',
  `cur_time` datetime(0) DEFAULT NULL COMMENT '发生时间',
  `country` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点在国家地区',
  `state` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点所在州、省',
  `city` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点所在城市',
  `street` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点所在街道',
  `longitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点所在经度',
  `latitude` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前节点所在纬度',
  `location` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '当前所在地点',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '物流信息详情表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for model
-- ----------------------------
DROP TABLE IF EXISTS `model`;
CREATE TABLE `model`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模特id',
  `type` tinyint(1) DEFAULT 0 COMMENT '模特类型(0:影响者,1:素人,3:都属于)',
  `type_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模特类型_字典值',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `sex` tinyint(1) NOT NULL DEFAULT 1 COMMENT '性别(1:男,0:女)',
  `sex_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性别_字典值',
  `birthday` date NOT NULL COMMENT '出生日期',
  `age` int(3) DEFAULT NULL COMMENT '年龄',
  `age_group` tinyint(1) DEFAULT NULL COMMENT '年龄层（1:婴幼儿,2:儿童,3:成年人,4:老年人）',
  `age_group_dict` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '年龄层_字典值',
  `nation` tinyint(1) NOT NULL COMMENT '国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）',
  `nation_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家_字典值',
  `recipient` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '收件人',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `state` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '州',
  `zipcode` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮编',
  `detail_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `source_from` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源',
  `pic` bigint(20) DEFAULT NULL COMMENT '模特图片（关联资源id）',
  `live_pic` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '生活场景照（关联资源id）',
  `amazon_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '亚马逊案例视频（关联资源id）',
  `tiktok_video` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'tiktok案例视频（关联资源id）',
  `platform` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '平台(0:Amazon,1:tiktok,2:其他)',
  `platform_dict` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台_字典值',
  `cooperation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '合作深度(0:一般模特,1:优质模特)',
  `acceptability` int(10) DEFAULT NULL COMMENT '待完成最高接受量',
  `commission_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）',
  `commission` decimal(12, 1) DEFAULT NULL COMMENT '模特佣金',
  `status` tinyint(1) DEFAULT 0 COMMENT '合作状态(0:正常,1:暂停,3:取消合作)',
  `top_time` datetime(0) DEFAULT NULL COMMENT '置顶时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模特信息表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for model_account
-- ----------------------------
DROP TABLE IF EXISTS `model_account`;
CREATE TABLE `model_account`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) NOT NULL COMMENT '模特Id',
  `account` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模特账号',
  `last_login_time` datetime(0) DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_model_id`(`model_id`) USING BTREE,
  UNIQUE INDEX `uk_account`(`account`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模特账号表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for model_person
-- ----------------------------
DROP TABLE IF EXISTS `model_person`;
CREATE TABLE `model_person`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) DEFAULT NULL COMMENT '模特id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '对接人id',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '模特对接人员表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for model_tag
-- ----------------------------
DROP TABLE IF EXISTS `model_tag`;
CREATE TABLE `model_tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) DEFAULT NULL COMMENT '模特id',
  `dict_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模特分类表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for model_travel
-- ----------------------------
DROP TABLE IF EXISTS `model_travel`;
CREATE TABLE `model_travel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model_id` bigint(20) NOT NULL COMMENT '模特id',
  `start_time` datetime(0) NOT NULL COMMENT '开始时间',
  `end_time` datetime(0) NOT NULL COMMENT '结束时间',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '行程原因',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:启用,1:禁用)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '模特行程表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for resource
-- ----------------------------
DROP TABLE IF EXISTS `resource`;
CREATE TABLE `resource`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '资源名称',
  `video_url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '视频url',
  `pic_url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片url',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '图片视频资源表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tag
-- ----------------------------
DROP TABLE IF EXISTS `tag`;
CREATE TABLE `tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `parent_id` bigint(20) NOT NULL COMMENT '父标签id',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `english_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签英文名称',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签分类path',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类id',
  `sort` int(11) DEFAULT 0 COMMENT '排序（暂时没用，默认0）',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（1:禁用，0:启用）',
  `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) DEFAULT NULL,
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL,
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标签表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for tag_category
-- ----------------------------
DROP TABLE IF EXISTS `tag_category`;
CREATE TABLE `tag_category`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父分类id',
  `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类path',
  `name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `remark` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) NOT NULL COMMENT '状态（0:启用,1:禁用）',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `create_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人手机号',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `update_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人手机号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标签分类表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for we_chat_external_user
-- ----------------------------
DROP TABLE IF EXISTS `we_chat_external_user`;
CREATE TABLE `we_chat_external_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `external_userid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'ExternalUserID企业微信外部联系人id',
  `unionid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'unionid',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户名',
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '职位',
  `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '头像url',
  `corp_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司名称',
  `corp_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '公司全名称',
  `type` tinyint(1) DEFAULT NULL COMMENT '该成员添加此外部联系人所打标签类型（1:企业设,2:用户自定义,3:规则组标签）',
  `gender` tinyint(1) DEFAULT NULL COMMENT '性别',
  `status` tinyint(1) DEFAULT 0 COMMENT '启用状态（0:启用,1:禁用）',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '企业微信外部联系人信息表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
