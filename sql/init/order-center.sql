SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for order_comment
-- ----------------------------
DROP TABLE IF EXISTS `order_comment`;
CREATE TABLE `order_comment`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频订单id',
  `comment_content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '备注内容',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人姓名',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户id',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_comment_id_IDX`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单备注表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_document_resource
-- ----------------------------
DROP TABLE IF EXISTS `order_document_resource`;
CREATE TABLE `order_document_resource`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号 (FK：order_table.order_num)',
  `resource_id` bigint(20) NOT NULL COMMENT '图片资源地址id（FK：resource.id）',
  `upload_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0-商家上传， 1-平台上传',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单支付凭证关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_feed_back
-- ----------------------------
DROP TABLE IF EXISTS `order_feed_back`;
CREATE TABLE `order_feed_back`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频订单id',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '成品url',
  `video_score` tinyint(1) DEFAULT NULL COMMENT '视频评分',
  `video_score_content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '评价内容',
  `over_time` datetime(0) DEFAULT NULL COMMENT '自动确认时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人姓名',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建用户id',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_feed_back_id_IDX`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单反馈表(商家)' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_feed_back_material
-- ----------------------------
DROP TABLE IF EXISTS `order_feed_back_material`;
CREATE TABLE `order_feed_back_material`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '视频订单id',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '驳回标题',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '备注（驳回理由）',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:待确认,1:已驳回,2:已完成',
  `reply_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '模特是否回复被驳回的素材（0:已回复,1:未回复）',
  `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_feed_back_material_id_IDX`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单反馈表(模特)' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_feed_back_material_info
-- ----------------------------
DROP TABLE IF EXISTS `order_feed_back_material_info`;
CREATE TABLE `order_feed_back_material_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `material_id` bigint(20) NOT NULL COMMENT '素材id',
  `link` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '链接',
  `note` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `upload_time` datetime(0) DEFAULT NULL COMMENT '上传时间',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单反馈表(模特)_详细信息' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_invoice
-- ----------------------------
DROP TABLE IF EXISTS `order_invoice`;
CREATE TABLE `order_invoice`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单号',
  `type` tinyint(1) DEFAULT NULL COMMENT '订单类型（0:视频订单,1:会员订单）',
  `pay_time` datetime(0) DEFAULT NULL COMMENT '支付时间',
  `merchant_id` bigint(20) DEFAULT NULL COMMENT '商家id',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发票抬头',
  `duty_paragraph` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '税号',
  `content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发票内容',
  `amount` decimal(12, 1) DEFAULT NULL COMMENT '开票金额',
  `status` tinyint(1) DEFAULT NULL COMMENT '开票状态（1:待开票,2:待确认,3:已投递,4:已作废）',
  `operator_by` bigint(20) DEFAULT NULL COMMENT '操作人',
  `operator_time` datetime(0) DEFAULT NULL COMMENT '操作时间',
  `resource_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '资源id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_num`(`order_num`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_发票表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_member
-- ----------------------------
DROP TABLE IF EXISTS `order_member`;
CREATE TABLE `order_member`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '订单状态(1待支付、2待审核、3交易成功、4交易关闭)',
  `package_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '套餐类型：0-980套餐，1-1980套餐, 2-4980套餐',
  `member_start_time` datetime(0) DEFAULT NULL COMMENT '订单开始时间',
  `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人id FK: business.id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人名称： FK: business.name',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint(20) DEFAULT NULL COMMENT '修改人id	FK:  business.id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人名称  FK: business.name',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_会员表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_note
-- ----------------------------
DROP TABLE IF EXISTS `order_note`;
CREATE TABLE `order_note`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单编号',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票抬头',
  `duty_paragraph` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '税号',
  `content` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '发票内容',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_商家开票信息' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_pay_info
-- ----------------------------
DROP TABLE IF EXISTS `order_pay_info`;
CREATE TABLE `order_pay_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `link_id` bigint(20) NOT NULL COMMENT '关联id',
  `link_type` bigint(20) NOT NULL COMMENT '关联类型(0:订单,1:小订单(视频))',
  `pay_type` tinyint(1) NOT NULL COMMENT '支付类型,0:微信,1:支付宝支付,2:云闪付/银联,3:数字人民币,4:未知支付方式',
  `video_price` decimal(10, 2) NOT NULL COMMENT '视频价格',
  `pic_price` decimal(10, 2) NOT NULL COMMENT '图片费用',
  `exchange_price` decimal(10, 2) NOT NULL COMMENT '手续费',
  `service_price` decimal(10, 2) NOT NULL COMMENT '服务费用',
  `current_exchange_rate` decimal(10, 4) NOT NULL COMMENT '当前汇率',
  `total_price` decimal(10, 2) NOT NULL COMMENT '总价',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单支付状态表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_table
-- ----------------------------
DROP TABLE IF EXISTS `order_table`;
CREATE TABLE `order_table`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
  `order_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单类型（0-视频订单，1-会员订单）',
  `video_count` int(5) NOT NULL DEFAULT 0 COMMENT '视频数量',
  `order_amount` decimal(12, 1) NOT NULL COMMENT '订单金额（单位：￥）',
  `pay_amount` decimal(12, 1) DEFAULT NULL COMMENT '支付金额（单位：￥）',
  `real_pay_amount` decimal(12, 2) NOT NULL DEFAULT 0.00 COMMENT '订单实付金额（单位：￥）',
  `current_exchange_rate` decimal(12, 4) DEFAULT NULL COMMENT '当前汇率',
  `use_balance` decimal(12, 1) NOT NULL DEFAULT 0.0 COMMENT '使用余额（单位：￥）',
  `tax_point` decimal(12, 1) DEFAULT NULL COMMENT '税点（单位：%）',
  `tax_point_cost` decimal(12, 1) DEFAULT NULL COMMENT '税点费用（单位：￥）',
  `order_user_id` bigint(20) NOT NULL COMMENT '下单用户id：FK  business_account.id',
  `order_time` datetime(0) NOT NULL COMMENT '下单时间',
  `pay_user_id` bigint(20) DEFAULT NULL COMMENT '支付用户id    FK  business_account.id',
  `pay_time` datetime(0) DEFAULT NULL COMMENT '支付时间',
  `pay_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '支付账户（对公转账）',
  `pay_type` tinyint(2) DEFAULT NULL COMMENT '支付方式（1:微信,2:支付宝支付,3:云闪付/银联,4.数字人民币,5.银行卡转账,6:对公转账,10:余额支付,11:微信支付+余额支付,12:支付宝支付+余额支付,13:云闪付/银联+余额支付,14.数字人民币+余额支付,15.银行卡转账+余额支付,16:对公转账+余额支付）',
  `audit_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '财务审核状态（0:待审核,1:审核通过,2.审核异常）',
  `is_record` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否入账：0-否，1-是',
  `record_time` datetime(0) DEFAULT NULL COMMENT '入账时间',
  `order_remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '订单备注',
  `merchant_id` bigint(20) NOT NULL COMMENT ' 商家id  FK  business.id',
  `merchant_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商家会员编码  FK  business.member_code',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_num`(`order_num`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_task
-- ----------------------------
DROP TABLE IF EXISTS `order_task`;
CREATE TABLE `order_task`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频订单id',
  `task_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工单编号',
  `type` tinyint(1) NOT NULL COMMENT '工单类型（1:要原视频,2:修改视频,3:重新上传,4:重拍视频,5:没拍照片,6:没拍视频,7:补拍视频,8:要剪辑）',
  `content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '工单内容',
  `submit_by` bigint(20) DEFAULT NULL COMMENT '提交人id',
  `submit_time` datetime(0) DEFAULT NULL COMMENT '提交时间',
  `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '视频编码',
  `product_chinese` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品中文名',
  `product_english` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品英文名',
  `shoot_model_id` bigint(20) DEFAULT NULL COMMENT '拍摄模特id',
  `order_user_id` bigint(20) DEFAULT NULL COMMENT '下单用户id',
  `priority` tinyint(1) DEFAULT NULL COMMENT '优先级（1:紧急,2:一般）',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '处理人id',
  `status` tinyint(1) DEFAULT 1 COMMENT '工单状态（1:待处理,2:处理中,3:已处理,4:已拒绝,5:已关闭）',
  `end_time` datetime(0) DEFAULT NULL COMMENT '关闭/完成时间',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_工单任务表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_task_flow_record
-- ----------------------------
DROP TABLE IF EXISTS `order_task_flow_record`;
CREATE TABLE `order_task_flow_record`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_num` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '工单编号',
  `time` datetime(0) DEFAULT NULL COMMENT '记录时间',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '处理人id',
  `operate_type` tinyint(1) DEFAULT NULL COMMENT '操作类型（1:创建工单,2:取消工单,3:重新打开,4:拒绝工单,5:标记处理中,6:标记已处理）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_工单任务_流转记录表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video
-- ----------------------------
DROP TABLE IF EXISTS `order_video`;
CREATE TABLE `order_video`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
  `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '视频编码',
  `status` tinyint(2) NOT NULL COMMENT '订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:待发货,6:待完成,7:已完成,8:交易关闭）',
  `platform` tinyint(1) NOT NULL COMMENT '使用平台(0:Amazon,1:tiktok,2:其他)',
  `is_care` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否照顾单（0=否,1=是）',
  `product_chinese` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品中文名',
  `product_english` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '产品英文名',
  `product_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品链接',
  `product_pic` bigint(20) DEFAULT NULL COMMENT '产品图（关联资源id）',
  `reference_video_link` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '参考视频链接',
  `video_format` tinyint(1) NOT NULL COMMENT '视频格式（1:横屏16：9,2:竖屏9：16）',
  `video_duration` int(10) DEFAULT NULL COMMENT '视频时长（单位：秒）',
  `clips_required` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '剪辑要求',
  `shooting_country` tinyint(1) NOT NULL COMMENT '拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）',
  `model_type` tinyint(1) NOT NULL COMMENT '模特类型（0:影响者,1:素人,3:都可以）',
  `is_object` tinyint(1) NOT NULL COMMENT '实物（0:是,1:不是）',
  `pic_count` tinyint(1) DEFAULT NULL COMMENT '照片数量（1:2张/$10,2:5张/$20）',
  `reference_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '参考图片（关联资源id，多个用,隔开）',
  `intention_model_id` bigint(20) DEFAULT NULL COMMENT '意向模特id',
  `shoot_model_id` bigint(20) DEFAULT NULL COMMENT '拍摄模特id',
  `logistic_flag` tinyint(1) DEFAULT NULL COMMENT '标记物流状态（1:标记发货）',
  `shipping_remark` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货备注',
  `shipping_pic` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发货备注图片（关联资源id，多个用,隔开）',
  `contact_id` bigint(20) DEFAULT NULL COMMENT '对接人id',
  `issue_id` bigint(20) DEFAULT NULL COMMENT '出单人id',
  `amount` decimal(12, 1) DEFAULT NULL COMMENT '视频金额（单位：￥）',
  `video_price` decimal(12, 1) DEFAULT NULL COMMENT '视频价格（单位：$）',
  `pic_price` decimal(12, 1) DEFAULT NULL COMMENT '图片费用（单位：$）',
  `exchange_price` decimal(12, 1) DEFAULT NULL COMMENT '手续费（单位：$）',
  `service_price` decimal(12, 1) DEFAULT NULL COMMENT '服务费（单位：$）',
  `order_flag` tinyint(1) DEFAULT NULL COMMENT '订单标记（1:已被标记）',
  `schedule_type` tinyint(1) DEFAULT NULL COMMENT '排单类型（1:排单,2:携带排单）',
  `commission_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）',
  `commission` decimal(12, 1) DEFAULT NULL COMMENT '模特佣金',
  `carry_type` tinyint(1) DEFAULT NULL COMMENT '携带类型（1:主携带,2:被携带）',
  `main_carry_count` int(11) DEFAULT NULL COMMENT '主携带数量',
  `allow_upload` tinyint(1) DEFAULT 1 COMMENT '商家是否允许运营上传素材至平台（0:允许,1:不允许）',
  `upload_platform_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运营上传至平台的url',
  `recipient` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件人（模特）',
  `city` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件城市（模特）',
  `state` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件州（模特）',
  `zipcode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件邮编（模特）',
  `detail_address` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件详细地址（模特）',
  `release_time` datetime(0) DEFAULT NULL COMMENT '订单释放时间（商家无意向模特且首次到达待匹配状态、首个意向模特不想要，以上两种情况会设置此值）',
  `release_flag` tinyint(1) DEFAULT 0 COMMENT '订单完全释放flag（1:表示视频订单不需要再过滤24小时，可以放到模特订单公池中）',
  `un_confirm_time` datetime(0) DEFAULT NULL COMMENT '订单进入待确认的时间',
  `un_match_flag` tinyint(1) DEFAULT 0 COMMENT '运营提交预选模特，订单不需要发货时的标记（1:已被标记）',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE  
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_carry
-- ----------------------------
DROP TABLE IF EXISTS `order_video_carry`;
CREATE TABLE `order_video_carry`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) NOT NULL COMMENT '模特id',
  `main_carry_video_id` bigint(20) NOT NULL COMMENT '主携带视频订单id',
  `carry_video_id` bigint(20) NOT NULL COMMENT '被携带视频订单id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_携带表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_case
-- ----------------------------
DROP TABLE IF EXISTS `order_video_case`;
CREATE TABLE `order_video_case` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频id',
  `send_id` bigint(20) NOT NULL COMMENT '发送人id',
  `send_content` varchar(300) NOT NULL COMMENT '发送内容',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `reply_id` bigint(20) DEFAULT NULL COMMENT '回复人id',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `reply_content` tinyint(1) DEFAULT NULL COMMENT '回复内容(1:同意,2:不同意)',
  `operate_edit` tinyint(1) DEFAULT '0' COMMENT '运营是否修改了订单（1:修改了,0:还没修改）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(255) DEFAULT NULL,
  `update_by` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_匹配情况反馈表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_info_history
-- ----------------------------
CREATE TABLE `order_video_info_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) DEFAULT NULL COMMENT '视频id',
  `shooting_country` tinyint(1) DEFAULT NULL COMMENT '拍摄国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）',
  `model_type` tinyint(1) DEFAULT NULL COMMENT '模特类型（0:影响者,1:素人,3:都可以）',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人id',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_信息_历史记录表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_logistic
-- ----------------------------
DROP TABLE IF EXISTS `order_video_logistic`;
CREATE TABLE `order_video_logistic`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频id',
  `number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '物流单号',
  `reissue` tinyint(1) NOT NULL COMMENT '是否补发（0:补发,1:不是补发）',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_物流关联表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_model
-- ----------------------------
DROP TABLE IF EXISTS `order_video_model`;
CREATE TABLE `order_video_model`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) DEFAULT NULL COMMENT '模特id',
  `video_id` bigint(20) DEFAULT NULL COMMENT '视频id',
  `accept_time` datetime(0) DEFAULT NULL COMMENT '接单时间（模特接到订单的时间，即流转到待完成的时间）',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_关联模特表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_model_search
-- ----------------------------
DROP TABLE IF EXISTS `order_video_model_search`;
CREATE TABLE `order_video_model_search`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `model_id` bigint(20) NOT NULL COMMENT '模特id',
  `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '搜索内容',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除（0:未删除,1:已删除）',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_模特搜索记录表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_model_select
-- ----------------------------
DROP TABLE IF EXISTS `order_video_model_select`;
CREATE TABLE `order_video_model_select`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频订单id',
  `preselect_id` bigint(20) NOT NULL COMMENT '预选id FK:order_video_preselect_model.id',
  `model_id` bigint(20) NOT NULL COMMENT '模特id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '当前进度状态（0:待处理,1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄）',
  `select_time` datetime(0) DEFAULT NULL COMMENT '选择时间',
  `over_time` datetime(0) DEFAULT NULL COMMENT '到期时间',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_模特选择记录表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_preselect_model
-- ----------------------------
DROP TABLE IF EXISTS `order_video_preselect_model`;
CREATE TABLE `order_video_preselect_model`  (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频id',
  `add_type` tinyint(1) DEFAULT NULL COMMENT '添加方式（1:意向模特,2:模特自选,3:运营添加）',
  `add_user_id` bigint(20) DEFAULT NULL COMMENT '添加人id',
  `add_time` datetime(0) DEFAULT NULL COMMENT '添加时间',
  `model_id` bigint(20) DEFAULT NULL COMMENT '模特id',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态（0:未对接,1:已对接,2:已选定,3:已淘汰）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
  `flag` tinyint(1) DEFAULT 0 COMMENT '商家标记（1:商家拒绝）',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_预选模特表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_refund
-- ----------------------------
DROP TABLE IF EXISTS `order_video_refund`;
CREATE TABLE `order_video_refund`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `apply_time` datetime(0) NOT NULL COMMENT '申请时间',
  `video_id` bigint(20) NOT NULL COMMENT '视频订单id',
  `order_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单号',
  `refund_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '退款单号',
  `status` tinyint(1) DEFAULT NULL COMMENT '申请时订单状态（1:待支付,2:待审核,3:待确认,4:待匹配,5:待发货,6:待完成,7:已完成）',
  `video_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '视频编码',
  `product_chinese` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品中文名',
  `product_english` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '产品英文名',
  `shoot_model_id` bigint(20) DEFAULT NULL COMMENT '拍摄模特id',
  `merchant_id` bigint(20) DEFAULT NULL COMMENT '商家id',
  `merchant_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '商家编码',
  `amount` decimal(12, 1) DEFAULT NULL COMMENT '视频金额（单位：￥）',
  `refund_amount` decimal(12, 1) DEFAULT NULL COMMENT '退款金额（单位：￥）',
  `refund_type` tinyint(1) DEFAULT NULL COMMENT '退款类型（1:补偿,2:取消订单,3:取消选配）',
  `initiator_source` tinyint(1) DEFAULT NULL COMMENT '发起方（1:商家,2:平台）',
  `initiator_id` bigint(20) DEFAULT NULL COMMENT '发起人id',
  `refund_cause` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '退款原因',
  `refund_status` tinyint(1) DEFAULT 0 COMMENT '退款状态（0:退款待审核,1:退款中,2:已拒绝,3:已取消,4:退款成功）',
  `operate_time` datetime(0) DEFAULT NULL COMMENT '操作时间',
  `reject_cause` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '拒绝理由',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_退款表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_remark
-- ----------------------------
DROP TABLE IF EXISTS `order_video_remark`;
CREATE TABLE `order_video_remark`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) DEFAULT NULL COMMENT '视频id',
  `operation_id` bigint(20) DEFAULT NULL COMMENT '备注人id',
  `content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注内容',
  `time` datetime(0) DEFAULT NULL COMMENT '备注时间',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_备注表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for order_video_tag
-- ----------------------------
DROP TABLE IF EXISTS `order_video_tag`;
CREATE TABLE `order_video_tag`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) DEFAULT NULL COMMENT '视频id',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  `category_id` bigint(20) DEFAULT NULL COMMENT '标签分类id',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '订单_视频_关联标签' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for third_pay_log
-- ----------------------------
DROP TABLE IF EXISTS `third_pay_log`;
CREATE TABLE `third_pay_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `curr_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '货币种类',
  `mchnt_order_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '商户订单号',
  `order_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内部订单号',
  `order_amt` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单金额',
  `order_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '订单类型：ALIPAY, WECHAT, UNIONPAY(银联二维码)等',
  `reserved_addn_inf` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `reserved_bank_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '付款方式',
  `reserved_buyer_logon_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '买家登录账号',
  `reserved_channel_order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '银行交易号',
  `reserved_coupon_fee` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '优惠金额',
  `reserved_fund_bill_list` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道信息',
  `reserved_fy_settle_dt` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '富友清算日',
  `reserved_fy_trace_no` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '富友追踪号',
  `reserved_is_credit` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '信用卡标识',
  `reserved_promotion_detail` varchar(6000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '微信营销详情',
  `reserved_settlement_amt` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '应结算订单金额',
  `settle_order_amt` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '应结订单金额',
  `term_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '终端号',
  `transaction_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '渠道流水号',
  `txn_fin_ts` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '支付完成时间',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '用户在商户的ID',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '修改时间',
  `remark` json COMMENT '回调数据',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `third_pay_log_mchnt_order_no_IDX`(`mchnt_order_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '支付回调记录表' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for video_content
-- ----------------------------
DROP TABLE IF EXISTS `video_content`;
CREATE TABLE `video_content`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频id',
  `target` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '目标对象（1:商家,2:运营,3:模特）',
  `type` tinyint(1) NOT NULL COMMENT '类型（1:拍摄要求,2:限制条件）',
  `content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '内容',  
  `sort` int(2) NOT NULL COMMENT '排序',
  `create_time` datetime(0) DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '视频_关联内容' ROW_FORMAT = Compact;

-- ----------------------------
-- Table structure for video_content_history
-- ----------------------------
DROP TABLE IF EXISTS `video_content_history`;
CREATE TABLE `video_content_history`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `video_id` bigint(20) NOT NULL COMMENT '视频id FK:order_video.id',
  `video_info_history_id` bigint(20) NOT NULL COMMENT '视频订单历史记录id FK:order_video_info_history.id',
  `target` varchar(5) DEFAULT NULL COMMENT '目标对象（1:商家,2:运营,3:模特）',
  `type` tinyint(1) NOT NULL COMMENT '类型（1:拍摄要求,2:限制条件）',
  `content` varchar(300) NOT NULL COMMENT '内容',
  `sort` int(2) NOT NULL COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB AUTO_INCREMENT = 3000 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '视频_关联内容_历史表' ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
