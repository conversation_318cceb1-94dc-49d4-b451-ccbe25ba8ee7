package com.wnkx.db.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.wnkx.db.properties.MybatisPlusAutoFillProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * mybatis-plus自动配置
 *
 * <AUTHOR>
 */
@EnableConfigurationProperties(MybatisPlusAutoFillProperties.class)
@RequiredArgsConstructor
public class MybatisPlusAutoConfigure {

    private final MybatisPlusAutoFillProperties autoFillProperties;

    /**
     * 分页插件
     *
     * @ deprecated 因ruoyi自带pagehelper与mybatis-plus冲突，故注释，使用pagehelper
     */
//    @Bean
//    public MybatisPlusInterceptor paginationInterceptor() {
//        MybatisPlusInterceptor mpInterceptor = new MybatisPlusInterceptor();
//        mpInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
//        return mpInterceptor;
//    }
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "wnkx.mybatis-plus.auto-fill", name = "enabled", havingValue = "true", matchIfMissing = true)
    public MetaObjectHandler metaObjectHandler() {
        return new DateMetaObjectHandler(autoFillProperties);
    }
}
