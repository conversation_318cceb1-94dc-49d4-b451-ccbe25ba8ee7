package com.wnkx.db.mapper;


import com.baomidou.mybatisplus.core.enums.SqlMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * mapper 父类，这里可以放一些公共的方法.
 * </p>
 * 注意这个类不要让 mp 扫描到！！
 *
 * <AUTHOR>
 */
public interface SuperMapper<T> extends BaseMapper<T> {
    Log log = LogFactory.getLog(SuperMapper.class); //NOPMD - suppressed ProperLogger - Interface 中的属性都是常量

    @Transactional
    default void saveBatch(Collection<T> values) {
        Class<?> currentMapperClass = AopProxyUtils.proxiedUserInterfaces(this)[0];
        String sqlStatement = SqlHelper.getSqlStatement(currentMapperClass, SqlMethod.INSERT_ONE);

        Class<?> currentModelClass = ReflectionKit.getSuperClassGenericType(this.getClass(), SuperMapper.class, 0);
        SqlHelper.executeBatch(currentModelClass, log, values, 1000,
            (sqlSession, entity) -> sqlSession.insert(sqlStatement, entity));
    }

    @Transactional
    default void updateBatchById(Collection<T> values) {
        Class<?> currentMapperClass = AopProxyUtils.proxiedUserInterfaces(this)[0];
        String sqlStatement = SqlHelper.getSqlStatement(currentMapperClass, SqlMethod.UPDATE_BY_ID);

        Class<?> currentModelClass = ReflectionKit.getSuperClassGenericType(this.getClass(), SuperMapper.class, 0);
        SqlHelper.executeBatch(currentModelClass, log, values, 1000, (sqlSession, entity) -> {
            MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap<>();
            param.put(Constants.ENTITY, entity);
            sqlSession.update(sqlStatement, param);
        });
    }

    @SuppressWarnings("unchecked")
    default void removeBatchByIds(Collection<?> collect) {
        Class<?> currentMapperClass = AopProxyUtils.proxiedUserInterfaces(this)[0];

        Class<T> modelClass = (Class<T>) ReflectionKit.getSuperClassGenericType(this.getClass(), SuperMapper.class, 0);
        TableInfo tableInfo = TableInfoHelper.getTableInfo(modelClass);
        String sqlStatement = SqlHelper.getSqlStatement(currentMapperClass, SqlMethod.DELETE_BY_ID);
        SqlHelper.executeBatch(modelClass, log, collect, 1000, (sqlSession, e) -> {
            if (tableInfo.isWithUpdateFill() && tableInfo.isWithLogicDelete()) {
                if (modelClass.isAssignableFrom(e.getClass())) {
                    sqlSession.update(sqlStatement, e);
                } else {
                    T instance = tableInfo.newInstance();
                    tableInfo.setPropertyValue(instance, tableInfo.getKeyProperty(), e);
                    sqlSession.update(sqlStatement, instance);
                }
            } else {
                sqlSession.update(sqlStatement, e);
            }
        });
    }
}
