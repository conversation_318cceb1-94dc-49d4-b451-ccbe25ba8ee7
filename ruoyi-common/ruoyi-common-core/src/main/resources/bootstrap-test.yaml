management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'
spring:
  cloud:
    nacos:
      config:
        file-extension: yml
        server-addr: ${wnkx.nacos.server-addr:10.160.0.1:8848}
        shared-configs:
          - data-id: common.yml
            refresh: true
      discovery:
        server-addr: ${wnkx.nacos.server-addr:10.160.0.1:8848}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: ${wnkx.sentinel.dashboard.server-addr:10.160.0.1:8718}
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active: test
