package com.ruoyi.common.core.exception;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public final class SystemException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public SystemException() {
    }

    public SystemException(String message) {
        this.message = message;
    }

    public SystemException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public SystemException setMessage(String message) {
        this.message = message;
        return this;
    }

    public SystemException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}