package com.ruoyi.common.core.exception;

/**
 * <AUTHOR>
 * @date 2024/10/23 16:03
 */
public class ConvertObjectException extends RuntimeException {
    private static final long serialVersionUID = -7165668262362747784L;
    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public ConvertObjectException() {
    }

    public ConvertObjectException(String message) {
        this.message = message;
    }

    public ConvertObjectException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }
}
