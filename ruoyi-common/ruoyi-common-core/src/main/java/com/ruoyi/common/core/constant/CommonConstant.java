package com.ruoyi.common.core.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
public interface CommonConstant {

    /**
     * 日志链路追踪id信息头
     */
    String TRACE_ID_HEADER = "x-traceId-header";
    /**
     * 日志链路追踪id日志标志
     */
    String LOG_TRACE_ID = "traceId";
    /**
     * 负载均衡策略-版本号 信息头
     */
    String W_N_K_X_VERSION = "w-n-k-x-version";

    /**
     * 安全指纹策略-指纹头 信息头
     */
    String W_N_K_X_FP = "w-n-k-x-fp";

    /**
     * 注册中心元数据 版本号
     */
    String METADATA_VERSION = "version";

    /**
     * 文件分隔符
     */
    String PATH_SPLIT = "/";

    Integer SHARE_OFF = 0;

    Integer SHARE_ON = 1;

    String X_FORWARDED_FOR = "X-Forwarded-For";

    /**
     * json头
     */
    String JSON_MEDIATYPE = "application/json;charset=utf-8";

    /**
     * 视频默认价格
     */
    BigDecimal VIDEO_PRICE = BigDecimal.valueOf(29.9);

    /**
     * 两图默认价格
     */
    Integer VIDEO_TWO_PIC_PRICE = 10;

    /**
     * 五图价格
     */
    Integer VIDEO_FIVE_PIC_PRICE = 20;

    /**
     * 手续费
     */
    Double PAYPAL_EXCHANGE_RATE = 0.044;

    /**
     * 手续费
     */
    Double PAYPAL_EXCHANGE_PRICE = 0.3;

    /**
     * 代理服务费
     */
    Integer PAYPAL_PROXY_SERVICE_PRICE = 3;

    /**
     * 非代理服务费
     */
    Integer PAYPAL_SERVICE_PRICE = 6;

    /**
     * 代理服务费_原价
     */
    Integer PAYPAL_PROXY_SERVICE_PRICE_ORIGIN = 6;

    /**
     * 非代理服务费_原价
     */
    Integer PAYPAL_SERVICE_PRICE_ORIGIN = 6;

    /**
     * 佣金代缴服务费比例
     */
    Double COMMISSION_PAYS_TAXES_RATE = 0.06;

    Integer QUICK_LINE_LESS_GROUP_NUM = 4;


    String ACCESS_TOKEN = "access_token";
    String PDF_FILE_NAME = "凭证信息";
    String PDF_COMPANY = "泉州润一进出口贸易有限公司";
    String PDF_NAME = "海外视频拍摄服务费";
    String PDF_RECEIPT_COMPANY = "泉州润一进出口贸易有限公司";
    String PDF_OPENING_BANK = "中国农业银行股份有限公司南安东田支行";
    String PDF_PROCEEDS_ACCOUNT = "*****************";
}
