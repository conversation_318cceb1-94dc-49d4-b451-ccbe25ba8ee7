package com.ruoyi.common.core.exception;

/**
 * 检查异常
 * 
 * <AUTHOR>
 */
public class CheckedException extends RuntimeException
{

    private static final long serialVersionUID = 8087153879403703437L;
    //    错误信息放msg里
    private static Integer ERROR_IN_MSG = 1;

    //    错误信息放data里
    public static Integer ERROR_IN_DATA = 2;


    private Integer type;
    public Integer getType() {
        return type;
    }

    public CheckedException(Integer type, String message) {
        super(message);
        this.type = type;
    }

    public CheckedException(String message) {
        super(message);
        this.type = 1;
    }


    public CheckedException(Throwable cause)
    {
        super(cause);
    }

    public CheckedException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public CheckedException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace)
    {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
