package com.ruoyi.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 缓存有效期，默认5760（分钟） = 72个小时
     */
    public final static long EXPIRATION = 5760;

    /**
     * 商家端缓存有效期  30天
     */
    public final static long BUSINESS_EXPIRATION = 43200;

    /**
     * 缓存刷新时间，默认120（分钟）
     */
    public final static long REFRESH_TIME = 120;


    public final static long MINUTE_SECOND = 60;

    /**
     * 权限缓存前缀
     */
    public final static String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * account_login
     */
    public final static String ACCOUNT_LOGIN_KEY = "account_login:";
    /**
     * 缓存的模特token前缀
     */
    public final static String MODEL_TOKEN_KEY = "model_tokens:";

    /**
     * 当前汇率
     */
    public final static String EXCHANGE_RATE_KEY = "exchange_rate";

    /**
     * 汇率有效期 默认300（秒）
     */
    public final static long EXCHANGE_RATE_EXPIRATION = 300;

    public final static String PROCESS_ORDER_KEY = "process_key:";
    public final static String THIRD_PAY_KEY= "third_pay_key:";
    public final static String ALIPAY_PAY_KEY= "alipay_pay_key:";
    public final static String FY_ORDER_TABLE_KEY= "fy_order_table_key:";
    public final static String ALIPAY_ORDER_TABLE_KEY= "alipay_order_table_key:";
    public final static String ORDER_FLOW_KEY = "order_flow_key:";
    public final static String ORDER_LOGISTIC_FOLLOW_FLOW_KEY = "order_logistic_follow_flow_key:";
    public final static String ORDER_PAY_LOCK_KEY = "order_pay_lock_key:";
    public final static String ORDER_MEMBER_FLOW_KEY = "order_member_flow_key:";
    public final static String MEMBER_SEED_RECORD_WITHDRAWAL_INIT_KEY = "member_seed_record_withdrawal_init_key:";
    public final static String REFUND_FLOW_KEY = "refund_flow_key:";
    public final static String VIDEO_CART_LOCK_KEY = "video_cart_lock_key:";

    public final static String ORDER_NUMBER_PREFIX = "out_trade_order_number:";
    public final static String ALIPAY_ORDER_NUMBER_PREFIX = "alipay_out_trade_order_number:";

    /**
     * 企业微信accessToken缓存前缀
     */
    public final static String WORK_WX_CACHE_ACCESS_TOKEN = "work_wx_access_token";

    /**
     * 微信登录二维码status前缀
     */
    public final static String WECHAT_LOGIN_STATUS_PREFIX = "wechat_login_status:";
    /**
     * 微信登录二维码ip缓存前缀
     */
    public final static String WECHAT_LOGIN_UNION_ID_IP_PREFIX = "wechat_login_status:unionid:";
    /**
     * 企业微信登录前缀
     */
    public final static String WORK_WECHAT_LOGIN_STATUS_PREFIX = "work_wechat_login:";


    public final static String WECHAT_JOIN_BUSINESS_CODE_UNIONID_PREFIX = "wechat_join_business_code_unionid:";

    public final static String WECHAT_JOIN_BUSINESS_UNIONI_DTO_PREFIX = "wechat_join_business_unionid_join_business_dto:";

    /**
     * 企业微信ticket unionId缓存
     */
    public final static String WORK_WECHAT_LOGIN_TICKET_UNIONID_PREFIX = "work_wechat_login_ticket_union_id:";

    /**
     * 微信登录默认过期时间
     */
    public final static Long WECHAT_LOGIN_EXPIRATION_TIMEOUT = 600L;

    /**
     * 初始化商家账号计数KAY
     */
    public final static String BUSINESS_ACCOUNT_KEY = "business_account_key:";

    /**
     * 专属链接code 计数KAY
     */
    public final static String DISTRIBUTION_CHANNEL_LINK_CODE = "distribution_channel_link_code:";

    /**
     * 种草码code 计数KAY
     */
    public final static String DISTRIBUTION_CHANNEL_SEED_CODE = "distribution_channel_seed_code:";

    /**
     * 种草码ID 计数KAY
     */
    public final static String DISTRIBUTION_CHANNEL_SEED_ID = "distribution_channel_seed_id:";

    /**
     * 初始化模特账号计数KEY
     */
    public final static String MODEL_ACCOUNT_KEY = "model_account_key:";

    /**
     * 企业微信unionId extraId缓存
     */
    public final static String WORK_WX_UNIONID_EXTRA_PREFIX = "work_wechat_login_unionid_extra:";


    /**
     * 延迟队列 队列处理锁key
     */
    public final static String DELAY_QUEUE_PROCESS_QUEUE_LOCK_KEY = "delay_queue_process_queue_lock_key:";
    /**
     * 延迟队列 任务处理锁key
     */
    public final static String DELAY_QUEUE_PROCESS_TASK_LOCK_KEY = "delay_queue_process_task_lock_key:";
    /**
     * 延迟队列 正在进行的队列key
     */
    public final static String DELAY_QUEUE_RUNNING_KEY = "delay_queue_running_key:";

    /**
     * 页面精选配置信息key
     */
    public final static String PAGE_CHOOSE_CASE_CONFIG_KEY = "page_choose_case_config_key:";

    /**
     * 首页配置信息Key
     */
    public final static String PAGE_HOME_PAGE_CONFIG_KEY = "page_home_page_config_key:";

    /**
     * 页面配置过期时间
     */
    public final static Long PAGE_CONFIG_TIMEOUT = 24L;

    /**
     * ticket - phone
     */
    public final static String PHONE_LOGIN_TICKET_PHONE_KEY = "phone_login_ticket_phone_key:";

    public final static String BANG_ACCOUNT = "bang_account:";

    /**
     * phone - ticket
     */
    public final static String PHONE_LOGIN_PHONE_TICKET_KEY = "phone_login_phone_ticket_key:";


    /**
     * 手机号验证码过期限制
     */
    public final static String SMS_PHONE = "sms_phone:";
    /**
     * 手机号验证码默认过期时间 10分钟
     */
    public final static long SMS_PHONE_EXPIRATION = 10;
    /**
     * 再次获取手机验证码时间限制 540s
     */
    public final static long SMS_PHONE_AGAIN_EXPIRATION = 540;

    /**
     * 手机号验证码安全限制有效期 600s
     */
    public final static long SMS_SECURITY_PHONE_EXPIRATION = 600;

    /**
     * 手机号验证码安全限制
     */
    public final static String SMS_SECURITY_PHONE = "sms_security_phone:";

    /**
     * 取消模特合作
     */
    public final static String MODEL_LOGIN_LOCK_KEY = "model_login_lock_key:";

    public final static String CRAWL_PRODUCT_PIC_KEY = "crawl_product_pic_key:";

    /**
     * 市场渠道访问
     */
    public final static String MARKETING_CHANNEL_VISIT = "marketing_channel_visit:";

    /**
     * 任务单流转
     */
    public final static String ORDER_TASK_FLOW_KEY = "order_task_flow_key:";


    /**
     * 初始化商家余额流水号技数
     */
    public final static String BUSINESS_BALANCE_FLOW_NO_KEY = "business_balance_flow_no_key:";


    /**
     * 模特黑名单处理
     */
    public final static String BIZ_USER_BLACK_MODEL_KEY = "biz_user_black_model_key:";

    /**
     * 订单回退锁Key
     */
    public final static String ORDER_ROLLBACK_LOCK_KEY = "order_rollback_lock_key:";

    /**
     * 订单回退锁有效期
     */
    public final static Long ORDER_ROLLBACK_LOCK_KEY_SECOND = 60L;

    /**
     * 商家回访操作缓存KEY
     */
    public final static String BUSINESS_RETURN_VISIT_KEY = "business_return_visit_key:";

    /**
     * 商家回访操作缓存KEY有效期
     */
    public final static Long BUSINESS_RETURN_VISIT_KEY_SECOND = 60L;

    /**
     * 模特申请订单缓存KEY
     */
    public final static String MODEL_APPLY_KEY = "model_apply_key:";

    /**
     * 匹配单操作锁KEY
     */
    public final static String ORDER_MATCH_LOCK_KEY = "order_match_lock_key:";

    /**
     * 匹配单操作锁KEY有效期
     */
    public final static Long ORDER_MATCH_LOCK_KEY_SECOND = 60L;


    /**
     * 当前生效的用户Id
     */
    public final static String CURRENT_CONTACT_USER_ID = "add_contact_info:current_user_id";

    /**
     * 当前生效的url前缀
     */
    public final static String CURRENT_ACTIVE_URL_PREFIX = "add_contact_info:current_active_url_";

    /**
     * 渠道添加人redisKey
     */
    public final static String CURRENT_CHANNEL_ACTIVE_KEY_PREFIX = "add_contact_info:current_channel_active:";

    /**
     * 合并订单操作锁KEY
     */
    public final static String ORDER_MERGE_LOCK_KEY = "order_merge_lock_key:";

    /**
     * 合并订单操作锁KEY有效期
     */
    public final static Long ORDER_MERGE_LOCK_KEY_SECOND = 60L;

    /**
     * amazon抓图操作Key
     */
    public final static String AMAZON_COOKIE_CACHE_KEY = "amazon_cookie_cache_key";

    /**
     * 模特反馈素材详情操作锁KEY有效期
     */
    public final static Long MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY_SECOND = 60L;

    /**
     * 模特反馈素材详情操作锁KEY
     */
    public final static String MODEL_FEED_BACK_MATERIAL_INFO_LOCK_KEY = "model_feed_back_material_info_lock_key:";

    /**
     * 订单上传链接操作锁KEY有效期
     */
    public final static Long ORDER_UPLOAD_LINK_LOCK_KEY_SECOND = 60L;

    /**
     * 订单上传链接操作锁KEY
     */
    public final static String ORDER_UPLOAD_LINK_LOCK_KEY = "order_upload_link_lock_key:";

    /**
     * 分销渠道结算操作锁KEY有效期
     */
    public final static Long CHANNEL_SETTLEMENT_LOCK_KEY_SECOND = 60L;

    /**
     * 分销渠道结算操作锁KEY
     */
    public final static String CHANNEL_SETTLEMENT_LOCK_KEY = "channel_settlement_lock_key:";


    public final static String RECHARGE_PREPAY_NUM_LOCK_KEY = "recharge_prepay_num_lock_key:";

    /**
     * 子账号入驻 微信授权code 跟 BizUser 缓存KEY
     */
    public final static String SUB_ACCOUNT_WECHAT_AUTH_CODE_CACHE_KEY = "sub_account_wechat_auth_code_cache_key:";

    /**
     * 提醒商家会员临期半价续费KEY
     */
    public final static String REMIND_BUSINESS_MEMBER_HALF_PRICE_RENEWAL_KEY = "remind_business_member_half_price_renewal_key:";


    /**
     * 初始化种草码提现计数KAY
     */
    public final static String MEMBER_SEED_RECORD_WITHDRAWAL_NUM_KEY = "member_seed_record_withdrawal_num_key:";

    /**
     * 当前服务费redis缓存
     */
    public final static String CURRENT_SERVICE_FEE_CACHE_KEY = "order_base_config:current_service_fee_cache_key";

    /**
     * 待生效服务费缓存
     */
    public final static String PENDING_EFFECT_SERVICE_FEE_CACHE_KEY = "order_base_config:pending_effect_service_fee_cache_key";

    /**
     * 创建视频订单操作锁KEY
     */
    public final static String VIDEO_ORDER_CREATE_LOCK_KEY = "video_order_create_lock_key:";

    /**
     * 创建视频订单操作锁KEY有效期
     */
    public final static Long VIDEO_ORDER_CREATE_LOCK_KEY_SECOND = 60L;

}
