package com.ruoyi.common.core.exception.file;

import org.apache.commons.fileupload.FileUploadException;

import java.util.Arrays;

/**
 * 文件上传 误异常类
 * 
 * <AUTHOR>
 */
public class InvalidExtensionException extends FileUploadException
{

    private static final long serialVersionUID = -3035468522821196059L;
    private String[] allowedExtension;
    private String extension;
    private String filename;

    public InvalidExtensionException(String[] allowedExtension, String extension, String filename) {
        super("只允许上传" + Arrays.toString(allowedExtension) + "类型的文件");
        this.allowedExtension = allowedExtension;
        this.extension = extension;
        this.filename = filename;
    }

    public String[] getAllowedExtension()
    {
        return allowedExtension;
    }

    public String getExtension()
    {
        return extension;
    }

    public String getFilename()
    {
        return filename;
    }

    public static class InvalidImageExtensionException extends InvalidExtensionException
    {

        private static final long serialVersionUID = -8659911020282260725L;

        public InvalidImageExtensionException(String[] allowedExtension, String extension, String filename)
        {
            super(allowedExtension, extension, filename);
        }
    }

    public static class InvalidFlashExtensionException extends InvalidExtensionException
    {

        private static final long serialVersionUID = -5362370838367401882L;

        public InvalidFlashExtensionException(String[] allowedExtension, String extension, String filename)
        {
            super(allowedExtension, extension, filename);
        }
    }

    public static class InvalidMediaExtensionException extends InvalidExtensionException
    {

        private static final long serialVersionUID = 2823808193885702219L;

        public InvalidMediaExtensionException(String[] allowedExtension, String extension, String filename)
        {
            super(allowedExtension, extension, filename);
        }
    }
    
    public static class InvalidVideoExtensionException extends InvalidExtensionException
    {

        private static final long serialVersionUID = -1783933418810447877L;

        public InvalidVideoExtensionException(String[] allowedExtension, String extension, String filename)
        {
            super(allowedExtension, extension, filename);
        }
    }
}
