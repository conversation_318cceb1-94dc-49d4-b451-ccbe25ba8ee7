package com.ruoyi.common.core.validated.annotation;

import com.ruoyi.common.core.validated.handle.EnumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = EnumValidator.class)
public @interface EnumValid {

    Class<? extends Enum<?>> enumClass();

    String message() default "Value is not valid for the specified enum";

    String enumField() default "code"; // 默认用于比较的枚举字段

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}