package com.ruoyi.common.core.validated.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.validated.annotation.EnumValid;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
public class EnumValidator implements ConstraintValidator<EnumValid, Object> {

    private Class<? extends Enum<?>> enumClass;
    private String enumField;

    @Override
    public void initialize(EnumValid constraintAnnotation) {
        this.enumClass = constraintAnnotation.enumClass();
        this.enumField = constraintAnnotation.enumField();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 允许空值，交给 @NotNull 或其他注解处理
        }
        List<String> valueList = getValue(value);
        try {
            // 通过反射获取枚举中的指定字段（如 code 字段）
            Field field = enumClass.getDeclaredField(enumField);
            field.setAccessible(true); // 允许访问私有字段

            // 遍历所有枚举实例，检查指定字段的值是否与传入的值匹配
            List<String> enumCodes = Arrays.stream(enumClass.getEnumConstants()).map(item -> {
                try {
                    return Convert.toStr(field.get(item));
                } catch (Exception e) {
                    return StrUtil.EMPTY;
                }
            }).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            return CollUtil.containsAll(enumCodes, valueList);
        } catch (Exception e) {
            // 如果字段不存在或其他反射异常，视为无效值
            return false;
        }
    }

    private List<String> getValue(Object value) {
        List<String> validString = new ArrayList<>();
        if (value instanceof Integer) {
            validString.add(Convert.toStr(value));
        } else if (value instanceof String) {
            validString.addAll(StrUtil.split(value.toString(), StrUtil.COMMA));
        } else if (value instanceof Collection) {
            ((Collection<?>) value).forEach(item -> {
                validString.add(Convert.toStr(item));
            });
        }
        return validString;
    }
}