package com.ruoyi.common.core.constant;

/**
 * Token的Key常量
 *
 * <AUTHOR>
 */
public class TokenConstants
{
    /**
     * 令牌自定义标识
     */
    public static final String AUTHENTICATION = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String PREFIX = "Bearer ";

    /**
     * 令牌秘钥
     */
    public final static String SECRET = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 登录类型-后台端登录
     */
    public final static String BACK = "back";
    /**
     * 登录类型-商家端登录
     */
    public final static String BUSINESS = "business";

    public final static String MODEL = "model";

    public final static String CHANNEL = "channel";
    /**
     * 注册类型-注册
     */
    public final static Integer TOKEN_REGISTER = 0;
    /**
     * 绑定类型-重新绑定
     */
    public final static Integer TOKEN_REBIND = 1;
    /**
     * 验证类型-验证
     */
    public final static Integer TOKEN_VERIFY = 2;

    /**
     * 新注册类型-注册登录
     */
    public final static Integer TOKEN_REGISTER_PLUS = 3;

    /**
     * 渠道类型-注册登录
     */
    public final static Integer CHANNEL_REGISTER = 4;

    /**
     * 商家端-子账号绑定
     */
    public final static Integer TOKEN_SUB_ACCOUNT_VER_PLUS = 5;


    //    REG REB VER 这三不参与随机数的分配
    public static final String BASE_CHAR_NUMBER = "acdfhijklmnopqstuwxyz".toUpperCase() + "acdfhijklmnopqstuwxyz0123456789";

    /**
     * 登录
     */
    public static final String REG = "REG";

    /**
     * 新注册/登录
     */
    public static final String REG_PLUS = "PLUSREG";

    /**
     * 商家端-子账号绑定
     */
    public static final String SUB_ACCOUNT_VER_PLUS = "SUBACCOUNTVERPLUS";

    /**
     * 绑定
     */
    public static final String REB = "REB";

    /**
     * 校验
     */
    public static final String VER = "VER";

    /**
     * 渠道
     */
    public static final String CHANNEL_REG = "ChannelREG";


    /**
     * 子账号绑定
     * */
    public static final String SUB_ACCOUNT_VER = "SubAccountVer";

    public static final String W_N_K_X_FP = "w-n-k-x-fp";

}
