package com.ruoyi.common.core.constant;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-06-24 17:35
 *
 * 已弃用 枚举类移动至
 * @see com.ruoyi.common.core.enums.PackageTypeEnum
 **/
@Deprecated(since = "2024-09-23", forRemoval = true)
public class TypeConstant {
    public static final Map<Integer, Integer> MEMBER_PACKAGE_DAY_MAP;
    public static final Map<Integer, BigDecimal> MEMBER_PACKAGE_PRICE_MAP;
    public static final Integer monthType = 0;
    public static final Integer yearType = 1;
    public static final Integer threeYearType = 2;

    static {
        MEMBER_PACKAGE_DAY_MAP = new HashMap<>();
        MEMBER_PACKAGE_DAY_MAP.put(monthType, 3);
        MEMBER_PACKAGE_DAY_MAP.put(yearType, 12);
        MEMBER_PACKAGE_DAY_MAP.put(threeYearType, 36);

        MEMBER_PACKAGE_PRICE_MAP = new HashMap<>();
        MEMBER_PACKAGE_PRICE_MAP.put(monthType, BigDecimal.valueOf(980));
        MEMBER_PACKAGE_PRICE_MAP.put(yearType, BigDecimal.valueOf(1980));
        MEMBER_PACKAGE_PRICE_MAP.put(threeYearType, BigDecimal.valueOf(4980));
    }


}
