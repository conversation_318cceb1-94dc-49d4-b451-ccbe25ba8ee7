package com.ruoyi.common.core.exception.file;

import cn.hutool.http.HttpStatus;
import com.ruoyi.common.core.exception.base.BaseException;

/**
 * 文件信息异常类
 *
 * <AUTHOR>
 */
public class FileException extends BaseException
{

    private static final long serialVersionUID = -1348310749437446329L;

    public FileException(String code, Object[] args)
    {
        super("file", code, args, null);
    }

    public FileException(String code, String message)
    {
        super("file", code, null, message);
    }

    public FileException(String message) {
        super("file", Integer.toString(HttpStatus.HTTP_INTERNAL_ERROR), null, message);
    }

}
