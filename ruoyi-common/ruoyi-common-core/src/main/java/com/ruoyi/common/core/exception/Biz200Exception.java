package com.ruoyi.common.core.exception;

import com.ruoyi.common.core.exception.base.BaseException;

/**
 * 业务异常 但是返回的是200
 * 用于部分业务中虽然异常 但是前端继续操作的情况
 * 500会被统一拦截器所处理
 *
 * <AUTHOR>
 */
public class Biz200Exception extends BaseException {

    private static final long serialVersionUID = -6033476447923210150L;
    //    错误信息放msg里
    private static Integer ERROR_IN_MSG = 1;

    //    错误信息放data里
    public static Integer ERROR_IN_DATA = 2;

    public static String SUCCESS = "success";


    private Integer type;
    public Integer getType() {
        return type;
    }



    public static Biz200Exception verifyError() {
        return new Biz200Exception(ERROR_IN_DATA, SUCCESS);
    }

    public Biz200Exception(Integer type, String message) {
        super(message);
        this.type = type;
    }

    public Biz200Exception(String message) {
        super(message);
        this.type = 1;
    }

    public Biz200Exception(String code, Object[] args) {
        super("biz200", code, args, null);
    }

    public Biz200Exception(String code, String message) {
        super("biz200", code, null, message);
    }

}
