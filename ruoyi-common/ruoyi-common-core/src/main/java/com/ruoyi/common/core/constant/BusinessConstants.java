package com.ruoyi.common.core.constant;

/**
 * 商家常量
 * <AUTHOR>
 */
public interface BusinessConstants {
    /**
     * 会员随机字符取值
     */
    public final static String randomKey = "ABCDEFGHJKLMNPQRSTUVWXYZ";

    public final static Integer preTimeout = 30;

    public final static String remoteExceptionPrefix = "remote_exception:";

    /**
     * 种草码记录提现编号前缀：FXTX
     */
    String MEMBER_SEED_RECORD_WITHDRAWAL_NUM_FXTX = "FXTX";


    // 通品：开始匹配时间 < 4天（即第0、1、2天）
    public static final int COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD = 4;
    // 非通品：开始匹配时间 < 1天（即第0天）
    public static final int NON_COMMON_PRODUCT_QUALITY_MODEL_DAYS_THRESHOLD = 1;

}
