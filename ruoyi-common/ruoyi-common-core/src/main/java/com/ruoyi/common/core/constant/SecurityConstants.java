package com.ruoyi.common.core.constant;

/**
 * 权限相关通用常量
 *
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner-2d7a6465e9ce45c98c4f838aa7a53621";

    /**
     * token类型
     */
    public static final String TOKEN_TYPE = "token_type";

    /**
     * 用户标识
     */
    public static final String USER_KEY = "user_key";

    /**
     * 登录用户
     */
    public static final String LOGIN_USER = "login_user";

    /**
     * 当前账号ID
     */
    public static final String BIZ_USER_ID = "biz_user_id";

    /**
     * 当前Ip
     */
    public static final String CURRENT_IP = "current_ip";
}
