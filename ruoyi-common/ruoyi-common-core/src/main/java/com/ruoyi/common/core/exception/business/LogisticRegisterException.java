package com.ruoyi.common.core.exception.business;

import lombok.Data;

/**
 * 物流注册单号异常
 */
@Data
public class LogisticRegisterException extends RuntimeException {


    private static final long serialVersionUID = -7318967258865336738L;
    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public LogisticRegisterException()
    {
    }

    public LogisticRegisterException(String message)
    {
        this.message = message;
    }

    public LogisticRegisterException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }
}
