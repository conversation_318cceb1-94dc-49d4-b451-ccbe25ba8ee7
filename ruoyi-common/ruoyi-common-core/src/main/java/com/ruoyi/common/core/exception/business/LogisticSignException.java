package com.ruoyi.common.core.exception.business;

import lombok.Data;

/**
 * 物流密钥校验不通过
 */
@Data
public class LogisticSignException extends RuntimeException {


    private static final long serialVersionUID = -6326137778394436455L;
    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public LogisticSignException()
    {
    }

    public LogisticSignException(String message)
    {
        this.message = message;
    }

    public LogisticSignException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }
}
