package com.ruoyi.common.core.constant;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.enums.ModelStatusEnum;

/**
 * 业务代码常量
 */
public interface BizConstant {
    //sort最大值
    Long maxSort = 100000L;
    //最大合作深度
    Integer cooperationMax = 4;

    String MODEL_SORTING_RULE = StrUtil.builder()
            .append("CASE WHEN m.STATUS = ").append(ModelStatusEnum.NORMAL.getCode()).append( " THEN 1  ").append("WHEN m.STATUS = ").append(ModelStatusEnum.JOURNEY.getCode()).append(" THEN 2  ").append("WHEN m.STATUS = ").append(ModelStatusEnum.PAUSE.getCode()).append(" THEN 3 ").append("WHEN m.STATUS = ").append(ModelStatusEnum.CANCEL.getCode()).append(" THEN 4 END,")
            .append("CASE WHEN m.top_time IS NOT NULL THEN ").append((cooperationMax * maxSort) + " + m.sort*10 + cooperationSort ")
            .append("WHEN m.top_time IS NULL THEN ").append(maxSort + "*cooperationSort + m.sort END DESC,")
            .append("m.create_time ASC , ")
            .append("m.id ASC ")
            .toString();

}
